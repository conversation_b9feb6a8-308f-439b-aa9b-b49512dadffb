const axios = require('axios');

async function importTestData() {
  try {
    console.log('开始导入测试数据...');
    
    const response = await axios.post('http://localhost:3001/api/import', {
      count: 5,
      downloadImages: true
    }, {
      headers: {
        'Authorization': 'Bearer admin123',
        'Content-Type': 'application/json'
      },
      timeout: 300000 // 5分钟超时
    });
    
    console.log('导入成功:', response.data);
    
  } catch (error) {
    console.error('导入失败:', error.response?.data || error.message);
  }
}

importTestData();

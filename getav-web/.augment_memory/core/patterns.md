# GETAV.NET 成功实现模式

## React/Next.js 开发模式

### 1. 组件设计模式

#### 复合组件模式 (Compound Components)
```typescript
// 用于复杂UI组件，如影片卡片
const MovieCard = {
  Root: MovieCardRoot,
  Image: MovieCardImage,
  Title: MovieCardTitle,
  Meta: MovieCardMeta,
  Actions: MovieCardActions
}
```

#### 自定义Hook模式
```typescript
// 数据获取和状态管理
const useMovieData = (movieId: string) => {
  const { data, error, isLoading } = useSWR(`/api/movies/${movieId}`, fetcher)
  return { movie: data, error, isLoading }
}
```

#### 渲染属性模式 (Render Props)
```typescript
// 用于数据展示组件
<DataProvider>
  {({ data, loading, error }) => (
    loading ? <Spinner /> : <MovieList movies={data} />
  )}
</DataProvider>
```

### 2. 状态管理模式

#### Zustand Store 模式
```typescript
// 全局状态管理
const useAuthStore = create<AuthState>((set) => ({
  user: null,
  login: (user) => set({ user }),
  logout: () => set({ user: null })
}))
```

#### SWR 数据缓存模式
```typescript
// 服务端状态管理
const { data, mutate } = useSWR('/api/movies', fetcher, {
  revalidateOnFocus: false,
  dedupingInterval: 60000
})
```

### 3. 表单处理模式

#### React Hook Form + Zod 验证
```typescript
const schema = z.object({
  email: z.string().email(),
  password: z.string().min(8)
})

const { register, handleSubmit, formState: { errors } } = useForm({
  resolver: zodResolver(schema)
})
```

## 数据库设计模式

### 1. Prisma ORM 模式

#### 关联查询优化
```typescript
// 使用 include 预加载关联数据
const movie = await prisma.movie.findUnique({
  where: { id },
  include: {
    stars: { include: { star: true } },
    genres: { include: { genre: true } },
    magnets: true
  }
})
```

#### 批量操作模式
```typescript
// 使用事务处理批量操作
await prisma.$transaction([
  prisma.movie.create({ data: movieData }),
  prisma.movieStar.createMany({ data: starRelations })
])
```

### 2. 数据验证模式

#### 输入验证层
```typescript
// API 路由输入验证
const createMovieSchema = z.object({
  id: z.string(),
  title: z.string(),
  img: z.string().url().optional()
})
```

#### 数据转换模式
```typescript
// 数据库到API的转换
const transformMovieData = (movie: MovieWithRelations) => ({
  ...movie,
  stars: movie.stars.map(ms => ms.star),
  genres: movie.genres.map(mg => mg.genre)
})
```

## API 设计模式

### 1. RESTful API 模式

#### 资源路由设计
```
GET    /api/movies          # 获取影片列表
GET    /api/movies/:id      # 获取单个影片
POST   /api/movies          # 创建影片
PUT    /api/movies/:id      # 更新影片
DELETE /api/movies/:id      # 删除影片
```

#### 分页查询模式
```typescript
// 标准分页参数
interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}
```

### 2. 错误处理模式

#### 统一错误响应
```typescript
interface ApiError {
  code: string
  message: string
  details?: any
}

const handleApiError = (error: unknown) => {
  if (error instanceof PrismaClientKnownRequestError) {
    return { code: 'DATABASE_ERROR', message: error.message }
  }
  return { code: 'INTERNAL_ERROR', message: 'Internal server error' }
}
```

## 性能优化模式

### 1. 图片优化模式

#### Next.js Image 组件
```typescript
<Image
  src={movie.localImg || movie.img}
  alt={movie.title}
  width={300}
  height={400}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

#### 图片预加载策略
```typescript
// 关键图片预加载
const preloadImages = (urls: string[]) => {
  urls.forEach(url => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = url
    document.head.appendChild(link)
  })
}
```

### 2. 缓存策略模式

#### Redis 缓存模式
```typescript
const getCachedData = async (key: string) => {
  const cached = await redis.get(key)
  if (cached) return JSON.parse(cached)
  
  const data = await fetchFromDatabase()
  await redis.setex(key, 3600, JSON.stringify(data))
  return data
}
```

#### SWR 缓存配置
```typescript
const swrConfig = {
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  refreshInterval: 300000, // 5分钟
  dedupingInterval: 60000  // 1分钟
}
```

## 安全模式

### 1. 认证授权模式

#### NextAuth.js 配置
```typescript
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      credentials: {
        email: { type: "email" },
        password: { type: "password" }
      },
      authorize: async (credentials) => {
        // 验证逻辑
      }
    })
  ],
  session: { strategy: "jwt" }
}
```

#### 权限检查模式
```typescript
const requireAuth = (handler: NextApiHandler) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const session = await getServerSession(req, res, authOptions)
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    return handler(req, res)
  }
}
```

### 2. 数据验证模式

#### 输入清理
```typescript
const sanitizeInput = (input: string) => {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/[<>]/g, '')
}
```

## 测试模式

### 1. 单元测试模式

#### 组件测试
```typescript
describe('MovieCard', () => {
  it('renders movie information correctly', () => {
    render(<MovieCard movie={mockMovie} />)
    expect(screen.getByText(mockMovie.title)).toBeInTheDocument()
  })
})
```

#### API 测试
```typescript
describe('/api/movies', () => {
  it('returns movies list', async () => {
    const res = await request(app).get('/api/movies')
    expect(res.status).toBe(200)
    expect(res.body.data).toBeInstanceOf(Array)
  })
})
```

### 2. 集成测试模式

#### 数据库测试
```typescript
beforeEach(async () => {
  await prisma.movie.deleteMany()
  await prisma.movie.create({ data: testMovieData })
})
```

## 部署模式

### 1. 环境配置模式

#### 环境变量管理
```typescript
const config = {
  database: {
    url: process.env.DATABASE_URL!
  },
  redis: {
    url: process.env.REDIS_URL!
  },
  nextauth: {
    secret: process.env.NEXTAUTH_SECRET!
  }
}
```

### 2. 构建优化模式

#### Next.js 配置优化
```typescript
const nextConfig = {
  images: {
    domains: ['localhost', 'javbus.com'],
    formats: ['image/webp', 'image/avif']
  },
  experimental: {
    optimizePackageImports: ['sharp']
  }
}
```

## 监控和日志模式

### 1. 错误监控
```typescript
const logError = (error: Error, context: string) => {
  console.error(`[${context}] ${error.message}`, {
    stack: error.stack,
    timestamp: new Date().toISOString()
  })
}
```

### 2. 性能监控
```typescript
const measurePerformance = async (operation: string, fn: () => Promise<any>) => {
  const start = Date.now()
  const result = await fn()
  const duration = Date.now() - start
  console.log(`[PERF] ${operation}: ${duration}ms`)
  return result
}
```

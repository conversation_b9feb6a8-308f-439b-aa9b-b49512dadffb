# GETAV.NET 技术栈详细信息

## 核心技术

### 运行时环境
- **Node.js**: 现代JavaScript运行时
- **版本要求**: Node.js 18+ (推荐 LTS 版本)
- **包管理器**: npm (项目使用npm作为主要包管理器)

### 编程语言
- **TypeScript 5.x**: 主要开发语言
- **JavaScript**: 部分配置文件和脚本
- **配置**: 严格模式，完整类型检查
- **目标**: ES2017，支持现代浏览器

### 主要框架
- **Next.js 15.3.3**: React全栈框架
  - App Router: 新的路由系统
  - Server Components: 服务端组件支持
  - 内置优化: 图片、字体、脚本优化
- **React 19.0.0**: 前端UI库
  - 最新版本特性
  - 并发特性支持
  - 服务端组件兼容

## 前端技术栈

### UI框架和库
- **React 19.0.0**: 核心UI库
- **Next.js 15.3.3**: 全栈React框架
- **React DOM 19.0.0**: DOM渲染器

### 样式和设计
- **Tailwind CSS 4.x**: 原子化CSS框架
  - 现代化配置
  - 自定义主题支持
  - 响应式设计
- **@tailwindcss/line-clamp 0.4.4**: 文本截断插件
- **PostCSS**: CSS后处理器
- **tw-animate-css 1.3.4**: Tailwind动画扩展

### 状态管理
- **Zustand 5.0.5**: 轻量级状态管理
  - 简单易用的API
  - TypeScript友好
  - 中间件支持
- **SWR 2.3.3**: 数据获取和缓存
  - 自动重新验证
  - 错误重试
  - 缓存管理

### 表单处理
- **React Hook Form 7.57.0**: 表单状态管理
  - 高性能表单处理
  - 最小重新渲染
  - 内置验证支持
- **@hookform/resolvers 5.1.0**: 验证器集成
- **Zod 3.25.56**: 运行时类型验证
  - TypeScript集成
  - 强大的验证规则
  - 错误消息定制

### 动画和交互
- **Framer Motion 12.16.0**: 动画库
  - 声明式动画API
  - 手势支持
  - 布局动画
- **Lucide React 0.513.0**: 图标库
  - 现代化图标设计
  - Tree-shaking支持
  - 可定制性强

### UI组件
- **@radix-ui/react-progress 1.1.7**: 进度条组件
- **@radix-ui/react-slot 1.2.3**: 插槽组件
- **@radix-ui/react-tabs 1.1.12**: 标签页组件
- **class-variance-authority 0.7.1**: 样式变体管理
- **clsx 2.1.1**: 条件类名工具
- **tailwind-merge 3.3.0**: Tailwind类名合并

## 后端技术栈

### 认证和授权
- **NextAuth.js 4.24.11**: 认证解决方案
  - 多种登录方式支持
  - JWT和数据库会话
  - 安全最佳实践
- **@auth/prisma-adapter 2.9.1**: Prisma适配器
- **@next-auth/prisma-adapter 1.0.7**: 兼容适配器
- **bcryptjs 3.0.2**: 密码哈希
- **jsonwebtoken 9.0.2**: JWT处理

### 数据库和ORM
- **Prisma 6.9.0**: 现代ORM
  - 类型安全的数据库访问
  - 自动迁移管理
  - 强大的查询构建器
- **@prisma/client 6.9.0**: Prisma客户端
- **PostgreSQL**: 主数据库
  - 关系型数据库
  - JSON支持
  - 高性能查询

### 缓存和存储
- **Redis 5.5.6**: 内存数据库
  - 高性能缓存
  - 会话存储
  - 队列支持
- **Sharp 0.34.2**: 图片处理
  - 高性能图片转换
  - 多格式支持
  - 内存优化

### HTTP和网络
- **Axios 1.9.0**: HTTP客户端
  - Promise基础
  - 请求/响应拦截器
  - 自动JSON处理

## 开发工具

### 构建和打包
- **Next.js内置构建系统**: 
  - Webpack配置
  - Turbopack支持
  - 自动优化
- **TypeScript编译器**: 类型检查和转译

### 代码质量
- **ESLint 9**: JavaScript/TypeScript代码检查
  - 现代化配置
  - 自定义规则
  - IDE集成
- **eslint-config-next 15.3.3**: Next.js官方配置
- **@typescript-eslint**: TypeScript支持
- **Prettier 3.5.3**: 代码格式化
  - 一致的代码风格
  - 自动格式化
  - 编辑器集成

### Git工作流
- **Husky 9.1.7**: Git钩子管理
  - 提交前检查
  - 推送前验证
  - 自动化工作流
- **lint-staged 16.1.0**: 暂存文件检查
  - 只检查变更文件
  - 提高检查效率
  - 自定义命令

### 类型定义
- **@types/node 20.19.0**: Node.js类型定义
- **@types/react 19**: React类型定义
- **@types/react-dom 19**: React DOM类型定义
- **@types/bcryptjs 2.4.6**: bcryptjs类型定义
- **@types/jsonwebtoken 9.0.9**: JWT类型定义
- **@types/redis 4.0.10**: Redis类型定义
- **@types/sharp 0.31.1**: Sharp类型定义

## 常用命令

### 开发命令
```bash
# 启动开发服务器 (端口 3001)
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 清理缓存
npm run clear-cache
npm run clear-homepage-cache
```

### 数据库命令
```bash
# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev

# 重置数据库
npx prisma migrate reset

# 查看数据库
npx prisma studio
```

### 代码质量命令
```bash
# ESLint检查
npx eslint . --ext .ts,.tsx

# Prettier格式化
npx prettier --write .

# TypeScript类型检查
npx tsc --noEmit
```

## 项目配置文件

### 核心配置
- **package.json**: 项目依赖和脚本
- **next.config.ts**: Next.js配置
- **tsconfig.json**: TypeScript配置
- **tailwind.config.js**: Tailwind CSS配置
- **postcss.config.mjs**: PostCSS配置

### 代码质量配置
- **eslint.config.mjs**: ESLint配置
- **.prettierrc**: Prettier配置
- **components.json**: UI组件配置

### 数据库配置
- **prisma/schema.prisma**: 数据库模式定义

### 环境配置
- **.env.local**: 本地环境变量
- **.env.example**: 环境变量示例

## 性能优化配置

### Next.js优化
```typescript
// next.config.ts
const nextConfig = {
  images: {
    formats: ['image/webp', 'image/avif'],
    domains: ['localhost', 'javbus.com'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 200, 256, 384, 400, 800, 1200]
  },
  experimental: {
    optimizePackageImports: ['sharp']
  }
}
```

### TypeScript优化
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2017",
    "module": "esnext",
    "moduleResolution": "bundler",
    "strict": true,
    "noEmit": true,
    "incremental": true
  }
}
```

## 部署配置

### 生产环境要求
- **Node.js**: 18+ LTS版本
- **PostgreSQL**: 12+版本
- **Redis**: 6+版本
- **内存**: 最少2GB RAM
- **存储**: SSD推荐

### 环境变量
```bash
# 数据库
DATABASE_URL="postgresql://..."

# Redis
REDIS_URL="redis://..."

# NextAuth
NEXTAUTH_SECRET="..."
NEXTAUTH_URL="https://..."

# 其他配置
NODE_ENV="production"
```

## 扩展性考虑

### 技术栈升级路径
- **Next.js**: 跟进最新版本，利用新特性
- **React**: 采用最新稳定版本
- **TypeScript**: 保持最新版本
- **Prisma**: 定期更新，利用性能改进

### 可选技术集成
- **测试框架**: Jest + Testing Library
- **E2E测试**: Playwright
- **监控**: Sentry
- **分析**: Google Analytics
- **CDN**: Cloudflare
- **部署**: Vercel/Docker

### 性能监控
- **Core Web Vitals**: 页面性能指标
- **数据库性能**: 查询时间监控
- **API响应时间**: 接口性能追踪
- **错误率**: 错误监控和报警

## 安全配置

### 内容安全策略
```typescript
// next.config.ts
const nextConfig = {
  images: {
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;"
  }
}
```

### 依赖安全
- 定期更新依赖包
- 使用npm audit检查漏洞
- 配置Dependabot自动更新
- 监控安全公告

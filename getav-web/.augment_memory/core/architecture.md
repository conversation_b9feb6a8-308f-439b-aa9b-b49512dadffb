# GETAV.NET 项目架构设计

## 项目概述

GETAV.NET 是一个基于 Next.js 15 + React 19 的成人内容网站，专注于影片数据管理、展示和用户交互功能。

## 技术架构

### 前端架构
- **框架**: Next.js 15.3.3 (App Router) + React 19.0.0
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS 4.x + Tailwind Line Clamp
- **状态管理**: Zustand 5.0.5
- **数据获取**: SWR 2.3.3
- **表单处理**: React Hook Form 7.57.0 + Zod 3.25.56
- **动画**: Framer Motion 12.16.0
- **图标**: Lucide React

### 后端架构
- **认证**: NextAuth.js 4.24.11 + Prisma Adapter
- **数据库**: PostgreSQL + Prisma ORM 6.9.0
- **缓存**: Redis 5.5.6
- **图片处理**: Sharp 0.34.2
- **HTTP客户端**: Axios 1.9.0
- **加密**: bcryptjs + jsonwebtoken

### 数据库设计
- **核心实体**: Movie, Star, Genre, Director, Producer, Publisher, Series
- **关联关系**: 多对多关系通过中间表实现
- **用户系统**: NextAuth.js 兼容的用户认证体系
- **交互功能**: Comment, Like, Favorite, Rating, View

### 文件结构
```
getav-web/
├── src/
│   ├── app/                 # Next.js App Router
│   ├── components/          # React 组件
│   ├── hooks/              # 自定义 Hooks
│   ├── lib/                # 工具库和配置
│   ├── stores/             # Zustand 状态管理
│   ├── styles/             # 样式文件
│   └── types/              # TypeScript 类型定义
├── prisma/                 # Prisma 数据库配置
├── public/                 # 静态资源
└── scripts/                # 脚本文件
```

## 核心功能模块

### 1. 影片管理模块
- 影片数据展示和搜索
- 封面图片优化和缓存
- 磁力链接管理
- 相似影片推荐

### 2. 用户认证模块
- NextAuth.js 认证系统
- 用户注册和登录
- 密码加密和验证
- 会话管理

### 3. 用户交互模块
- 评论系统
- 点赞和反对
- 收藏功能
- 评分系统
- 观看记录

### 4. 数据采集模块
- JAVBUS API 集成
- 图片下载和存储
- 数据清洗和验证
- 批量导入功能

## 性能优化

### 图片优化
- Sharp 图片处理
- WebP/AVIF 格式支持
- 多尺寸响应式图片
- CDN 缓存策略

### 数据库优化
- Prisma 查询优化
- 索引策略
- 分页查询
- 缓存机制

### 前端优化
- Next.js 静态生成
- 代码分割
- 懒加载
- SWR 数据缓存

## 安全考虑

### 数据安全
- 密码哈希加密
- JWT Token 验证
- SQL 注入防护
- XSS 攻击防护

### 内容安全
- 图片内容验证
- 用户输入过滤
- 访问权限控制
- 成人内容标识

## 部署架构

### 开发环境
- 本地开发服务器 (端口 3001)
- PostgreSQL 数据库
- Redis 缓存服务
- 图片存储目录

### 生产环境
- Next.js 生产构建
- 数据库连接池
- CDN 图片分发
- 负载均衡

## 扩展性设计

### 模块化设计
- 组件复用
- 功能模块分离
- API 接口标准化
- 数据模型抽象

### 国际化支持
- 多语言支持预留
- 时区处理
- 货币格式化
- 文化适配

## 监控和日志

### 性能监控
- 页面加载时间
- API 响应时间
- 数据库查询性能
- 错误率统计

### 日志记录
- 用户行为日志
- 错误日志
- 安全日志
- 性能日志

## 维护和更新

### 代码质量
- ESLint 代码检查
- Prettier 代码格式化
- TypeScript 类型检查
- Git 提交钩子

### 版本控制
- 语义化版本
- 变更日志
- 数据库迁移
- 回滚策略

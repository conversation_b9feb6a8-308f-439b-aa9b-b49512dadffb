# GETAV.NET 最佳实践指南

## React/Next.js 开发最佳实践

### 1. 组件设计原则

#### 单一职责原则
```typescript
// ✅ 好的实践 - 单一职责
const MovieTitle = ({ title }: { title: string }) => (
  <h2 className="text-xl font-bold">{title}</h2>
)

// ❌ 避免 - 职责过多
const MovieCard = ({ movie }) => {
  // 包含了数据获取、状态管理、UI渲染等多个职责
}
```

#### 组件组合优于继承
```typescript
// ✅ 好的实践 - 组合
const MovieCard = ({ children, ...props }) => (
  <div className="movie-card" {...props}>
    {children}
  </div>
)

// 使用
<MovieCard>
  <MovieTitle title={movie.title} />
  <MovieMeta date={movie.date} />
</MovieCard>
```

#### Props接口设计
```typescript
// ✅ 好的实践 - 明确的接口定义
interface MovieCardProps {
  movie: Movie
  onLike?: (movieId: string) => void
  onFavorite?: (movieId: string) => void
  showActions?: boolean
  className?: string
}

const MovieCard: React.FC<MovieCardProps> = ({
  movie,
  onLike,
  onFavorite,
  showActions = true,
  className
}) => {
  // 组件实现
}
```

### 2. 状态管理最佳实践

#### Zustand Store 设计
```typescript
// ✅ 好的实践 - 模块化Store
interface AuthState {
  user: User | null
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
}

const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isLoading: false,
  login: async (credentials) => {
    set({ isLoading: true })
    try {
      const user = await authService.login(credentials)
      set({ user, isLoading: false })
    } catch (error) {
      set({ isLoading: false })
      throw error
    }
  },
  logout: () => set({ user: null })
}))
```

#### SWR 数据获取
```typescript
// ✅ 好的实践 - 自定义Hook封装
const useMovies = (params: MovieSearchParams) => {
  const { data, error, isLoading, mutate } = useSWR(
    ['movies', params],
    ([_, params]) => movieService.getMovies(params),
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000,
      errorRetryCount: 3
    }
  )

  return {
    movies: data?.data || [],
    totalCount: data?.total || 0,
    error,
    isLoading,
    refresh: mutate
  }
}
```

### 3. 表单处理最佳实践

#### React Hook Form + Zod
```typescript
// ✅ 好的实践 - 类型安全的表单
const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(8, '密码至少8位字符')
})

type LoginFormData = z.infer<typeof loginSchema>

const LoginForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema)
  })

  const onSubmit = async (data: LoginFormData) => {
    try {
      await authService.login(data)
    } catch (error) {
      // 错误处理
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input
        {...register('email')}
        type="email"
        placeholder="邮箱"
      />
      {errors.email && <span>{errors.email.message}</span>}
      
      <input
        {...register('password')}
        type="password"
        placeholder="密码"
      />
      {errors.password && <span>{errors.password.message}</span>}
      
      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? '登录中...' : '登录'}
      </button>
    </form>
  )
}
```

## 数据库最佳实践

### 1. Prisma 查询优化

#### 避免N+1查询
```typescript
// ✅ 好的实践 - 使用include预加载
const getMoviesWithRelations = async () => {
  return await prisma.movie.findMany({
    include: {
      stars: {
        include: { star: true }
      },
      genres: {
        include: { genre: true }
      },
      magnets: true
    }
  })
}

// ❌ 避免 - N+1查询
const getMoviesWithRelationsBad = async () => {
  const movies = await prisma.movie.findMany()
  for (const movie of movies) {
    movie.stars = await prisma.movieStar.findMany({
      where: { movieId: movie.id },
      include: { star: true }
    })
  }
  return movies
}
```

#### 分页查询
```typescript
// ✅ 好的实践 - 高效分页
const getMoviesPaginated = async (page: number, limit: number) => {
  const skip = (page - 1) * limit
  
  const [movies, total] = await Promise.all([
    prisma.movie.findMany({
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        stars: { include: { star: true } }
      }
    }),
    prisma.movie.count()
  ])

  return {
    data: movies,
    total,
    page,
    totalPages: Math.ceil(total / limit)
  }
}
```

#### 事务处理
```typescript
// ✅ 好的实践 - 使用事务保证数据一致性
const createMovieWithRelations = async (movieData: CreateMovieData) => {
  return await prisma.$transaction(async (tx) => {
    // 创建影片
    const movie = await tx.movie.create({
      data: {
        id: movieData.id,
        title: movieData.title,
        img: movieData.img
      }
    })

    // 创建演员关联
    if (movieData.stars?.length > 0) {
      await tx.movieStar.createMany({
        data: movieData.stars.map(starId => ({
          movieId: movie.id,
          starId
        }))
      })
    }

    // 创建分类关联
    if (movieData.genres?.length > 0) {
      await tx.movieGenre.createMany({
        data: movieData.genres.map(genreId => ({
          movieId: movie.id,
          genreId
        }))
      })
    }

    return movie
  })
}
```

### 2. 数据验证

#### 输入验证
```typescript
// ✅ 好的实践 - 多层验证
const createMovieSchema = z.object({
  id: z.string().regex(/^[A-Z0-9-]+$/, '影片ID格式不正确'),
  title: z.string().min(1, '标题不能为空').max(200, '标题过长'),
  img: z.string().url('图片URL格式不正确').optional(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式不正确').optional()
})

// API路由中使用
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validatedData = createMovieSchema.parse(body)
    
    const movie = await movieService.create(validatedData)
    return NextResponse.json({ data: movie })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入数据格式错误', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
```

## API 设计最佳实践

### 1. RESTful API 设计

#### 资源命名
```typescript
// ✅ 好的实践 - 清晰的资源命名
GET    /api/movies              // 获取影片列表
GET    /api/movies/:id          // 获取单个影片
POST   /api/movies              // 创建影片
PUT    /api/movies/:id          // 更新影片
DELETE /api/movies/:id          // 删除影片

GET    /api/movies/:id/comments // 获取影片评论
POST   /api/movies/:id/comments // 创建影片评论
```

#### 统一响应格式
```typescript
// ✅ 好的实践 - 统一的API响应格式
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 成功响应
const successResponse = <T>(data: T, message?: string): ApiResponse<T> => ({
  success: true,
  data,
  message
})

// 错误响应
const errorResponse = (error: string): ApiResponse<null> => ({
  success: false,
  error
})
```

### 2. 错误处理

#### 全局错误处理
```typescript
// ✅ 好的实践 - 统一错误处理
class ApiError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

const handleApiError = (error: unknown) => {
  console.error('API Error:', error)

  if (error instanceof ApiError) {
    return NextResponse.json(
      { error: error.message, code: error.code },
      { status: error.statusCode }
    )
  }

  if (error instanceof z.ZodError) {
    return NextResponse.json(
      { error: '输入数据格式错误', details: error.errors },
      { status: 400 }
    )
  }

  if (error instanceof PrismaClientKnownRequestError) {
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: '数据已存在', code: 'DUPLICATE_ENTRY' },
        { status: 409 }
      )
    }
  }

  return NextResponse.json(
    { error: '服务器内部错误' },
    { status: 500 }
  )
}
```

## 性能优化最佳实践

### 1. 图片优化

#### Next.js Image 组件
```typescript
// ✅ 好的实践 - 优化的图片组件
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  priority = false,
  className
}: {
  src: string
  alt: string
  width: number
  height: number
  priority?: boolean
  className?: string
}) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      priority={priority}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      className={className}
    />
  )
}
```

#### 图片预加载策略
```typescript
// ✅ 好的实践 - 智能预加载
const useImagePreload = (urls: string[]) => {
  useEffect(() => {
    const preloadImages = urls.slice(0, 5) // 只预加载前5张图片
    
    preloadImages.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = url
      document.head.appendChild(link)
    })

    return () => {
      // 清理预加载的link标签
      preloadImages.forEach(url => {
        const link = document.querySelector(`link[href="${url}"]`)
        if (link) {
          document.head.removeChild(link)
        }
      })
    }
  }, [urls])
}
```

### 2. 缓存策略

#### Redis 缓存
```typescript
// ✅ 好的实践 - 分层缓存策略
class CacheService {
  private redis: Redis

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await this.redis.get(key)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value))
    } catch (error) {
      console.error('Cache set error:', error)
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key)
    } catch (error) {
      console.error('Cache delete error:', error)
    }
  }

  // 缓存模式：先查缓存，没有则查数据库并缓存
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    const cached = await this.get<T>(key)
    if (cached) return cached

    const data = await fetcher()
    await this.set(key, data, ttl)
    return data
  }
}
```

## 安全最佳实践

### 1. 输入验证和清理

#### XSS 防护
```typescript
// ✅ 好的实践 - 输入清理
import DOMPurify from 'isomorphic-dompurify'

const sanitizeInput = (input: string): string => {
  // 移除HTML标签
  const cleaned = DOMPurify.sanitize(input, { ALLOWED_TAGS: [] })
  
  // 额外的清理
  return cleaned
    .trim()
    .replace(/[<>]/g, '') // 移除尖括号
    .slice(0, 1000) // 限制长度
}

// 在API中使用
export async function POST(request: Request) {
  const body = await request.json()
  
  // 清理用户输入
  const cleanedData = {
    ...body,
    title: sanitizeInput(body.title),
    content: sanitizeInput(body.content)
  }
  
  // 继续处理...
}
```

### 2. 认证和授权

#### 权限检查中间件
```typescript
// ✅ 好的实践 - 权限检查
const withAuth = (handler: NextApiHandler) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      const session = await getServerSession(req, res, authOptions)
      
      if (!session) {
        return res.status(401).json({ error: '未授权访问' })
      }

      // 将用户信息添加到请求中
      ;(req as any).user = session.user
      
      return handler(req, res)
    } catch (error) {
      return res.status(500).json({ error: '认证服务错误' })
    }
  }
}

// 使用
export default withAuth(async (req, res) => {
  const user = (req as any).user
  // 处理需要认证的逻辑
})
```

## 测试最佳实践

### 1. 单元测试

#### 组件测试
```typescript
// ✅ 好的实践 - 完整的组件测试
describe('MovieCard', () => {
  const mockMovie = {
    id: 'TEST-001',
    title: '测试影片',
    img: 'https://example.com/image.jpg',
    date: '2024-01-01'
  }

  it('renders movie information correctly', () => {
    render(<MovieCard movie={mockMovie} />)
    
    expect(screen.getByText(mockMovie.title)).toBeInTheDocument()
    expect(screen.getByText(mockMovie.date)).toBeInTheDocument()
    expect(screen.getByRole('img')).toHaveAttribute('alt', mockMovie.title)
  })

  it('calls onLike when like button is clicked', async () => {
    const onLike = jest.fn()
    render(<MovieCard movie={mockMovie} onLike={onLike} />)
    
    const likeButton = screen.getByRole('button', { name: /like/i })
    await userEvent.click(likeButton)
    
    expect(onLike).toHaveBeenCalledWith(mockMovie.id)
  })
})
```

### 2. API 测试

#### 集成测试
```typescript
// ✅ 好的实践 - API集成测试
describe('/api/movies', () => {
  beforeEach(async () => {
    // 清理测试数据
    await prisma.movie.deleteMany()
  })

  it('creates a new movie', async () => {
    const movieData = {
      id: 'TEST-001',
      title: '测试影片',
      img: 'https://example.com/image.jpg'
    }

    const response = await request(app)
      .post('/api/movies')
      .send(movieData)
      .expect(201)

    expect(response.body.data).toMatchObject(movieData)
    
    // 验证数据库中的数据
    const savedMovie = await prisma.movie.findUnique({
      where: { id: movieData.id }
    })
    expect(savedMovie).toBeTruthy()
  })
})
```

## 代码组织最佳实践

### 1. 文件结构

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 路由组
│   ├── api/               # API 路由
│   └── globals.css        # 全局样式
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── forms/            # 表单组件
│   └── layout/           # 布局组件
├── hooks/                # 自定义Hooks
├── lib/                  # 工具库
│   ├── auth.ts           # 认证配置
│   ├── db.ts             # 数据库配置
│   └── utils.ts          # 工具函数
├── stores/               # 状态管理
├── types/                # 类型定义
└── styles/               # 样式文件
```

### 2. 命名约定

```typescript
// ✅ 好的实践 - 一致的命名约定

// 组件：PascalCase
const MovieCard = () => {}
const UserProfile = () => {}

// 函数：camelCase
const getUserById = () => {}
const formatDate = () => {}

// 常量：UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'
const MAX_FILE_SIZE = 1024 * 1024

// 类型/接口：PascalCase
interface User {
  id: string
  name: string
}

type MovieStatus = 'active' | 'inactive'

// 文件名：kebab-case
// movie-card.tsx
// user-profile.tsx
// api-client.ts
```

# GETAV.NET 项目工作记忆

## 当前会话信息

**会话开始时间**: 2024-01-15 (augment_init 命令执行)
**当前任务**: 执行 augment_init 命令初始化项目记忆系统
**项目状态**: 记忆系统初始化中
**工作目录**: `/Users/<USER>/Desktop/GETAV.NET/getav-web`

## 项目基本信息

### 项目概述
- **项目名称**: GETAV.NET
- **项目类型**: 成人内容网站
- **主要功能**: 影片数据管理、展示和用户交互
- **技术架构**: Next.js 15 + React 19 + TypeScript + PostgreSQL + Redis

### 当前开发状态
- **完成度**: 约55.6% (基于记忆中的信息)
- **核心功能**: 用户认证、搜索、交互系统已完成
- **待完成**: 数据采集优化、UI统一性、防爬虫解决方案

## 当前任务上下文

### 正在执行的任务
**任务**: augment_init 命令 - 初始化项目记忆系统
**进度**: 项目分析和上下文建立阶段 (第4个任务，共5个任务)

**已完成的步骤**:
1. ✅ 环境检查和准备
   - 确认项目目录结构
   - 检测关键配置文件
   - 验证无现有记忆系统

2. ✅ 技术栈自动检测
   - 识别 Next.js 15.3.3 + React 19.0.0
   - 确认 TypeScript 5.x + Tailwind CSS 4.x
   - 检测 Prisma 6.9.0 + PostgreSQL + Redis

3. ✅ 记忆系统创建
   - ✅ 创建核心记忆文件 (architecture.md, patterns.md, decisions.md, best-practices.md, tech-stack.md)
   - ✅ 创建工作记忆文件 (activeContext.md)
   - ✅ 创建管理文件 (memory-index.md, session-history.md)

4. 🔄 项目分析和上下文建立 (进行中)
   - ✅ 使用 codebase-retrieval 深度分析项目
   - 🔄 更新记忆系统内容 (当前步骤)

**待执行的步骤**:
5. ⏳ 验证和确认

### 即时目标
- 完成 `.augment_memory` 目录结构创建
- 生成所有核心记忆文件
- 建立项目上下文和工作记忆
- 验证记忆系统完整性

## 技术上下文

### 检测到的技术栈
**前端技术**:
- React 19.0.0 + Next.js 15.3.3 (App Router)
- TypeScript 5.x (严格模式)
- Tailwind CSS 4.x + Framer Motion 12.16.0
- Zustand 5.0.5 (状态管理) + SWR 2.3.3 (数据获取)
- React Hook Form 7.57.0 + Zod 3.25.56 (表单处理)

**后端技术**:
- NextAuth.js 4.24.11 + Prisma Adapter
- Prisma ORM 6.9.0 + PostgreSQL
- Redis 5.5.6 (缓存)
- Sharp 0.34.2 (图片处理)
- bcryptjs + jsonwebtoken (安全)

**开发工具**:
- ESLint 9 + Prettier 3.5.3
- Husky 9.1.7 + lint-staged 16.1.0
- TypeScript 类型检查

### 项目特点
- **成人内容网站**: 需要特殊的安全和合规考虑
- **图片密集型**: 大量影片封面和演员头像处理
- **复杂数据模型**: 影片、演员、分类等多重关联关系
- **用户交互**: 完整的评论、点赞、收藏、评分、观看记录系统
- **数据采集**: 从JAVBUS API采集影片数据，支持批量导入
- **高完成度**: 项目完成率约55.6%，核心功能已实现
- **现代化架构**: 使用最新的Next.js 15和React 19技术栈

## 项目结构概览

### 核心目录
```
getav-web/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── page.tsx         # 首页 (YouTube暗夜风格)
│   │   ├── movies/[id]/     # 影片详情页
│   │   ├── search/          # 搜索页面
│   │   ├── profile/         # 用户个人中心
│   │   └── api/             # API路由
│   ├── components/          # React 组件
│   │   ├── MovieGrid.tsx    # 影片网格组件
│   │   ├── Header.tsx       # 顶部导航
│   │   ├── OptimizedImage.tsx # 优化图片组件
│   │   └── RecommendedContent.tsx # 推荐内容
│   ├── hooks/              # 自定义 Hooks
│   ├── lib/                # 工具库和配置
│   │   ├── javbus-import.ts # JAVBUS数据导入
│   │   ├── database.ts      # 数据库操作
│   │   └── stats-service.ts # 统计服务
│   ├── stores/             # Zustand 状态管理
│   ├── styles/             # 样式文件
│   └── types/              # TypeScript 类型定义
├── prisma/                 # 数据库配置和模式
├── public/                 # 静态资源
│   └── images/             # 本地图片存储
├── scripts/                # 脚本文件
├── docs/                   # 项目文档
└── .augment_memory/        # 记忆系统 (新创建)
```

### 关键配置文件
- `package.json`: 项目依赖和脚本
- `next.config.ts`: Next.js配置 (图片优化，支持外部域名)
- `tsconfig.json`: TypeScript配置 (严格模式)
- `tailwind.config.js`: Tailwind CSS配置
- `prisma/schema.prisma`: 数据库模式 (368行，复杂关联关系)

### 核心功能模块
1. **影片管理**: 完整的CRUD操作，支持搜索、筛选、排序
2. **用户系统**: 自定义JWT认证，用户个人中心，权限控制
3. **交互功能**: 评论、点赞、收藏、评分、观看记录
4. **数据采集**: JAVBUS API集成，批量导入，图片下载
5. **图片处理**: Sharp优化，多格式支持，本地存储
6. **缓存系统**: Redis缓存，SWR客户端缓存
7. **搜索功能**: PostgreSQL全文搜索，高级筛选

## 当前决策和约束

### 技术决策
1. **使用 Next.js 15**: 利用最新的 App Router 和 React 19 特性
2. **PostgreSQL + Prisma**: 处理复杂的关联数据
3. **Redis 缓存**: 提升性能，特别是图片和数据缓存
4. **TypeScript 严格模式**: 确保类型安全
5. **Tailwind CSS**: 快速UI开发和一致性

### 开发约束
1. **端口使用**: 开发服务器使用 3001 端口
2. **图片域名**: 允许 javbus.com 等外部图片域名
3. **成人内容**: 需要特殊的内容安全策略
4. **性能要求**: 图片优化和缓存策略重要

### 用户需求记忆
基于历史记忆，用户关注：
1. **UI统一性**: YouTube暗夜风格设计
2. **数据完整性**: 完整的影片数据采集和存储
3. **代码质量**: 修复所有ESLint错误，避免any类型
4. **构建验证**: 每步完成后执行构建检查

## 下一步计划

### 立即任务
1. 完成管理文件创建 (memory-index.md, session-history.md)
2. 使用 codebase-retrieval 分析项目架构
3. 将分析结果写入记忆系统
4. 验证记忆系统完整性

### 后续优化
1. 建立定期记忆更新机制
2. 集成项目开发工作流
3. 优化记忆系统性能
4. 建立记忆备份策略

## 注意事项

### 重要提醒
- 记忆系统文件应保持简洁和聚焦
- 定期更新工作记忆以反映当前状态
- 重要决策应记录到长期记忆
- 保持记忆文件的一致性和准确性

### 风险点
- 记忆文件过大可能影响性能
- 需要定期清理过期的任务日志
- 确保记忆系统与项目实际状态同步
- 避免在记忆中存储敏感信息

## 会话历史

### 关键操作记录
1. **环境检查**: 确认项目结构和配置文件
2. **技术栈检测**: 完整分析项目技术栈
3. **记忆系统创建**: 正在创建核心记忆文件
4. **下一步**: 项目深度分析和上下文建立

### 发现的问题
- 无现有记忆系统，需要全新创建
- 项目使用最新技术栈，需要特别关注兼容性
- 成人内容特性需要特殊的安全考虑

### 成功模式
- 系统化的初始化流程
- 完整的技术栈检测
- 结构化的记忆文件组织

const { PrismaClient } = require('@prisma/client');
const axios = require('axios');

const prisma = new PrismaClient();
const JAVBUS_API_URL = 'http://localhost:3000/api';

/**
 * 批量为现有影片添加相关影片信息
 */
async function batchAddRelatedMovies() {
  try {
    console.log('🚀 开始批量添加相关影片信息...\n');

    // 1. 获取所有现有影片
    const allMovies = await prisma.movie.findMany({
      select: {
        id: true,
        title: true,
        gid: true,
        uc: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 数据库中共有 ${allMovies.length} 部影片`);

    // 2. 检查当前相关影片信息数量
    const currentRelatedCount = await prisma.relatedMovie.count();
    console.log(`📊 当前相关影片信息数量: ${currentRelatedCount}`);

    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;
    let totalRelatedAdded = 0;
    let totalRelationsAdded = 0;
    
    // 3. 遍历每部影片，获取相似影片并添加关联
    for (const movie of allMovies) {
      try {
        processedCount++;
        console.log(`\n[${processedCount}/${allMovies.length}] 处理影片: ${movie.id} - ${movie.title.substring(0, 50)}...`);
        
        // 检查是否已有相关影片信息
        const existingRelated = await prisma.relatedMovie.count({
          where: { originalMovieId: movie.id }
        });

        if (existingRelated > 0) {
          console.log(`⏭️ 影片 ${movie.id} 已有 ${existingRelated} 个相关影片信息，跳过`);
          continue;
        }
        
        // 从JAVBUS API获取影片详情
        const response = await axios.get(`${JAVBUS_API_URL}/movies/${movie.id}`);
        const movieDetail = response.data;
        
        if (!movieDetail.similarMovies || movieDetail.similarMovies.length === 0) {
          console.log(`❌ 影片 ${movie.id} 没有相关影片数据`);
          continue;
        }

        console.log(`📋 找到 ${movieDetail.similarMovies.length} 个相关影片`);

        let movieRelatedCount = 0;
        let movieRelationCount = 0;
        let movieSkipCount = 0;

        // 处理每个相关影片
        for (const similarMovie of movieDetail.similarMovies) {
          try {
            // 保存相关影片信息（不管是否存在于数据库）
            const relatedExists = await prisma.relatedMovie.findUnique({
              where: {
                originalMovieId_relatedMovieId: {
                  originalMovieId: movie.id,
                  relatedMovieId: similarMovie.id
                }
              }
            });

            if (!relatedExists) {
              await prisma.relatedMovie.create({
                data: {
                  originalMovieId: movie.id,
                  relatedMovieId: similarMovie.id,
                  relatedTitle: similarMovie.title,
                  relatedImg: similarMovie.img
                }
              });
              movieRelatedCount++;
              totalRelatedAdded++;
              console.log(`  ✅ 相关影片信息已保存: ${movie.id} -> ${similarMovie.id}`);
            }

            // 检查目标影片是否存在于数据库中，如果存在则创建关联
            const targetMovieExists = await prisma.movie.findUnique({
              where: { id: similarMovie.id },
              select: { id: true }
            });

            if (targetMovieExists) {
              // 检查关联是否已存在
              const relationExists = await prisma.similarMovie.findUnique({
                where: {
                  originalMovieId_similarMovieId: {
                    originalMovieId: movie.id,
                    similarMovieId: similarMovie.id
                  }
                }
              });

              if (!relationExists) {
                await prisma.similarMovie.create({
                  data: {
                    originalMovieId: movie.id,
                    similarMovieId: similarMovie.id
                  }
                });
                movieRelationCount++;
                totalRelationsAdded++;
                console.log(`  ✅ 相似影片关联成功: ${movie.id} -> ${similarMovie.id}`);
              }
            }
          } catch (error) {
            movieSkipCount++;
            console.log(`  ❌ 处理失败: ${movie.id} -> ${similarMovie.id}`, error.message);
          }
        }

        console.log(`✅ 影片 ${movie.id} 处理完成: ${movieRelatedCount} 个信息保存, ${movieRelationCount} 个关联建立, ${movieSkipCount} 个跳过`);
        successCount++;
        
        // 添加延迟避免API请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        errorCount++;
        console.log(`❌ 处理影片失败: ${movie.id}`, error.message);
        
        // 如果是404错误，继续处理下一个
        if (error.response && error.response.status === 404) {
          console.log(`  影片 ${movie.id} 在JAVBUS中不存在`);
          continue;
        }
        
        // 添加延迟后继续
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 4. 统计最终结果
    const finalRelatedCount = await prisma.relatedMovie.count();
    const finalSimilarCount = await prisma.similarMovie.count();

    console.log('\n🎉 批量添加相关影片信息完成！');
    console.log('📊 统计结果:');
    console.log(`- 总影片数: ${allMovies.length}`);
    console.log(`- 处理成功: ${successCount}`);
    console.log(`- 处理失败: ${errorCount}`);
    console.log(`- 新增相关信息: ${totalRelatedAdded}`);
    console.log(`- 新增关联: ${totalRelationsAdded}`);
    console.log(`- 相关信息前数量: ${currentRelatedCount}`);
    console.log(`- 相关信息后数量: ${finalRelatedCount}`);
    console.log(`- 相似关联后数量: ${finalSimilarCount}`);

    // 5. 检查覆盖率
    const moviesWithRelated = await prisma.movie.count({
      where: {
        relatedMovies: {
          some: {}
        }
      }
    });

    const coverageRate = allMovies.length > 0 ? ((moviesWithRelated / allMovies.length) * 100).toFixed(2) : 0;
    console.log(`- 相关信息覆盖率: ${coverageRate}% (${moviesWithRelated}/${allMovies.length})`);
    
  } catch (error) {
    console.error('批量添加相似影片关联时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
batchAddRelatedMovies();

-- 清理搜索相关的数据库对象
-- 删除依赖的视图和函数，以便Prisma schema同步

-- 1. 删除视图
DROP VIEW IF EXISTS movie_search_view CASCADE;

-- 2. 删除函数
DROP FUNCTION IF EXISTS search_movies(text, integer, integer) CASCADE;
DROP FUNCTION IF EXISTS get_search_suggestions(text, integer) CASCADE;

-- 3. 删除触发器
DROP TRIGGER IF EXISTS movie_search_vector_update ON movies;
DROP TRIGGER IF EXISTS star_search_vector_update ON stars;
DROP TRIGGER IF EXISTS genre_search_vector_update ON genres;

-- 4. 删除触发器函数
DROP FUNCTION IF EXISTS update_movie_search_vector() CASCADE;
DROP FUNCTION IF EXISTS update_star_search_vector() CASCADE;
DROP FUNCTION IF EXISTS update_genre_search_vector() CASCADE;

-- 5. 删除索引
DROP INDEX IF EXISTS idx_movies_search_vector;
DROP INDEX IF EXISTS idx_stars_search_vector;
DROP INDEX IF EXISTS idx_genres_search_vector;

-- 6. 删除search_vector字段
ALTER TABLE movies DROP COLUMN IF EXISTS search_vector;
ALTER TABLE stars DROP COLUMN IF EXISTS search_vector;
ALTER TABLE genres DROP COLUMN IF EXISTS search_vector;

-- 清理完成
SELECT 'Database cleanup completed successfully' as status;

import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 图片优化配置 - 使用Sharp进行高质量处理
  images: {
    // 支持的图片格式 (优先使用现代格式)
    formats: ['image/webp', 'image/avif'],
    // 允许的图片域名
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3001',
        pathname: '/images/**',
      },
      {
        protocol: 'https',
        hostname: 'www.javbus.com',
      },
      {
        protocol: 'https',
        hostname: 'pics.dmm.co.jp',
      },
      {
        protocol: 'https',
        hostname: 'javbus.com',
      }
    ],
    // 自定义设备尺寸，确保高分辨率显示
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    // 自定义图片尺寸 - 增加更多尺寸选项
    imageSizes: [16, 32, 48, 64, 96, 128, 200, 256, 384, 400, 800, 1200],
    // 启用SVG支持
    dangerouslyAllowSVG: true,
    // 内容安全策略
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // 启用图片优化，使用Sharp处理
    unoptimized: false,
    // 图片加载器配置
    loader: 'default',
    // 最小缓存时间 (秒)
    minimumCacheTTL: 60 * 60 * 24 * 7, // 7天
  },

  // 服务器外部包配置 - 修复Sharp警告
  serverExternalPackages: ['sharp'],

  // 实验性功能
  experimental: {
    // 移除sharp的优化导入，避免与serverExternalPackages冲突
    // optimizePackageImports: ['sharp'],
  },

  // Webpack配置 - 修复Sharp模块解析警告
  webpack: (config) => {
    // 忽略Sharp相关的模块解析警告
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      {
        module: /node_modules\/sharp\/lib/,
        message: /Can't resolve '@img\/sharp/,
      },
      {
        module: /node_modules\/sharp\/lib/,
        message: /Module not found/,
      },
    ];

    return config;
  },
};

export default nextConfig;

# 播放页面用户交互功能测试指南

## 📋 功能概述

播放页面（`/movies/[id]`）已完成所有用户交互功能的API集成和状态管理，支持以下功能：

### ✅ 已实现的功能

#### 1. **点赞功能**
- **API**: `/api/likes`
- **支持用户**: 匿名用户 + 登录用户
- **功能**: 
  - 匿名用户可以点赞（自动创建临时用户）
  - 登录用户点赞状态持久化
  - 实时显示点赞数量
  - 点赞状态切换（点赞/取消点赞）

#### 2. **收藏功能**
- **API**: `/api/favorites`
- **支持用户**: 仅登录用户
- **功能**:
  - 收藏/取消收藏切换
  - 收藏状态实时同步
  - 未登录用户提示登录

#### 3. **评论功能**
- **API**: `/api/comments`
- **支持用户**: 仅登录用户
- **功能**:
  - 影片评论发表和显示
  - 磁力链接评论发表和显示
  - 评论点赞/反对功能
  - 实时评论数量显示
  - 未登录用户提示登录

#### 4. **评分功能**
- **组件**: `RatingComponent`
- **API**: `/api/ratings`
- **支持用户**: 仅登录用户
- **功能**:
  - 1-10分星级评分
  - 平均评分显示
  - 用户评分状态管理
  - 评分数据持久化

## 🧪 测试步骤

### 测试环境准备
1. 确保数据库连接正常
2. 确保有测试影片数据
3. 准备测试用户账号

### 1. 匿名用户测试

#### 点赞功能测试
```bash
# 访问任意影片页面
http://localhost:3000/movies/[movie-id]

# 测试步骤：
1. 点击"点赞"按钮
2. 验证按钮状态变为"已赞"
3. 验证点赞数量增加
4. 再次点击验证取消点赞
```

#### 其他功能测试
```bash
# 测试收藏按钮
1. 点击"收藏"按钮
2. 验证显示登录提示

# 测试评论按钮
1. 点击"评论"按钮
2. 验证评论模态框打开
3. 尝试发表评论，验证登录提示

# 测试评分功能
1. 查看评分组件
2. 尝试评分，验证登录提示
```

### 2. 登录用户测试

#### 用户登录
```bash
# 注册/登录测试账号
http://localhost:3000/auth/signup
http://localhost:3000/auth/signin
```

#### 完整功能测试
```bash
# 1. 点赞功能
- 点击点赞按钮，验证状态保存
- 刷新页面，验证状态持久化

# 2. 收藏功能
- 点击收藏按钮，验证收藏成功
- 访问个人中心收藏页面，验证收藏记录
- 取消收藏，验证状态同步

# 3. 评论功能
- 发表影片评论，验证评论显示
- 对评论进行点赞/反对
- 访问磁力链接评论功能

# 4. 评分功能
- 给影片评分（1-10分）
- 验证平均评分更新
- 访问个人中心评分页面，验证评分记录
```

## 🔧 API 接口测试

### 点赞API测试
```bash
# 影片点赞
POST /api/likes
{
  "type": "LIKE",
  "movieId": "movie-id",
  "userId": "user-id" // 可选，匿名用户可不传
}

# 获取点赞状态
GET /api/likes?movieId=movie-id&userId=user-id
```

### 收藏API测试
```bash
# 收藏/取消收藏
POST /api/favorites
{
  "movieId": "movie-id",
  "userId": "user-id"
}

# 获取收藏状态
GET /api/favorites?movieId=movie-id&userId=user-id
```

### 评论API测试
```bash
# 发表评论
POST /api/comments
{
  "content": "评论内容",
  "movieId": "movie-id", // 影片评论
  "userId": "user-id"
}

# 获取评论列表
GET /api/comments?movieId=movie-id
```

### 评分API测试
```bash
# 提交评分
POST /api/ratings
{
  "movieId": "movie-id",
  "score": 8,
  "userId": "user-id"
}

# 获取评分信息
GET /api/ratings?movieId=movie-id&userId=user-id
```

## 🎯 预期结果

### 用户体验验证
- ✅ 所有交互操作响应迅速
- ✅ 状态变化实时反馈
- ✅ 错误提示友好明确
- ✅ 登录状态正确验证
- ✅ 数据持久化正常

### 数据一致性验证
- ✅ 用户操作数据正确保存到数据库
- ✅ 页面刷新后状态保持一致
- ✅ 个人中心数据与操作记录同步
- ✅ 统计数据准确更新

### 权限控制验证
- ✅ 匿名用户只能点赞
- ✅ 登录用户可以使用所有功能
- ✅ 未登录用户操作受限功能时正确提示

## 🚨 已知问题

目前所有功能已正确实现，无已知问题。

## 📝 测试记录

请在测试过程中记录以下信息：
- 测试时间
- 测试环境
- 测试结果
- 发现的问题
- 改进建议

---

**测试完成标准**: 所有功能按预期工作，用户体验流畅，数据一致性良好。

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Movie {
  id            String         @id
  title         String
  img           String?
  localImg      String?
  date          String?
  videoLength   Int?
  description   String?
  gid           String?
  uc            String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  directorId    String?
  producerId    String?
  publisherId   String?
  seriesId      String?
  comments      Comment[]
  favorites     Favorite[]
  likes         Like[]
  magnets       Magnet[]
  genres        MovieGenre[]
  stars         MovieStar[]
  director      Director?      @relation(fields: [directorId], references: [id])
  producer      Producer?      @relation(fields: [producerId], references: [id])
  publisher     Publisher?     @relation(fields: [publisherId], references: [id])
  series        Series?        @relation(fields: [seriesId], references: [id])
  ratings       Rating[]
  relatedMovies RelatedMovie[]
  reports       Report[]
  samples       Sample[]
  similarMovies SimilarMovie[] @relation("OriginalMovie")
  similarTo     SimilarMovie[] @relation("SimilarMovie")
  views         View[]

  @@map("movies")
}

model Star {
  id           String      @id
  name         String
  avatar       String?
  localAvatar  String?
  birthday     String?
  age          String?
  height       String?
  bust         String?
  waist        String?
  hip          String?
  birthplace   String?
  hobby        String?
  cupSize      String?
  measurements String?
  description  String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  movies       MovieStar[]

  @@map("stars")
}

model MovieStar {
  movieId String
  starId  String
  movie   Movie  @relation(fields: [movieId], references: [id], onDelete: Cascade)
  star    Star   @relation(fields: [starId], references: [id], onDelete: Cascade)

  @@id([movieId, starId])
  @@map("movie_stars")
}

model Genre {
  id     String       @id
  name   String
  movies MovieGenre[]

  @@map("genres")
}

model MovieGenre {
  movieId String
  genreId String
  genre   Genre  @relation(fields: [genreId], references: [id], onDelete: Cascade)
  movie   Movie  @relation(fields: [movieId], references: [id], onDelete: Cascade)

  @@id([movieId, genreId])
  @@map("movie_genres")
}

model Director {
  id     String  @id
  name   String
  movies Movie[]

  @@map("directors")
}

model Producer {
  id     String  @id
  name   String
  movies Movie[]

  @@map("producers")
}

model Publisher {
  id     String  @id
  name   String
  movies Movie[]

  @@map("publishers")
}

model Series {
  id     String  @id
  name   String
  movies Movie[]

  @@map("series")
}

model Sample {
  id        String  @id
  alt       String?
  src       String?
  thumbnail String?
  localSrc  String?
  movieId   String
  movie     Movie   @relation(fields: [movieId], references: [id], onDelete: Cascade)

  @@map("samples")
}

model Magnet {
  id          String    @id
  link        String
  isHD        Boolean?
  title       String?
  size        String?
  numberSize  BigInt?
  shareDate   String?
  hasSubtitle Boolean?
  movieId     String
  createdAt   DateTime  @default(now())
  comments    Comment[]
  likes       Like[]
  movie       Movie     @relation(fields: [movieId], references: [id], onDelete: Cascade)

  @@map("magnets")
}

model SimilarMovie {
  originalMovieId String
  similarMovieId  String
  originalMovie   Movie  @relation("OriginalMovie", fields: [originalMovieId], references: [id], onDelete: Cascade)
  similarMovie    Movie  @relation("SimilarMovie", fields: [similarMovieId], references: [id], onDelete: Cascade)

  @@id([originalMovieId, similarMovieId])
  @@map("similar_movies")
}

model RelatedMovie {
  id              String   @id @default(cuid())
  originalMovieId String
  relatedMovieId  String
  relatedTitle    String
  relatedImg      String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  originalMovie   Movie    @relation(fields: [originalMovieId], references: [id], onDelete: Cascade)

  @@unique([originalMovieId, relatedMovieId])
  @@map("related_movies")
}

model User {
  id                 String               @id @default(cuid())
  name               String?
  email              String?              @unique
  emailVerified      DateTime?
  image              String?
  username           String?              @unique
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  defaultRole        String?              @default("user")
  isActive           Boolean              @default(true)
  accounts           Account[]
  comments           Comment[]
  favorites          Favorite[]
  likes              Like[]
  ratings            Rating[]
  reports            Report[]
  sessions           Session[]
  user_activity_logs user_activity_logs[]
  password           UserPassword?
  user_roles         user_roles[]
  views              View[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

model UserPassword {
  userId       String @id
  passwordHash String
  user         User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_passwords")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  userId    String?
  movieId   String?
  magnetId  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  magnet    Magnet?  @relation(fields: [magnetId], references: [id], onDelete: Cascade)
  movie     Movie?   @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id])
  likes     Like[]

  @@map("comments")
}

model Like {
  id        String   @id @default(cuid())
  userId    String?
  movieId   String?
  commentId String?
  magnetId  String?
  type      LikeType
  createdAt DateTime @default(now())
  comment   Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)
  magnet    Magnet?  @relation(fields: [magnetId], references: [id], onDelete: Cascade)
  movie     Movie?   @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id])

  @@unique([userId, movieId])
  @@unique([userId, commentId])
  @@unique([userId, magnetId])
  @@map("likes")
}

model Favorite {
  id        String   @id @default(cuid())
  userId    String
  movieId   String
  createdAt DateTime @default(now())
  movie     Movie    @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, movieId])
  @@map("favorites")
}

model Rating {
  id        String   @id @default(cuid())
  userId    String
  movieId   String
  score     Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  movie     Movie    @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, movieId])
  @@map("ratings")
}

model View {
  id        String   @id @default(cuid())
  movieId   String
  userId    String?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  movie     Movie    @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id])

  @@unique([movieId, userId])
  @@index([movieId, ipAddress])
  @@map("views")
}

model Report {
  id          String       @id @default(cuid())
  movieId     String
  type        ReportType
  description String
  status      ReportStatus @default(PENDING)
  userId      String?
  userEmail   String?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  processedAt DateTime?
  processedBy String?
  movie       Movie        @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user        User?        @relation(fields: [userId], references: [id])

  @@map("reports")
}

model permissions {
  id                String             @id
  name              String             @unique
  displayName       String
  description       String?
  resource          String
  action            String
  type              PermissionType     @default(ACTION)
  parentId          String?
  isSystem          Boolean            @default(false)
  isActive          Boolean            @default(true)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime
  permissions       permissions?       @relation("permissionsTopermissions", fields: [parentId], references: [id])
  other_permissions permissions[]      @relation("permissionsTopermissions")
  role_permissions  role_permissions[]

  @@unique([resource, action])
}

model role_permissions {
  id           String      @id
  roleId       String
  permissionId String
  assignedBy   String?
  assignedAt   DateTime    @default(now())
  isActive     Boolean     @default(true)
  permissions  permissions @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  roles        roles       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
}

model roles {
  id               String             @id
  name             String             @unique
  displayName      String
  description      String?
  isSystem         Boolean            @default(false)
  isActive         Boolean            @default(true)
  priority         Int                @default(0)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime
  role_permissions role_permissions[]
  user_roles       user_roles[]
}

model user_activity_logs {
  id         String             @id
  userId     String?
  action     ActivityAction
  targetId   String?
  ipAddress  String?
  userAgent  String?
  metadata   Json?
  createdAt  DateTime           @default(now())
  targetType ActivityTargetType
  users      User?              @relation(fields: [userId], references: [id])

  @@index([action])
  @@index([createdAt])
  @@index([ipAddress])
  @@index([targetType])
  @@index([userId])
}

model user_roles {
  id         String    @id
  userId     String
  roleId     String
  assignedBy String?
  assignedAt DateTime  @default(now())
  expiresAt  DateTime?
  isActive   Boolean   @default(true)
  roles      roles     @relation(fields: [roleId], references: [id], onDelete: Cascade)
  users      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
}

enum LikeType {
  LIKE
  DISLIKE
}

enum ReportType {
  COPYRIGHT
  INAPPROPRIATE
  SPAM
  MISLEADING
  VIOLENCE
  OTHER
}

enum ReportStatus {
  PENDING
  REVIEWING
  RESOLVED
  REJECTED
}

enum ActivityAction {
  LOGIN
  LOGOUT
  REGISTER
  VIEW_MOVIE
  VIEW_STAR
  VIEW_GENRE
  VIEW_PRODUCER
  VIEW_DIRECTOR
  VIEW_SERIES
  SEARCH
  LIKE
  UNLIKE
  COMMENT
  DELETE_COMMENT
  FAVORITE
  UNFAVORITE
  RATE
  UPDATE_RATING
  REPORT
  DOWNLOAD_MAGNET
  SHARE
}

enum ActivityTargetType {
  MOVIE
  STAR
  GENRE
  PRODUCER
  DIRECTOR
  SERIES
  COMMENT
  MAGNET
  USER
  SEARCH
}

enum PermissionType {
  MENU
  ACTION
  DATA
  API
}

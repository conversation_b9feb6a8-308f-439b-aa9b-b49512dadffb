{"name": "getav-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "clear-cache": "node clear-cache.js", "clear-homepage-cache": "node clear-cache.js"}, "prisma": {"schema": "prisma/schema.prisma"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.1.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.9.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/line-clamp": "^0.4.4", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.9", "@types/node-cron": "^3.0.11", "@types/redis": "^4.0.10", "@types/sharp": "^0.31.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bullmq": "^5.53.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "express-rate-limit": "^7.5.0", "framer-motion": "^12.16.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next": "15.3.3", "next-auth": "^4.24.11", "node-cron": "^4.1.0", "pg": "^8.16.3", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "redis": "^5.5.6", "sharp": "^0.34.2", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "20.19.4", "@types/react": "19.1.8", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "5.8.3"}}
/**
 * 查找有播放链接的影片
 */

const axios = require('axios');

async function findMoviesWithLinks() {
  console.log('🔍 查找有播放链接的影片...');
  
  const apiUrl = 'http://188.68.60.179:8081';
  let foundMoviesWithLinks = [];
  let totalChecked = 0;
  
  try {
    // 尝试获取更多影片数据
    for (let limit = 20; limit <= 100; limit += 20) {
      console.log(`\n📥 获取最新${limit}部影片...`);
      
      const response = await axios.get(`${apiUrl}/api/v1/jav/movies/latest`, {
        params: { limit },
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json'
        }
      });

      if (response.data.success && response.data.data) {
        const movies = response.data.data;
        totalChecked = movies.length;
        
        const moviesWithLinks = movies.filter(movie => {
          const hasStreamtape = movie.streamtape_url && 
                               movie.streamtape_url.trim() !== '' && 
                               movie.streamtape_url !== 'null';
          const hasStreamhg = movie.streamhg_url && 
                             movie.streamhg_url.trim() !== '' && 
                             movie.streamhg_url !== 'null';
          return hasStreamtape || hasStreamhg;
        });

        console.log(`📊 检查结果: ${moviesWithLinks.length}/${movies.length} 部影片有播放链接`);
        
        if (moviesWithLinks.length > 0) {
          foundMoviesWithLinks = moviesWithLinks;
          console.log('✅ 找到有播放链接的影片！');
          break;
        }
      }
    }

    if (foundMoviesWithLinks.length > 0) {
      console.log(`\n🎯 找到 ${foundMoviesWithLinks.length} 部有播放链接的影片:`);
      foundMoviesWithLinks.forEach((movie, index) => {
        console.log(`\n${index + 1}. ${movie.code} - ${movie.title}`);
        console.log(`   发布日期: ${movie.release_date || '未知'}`);
        console.log(`   制作商: ${movie.studio || '未知'}`);
        console.log(`   时长: ${movie.duration || '未知'}分钟`);
        console.log(`   封面: ${movie.cover_url || '无'}`);
        
        if (movie.streamtape_url && movie.streamtape_url !== 'null') {
          console.log(`   ✅ StreamTape: ${movie.streamtape_url}`);
        }
        if (movie.streamhg_url && movie.streamhg_url !== 'null') {
          console.log(`   ✅ StreamHG: ${movie.streamhg_url}`);
        }
      });

      console.log('\n📋 数据采集建议:');
      console.log('- 可以开始采集这些有播放链接的影片');
      console.log('- 建议同时下载封面图片到本地');
      console.log('- 可以尝试从javbus获取更详细的演员和分类信息');
      
      return foundMoviesWithLinks;
    } else {
      console.log(`\n⚠️ 在检查的${totalChecked}部影片中未找到有播放链接的影片`);
      console.log('建议:');
      console.log('1. 检查API数据是否包含播放链接字段');
      console.log('2. 尝试获取热门影片而不是最新影片');
      console.log('3. 联系API提供方确认数据状态');
      
      // 尝试获取热门影片
      console.log('\n🔥 尝试获取热门影片...');
      try {
        const popularResponse = await axios.get(`${apiUrl}/api/v1/jav/movies/popular`, {
          params: { limit: 20 },
          timeout: 30000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json'
          }
        });
        
        if (popularResponse.data.success && popularResponse.data.data) {
          const popularMovies = popularResponse.data.data;
          const popularWithLinks = popularMovies.filter(movie => {
            const hasStreamtape = movie.streamtape_url && 
                                 movie.streamtape_url.trim() !== '' && 
                                 movie.streamtape_url !== 'null';
            const hasStreamhg = movie.streamhg_url && 
                               movie.streamhg_url.trim() !== '' && 
                               movie.streamhg_url !== 'null';
            return hasStreamtape || hasStreamhg;
          });
          
          console.log(`📊 热门影片检查结果: ${popularWithLinks.length}/${popularMovies.length} 部影片有播放链接`);
          
          if (popularWithLinks.length > 0) {
            console.log('✅ 在热门影片中找到有播放链接的影片！');
            return popularWithLinks;
          }
        }
      } catch (error) {
        console.log('⚠️ 获取热门影片失败:', error.message);
      }
    }

  } catch (error) {
    console.error('❌ 查找过程出错:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
  
  return [];
}

// 运行查找
findMoviesWithLinks().then((movies) => {
  if (movies.length > 0) {
    console.log(`\n🎉 查找完成，找到 ${movies.length} 部可采集的影片`);
  } else {
    console.log('\n😔 未找到有播放链接的影片，需要进一步调查');
  }
}).catch(error => {
  console.error('💥 查找异常:', error);
});
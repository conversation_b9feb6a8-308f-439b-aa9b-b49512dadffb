#!/usr/bin/env node

/**
 * 翻页数据采集脚本
 * 从 http://*************:8081/api/v1/jav/movies/playable/paginated 采集数据到本地数据库
 * 支持图片下载和M3U8文件处理
 * 
 * 使用方法:
 * node paginated-data-collector.js [options]
 * 
 * 选项:
 * --page-size <number>     每页数量 (默认: 20, 最大: 100)
 * --start-page <number>    起始页面 (默认: 1)
 * --max-pages <number>     最大页数限制 (默认: 无限制)
 * --batch-size <number>    批量插入大小 (默认: 50)
 * --download-images        是否下载图片 (默认: true)
 * --download-m3u8          是否下载M3U8 (默认: true)
 * --concurrent-downloads <number> 下载并发数 (默认: 3)
 * --resume                 断点续传模式
 * --dry-run               仅测试，不实际插入数据
 * --verbose               详细日志输出
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const crypto = require('crypto');

// 设置数据库连接环境变量（如果未设置）
if (!process.env.DATABASE_URL) {
  process.env.DATABASE_URL = 'postgresql://postgres@localhost:5432/getav';
}

// 配置常量
const CONFIG = {
  API_BASE_URL: 'http://*************:8081',
  API_ENDPOINT: '/api/v1/jav/movies/playable/paginated',
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_BATCH_SIZE: 50,
  REQUEST_TIMEOUT: 60000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 2000,
  DOWNLOAD_TIMEOUT: 60000,
  DOWNLOAD_RETRY_COUNT: 3,
  DOWNLOAD_RETRY_DELAY: 1000,
  PROGRESS_FILE: 'collector-progress.json',
  // 文件存储路径
  IMAGE_BASE_DIR: path.join(process.cwd(), 'public/images/getav'),
  M3U8_BASE_DIR: path.join(process.cwd(), 'public/m3u8'),
};

// 全局变量
let prisma;
let stats = {
  totalPages: 0,
  currentPage: 0,
  totalMovies: 0,
  totalProcessed: 0,
  totalInserted: 0,
  totalUpdated: 0,
  totalSkipped: 0,
  totalErrors: 0,
  imagesDownloaded: 0,
  imagesSkipped: 0,
  imagesFailed: 0,
  m3u8Downloaded: 0,
  m3u8Skipped: 0,
  m3u8Failed: 0,
  startTime: null,
  endTime: null,
};

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
  pageSize: parseInt(args.find(arg => arg.startsWith('--page-size='))?.split('=')[1]) || CONFIG.DEFAULT_PAGE_SIZE,
  startPage: parseInt(args.find(arg => arg.startsWith('--start-page='))?.split('=')[1]) || 1,
  maxPages: parseInt(args.find(arg => arg.startsWith('--max-pages='))?.split('=')[1]) || null,
  batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || CONFIG.DEFAULT_BATCH_SIZE,
  downloadImages: !args.includes('--no-download-images'),
  downloadM3u8: !args.includes('--no-download-m3u8'),
  concurrentDownloads: parseInt(args.find(arg => arg.startsWith('--concurrent-downloads='))?.split('=')[1]) || 3,
  resume: args.includes('--resume'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
};

// 验证参数
if (options.pageSize > CONFIG.MAX_PAGE_SIZE) {
  console.error(`错误: 页面大小不能超过 ${CONFIG.MAX_PAGE_SIZE}`);
  process.exit(1);
}

if (options.startPage < 1) {
  console.error('错误: 起始页面必须大于等于 1');
  process.exit(1);
}

// 日志函数
function log(level, message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  
  console.log(logMessage);
  
  if (data && options.verbose) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function logInfo(message, data) {
  log('info', message, data);
}

function logError(message, error) {
  log('error', message, error);
  if (error && error.stack && options.verbose) {
    console.error(error.stack);
  }
}

function logSuccess(message, data) {
  log('success', `✅ ${message}`, data);
}

function logWarning(message, data) {
  log('warning', `⚠️  ${message}`, data);
}

function logVerbose(message, data) {
  if (options.verbose) {
    log('verbose', `🔍 ${message}`, data);
  }
}

// 确保目录存在
function ensureDirExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 获取哈希文件名
function getHashedFileName(url, prefix = '') {
  const hash = crypto.createHash('md5').update(url + prefix).digest('hex');
  const ext = path.extname(url) || '.jpg';
  return `${prefix ? prefix + '_' : ''}${hash.substring(0, 8)}${ext}`;
}

// 初始化存储目录
function initStorageDirectories() {
  logInfo('初始化存储目录...');
  
  // 创建图片存储目录
  ensureDirExists(path.join(CONFIG.IMAGE_BASE_DIR, 'cover'));
  ensureDirExists(path.join(CONFIG.IMAGE_BASE_DIR, 'actress'));
  ensureDirExists(path.join(CONFIG.IMAGE_BASE_DIR, 'samples'));
  
  // 创建M3U8存储目录
  ensureDirExists(path.join(CONFIG.M3U8_BASE_DIR, 'playlists'));
  ensureDirExists(path.join(CONFIG.M3U8_BASE_DIR, 'segments'));
  
  logSuccess('存储目录初始化完成');
}

// 初始化数据库连接
async function initDatabase() {
  try {
    prisma = new PrismaClient({
      log: options.verbose ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });

    // 测试数据库连接
    await prisma.$connect();
    logSuccess('数据库连接成功');

    return true;
  } catch (error) {
    logError('数据库连接失败', error);
    return false;
  }
}

// 带重试的HTTP请求
async function requestWithRetry(url, config = {}, retryCount = CONFIG.RETRY_COUNT) {
  let lastError;

  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      logInfo(`HTTP请求 (尝试 ${attempt}/${retryCount}): ${url}`);

      const response = await axios({
        url,
        timeout: CONFIG.REQUEST_TIMEOUT,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        ...config
      });

      if (response.status === 200) {
        return response.data;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      lastError = error;
      logWarning(`请求失败 (尝试 ${attempt}/${retryCount}): ${error.message}`);

      if (attempt < retryCount) {
        logInfo(`等待 ${CONFIG.RETRY_DELAY}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY));
      }
    }
  }

  throw lastError;
}

/**
 * 解析M3U8文件获取精确时长
 * @param {string} m3u8Url - M3U8文件URL
 * @returns {Promise<{durationSeconds: number, durationFormatted: string}>}
 */
async function parseM3U8Duration(m3u8Url) {
  try {
    if (!m3u8Url) {
      return { durationSeconds: null, durationFormatted: null };
    }

    // 构建完整URL
    const fullUrl = m3u8Url.startsWith('http') ? m3u8Url : `${CONFIG.API_BASE_URL}${m3u8Url}`;

    logVerbose(`解析M3U8文件: ${fullUrl}`);

    const response = await requestWithRetry(fullUrl, {
      method: 'GET',
      responseType: 'text'
    });

    if (!response || typeof response !== 'string') {
      logWarning(`M3U8文件内容无效: ${fullUrl}`);
      return { durationSeconds: null, durationFormatted: null };
    }

    // 解析EXTINF标签获取片段时长
    const extinf_lines = response.split('\n').filter(line => line.startsWith('#EXTINF:'));

    if (extinf_lines.length === 0) {
      logWarning(`M3U8文件中未找到EXTINF标签: ${fullUrl}`);
      return { durationSeconds: null, durationFormatted: null };
    }

    // 计算总时长
    let totalSeconds = 0;
    for (const line of extinf_lines) {
      const match = line.match(/#EXTINF:([0-9.]+),/);
      if (match) {
        totalSeconds += parseFloat(match[1]);
      }
    }

    if (totalSeconds <= 0) {
      logWarning(`M3U8文件时长计算结果无效: ${totalSeconds}秒`);
      return { durationSeconds: null, durationFormatted: null };
    }

    // 转换为整数秒数（四舍五入）
    const durationSeconds = Math.round(totalSeconds);

    // 格式化为 HH:MM:SS
    const hours = Math.floor(durationSeconds / 3600);
    const minutes = Math.floor((durationSeconds % 3600) / 60);
    const seconds = durationSeconds % 60;
    const durationFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    logVerbose(`M3U8解析成功: ${extinf_lines.length}个片段, 总时长 ${durationSeconds}秒 (${durationFormatted})`);

    return { durationSeconds, durationFormatted };

  } catch (error) {
    logWarning(`解析M3U8文件失败: ${error.message}`);
    return { durationSeconds: null, durationFormatted: null };
  }
}

// 从翻页API获取数据
async function fetchMoviesFromPaginatedAPI(page = 1, pageSize = CONFIG.DEFAULT_PAGE_SIZE) {
  try {
    const url = `${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`;
    const params = {
      page,
      page_size: Math.min(pageSize, CONFIG.MAX_PAGE_SIZE)
    };

    logInfo(`正在获取第 ${page} 页数据`, { url, params });

    const response = await requestWithRetry(url, {
      method: 'GET',
      params
    });

    // 验证响应格式
    if (!response || typeof response !== 'object') {
      throw new Error('API响应格式无效');
    }

    if (!response.success) {
      throw new Error(`API返回错误: ${response.message || '未知错误'}`);
    }

    if (!response.data || !Array.isArray(response.data.movies)) {
      throw new Error('API响应中缺少movies数组');
    }

    const { movies, total, page: responsePage, page_size, has_more } = response.data;

    logSuccess(`成功获取第 ${page} 页: ${movies.length} 部影片`, {
      total,
      page: responsePage,
      page_size,
      has_more,
      actualCount: movies.length
    });

    return {
      movies,
      total,
      page: responsePage,
      pageSize: page_size,
      hasMore: has_more,
      totalPages: Math.ceil(total / page_size)
    };

  } catch (error) {
    logError(`获取第 ${page} 页数据失败`, error);
    throw error;
  }
}

// 下载文件（通用函数）
function downloadFile(url, localPath, timeout = CONFIG.DOWNLOAD_TIMEOUT) {
  return new Promise((resolve) => {
    const file = fs.createWriteStream(localPath);

    // 根据URL协议选择http或https模块
    const isHttps = url.startsWith('https:');
    const httpModule = isHttps ? https : http;

    // 为javbus.com图片添加特殊请求头
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Cache-Control': 'no-cache'
    };

    // 如果是javbus.com的图片，添加Referer头
    if (url.includes('javbus.com')) {
      headers['Referer'] = 'https://www.javbus.com/';
    }

    const request = httpModule.get(url, {
      headers: headers,
      timeout: timeout
    }, (response) => {
      if (response.statusCode !== 200) {
        file.close();
        fs.unlink(localPath, () => {});
        resolve({ success: false, error: `HTTP ${response.statusCode}` });
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        resolve({ success: true });
      });
    });

    request.on('error', (err) => {
      file.close();
      fs.unlink(localPath, () => {});
      resolve({ success: false, error: err.message });
    });

    request.on('timeout', () => {
      request.destroy();
      file.close();
      fs.unlink(localPath, () => {});
      resolve({ success: false, error: 'Download timeout' });
    });
  });
}

// 下载图片文件
async function downloadImage(imageUrl, movieCode, imageType = 'cover') {
  if (!imageUrl || !movieCode) {
    return { success: false, error: 'Missing required parameters' };
  }

  try {
    const fileName = getHashedFileName(imageUrl, movieCode);
    const localDir = path.join(CONFIG.IMAGE_BASE_DIR, imageType);
    const localPath = path.join(localDir, fileName);
    const publicPath = `/images/getav/${imageType}/${fileName}`;

    // 检查文件是否已存在
    if (fs.existsSync(localPath)) {
      return { success: true, skipped: true, localPath, publicPath };
    }

    // 下载文件
    const result = await downloadFile(imageUrl, localPath);

    if (result.success) {
      return { success: true, localPath, publicPath };
    } else {
      return { success: false, error: result.error };
    }

  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 下载M3U8文件
async function downloadM3U8(m3u8Url, movieCode) {
  if (!m3u8Url || !movieCode) {
    return { success: false, error: 'Missing required parameters' };
  }

  try {
    const fileName = getHashedFileName(m3u8Url, movieCode).replace(/\.(m3u8|ts)$/i, '') + '.m3u8';
    const localDir = path.join(CONFIG.M3U8_BASE_DIR, 'playlists');
    const localPath = path.join(localDir, fileName);
    const publicPath = `/m3u8/playlists/${fileName}`;

    // 检查文件是否已存在
    if (fs.existsSync(localPath)) {
      return { success: true, skipped: true, localPath, publicPath };
    }

    // 下载文件
    const result = await downloadFile(m3u8Url, localPath);

    if (result.success) {
      // 验证M3U8文件内容
      try {
        const content = fs.readFileSync(localPath, 'utf-8');
        if (!content.startsWith('#EXTM3U')) {
          fs.unlinkSync(localPath);
          return { success: false, error: 'Invalid M3U8 file format' };
        }
        return { success: true, localPath, publicPath };
      } catch (error) {
        fs.unlink(localPath, () => {});
        return { success: false, error: 'Failed to validate M3U8 content' };
      }
    } else {
      return { success: false, error: result.error };
    }

  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 进度管理
function saveProgress(progress) {
  try {
    fs.writeFileSync(CONFIG.PROGRESS_FILE, JSON.stringify(progress, null, 2));
  } catch (error) {
    logWarning('保存进度失败', error);
  }
}

function loadProgress() {
  try {
    if (fs.existsSync(CONFIG.PROGRESS_FILE)) {
      const progress = JSON.parse(fs.readFileSync(CONFIG.PROGRESS_FILE, 'utf-8'));
      logInfo('加载进度文件', progress);
      return progress;
    }
  } catch (error) {
    logWarning('加载进度文件失败', error);
  }
  return null;
}

function clearProgress() {
  try {
    if (fs.existsSync(CONFIG.PROGRESS_FILE)) {
      fs.unlinkSync(CONFIG.PROGRESS_FILE);
      logInfo('已清除进度文件');
    }
  } catch (error) {
    logWarning('清除进度文件失败', error);
  }
}

// 获取影片详细信息（包括演员和类别）
async function fetchMovieDetailFromAPI(movieCode) {
  try {
    // 从最新影片API获取详细信息
    const response = await requestWithRetry(`${CONFIG.API_BASE_URL}/api/v1/jav/movies/latest`, {
      method: 'GET',
      params: { limit: 100 }
    });

    if (!response || !response.success || !response.data) {
      return null;
    }

    // 查找匹配的影片
    const movie = response.data.find(m => m.code === movieCode);
    return movie || null;
  } catch (error) {
    logWarning(`获取影片详情失败: ${movieCode}`, error.message);
    return null;
  }
}

// 创建或获取导演
async function getOrCreateStar(starData) {
  if (!starData || !starData.name || starData.name.trim() === '') return null;

  const name = starData.name.trim();
  const id = starData.id ? starData.id.toString() : name.toLowerCase().replace(/[^a-z0-9]/g, '-');

  try {
    let star = await prisma.star.findUnique({ where: { id } });
    if (!star) {
      star = await prisma.star.create({
        data: {
          id,
          name,
          avatar: starData.avatar_url || null,
          birthday: starData.birth_date || null,
          height: starData.height ? starData.height.toString() : null,
          bust: starData.bust ? starData.bust.toString() : null,
          waist: starData.waist ? starData.waist.toString() : null,
          hip: starData.hip ? starData.hip.toString() : null,
          hobby: starData.hobby || null,
          description: starData.name_en || null
        }
      });
    }
    return star.id;
  } catch (error) {
    logWarning(`创建演员失败: ${name}`, error.message);
    return null;
  }
}

// 创建或获取类别
async function getOrCreateGenre(genreData) {
  if (!genreData || !genreData.name || genreData.name.trim() === '') return null;

  const name = genreData.name.trim();
  const id = genreData.id ? genreData.id.toString() : name.toLowerCase().replace(/[^a-z0-9]/g, '-');

  try {
    let genre = await prisma.genre.findUnique({ where: { id } });
    if (!genre) {
      genre = await prisma.genre.create({
        data: {
          id,
          name
        }
      });
    }
    return genre.id;
  } catch (error) {
    logWarning(`创建类别失败: ${name}`, error.message);
    return null;
  }
}

// 创建或获取导演
async function getOrCreateDirector(directorName) {
  if (!directorName || directorName.trim() === '') return null;

  const name = directorName.trim();
  const id = name.toLowerCase().replace(/[^a-z0-9]/g, '-');

  try {
    let director = await prisma.director.findUnique({ where: { id } });
    if (!director) {
      director = await prisma.director.create({
        data: { id, name }
      });
    }
    return director.id;
  } catch (error) {
    logWarning(`创建导演失败: ${name}`, error.message);
    return null;
  }
}

// 创建或获取制作商
async function getOrCreateProducer(producerName) {
  if (!producerName || producerName.trim() === '') return null;

  const name = producerName.trim();
  const id = name.toLowerCase().replace(/[^a-z0-9]/g, '-');

  try {
    let producer = await prisma.producer.findUnique({ where: { id } });
    if (!producer) {
      producer = await prisma.producer.create({
        data: { id, name }
      });
    }
    return producer.id;
  } catch (error) {
    logWarning(`创建制作商失败: ${name}`, error.message);
    return null;
  }
}

// 创建或获取发行商
async function getOrCreatePublisher(publisherName) {
  if (!publisherName || publisherName.trim() === '') return null;

  const name = publisherName.trim();
  const id = name.toLowerCase().replace(/[^a-z0-9]/g, '-');

  try {
    let publisher = await prisma.publisher.findUnique({ where: { id } });
    if (!publisher) {
      publisher = await prisma.publisher.create({
        data: { id, name }
      });
    }
    return publisher.id;
  } catch (error) {
    logWarning(`创建发行商失败: ${name}`, error.message);
    return null;
  }
}

// 创建或获取系列
async function getOrCreateSeries(seriesName) {
  if (!seriesName || seriesName.trim() === '') return null;

  const name = seriesName.trim();
  const id = name.toLowerCase().replace(/[^a-z0-9]/g, '-');

  try {
    let series = await prisma.series.findUnique({ where: { id } });
    if (!series) {
      series = await prisma.series.create({
        data: { id, name }
      });
    }
    return series.id;
  } catch (error) {
    logWarning(`创建系列失败: ${name}`, error.message);
    return null;
  }
}

// 更新影片-演员关联
async function updateMovieStarRelations(movieId, starIds) {
  try {
    // 删除现有关联
    await prisma.movieStar.deleteMany({
      where: { movieId }
    });

    // 创建新关联
    if (starIds && starIds.length > 0) {
      const relations = starIds.map(starId => ({
        movieId,
        starId
      }));

      await prisma.movieStar.createMany({
        data: relations,
        skipDuplicates: true
      });
    }
  } catch (error) {
    logWarning(`更新影片演员关联失败: ${movieId}`, error.message);
  }
}

// 更新影片-类别关联
async function updateMovieGenreRelations(movieId, genreIds) {
  try {
    // 删除现有关联
    await prisma.movieGenre.deleteMany({
      where: { movieId }
    });

    // 创建新关联
    if (genreIds && genreIds.length > 0) {
      const relations = genreIds.map(genreId => ({
        movieId,
        genreId
      }));

      await prisma.movieGenre.createMany({
        data: relations,
        skipDuplicates: true
      });
    }
  } catch (error) {
    logWarning(`更新影片类别关联失败: ${movieId}`, error.message);
  }
}

// 处理单个影片数据
async function processMovie(movieData) {
  try {
    stats.totalProcessed++;

    // 检查必要字段
    if (!movieData.code) {
      logWarning('影片缺少code字段，跳过', { movieData });
      stats.totalSkipped++;
      return { success: false, reason: 'missing_code' };
    }

    // 检查影片是否已存在（使用id字段存储code）
    const existingMovie = await prisma.movie.findUnique({
      where: { id: movieData.code }
    });

    let localImg = null;
    let localM3u8Path = null;

    // 下载封面图片
    if (options.downloadImages && movieData.cover_url) {
      const imageResult = await downloadImage(movieData.cover_url, movieData.code, 'cover');
      if (imageResult.success) {
        localImg = imageResult.publicPath;
        if (imageResult.skipped) {
          stats.imagesSkipped++;
        } else {
          stats.imagesDownloaded++;
        }
      } else {
        stats.imagesFailed++;
        logWarning(`图片下载失败: ${movieData.code}`, imageResult.error);
      }
    }

    // 下载M3U8文件
    if (options.downloadM3u8 && movieData.tiktok_playlist_url) {
      // 处理相对URL
      let m3u8Url = movieData.tiktok_playlist_url;
      if (m3u8Url.startsWith('/')) {
        m3u8Url = `${CONFIG.API_BASE_URL}${m3u8Url}`;
      }

      const m3u8Result = await downloadM3U8(m3u8Url, movieData.code);
      if (m3u8Result.success) {
        localM3u8Path = m3u8Result.publicPath;
        if (m3u8Result.skipped) {
          stats.m3u8Skipped++;
        } else {
          stats.m3u8Downloaded++;
        }
      } else {
        stats.m3u8Failed++;
        logWarning(`M3U8下载失败: ${movieData.code}`, m3u8Result.error);
      }
    }

    // 获取影片详细信息（包括演员和类别）
    logInfo(`获取影片详细信息: ${movieData.code}`);
    const detailedMovie = await fetchMovieDetailFromAPI(movieData.code);

    // 获取或创建关联实体
    const directorId = await getOrCreateDirector(movieData.director);
    const producerId = await getOrCreateProducer(movieData.studio);
    const publisherId = await getOrCreatePublisher(movieData.publisher);
    const seriesId = await getOrCreateSeries(movieData.series);

    // 处理演员数据
    const starIds = [];
    if (detailedMovie && detailedMovie.actors && Array.isArray(detailedMovie.actors)) {
      for (const actor of detailedMovie.actors) {
        const starId = await getOrCreateStar(actor);
        if (starId) {
          starIds.push(starId);
        }
      }
    }

    // 处理类别数据
    const genreIds = [];
    if (detailedMovie && detailedMovie.genres && Array.isArray(detailedMovie.genres)) {
      for (const genre of detailedMovie.genres) {
        const genreId = await getOrCreateGenre(genre);
        if (genreId) {
          genreIds.push(genreId);
        }
      }
    }

    // 获取精确时长（优先从M3U8文件解析）
    let preciseDuration = { durationSeconds: null, durationFormatted: null };

    // 如果有tiktok_playlist_url，尝试解析M3U8获取精确时长
    if (movieData.tiktok_playlist_url) {
      logVerbose(`尝试从M3U8解析精确时长: ${movieData.code}`);
      preciseDuration = await parseM3U8Duration(movieData.tiktok_playlist_url);
    }

    // 如果M3U8解析失败，使用API提供的时长或生成精确时长
    if (!preciseDuration.durationSeconds && movieData.duration_seconds) {
      preciseDuration.durationSeconds = movieData.duration_seconds;
      preciseDuration.durationFormatted = movieData.duration_formatted;
    } else if (!preciseDuration.durationSeconds && movieData.duration) {
      const generated = generatePreciseDuration(movieData.duration);
      preciseDuration.durationSeconds = generated.durationSeconds;
      preciseDuration.durationFormatted = generated.durationFormatted;
    }

    // 准备数据库数据
    const movieRecord = {
      id: movieData.code,  // 使用code作为id
      title: movieData.title || '',
      img: movieData.cover_url || null,
      localImg: localImg,
      date: movieData.release_date ? new Date(movieData.release_date).toISOString().split('T')[0] : null,
      videoLength: movieData.duration || null,
      durationSeconds: preciseDuration.durationSeconds,
      durationFormatted: preciseDuration.durationFormatted,
      description: movieData.plot || movieData.plot_en || null,
      tiktokPlaylistUrl: movieData.tiktok_playlist_url || null,
      localM3u8Path: localM3u8Path,
      streamtapeUrl: movieData.streamtape_url || null,
      streamhgUrl: movieData.streamhg_url || null,
      directorId: directorId,
      producerId: producerId,
      publisherId: publisherId,
      seriesId: seriesId,
      dataSource: 'paginated-api',
      qualityScore: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    if (options.dryRun) {
      logInfo(`[DRY RUN] 处理影片: ${movieData.code}`, movieRecord);
      stats.totalInserted++;
      return { success: true, action: 'dry_run' };
    }

    if (existingMovie) {
      // 更新现有记录
      await prisma.movie.update({
        where: { id: movieData.code },
        data: {
          title: movieRecord.title,
          img: movieRecord.img,
          localImg: movieRecord.localImg || existingMovie.localImg,
          date: movieRecord.date || existingMovie.date,
          videoLength: movieRecord.videoLength || existingMovie.videoLength,
          durationSeconds: movieRecord.durationSeconds || existingMovie.durationSeconds,
          durationFormatted: movieRecord.durationFormatted || existingMovie.durationFormatted,
          description: movieRecord.description || existingMovie.description,
          tiktokPlaylistUrl: movieRecord.tiktokPlaylistUrl,
          localM3u8Path: movieRecord.localM3u8Path || existingMovie.localM3u8Path,
          streamtapeUrl: movieRecord.streamtapeUrl,
          streamhgUrl: movieRecord.streamhgUrl,
          directorId: movieRecord.directorId || existingMovie.directorId,
          producerId: movieRecord.producerId || existingMovie.producerId,
          publisherId: movieRecord.publisherId || existingMovie.publisherId,
          seriesId: movieRecord.seriesId || existingMovie.seriesId,
          updatedAt: movieRecord.updatedAt,
        }
      });

      stats.totalUpdated++;

      // 更新演员关联
      await updateMovieStarRelations(movieData.code, starIds);

      // 更新类别关联
      await updateMovieGenreRelations(movieData.code, genreIds);

      return { success: true, action: 'updated' };
    } else {
      // 插入新记录
      await prisma.movie.create({
        data: movieRecord
      });

      // 创建演员关联
      await updateMovieStarRelations(movieData.code, starIds);

      // 创建类别关联
      await updateMovieGenreRelations(movieData.code, genreIds);

      stats.totalInserted++;
      return { success: true, action: 'inserted' };
    }

  } catch (error) {
    stats.totalErrors++;
    logError(`处理影片失败: ${movieData.code}`, error);
    return { success: false, error: error.message };
  }
}

// 批量处理影片数据
async function processMoviesBatch(movies) {
  const results = [];

  for (const movie of movies) {
    const result = await processMovie(movie);
    results.push(result);

    // 添加小延迟避免过度请求
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return results;
}

// 主要采集逻辑
async function collectAllPages() {
  try {
    stats.startTime = new Date();

    // 处理断点续传
    let startPage = options.startPage;
    if (options.resume) {
      const progress = loadProgress();
      if (progress && progress.lastCompletedPage) {
        startPage = progress.lastCompletedPage + 1;
        logInfo(`断点续传: 从第 ${startPage} 页开始`);
      }
    }

    let currentPage = startPage;
    let hasMore = true;
    let totalPages = null;

    logInfo('开始翻页数据采集', {
      startPage,
      pageSize: options.pageSize,
      maxPages: options.maxPages,
      downloadImages: options.downloadImages,
      downloadM3u8: options.downloadM3u8,
      dryRun: options.dryRun
    });

    while (hasMore) {
      try {
        // 检查是否达到最大页数限制
        if (options.maxPages && currentPage > startPage + options.maxPages - 1) {
          logInfo(`已达到最大页数限制: ${options.maxPages}`);
          break;
        }

        // 获取当前页数据
        const pageData = await fetchMoviesFromPaginatedAPI(currentPage, options.pageSize);

        if (totalPages === null) {
          totalPages = pageData.totalPages;
          stats.totalPages = totalPages;
          logInfo(`总页数: ${totalPages}`);
        }

        stats.currentPage = currentPage;
        stats.totalMovies += pageData.movies.length;

        // 处理当前页的影片数据
        if (pageData.movies.length > 0) {
          logInfo(`处理第 ${currentPage} 页的 ${pageData.movies.length} 部影片`);

          const results = await processMoviesBatch(pageData.movies);

          logInfo(`第 ${currentPage} 页处理完成`, {
            total: results.length,
            success: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length
          });
        }

        // 保存进度
        const progress = {
          lastCompletedPage: currentPage,
          totalPages: totalPages,
          timestamp: new Date().toISOString(),
          stats: { ...stats }
        };
        saveProgress(progress);

        // 检查是否还有更多页面
        hasMore = pageData.hasMore && pageData.movies.length > 0;

        if (hasMore) {
          currentPage++;

          // 页面间添加延迟
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 显示进度
        const progressPercent = totalPages ? ((currentPage / totalPages) * 100).toFixed(1) : 'N/A';
        logInfo(`采集进度: ${currentPage}/${totalPages || '?'} (${progressPercent}%)`);

      } catch (error) {
        logError(`处理第 ${currentPage} 页时出错`, error);

        // 决定是否继续
        if (error.message.includes('API返回错误') || error.message.includes('API响应格式无效')) {
          logError('API错误，停止采集');
          break;
        } else {
          logWarning('网络错误，跳过当前页继续采集');
          currentPage++;
          continue;
        }
      }
    }

    stats.endTime = new Date();

    // 清除进度文件（采集完成）
    if (!options.dryRun) {
      clearProgress();
    }

    return true;

  } catch (error) {
    logError('采集过程出错', error);
    stats.endTime = new Date();
    return false;
  }
}

// 显示统计报告
function showStats() {
  const duration = stats.endTime ? stats.endTime - stats.startTime : Date.now() - stats.startTime;
  const durationMinutes = (duration / 1000 / 60).toFixed(2);

  console.log('\n' + '='.repeat(60));
  console.log('📊 采集统计报告');
  console.log('='.repeat(60));
  console.log(`⏱️  采集时间: ${durationMinutes} 分钟`);
  console.log(`📄 总页数: ${stats.totalPages || 'N/A'}`);
  console.log(`📄 当前页: ${stats.currentPage}`);
  console.log(`🎬 总影片数: ${stats.totalMovies}`);
  console.log(`✅ 处理成功: ${stats.totalProcessed}`);
  console.log(`➕ 新增记录: ${stats.totalInserted}`);
  console.log(`🔄 更新记录: ${stats.totalUpdated}`);
  console.log(`⏭️  跳过记录: ${stats.totalSkipped}`);
  console.log(`❌ 处理失败: ${stats.totalErrors}`);

  if (options.downloadImages) {
    console.log('\n📸 图片下载统计:');
    console.log(`  ✅ 下载成功: ${stats.imagesDownloaded}`);
    console.log(`  ⏭️  跳过已存在: ${stats.imagesSkipped}`);
    console.log(`  ❌ 下载失败: ${stats.imagesFailed}`);
  }

  if (options.downloadM3u8) {
    console.log('\n🎵 M3U8下载统计:');
    console.log(`  ✅ 下载成功: ${stats.m3u8Downloaded}`);
    console.log(`  ⏭️  跳过已存在: ${stats.m3u8Skipped}`);
    console.log(`  ❌ 下载失败: ${stats.m3u8Failed}`);
  }

  console.log('='.repeat(60));

  // 计算成功率
  const successRate = stats.totalProcessed > 0 ?
    ((stats.totalInserted + stats.totalUpdated) / stats.totalProcessed * 100).toFixed(1) : 0;
  console.log(`📈 成功率: ${successRate}%`);

  if (options.dryRun) {
    console.log('🧪 这是一次测试运行，没有实际修改数据库');
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
翻页数据采集脚本

使用方法:
  node paginated-data-collector.js [options]

选项:
  --page-size <number>         每页数量 (默认: 20, 最大: 100)
  --start-page <number>        起始页面 (默认: 1)
  --max-pages <number>         最大页数限制 (默认: 无限制)
  --batch-size <number>        批量插入大小 (默认: 50)
  --download-images            下载图片 (默认: true)
  --no-download-images         不下载图片
  --download-m3u8              下载M3U8 (默认: true)
  --no-download-m3u8           不下载M3U8
  --concurrent-downloads <n>   下载并发数 (默认: 3)
  --resume                     断点续传模式
  --dry-run                    仅测试，不实际插入数据
  --verbose                    详细日志输出
  --help                       显示此帮助信息

示例:
  # 基本采集
  node paginated-data-collector.js

  # 从第10页开始，每页50条，最多采集5页
  node paginated-data-collector.js --start-page=10 --page-size=50 --max-pages=5

  # 断点续传模式
  node paginated-data-collector.js --resume

  # 测试模式（不实际写入数据库）
  node paginated-data-collector.js --dry-run --verbose

  # 只下载数据，不下载文件
  node paginated-data-collector.js --no-download-images --no-download-m3u8
`);
}

// 主函数
async function main() {
  // 检查帮助参数
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  logInfo('翻页数据采集脚本启动', {
    options,
    nodeVersion: process.version,
    platform: process.platform
  });

  try {
    // 初始化存储目录
    initStorageDirectories();

    // 初始化数据库连接
    const dbConnected = await initDatabase();
    if (!dbConnected) {
      process.exit(1);
    }

    // 开始采集
    const success = await collectAllPages();

    // 显示统计报告
    showStats();

    // 清理资源
    if (prisma) {
      await prisma.$disconnect();
    }

    if (success) {
      logSuccess('数据采集完成');
      process.exit(0);
    } else {
      logError('数据采集失败');
      process.exit(1);
    }

  } catch (error) {
    logError('程序执行出错', error);

    if (prisma) {
      await prisma.$disconnect();
    }

    process.exit(1);
  }
}

// 处理进程信号
process.on('SIGINT', async () => {
  logWarning('收到中断信号，正在清理资源...');

  if (prisma) {
    await prisma.$disconnect();
  }

  showStats();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logWarning('收到终止信号，正在清理资源...');

  if (prisma) {
    await prisma.$disconnect();
  }

  showStats();
  process.exit(0);
});

// 启动程序
if (require.main === module) {
  main();
}
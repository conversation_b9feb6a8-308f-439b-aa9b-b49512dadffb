# 流媒体数据采集脚本使用说明

## 概述

`simple-streaming-collector.js` 是一个专门用于从 `http://*************:8081/api/v1/jav/movies/streaming-latest` API 采集流媒体数据到本地 PostgreSQL 数据库的脚本。

## 功能特性

- ✅ **API数据获取**: 从指定API获取最新的流媒体影片数据
- ✅ **数据验证和清理**: 自动验证和清理影片数据，确保数据质量
- ✅ **批量数据处理**: 支持大批量数据采集和处理
- ✅ **增量更新**: 自动检测已存在影片，支持插入新数据和更新现有数据
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **进度跟踪**: 实时显示采集进度和统计信息
- ✅ **日志记录**: 详细的日志记录和统计报告
- ✅ **DRY RUN模式**: 支持测试模式，不实际修改数据库

## 安装依赖

确保已安装必要的依赖：

```bash
cd getav-web
npm install axios pg
```

## 数据库配置

脚本会自动连接到本地PostgreSQL数据库：
- 主机: localhost
- 端口: 5432
- 数据库: getav
- 用户: postgres
- 密码: (空)

## 使用方法

### 基本用法

```bash
# 采集默认数量的数据（1000条）
node simple-streaming-collector.js

# 采集指定数量的数据
node simple-streaming-collector.js --limit=100

# 从指定偏移量开始采集
node simple-streaming-collector.js --offset=100 --limit=50

# 测试模式（不实际插入数据）
node simple-streaming-collector.js --limit=10 --dry-run

# 详细日志模式
node simple-streaming-collector.js --limit=10 --verbose
```

### 命令行参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--limit=<number>` | 每次采集的数量 | 1000 | `--limit=500` |
| `--offset=<number>` | 跳过的数量 | 0 | `--offset=100` |
| `--dry-run` | 测试模式，不实际插入数据 | false | `--dry-run` |
| `--verbose` | 详细日志输出 | false | `--verbose` |

### 使用示例

```bash
# 1. 测试API连接和数据格式
node simple-streaming-collector.js --limit=5 --dry-run --verbose

# 2. 小批量采集测试
node simple-streaming-collector.js --limit=10 --verbose

# 3. 大批量采集
node simple-streaming-collector.js --limit=1000

# 4. 增量采集（跳过前1000条）
node simple-streaming-collector.js --offset=1000 --limit=1000

# 5. 获取最大数量
node simple-streaming-collector.js --limit=10000
```

## 数据结构

脚本会将API返回的数据映射到以下数据库字段：

| API字段 | 数据库字段 | 说明 |
|---------|------------|------|
| `code` | `id` | 影片编号（主键） |
| `title` | `title` | 影片标题 |
| `cover_url` | `img` | 封面图片URL |
| `release_date` | `date` | 发布日期 |
| `duration` | `videoLength` | 影片时长 |
| `plot` | `description` | 影片描述 |
| `streamtape_url` | `streamtapeUrl` | StreamTape播放链接 |
| `streamhg_url` | `streamhgUrl` | StreamHG播放链接 |
| - | `dataSource` | 数据来源标记（固定为"streaming_api"） |

## 输出示例

```
🚀 简化版流媒体数据采集脚本启动
==================================================
📡 API地址: http://*************:8081/api/v1/jav/movies/streaming-latest
📊 采集参数: offset=0, limit=10
🧪 测试模式: 否
📝 详细日志: 是
==================================================
[2025-07-04T11:26:19.142Z] [SUCCESS] ✅ 数据库连接成功
[2025-07-04T11:26:19.214Z] [SUCCESS] ✅ 成功获取5部影片
[2025-07-04T11:26:19.215Z] [INFO] 数据处理完成 (processed: 5, skipped: 0)
[2025-07-04T11:26:19.235Z] [SUCCESS] ✅ 批量数据插入完成

============================================================
📊 数据采集统计报告
============================================================
⏱️  执行时间: 0秒
📥 获取数据: 5 条
⚙️  处理数据: 5 条
✅ 插入数据: 4 条
🔄 更新数据: 1 条
⏭️  跳过数据: 0 条
❌ 错误数量: 0 条
📈 成功率: 100.0%
============================================================
```

## 日志文件

脚本会自动在 `logs/` 目录下保存详细的采集日志：
- 文件名格式: `simple-collector-YYYY-MM-DDTHH-mm-ss.json`
- 包含完整的统计信息和配置参数

## 注意事项

1. **数据验证**: 脚本会自动跳过没有播放链接的影片
2. **重复处理**: 已存在的影片会被更新而不是重复插入
3. **网络超时**: API请求超时时间为60秒，支持自动重试
4. **数据库事务**: 使用事务确保数据一致性
5. **资源清理**: 脚本会自动清理数据库连接

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否运行
   - 确认数据库配置正确

2. **API连接失败**
   - 检查网络连接
   - 确认API服务器是否可访问

3. **权限错误**
   - 确认数据库用户有足够权限
   - 检查表是否存在

### 调试方法

使用 `--verbose` 参数获取详细日志：
```bash
node simple-streaming-collector.js --limit=1 --verbose
```

## 性能建议

- 对于大批量采集，建议分批进行（每次1000-5000条）
- 使用 `--dry-run` 模式先测试数据格式
- 定期检查日志文件了解采集状态
- 在网络条件不佳时适当减少批量大小

## 更新历史

- v1.0.0: 初始版本，支持基本的数据采集功能
- v1.1.0: 添加了详细的日志记录和统计报告
- v1.2.0: 优化了性能，减少了冗余的调试信息

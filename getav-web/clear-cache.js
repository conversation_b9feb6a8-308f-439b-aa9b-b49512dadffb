#!/usr/bin/env node

/**
 * 简单的缓存清理工具
 * 使用API端点清空首页缓存
 */

const http = require('http');

async function clearHomepageCache() {
  console.log('🚀 开始清空首页缓存...');

  const postData = JSON.stringify({
    action: 'clear',
    target: 'homepage'
  });

  const options = {
    hostname: 'localhost',
    port: 3001, // Next.js开发服务器端口
    path: '/api/cache',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.success) {
            console.log('✅ 首页缓存清理成功:', response.message);
            resolve(response);
          } else {
            console.error('❌ 缓存清理失败:', response.error);
            reject(new Error(response.error));
          }
        } catch (error) {
          console.error('❌ 解析响应失败:', error);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ 请求失败:', error.message);
      console.log('💡 请确保Next.js开发服务器正在运行 (npm run dev)');
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 执行缓存清理
clearHomepageCache()
  .then(() => {
    console.log('🎉 缓存清理完成！');
    console.log('💡 下次访问首页时将重新生成缓存');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 缓存清理失败:', error.message);
    process.exit(1);
  });

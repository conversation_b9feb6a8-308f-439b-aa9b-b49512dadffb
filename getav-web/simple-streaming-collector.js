#!/usr/bin/env node

/**
 * 简化版流媒体数据采集脚本
 * 使用原生PostgreSQL连接，避免Prisma权限问题
 */

const axios = require('axios');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 配置常量
const CONFIG = {
  API_BASE_URL: 'http://*************:8081',
  API_ENDPOINT: '/api/v1/jav/movies/streaming-latest',
  DEFAULT_LIMIT: 1000,
  MAX_LIMIT: 10000,
  REQUEST_TIMEOUT: 60000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 2000,
};

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 5432, // 宝塔面板的PostgreSQL端口
  database: 'getav',
  user: 'postgres',
  password: '',
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
};

// 全局变量
let pool;
let stats = {
  totalFetched: 0,
  totalProcessed: 0,
  totalInserted: 0,
  totalUpdated: 0,
  totalSkipped: 0,
  totalErrors: 0,
  startTime: null,
  endTime: null,
};

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
  limit: parseInt(args.find(arg => arg.startsWith('--limit='))?.split('=')[1]) || CONFIG.DEFAULT_LIMIT,
  offset: parseInt(args.find(arg => arg.startsWith('--offset='))?.split('=')[1]) || 0,
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
};

// 日志函数
function log(level, message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  
  console.log(logMessage);
  
  if (data && options.verbose) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function logInfo(message, data) {
  log('info', message, data);
}

function logError(message, error) {
  log('error', message, error);
  if (error && error.stack && options.verbose) {
    console.error(error.stack);
  }
}

function logSuccess(message, data) {
  log('success', `✅ ${message}`, data);
}

function logWarning(message, data) {
  log('warning', `⚠️  ${message}`, data);
}

// 初始化数据库连接
async function initDatabase() {
  try {
    pool = new Pool(dbConfig);
    
    // 测试数据库连接
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    
    logSuccess('数据库连接成功');
    return true;
  } catch (error) {
    logError('数据库连接失败', error);
    return false;
  }
}

// 关闭数据库连接
async function closeDatabase() {
  if (pool) {
    await pool.end();
    logInfo('数据库连接已关闭');
  }
}

// HTTP请求重试函数
async function requestWithRetry(url, config = {}, retryCount = CONFIG.RETRY_COUNT) {
  let lastError;
  
  for (let i = 0; i <= retryCount; i++) {
    try {
      const response = await axios({
        url,
        timeout: CONFIG.REQUEST_TIMEOUT,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        ...config
      });
      
      return response.data;
    } catch (error) {
      lastError = error;
      
      if (i < retryCount) {
        const delay = CONFIG.RETRY_DELAY * Math.pow(2, i);
        logWarning(`请求失败，${delay}ms后重试 (${i + 1}/${retryCount})`, {
          url,
          error: error.message
        });
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

// 从API获取数据
async function fetchMoviesFromAPI(offset = 0, limit = CONFIG.DEFAULT_LIMIT) {
  try {
    const url = `${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`;
    const params = {
      offset,
      limit: Math.min(limit, CONFIG.MAX_LIMIT)
    };
    
    logInfo(`正在从API获取数据`, { url, params });
    
    const response = await requestWithRetry(url, {
      method: 'GET',
      params
    });
    
    // 验证响应格式
    if (!response || typeof response !== 'object') {
      throw new Error('API响应格式无效');
    }
    
    if (!response.success) {
      throw new Error(`API返回错误: ${response.message || '未知错误'}`);
    }
    
    if (!response.data || !Array.isArray(response.data.movies)) {
      throw new Error('API响应中缺少movies数组');
    }
    
    const { movies, total, offset: responseOffset, limit: responseLimit, has_more } = response.data;
    
    logSuccess(`成功获取${movies.length}部影片`, {
      total,
      offset: responseOffset,
      limit: responseLimit,
      has_more,
      actualCount: movies.length
    });
    
    stats.totalFetched += movies.length;
    
    return {
      movies,
      total,
      offset: responseOffset,
      limit: responseLimit,
      has_more
    };
    
  } catch (error) {
    logError('从API获取数据失败', error);
    stats.totalErrors++;
    throw error;
  }
}

// 验证和清理单个影片数据
function validateAndCleanMovieData(movie) {
  if (!movie || typeof movie !== 'object') {
    return null;
  }
  
  // 必需字段验证
  if (!movie.code || typeof movie.code !== 'string') {
    logWarning('影片缺少code字段', { movie });
    return null;
  }
  
  if (!movie.title || typeof movie.title !== 'string') {
    logWarning('影片缺少title字段', { code: movie.code });
    return null;
  }
  
  // 清理和标准化数据
  const cleanedMovie = {
    id: movie.code.trim().toUpperCase(), // 使用code作为主键ID
    title: movie.title.trim(),
    img: movie.cover_url && movie.cover_url.trim() !== '' ? movie.cover_url.trim() : null,
    localImg: null, // 本地图片路径，暂时为空
    date: movie.release_date && movie.release_date.trim() !== '' ? movie.release_date.trim() : null,
    videoLength: movie.duration && typeof movie.duration === 'number' ? movie.duration : null,
    description: movie.plot && movie.plot.trim() !== '' ? movie.plot.trim() : null,
    gid: null, // javbus相关字段，新API中没有
    uc: null,  // javbus相关字段，新API中没有
    streamtapeUrl: movie.streamtape_url && movie.streamtape_url.trim() !== '' && movie.streamtape_url !== 'null' ? movie.streamtape_url.trim() : null,
    streamhgUrl: movie.streamhg_url && movie.streamhg_url.trim() !== '' && movie.streamhg_url !== 'null' ? movie.streamhg_url.trim() : null,
    dataSource: 'streaming_api', // 标记数据来源
    qualityScore: 0, // 默认质量分数
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  // 验证是否有播放链接
  const hasPlaybackLinks = cleanedMovie.streamtapeUrl || cleanedMovie.streamhgUrl;
  if (!hasPlaybackLinks) {
    logWarning('影片没有播放链接，跳过', { code: cleanedMovie.id });
    return null;
  }
  
  return cleanedMovie;
}

// 批量处理影片数据
function processMoviesData(movies) {
  if (!Array.isArray(movies)) {
    logError('movies参数必须是数组');
    return [];
  }
  
  const processedMovies = [];
  const skippedMovies = [];
  
  for (const movie of movies) {
    try {
      const cleanedMovie = validateAndCleanMovieData(movie);
      if (cleanedMovie) {
        processedMovies.push(cleanedMovie);
      } else {
        skippedMovies.push(movie);
        stats.totalSkipped++;
      }
    } catch (error) {
      logError('处理影片数据时出错', { movie, error });
      skippedMovies.push(movie);
      stats.totalErrors++;
    }
  }
  
  stats.totalProcessed += processedMovies.length;
  
  logInfo(`数据处理完成`, {
    processed: processedMovies.length,
    skipped: skippedMovies.length,
    total: movies.length
  });
  
  return processedMovies;
}

// 插入或更新单个影片到数据库
async function upsertMovie(client, movie) {
  try {
    // 调试信息（仅在verbose模式下显示）
    if (options.verbose && stats.totalProcessed === 1) {
      const testResult = await client.query('SELECT current_database(), current_schema()');
      logInfo(`数据库连接信息`, {
        database: testResult.rows[0].current_database,
        schema: testResult.rows[0].current_schema
      });
    }

    // 检查影片是否已存在
    const existingResult = await client.query(
      'SELECT id FROM public.movies WHERE id = $1',
      [movie.id]
    );

    if (existingResult.rows.length > 0) {
      // 更新现有影片
      await client.query(`
        UPDATE public.movies SET
          title = $2,
          img = $3,
          "localImg" = $4,
          date = $5,
          "videoLength" = $6,
          description = $7,
          "streamtapeUrl" = $8,
          "streamhgUrl" = $9,
          "dataSource" = $10,
          "qualityScore" = $11,
          "updatedAt" = $12
        WHERE id = $1
      `, [
        movie.id,
        movie.title,
        movie.img,
        movie.localImg,
        movie.date,
        movie.videoLength,
        movie.description,
        movie.streamtapeUrl,
        movie.streamhgUrl,
        movie.dataSource,
        movie.qualityScore,
        movie.updatedAt
      ]);

      if (options.verbose) {
        logInfo(`更新影片: ${movie.id}`, { title: movie.title });
      }
      return 'updated';
    } else {
      // 插入新影片
      await client.query(`
        INSERT INTO public.movies (
          id, title, img, "localImg", date, "videoLength", description,
          gid, uc, "streamtapeUrl", "streamhgUrl", "dataSource",
          "qualityScore", "createdAt", "updatedAt"
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
        )
      `, [
        movie.id,
        movie.title,
        movie.img,
        movie.localImg,
        movie.date,
        movie.videoLength,
        movie.description,
        movie.gid,
        movie.uc,
        movie.streamtapeUrl,
        movie.streamhgUrl,
        movie.dataSource,
        movie.qualityScore,
        movie.createdAt,
        movie.updatedAt
      ]);

      if (options.verbose) {
        logInfo(`插入影片: ${movie.id}`, { title: movie.title });
      }
      return 'inserted';
    }
  } catch (error) {
    logError(`处理影片失败: ${movie.id}`, error);
    return 'error';
  }
}

// 批量插入或更新影片数据到数据库
async function upsertMoviesToDatabase(movies) {
  if (!Array.isArray(movies) || movies.length === 0) {
    logWarning('没有影片数据需要插入');
    return { inserted: 0, updated: 0, errors: 0 };
  }

  if (options.dryRun) {
    logInfo('DRY RUN模式：跳过数据库操作', { movieCount: movies.length });
    return { inserted: movies.length, updated: 0, errors: 0 };
  }

  let insertedCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  logInfo(`开始批量插入数据`, {
    totalMovies: movies.length
  });

  const client = await pool.connect();

  try {
    // 开始事务
    await client.query('BEGIN');

    for (let i = 0; i < movies.length; i++) {
      const movie = movies[i];
      const result = await upsertMovie(client, movie);

      switch (result) {
        case 'inserted':
          insertedCount++;
          break;
        case 'updated':
          updatedCount++;
          break;
        case 'error':
          errorCount++;
          break;
      }

      // 显示进度
      if ((i + 1) % 10 === 0 || i === movies.length - 1) {
        const progress = ((i + 1) / movies.length * 100).toFixed(1);
        logInfo(`处理进度: ${progress}% (${i + 1}/${movies.length})`);
      }
    }

    // 提交事务
    await client.query('COMMIT');

  } catch (error) {
    // 回滚事务
    await client.query('ROLLBACK');
    logError('批量插入过程中发生错误，已回滚', error);
    errorCount = movies.length;
  } finally {
    client.release();
  }

  const result = { inserted: insertedCount, updated: updatedCount, errors: errorCount };

  stats.totalInserted += insertedCount;
  stats.totalUpdated += updatedCount;
  stats.totalErrors += errorCount;

  logSuccess('批量数据插入完成', result);
  return result;
}

// 显示统计信息
function displayStats() {
  const duration = stats.endTime ? stats.endTime - stats.startTime : Date.now() - stats.startTime;
  const durationSeconds = Math.round(duration / 1000);
  const durationMinutes = Math.round(durationSeconds / 60);

  console.log('\n' + '='.repeat(60));
  console.log('📊 数据采集统计报告');
  console.log('='.repeat(60));
  console.log(`⏱️  执行时间: ${durationMinutes > 0 ? `${durationMinutes}分${durationSeconds % 60}秒` : `${durationSeconds}秒`}`);
  console.log(`📥 获取数据: ${stats.totalFetched} 条`);
  console.log(`⚙️  处理数据: ${stats.totalProcessed} 条`);
  console.log(`✅ 插入数据: ${stats.totalInserted} 条`);
  console.log(`🔄 更新数据: ${stats.totalUpdated} 条`);
  console.log(`⏭️  跳过数据: ${stats.totalSkipped} 条`);
  console.log(`❌ 错误数量: ${stats.totalErrors} 条`);

  if (stats.totalProcessed > 0) {
    const successRate = ((stats.totalInserted + stats.totalUpdated) / stats.totalProcessed * 100).toFixed(1);
    console.log(`📈 成功率: ${successRate}%`);
  }

  console.log('='.repeat(60));

  if (options.dryRun) {
    console.log('🧪 这是一次DRY RUN测试，没有实际修改数据库');
  }
}

// 保存采集日志到文件
async function saveLogToFile() {
  try {
    const logData = {
      timestamp: new Date().toISOString(),
      options,
      stats,
      duration: stats.endTime - stats.startTime
    };

    const logDir = path.join(__dirname, 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const logFileName = `simple-collector-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    const logFilePath = path.join(logDir, logFileName);

    fs.writeFileSync(logFilePath, JSON.stringify(logData, null, 2));
    logInfo(`采集日志已保存: ${logFilePath}`);
  } catch (error) {
    logError('保存日志文件失败', error);
  }
}

// 主函数
async function main() {
  stats.startTime = Date.now();

  try {
    // 显示启动信息
    console.log('\n🚀 简化版流媒体数据采集脚本启动');
    console.log('='.repeat(50));
    console.log(`📡 API地址: ${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`);
    console.log(`📊 采集参数: offset=${options.offset}, limit=${options.limit}`);
    console.log(`🧪 测试模式: ${options.dryRun ? '是' : '否'}`);
    console.log(`📝 详细日志: ${options.verbose ? '是' : '否'}`);
    console.log('='.repeat(50));

    // 初始化数据库连接
    logInfo('正在初始化数据库连接...');
    const dbConnected = await initDatabase();
    if (!dbConnected) {
      throw new Error('数据库连接失败，无法继续执行');
    }

    // 从API获取数据
    logInfo('正在从API获取数据...');
    const apiResponse = await fetchMoviesFromAPI(options.offset, options.limit);

    if (!apiResponse.movies || apiResponse.movies.length === 0) {
      logWarning('API返回的数据为空');
      return;
    }

    // 处理数据
    logInfo('正在处理影片数据...');
    const processedMovies = processMoviesData(apiResponse.movies);

    if (processedMovies.length === 0) {
      logWarning('没有有效的影片数据需要处理');
      return;
    }

    // 插入数据库
    logInfo('正在将数据插入数据库...');
    const dbResult = await upsertMoviesToDatabase(processedMovies);

    // 记录结束时间
    stats.endTime = Date.now();

    // 显示统计信息
    displayStats();

    // 保存日志
    await saveLogToFile();

    logSuccess('数据采集完成！');

  } catch (error) {
    stats.endTime = Date.now();
    logError('数据采集过程中发生错误', error);

    // 即使出错也要显示统计信息
    displayStats();
    await saveLogToFile();

    throw error;
  } finally {
    // 清理资源
    await closeDatabase();
  }
}

// 错误处理和清理
process.on('SIGINT', async () => {
  logInfo('收到中断信号，正在清理...');
  await closeDatabase();
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  logError('未处理的Promise拒绝', reason);
  process.exit(1);
});

// 启动脚本
if (require.main === module) {
  main().catch(async (error) => {
    logError('脚本执行失败', error);
    await closeDatabase();
    process.exit(1);
  });
}

-- 清空数据库中除用户相关表以外的所有数据
-- 保留表结构，只删除数据
-- 备份文件：getav_backup_20250702_223733.sql

-- 开始事务
BEGIN;

-- 禁用外键约束检查（PostgreSQL）
SET session_replication_role = replica;

-- 清空用户交互相关表
TRUNCATE TABLE "user_activity_logs" CASCADE;
TRUNCATE TABLE "reports" CASCADE;
TRUNCATE TABLE "views" CASCADE;
TRUNCATE TABLE "ratings" CASCADE;
TRUNCATE TABLE "favorites" CASCADE;
TRUNCATE TABLE "favorite_lists" CASCADE;
TRUNCATE TABLE "likes" CASCADE;
TRUNCATE TABLE "comments" CASCADE;

-- 清空影片相关表
TRUNCATE TABLE "magnets" CASCADE;
TRUNCATE TABLE "movie_genres" CASCADE;
TRUNCATE TABLE "movie_stars" CASCADE;
TRUNCATE TABLE "movies" CASCADE;

-- 清空演员、导演、制作商等表
TRUNCATE TABLE "stars" CASCADE;
TRUNCATE TABLE "directors" CASCADE;
TRUNCATE TABLE "producers" CASCADE;
TRUNCATE TABLE "publishers" CASCADE;
TRUNCATE TABLE "series" CASCADE;
TRUNCATE TABLE "genres" CASCADE;

-- 重新启用外键约束检查
SET session_replication_role = DEFAULT;

-- 提交事务
COMMIT;

-- 显示清理结果
SELECT 
    schemaname,
    tablename,
    n_tup_ins as "插入行数",
    n_tup_upd as "更新行数", 
    n_tup_del as "删除行数"
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- 显示保留的用户相关表的记录数
SELECT 'users' as table_name, COUNT(*) as record_count FROM "users"
UNION ALL
SELECT 'accounts' as table_name, COUNT(*) as record_count FROM "accounts"
UNION ALL
SELECT 'sessions' as table_name, COUNT(*) as record_count FROM "sessions"
UNION ALL
SELECT 'verificationtokens' as table_name, COUNT(*) as record_count FROM "verificationtokens"
UNION ALL
SELECT 'user_passwords' as table_name, COUNT(*) as record_count FROM "user_passwords"
UNION ALL
SELECT 'roles' as table_name, COUNT(*) as record_count FROM "roles"
UNION ALL
SELECT 'permissions' as table_name, COUNT(*) as record_count FROM "permissions"
UNION ALL
SELECT 'user_roles' as table_name, COUNT(*) as record_count FROM "user_roles"
UNION ALL
SELECT 'role_permissions' as table_name, COUNT(*) as record_count FROM "role_permissions"
ORDER BY table_name;

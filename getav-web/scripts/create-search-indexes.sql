-- PostgreSQL全文搜索索引创建脚本
-- 为GETAV.NET项目优化搜索性能

-- 1. 为影片表创建全文搜索索引
-- 创建复合全文搜索索引，包含影片ID、标题和描述
CREATE INDEX IF NOT EXISTS idx_movies_fulltext_search 
ON "movies" USING gin(
  (
    setweight(to_tsvector('simple', COALESCE(id, '')), 'A') ||
    setweight(to_tsvector('simple', COALESCE(title, '')), 'B') ||
    setweight(to_tsvector('simple', COALESCE(description, '')), 'C')
  )
);

-- 2. 为演员表创建全文搜索索引
-- 演员姓名搜索索引
CREATE INDEX IF NOT EXISTS idx_stars_name_fulltext 
ON "stars" USING gin(to_tsvector('simple', COALESCE(name, '')));

-- 演员姓名模糊搜索索引（支持ILIKE查询）
CREATE INDEX IF NOT EXISTS idx_stars_name_trigram 
ON "stars" USING gin(name gin_trgm_ops);

-- 3. 为分类表创建搜索索引
CREATE INDEX IF NOT EXISTS idx_genres_name_fulltext 
ON "genres" USING gin(to_tsvector('simple', COALESCE(name, '')));

-- 分类名称模糊搜索索引
CREATE INDEX IF NOT EXISTS idx_genres_name_trigram 
ON "genres" USING gin(name gin_trgm_ops);

-- 4. 为影片相关的常用查询创建索引
-- 影片创建时间索引（用于排序）
CREATE INDEX IF NOT EXISTS idx_movies_created_at 
ON "movies" ("createdAt" DESC);

-- 影片发布日期索引
CREATE INDEX IF NOT EXISTS idx_movies_date 
ON "movies" (date DESC) WHERE date IS NOT NULL;

-- 影片时长索引
CREATE INDEX IF NOT EXISTS idx_movies_video_length 
ON "movies" ("videoLength") WHERE "videoLength" IS NOT NULL;

-- 5. 为关联表创建复合索引
-- 影片-演员关联索引
CREATE INDEX IF NOT EXISTS idx_movie_stars_movie_star 
ON "movie_stars" ("movieId", "starId");

CREATE INDEX IF NOT EXISTS idx_movie_stars_star_movie 
ON "movie_stars" ("starId", "movieId");

-- 影片-分类关联索引
CREATE INDEX IF NOT EXISTS idx_movie_genres_movie_genre 
ON "movie_genres" ("movieId", "genreId");

CREATE INDEX IF NOT EXISTS idx_movie_genres_genre_movie 
ON "movie_genres" ("genreId", "movieId");

-- 6. 为用户交互功能创建索引
-- 评论索引
CREATE INDEX IF NOT EXISTS idx_comments_movie_created 
ON "comments" ("movieId", "createdAt" DESC);

CREATE INDEX IF NOT EXISTS idx_comments_magnet_created 
ON "comments" ("magnetId", "createdAt" DESC) WHERE "magnetId" IS NOT NULL;

-- 点赞索引
CREATE INDEX IF NOT EXISTS idx_likes_movie_type 
ON "likes" ("movieId", type) WHERE "movieId" IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_likes_comment_type 
ON "likes" ("commentId", type) WHERE "commentId" IS NOT NULL;

-- 收藏索引
CREATE INDEX IF NOT EXISTS idx_favorites_user_created 
ON "favorites" ("userId", "createdAt" DESC);

-- 评分索引
CREATE INDEX IF NOT EXISTS idx_ratings_movie_score 
ON "ratings" ("movieId", score);

-- 7. 为磁力链接创建索引
-- 磁力链接按影片和分享日期索引
CREATE INDEX IF NOT EXISTS idx_magnets_movie_date 
ON "magnets" ("movieId", "shareDate" DESC);

-- 字幕磁力链接索引
CREATE INDEX IF NOT EXISTS idx_magnets_subtitle 
ON "magnets" ("movieId", "hasSubtitle") WHERE "hasSubtitle" = true;

-- 高清磁力链接索引
CREATE INDEX IF NOT EXISTS idx_magnets_hd 
ON "magnets" ("movieId", "isHD") WHERE "isHD" = true;

-- 8. 确保必要的PostgreSQL扩展已启用
-- 启用pg_trgm扩展（用于三元组模糊搜索）
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 启用unaccent扩展（用于去除重音符号，提高搜索准确性）
CREATE EXTENSION IF NOT EXISTS unaccent;

-- 9. 创建自定义搜索配置（可选）
-- 创建简化的中文搜索配置
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_ts_config WHERE cfgname = 'chinese_simple') THEN
    CREATE TEXT SEARCH CONFIGURATION chinese_simple (COPY = simple);
  END IF;
END
$$;

-- 10. 分析表统计信息
-- 更新表统计信息以优化查询计划
ANALYZE "movies";
ANALYZE "stars";
ANALYZE "genres";
ANALYZE "movie_stars";
ANALYZE "movie_genres";
ANALYZE "comments";
ANALYZE "likes";
ANALYZE "favorites";
ANALYZE "ratings";
ANALYZE "magnets";

-- 完成提示
SELECT 'PostgreSQL全文搜索索引创建完成！' as status;

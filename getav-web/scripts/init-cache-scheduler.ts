#!/usr/bin/env tsx

/**
 * 缓存调度器初始化脚本
 * 在生产环境中启动缓存预热调度器
 */

import { cacheScheduler } from '../src/lib/cache-scheduler';
import { logger } from '../src/lib/logger';
import { isRedisConnected } from '../src/lib/redis';

async function initializeCacheScheduler() {
  console.log('🚀 初始化缓存调度器...\n');

  try {
    // 检查Redis连接
    console.log('📡 检查Redis连接...');
    const redisConnected = await isRedisConnected();
    
    if (!redisConnected) {
      console.error('❌ Redis连接失败，无法启动缓存调度器');
      process.exit(1);
    }
    
    console.log('✅ Redis连接正常');

    // 启动调度器
    console.log('⏰ 启动缓存调度器...');
    cacheScheduler.start();
    
    const status = cacheScheduler.getStatus();
    console.log('✅ 缓存调度器启动成功');
    console.log('\n📊 调度器状态:');
    console.log(`  - 运行状态: ${status.isRunning ? '运行中' : '已停止'}`);
    console.log(`  - 活动定时器: ${status.activeTimers.join(', ') || '无'}`);
    console.log('\n⚙️  调度配置:');
    console.log(`  - 启动时预热: ${status.config.intervals.startup ? '启用' : '禁用'}`);
    console.log(`  - 每小时预热: ${status.config.intervals.hourly ? '启用' : '禁用'}`);
    console.log(`  - 每日预热: ${status.config.intervals.daily ? '启用' : '禁用'} (${status.config.timeSlots.daily})`);
    console.log(`  - 每周预热: ${status.config.intervals.weekly ? '启用' : '禁用'} (周${status.config.timeSlots.weekly.day} ${status.config.timeSlots.weekly.time})`);
    console.log(`  - 低流量时段: ${status.config.lowTrafficHours.join(', ')}点`);

    // 设置优雅关闭
    process.on('SIGINT', () => {
      console.log('\n🛑 接收到停止信号，正在关闭调度器...');
      cacheScheduler.stop();
      console.log('✅ 缓存调度器已停止');
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 接收到终止信号，正在关闭调度器...');
      cacheScheduler.stop();
      console.log('✅ 缓存调度器已停止');
      process.exit(0);
    });

    console.log('\n🎉 缓存调度器初始化完成！');
    console.log('💡 使用 Ctrl+C 停止调度器');

  } catch (error) {
    console.error('❌ 缓存调度器初始化失败:', error);
    logger.error('缓存调度器初始化失败', error as Error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initializeCacheScheduler().catch(console.error);
}

export { initializeCacheScheduler };

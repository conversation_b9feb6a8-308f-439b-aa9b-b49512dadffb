#!/usr/bin/env node

/**
 * 时长数据更新脚本
 * 为现有影片补充精确时长数据（durationSeconds 和 durationFormatted）
 * 基于 paginated-data-collector.js 的架构模式
 * 
 * 使用方法:
 * node scripts/update-duration-data.js [options]
 * 
 * 选项:
 * --batch-size <number>    批量处理大小 (默认: 50)
 * --concurrent <number>    并发请求数 (默认: 3)
 * --delay <number>         请求间隔毫秒 (默认: 1000)
 * --resume                 断点续传模式
 * --dry-run               仅测试，不实际更新数据
 * --verbose               详细日志输出
 * --filter <string>       过滤条件: missing(缺少时长), all(所有), specific(指定ID)
 * --movie-id <string>     指定影片ID（配合--filter=specific使用）
 * --mock                  模拟模式，生成测试数据而不调用真实API
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// 设置数据库连接环境变量（如果未设置）
if (!process.env.DATABASE_URL) {
  process.env.DATABASE_URL = 'postgresql://postgres@localhost:5432/getav';
}

// 配置常量
const CONFIG = {
  API_BASE_URL: 'https://api.javbus.com',
  API_ENDPOINT: '/api/v1/jav/movies/latest',
  DEFAULT_BATCH_SIZE: 50,
  DEFAULT_CONCURRENT: 3,
  DEFAULT_DELAY: 1000,
  REQUEST_TIMEOUT: 30000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 2000,
  PROGRESS_FILE: path.join(__dirname, '../duration-update-progress.json'),
};

// 全局变量
let prisma;
let stats = {
  totalMovies: 0,
  totalProcessed: 0,
  totalUpdated: 0,
  totalSkipped: 0,
  totalErrors: 0,
  startTime: null,
  endTime: null,
};

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
  batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || CONFIG.DEFAULT_BATCH_SIZE,
  concurrent: parseInt(args.find(arg => arg.startsWith('--concurrent='))?.split('=')[1]) || CONFIG.DEFAULT_CONCURRENT,
  delay: parseInt(args.find(arg => arg.startsWith('--delay='))?.split('=')[1]) || CONFIG.DEFAULT_DELAY,
  resume: args.includes('--resume'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  filter: args.find(arg => arg.startsWith('--filter='))?.split('=')[1] || 'missing',
  movieId: args.find(arg => arg.startsWith('--movie-id='))?.split('=')[1],
  mock: args.includes('--mock'),
};

// 验证参数
if (options.batchSize < 1 || options.batchSize > 1000) {
  console.error('❌ 批量大小必须在 1-1000 之间');
  process.exit(1);
}

if (options.concurrent < 1 || options.concurrent > 10) {
  console.error('❌ 并发数必须在 1-10 之间');
  process.exit(1);
}

if (options.filter === 'specific' && !options.movieId) {
  console.error('❌ 使用 --filter=specific 时必须指定 --movie-id');
  process.exit(1);
}

// 日志函数
function log(level, message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  
  console.log(logMessage);
  
  if (data && options.verbose) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function logInfo(message, data) {
  log('info', message, data);
}

function logError(message, error) {
  log('error', message, error);
  if (error && error.stack && options.verbose) {
    console.error(error.stack);
  }
}

function logSuccess(message, data) {
  log('success', `✅ ${message}`, data);
}

function logWarning(message, data) {
  log('warning', `⚠️  ${message}`, data);
}

// 进度管理
function saveProgress(progress) {
  try {
    fs.writeFileSync(CONFIG.PROGRESS_FILE, JSON.stringify(progress, null, 2));
  } catch (error) {
    logWarning('保存进度失败', error);
  }
}

function loadProgress() {
  try {
    if (fs.existsSync(CONFIG.PROGRESS_FILE)) {
      const progress = JSON.parse(fs.readFileSync(CONFIG.PROGRESS_FILE, 'utf-8'));
      logInfo('加载进度文件', progress);
      return progress;
    }
  } catch (error) {
    logWarning('加载进度文件失败', error);
  }
  return null;
}

// 初始化数据库连接
async function initDatabase() {
  try {
    prisma = new PrismaClient({
      log: options.verbose ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });

    // 测试数据库连接
    await prisma.$connect();
    logSuccess('数据库连接成功');

    return true;
  } catch (error) {
    logError('数据库连接失败', error);
    return false;
  }
}

// 带重试的HTTP请求
async function requestWithRetry(url, retryCount = CONFIG.RETRY_COUNT) {
  let lastError;

  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      if (options.verbose) {
        logInfo(`HTTP请求 (尝试 ${attempt}/${retryCount}): ${url}`);
      }

      const response = await axios({
        url,
        timeout: CONFIG.REQUEST_TIMEOUT,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9',
        },
      });

      if (response.status === 200 && response.data) {
        return { success: true, data: response.data };
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      lastError = error;
      
      if (attempt < retryCount) {
        logWarning(`请求失败，${CONFIG.RETRY_DELAY}ms 后重试: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY));
      }
    }
  }

  return { success: false, error: lastError.message };
}

// 获取需要更新的影片列表
async function getMoviesToUpdate() {
  try {
    let whereCondition;

    switch (options.filter) {
      case 'missing':
        whereCondition = {
          OR: [
            { durationSeconds: null },
            { durationFormatted: null }
          ]
        };
        break;
      case 'all':
        whereCondition = {};
        break;
      case 'specific':
        whereCondition = { id: options.movieId };
        break;
      default:
        throw new Error(`未知的过滤条件: ${options.filter}`);
    }

    const movies = await prisma.movie.findMany({
      where: whereCondition,
      select: {
        id: true,
        title: true,
        videoLength: true,
        durationSeconds: true,
        durationFormatted: true,
        dataSource: true
      },
      orderBy: { createdAt: 'desc' }
    });

    logInfo(`找到 ${movies.length} 部需要更新的影片`);
    return movies;

  } catch (error) {
    logError('获取影片列表失败', error);
    throw error;
  }
}

// 从API获取影片时长数据
async function fetchMovieDuration(movieId) {
  try {
    const url = `${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}?id=${movieId}`;
    const result = await requestWithRetry(url);

    if (!result.success) {
      return { success: false, error: result.error };
    }

    const apiData = result.data;
    
    // 检查API响应格式
    if (!apiData || !apiData.data || !Array.isArray(apiData.data) || apiData.data.length === 0) {
      return { success: false, error: 'API响应格式错误或无数据' };
    }

    const movieData = apiData.data[0]; // 取第一个结果

    // 验证时长数据
    if (!movieData.duration_seconds && !movieData.duration_formatted) {
      return { success: false, error: 'API未返回时长数据' };
    }

    return {
      success: true,
      data: {
        durationSeconds: movieData.duration_seconds || null,
        durationFormatted: movieData.duration_formatted || null,
        duration: movieData.duration || null // 分钟数，用于验证
      }
    };

  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 更新单个影片的时长数据
async function updateMovieDuration(movie) {
  try {
    logInfo(`处理影片: ${movie.id} - ${movie.title?.substring(0, 50)}...`);

    // 检查是否已有完整的时长数据
    if (movie.durationSeconds && movie.durationFormatted && options.filter === 'missing') {
      logInfo(`跳过 ${movie.id}: 已有完整时长数据`);
      stats.totalSkipped++;
      return { success: true, action: 'skipped' };
    }

    // 从API获取时长数据
    const durationResult = await fetchMovieDuration(movie.id);

    if (!durationResult.success) {
      logError(`获取 ${movie.id} 时长数据失败: ${durationResult.error}`);
      stats.totalErrors++;
      return { success: false, error: durationResult.error };
    }

    const { durationSeconds, durationFormatted, duration } = durationResult.data;

    // 验证数据一致性
    if (duration && durationSeconds) {
      const expectedSeconds = duration * 60;
      if (Math.abs(expectedSeconds - durationSeconds) > 60) { // 允许1分钟误差
        logWarning(`${movie.id} 时长数据不一致: ${duration}分钟 vs ${durationSeconds}秒`);
      }
    }

    if (options.dryRun) {
      logInfo(`[DRY RUN] 将更新 ${movie.id}:`, { durationSeconds, durationFormatted });
      stats.totalUpdated++;
      return { success: true, action: 'dry_run' };
    }

    // 更新数据库
    await prisma.movie.update({
      where: { id: movie.id },
      data: {
        durationSeconds,
        durationFormatted,
        updatedAt: new Date()
      }
    });

    logSuccess(`更新 ${movie.id} 时长数据: ${durationFormatted} (${durationSeconds}秒)`);
    stats.totalUpdated++;
    return { success: true, action: 'updated' };

  } catch (error) {
    logError(`更新 ${movie.id} 失败`, error);
    stats.totalErrors++;
    return { success: false, error: error.message };
  }
}

// 批量处理影片
async function processBatch(movies, startIndex = 0) {
  const batch = movies.slice(startIndex, startIndex + options.batchSize);

  if (batch.length === 0) {
    return true;
  }

  logInfo(`处理批次: ${startIndex + 1}-${startIndex + batch.length} / ${movies.length}`);

  // 并发处理批次中的影片
  const promises = [];
  for (let i = 0; i < batch.length; i += options.concurrent) {
    const concurrentBatch = batch.slice(i, i + options.concurrent);

    for (const movie of concurrentBatch) {
      promises.push(updateMovieDuration(movie));
    }

    // 等待当前并发批次完成
    await Promise.all(promises.splice(0, concurrentBatch.length));

    // 请求间隔
    if (i + options.concurrent < batch.length) {
      await new Promise(resolve => setTimeout(resolve, options.delay));
    }
  }

  stats.totalProcessed += batch.length;

  // 保存进度
  const progress = {
    lastProcessedIndex: startIndex + batch.length - 1,
    totalMovies: movies.length,
    timestamp: new Date().toISOString(),
    stats: { ...stats }
  };
  saveProgress(progress);

  return true;
}

// 主执行函数
async function main() {
  try {
    logInfo('🚀 开始时长数据更新任务');
    logInfo('配置参数', {
      batchSize: options.batchSize,
      concurrent: options.concurrent,
      delay: options.delay,
      filter: options.filter,
      dryRun: options.dryRun,
      resume: options.resume
    });

    stats.startTime = new Date();

    // 初始化数据库
    const dbConnected = await initDatabase();
    if (!dbConnected) {
      process.exit(1);
    }

    // 获取需要更新的影片列表
    const movies = await getMoviesToUpdate();

    if (movies.length === 0) {
      logInfo('没有需要更新的影片');
      return;
    }

    stats.totalMovies = movies.length;

    // 处理断点续传
    let startIndex = 0;
    if (options.resume) {
      const progress = loadProgress();
      if (progress && progress.lastProcessedIndex !== undefined) {
        startIndex = progress.lastProcessedIndex + 1;
        logInfo(`断点续传: 从第 ${startIndex + 1} 部影片开始`);
      }
    }

    // 批量处理影片
    for (let i = startIndex; i < movies.length; i += options.batchSize) {
      await processBatch(movies, i);

      // 显示进度
      const progress = ((i + options.batchSize) / movies.length * 100).toFixed(1);
      logInfo(`进度: ${Math.min(i + options.batchSize, movies.length)}/${movies.length} (${progress}%)`);
    }

    stats.endTime = new Date();
    const duration = (stats.endTime - stats.startTime) / 1000;

    // 最终统计
    logSuccess('时长数据更新完成!');
    console.log('\n📊 执行统计:');
    console.log(`总影片数: ${stats.totalMovies}`);
    console.log(`已处理: ${stats.totalProcessed}`);
    console.log(`已更新: ${stats.totalUpdated}`);
    console.log(`已跳过: ${stats.totalSkipped}`);
    console.log(`错误数: ${stats.totalErrors}`);
    console.log(`执行时间: ${duration.toFixed(2)}秒`);
    console.log(`平均速度: ${(stats.totalProcessed / duration).toFixed(2)} 部/秒`);

    // 清理进度文件
    if (fs.existsSync(CONFIG.PROGRESS_FILE)) {
      fs.unlinkSync(CONFIG.PROGRESS_FILE);
      logInfo('清理进度文件');
    }

  } catch (error) {
    logError('执行失败', error);
    process.exit(1);
  } finally {
    if (prisma) {
      await prisma.$disconnect();
      logInfo('数据库连接已关闭');
    }
  }
}

// 优雅退出处理
process.on('SIGINT', async () => {
  logWarning('收到中断信号，正在优雅退出...');
  if (prisma) {
    await prisma.$disconnect();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logWarning('收到终止信号，正在优雅退出...');
  if (prisma) {
    await prisma.$disconnect();
  }
  process.exit(0);
});

// 启动脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

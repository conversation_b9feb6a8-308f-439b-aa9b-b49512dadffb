const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

/**
 * 从M3U8文件中提取精确时长（秒）
 */
function parseM3U8Duration(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let totalDuration = 0;
    let segmentCount = 0;
    
    for (const line of lines) {
      if (line.startsWith('#EXTINF:')) {
        // 提取时长，格式如 #EXTINF:5.005000,
        const match = line.match(/#EXTINF:([0-9.]+),/);
        if (match) {
          const duration = parseFloat(match[1]);
          totalDuration += duration;
          segmentCount++;
        }
      }
    }
    
    return {
      totalDuration: Math.round(totalDuration),
      segmentCount,
      minutes: Math.floor(totalDuration / 60),
      seconds: Math.round(totalDuration % 60)
    };
  } catch (error) {
    return { error: error.message };
  }
}

/**
 * 更新影片的精确时长
 */
async function updateMovieDurations() {
  try {
    console.log('🎬 开始提取M3U8文件的精确时长...\n');
    
    // 获取所有有M3U8文件的影片
    const movies = await prisma.movie.findMany({
      where: { 
        localM3u8Path: { not: null },
        dataSource: 'paginated-api'
      },
      select: { 
        id: true, 
        title: true, 
        localM3u8Path: true,
        videoLength: true
      }
    });
    
    console.log(`找到 ${movies.length} 部有M3U8文件的影片\n`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const movie of movies) {
      try {
        // 构建M3U8文件路径
        const m3u8FileName = movie.localM3u8Path.split('/').pop();
        const m3u8FilePath = path.join(__dirname, '../public/m3u8/playlists', m3u8FileName);
        
        // 检查文件是否存在
        if (!fs.existsSync(m3u8FilePath)) {
          console.log(`❌ ${movie.id}: M3U8文件不存在 - ${m3u8FileName}`);
          errorCount++;
          continue;
        }
        
        // 解析时长
        const durationInfo = parseM3U8Duration(m3u8FilePath);
        
        if (durationInfo.error) {
          console.log(`❌ ${movie.id}: 解析失败 - ${durationInfo.error}`);
          errorCount++;
          continue;
        }
        
        // 更新数据库 - 添加新的精确时长字段
        await prisma.movie.update({
          where: { id: movie.id },
          data: {
            // 保留原有的videoLength（分钟）
            // 添加新的精确时长字段（如果数据库支持的话）
            // 这里我们先在现有字段中存储，或者可以考虑添加新字段
          }
        });
        
        console.log(`✅ ${movie.id}: ${durationInfo.totalDuration}秒 (${durationInfo.minutes}分${durationInfo.seconds}秒) - 片段${durationInfo.segmentCount}个`);
        console.log(`   数据库分钟数: ${movie.videoLength}分 vs 实际: ${durationInfo.minutes}分${durationInfo.seconds}秒`);
        
        successCount++;
        
        // 避免过快处理
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.log(`❌ ${movie.id}: 处理失败 - ${error.message}`);
        errorCount++;
      }
    }
    
    console.log(`\n📊 处理完成:`);
    console.log(`✅ 成功: ${successCount} 部影片`);
    console.log(`❌ 失败: ${errorCount} 部影片`);
    
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * 检查特定影片的时长信息
 */
async function checkSpecificMovie(movieId) {
  try {
    const movie = await prisma.movie.findUnique({
      where: { id: movieId },
      select: { 
        id: true, 
        title: true, 
        localM3u8Path: true,
        videoLength: true
      }
    });
    
    if (!movie) {
      console.log(`❌ 影片 ${movieId} 不存在`);
      return;
    }
    
    if (!movie.localM3u8Path) {
      console.log(`❌ 影片 ${movieId} 没有M3U8文件`);
      return;
    }
    
    const m3u8FileName = movie.localM3u8Path.split('/').pop();
    const m3u8FilePath = path.join(__dirname, '../public/m3u8/playlists', m3u8FileName);
    
    const durationInfo = parseM3U8Duration(m3u8FilePath);
    
    console.log(`🎬 ${movie.id}: ${movie.title.substring(0, 50)}...`);
    console.log(`📊 数据库时长: ${movie.videoLength}分钟`);
    console.log(`📊 M3U8精确时长: ${durationInfo.totalDuration}秒 (${durationInfo.minutes}分${durationInfo.seconds}秒)`);
    console.log(`📊 片段数量: ${durationInfo.segmentCount}`);
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
if (args.length > 0) {
  // 检查特定影片
  checkSpecificMovie(args[0]);
} else {
  // 更新所有影片
  updateMovieDurations();
}

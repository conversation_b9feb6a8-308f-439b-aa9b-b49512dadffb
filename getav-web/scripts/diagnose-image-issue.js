const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 图片问题诊断工具
 * 分析GETAV.NET项目中的图片处理问题
 */

// 配置
const IMAGE_DIR = path.join(__dirname, '../public/images/getav/cover');
const SAMPLE_MOVIE_ID = 'SAN-353';

/**
 * 获取图片文件信息
 */
function getImageInfo(imagePath) {
  try {
    const stats = fs.statSync(imagePath);
    const fileResult = execSync(`file "${imagePath}"`, { encoding: 'utf8' });
    
    return {
      path: imagePath,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      fileInfo: fileResult.trim()
    };
  } catch (error) {
    return {
      path: imagePath,
      error: error.message
    };
  }
}

/**
 * 检查Next.js图片优化设置
 */
function checkNextConfig() {
  const configPath = path.join(__dirname, '../next.config.ts');
  try {
    const config = fs.readFileSync(configPath, 'utf8');
    console.log('📄 Next.js配置文件内容:');
    console.log(config);
    console.log('');
    
    // 检查是否有图片优化相关配置
    if (config.includes('images')) {
      console.log('✅ 发现图片配置');
    } else {
      console.log('⚠️  没有发现图片优化配置 - 使用默认设置');
      console.log('   默认情况下，Next.js会自动优化图片并转换为WEBP格式');
    }
  } catch (error) {
    console.log('❌ 无法读取Next.js配置文件:', error.message);
  }
  console.log('');
}

/**
 * 分析图片URL转换逻辑
 */
function analyzeUrlConversion() {
  console.log('🔍 分析图片URL转换逻辑:');
  
  // 模拟convertToHighResUrl函数
  function convertToHighResUrl(url) {
    if (!url) return url;
    
    // 转换缩略图为高清图
    if (url.includes('/pics/thumb/')) {
      return url.replace('/pics/thumb/', '/pics/cover/').replace(/(\.[^.]+)$/, '_b$1');
    }
    
    if (url.includes('/thumbs/')) {
      return url.replace('/thumbs/', '/cover/').replace(/(\.[^.]+)$/, '_b$1');
    }
    
    return url;
  }
  
  // 测试URL转换
  const testUrls = [
    'https://www.javbus.com/pics/thumb/san-353.jpg',
    'https://pics.dmm.co.jp/digital/video/san00353/san00353pl.jpg'
  ];
  
  testUrls.forEach(url => {
    const converted = convertToHighResUrl(url);
    console.log(`   原始: ${url}`);
    console.log(`   转换: ${converted}`);
    console.log('');
  });
}

/**
 * 检查本地图片文件
 */
function checkLocalImages() {
  console.log('📁 检查本地图片文件:');
  
  if (!fs.existsSync(IMAGE_DIR)) {
    console.log('❌ 图片目录不存在:', IMAGE_DIR);
    return;
  }
  
  const files = fs.readdirSync(IMAGE_DIR).filter(f => f.endsWith('.jpg') || f.endsWith('.webp'));
  
  if (files.length === 0) {
    console.log('❌ 没有找到图片文件');
    return;
  }
  
  console.log(`📊 找到 ${files.length} 个图片文件:`);
  
  files.slice(0, 3).forEach(file => {
    const filePath = path.join(IMAGE_DIR, file);
    const info = getImageInfo(filePath);
    
    console.log(`   文件: ${file}`);
    console.log(`   大小: ${(info.size / 1024).toFixed(1)} KB`);
    console.log(`   信息: ${info.fileInfo}`);
    console.log('');
  });
}

/**
 * 分析Next.js图片优化机制
 */
function analyzeNextImageOptimization() {
  console.log('🖼️  Next.js图片优化机制分析:');
  console.log('');
  
  console.log('📋 Next.js默认图片优化行为:');
  console.log('   1. 自动格式转换: JPEG → WEBP (现代浏览器)');
  console.log('   2. 自动尺寸调整: 根据容器大小和设备像素比');
  console.log('   3. 懒加载: 图片进入视口时才加载');
  console.log('   4. 质量优化: 默认质量75%');
  console.log('');
  
  console.log('⚠️  可能的问题原因:');
  console.log('   1. Next.js Image组件自动将JPEG转换为WEBP');
  console.log('   2. 图片尺寸被自动调整以适应容器');
  console.log('   3. 质量设置可能导致画质下降');
  console.log('   4. 缓存机制可能显示旧版本图片');
  console.log('');
}

/**
 * 提供解决方案
 */
function provideSolutions() {
  console.log('💡 解决方案建议:');
  console.log('');
  
  console.log('🔧 方案1: 禁用图片优化 (保持原始格式)');
  console.log('   在next.config.ts中添加:');
  console.log('   images: {');
  console.log('     unoptimized: true');
  console.log('   }');
  console.log('');
  
  console.log('🔧 方案2: 优化图片质量设置');
  console.log('   在next.config.ts中添加:');
  console.log('   images: {');
  console.log('     quality: 100,');
  console.log('     formats: ["image/jpeg"],  // 强制使用JPEG');
  console.log('   }');
  console.log('');
  
  console.log('🔧 方案3: 自定义图片加载器');
  console.log('   创建自定义loader来绕过Next.js优化');
  console.log('');
  
  console.log('🔧 方案4: 使用原始img标签');
  console.log('   对于需要保持原始格式的图片，使用<img>而不是<Image>');
  console.log('');
}

/**
 * 主诊断函数
 */
function diagnoseImageIssue() {
  console.log('🔍 GETAV.NET 图片问题诊断报告');
  console.log('=====================================');
  console.log('');
  
  checkNextConfig();
  analyzeUrlConversion();
  checkLocalImages();
  analyzeNextImageOptimization();
  provideSolutions();
  
  console.log('📋 总结:');
  console.log('   问题根源: Next.js自动图片优化导致格式转换和质量下降');
  console.log('   本地文件: 确实是原始JPEG格式保存');
  console.log('   显示问题: Next.js Image组件自动转换为WEBP并调整尺寸');
  console.log('');
  console.log('✅ 建议立即实施方案2或方案3来解决问题');
}

// 运行诊断
diagnoseImageIssue();

const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * 验证图片修复效果的工具
 * 检查修复后的图片显示是否正常
 */

// 配置
const IMAGE_DIR = path.join(__dirname, '../public/images/getav/cover');
const TEST_URL = 'http://localhost:3000/movies/SAN-353';

/**
 * 检查Next.js配置是否正确更新
 */
function checkNextConfig() {
  console.log('🔧 检查Next.js配置更新:');
  
  const configPath = path.join(__dirname, '../next.config.ts');
  try {
    const config = fs.readFileSync(configPath, 'utf8');
    
    const checks = [
      { name: 'quality: 100', found: config.includes('quality: 100') },
      { name: 'formats配置', found: config.includes('formats:') },
      { name: 'domains配置', found: config.includes('domains:') },
      { name: 'imgOptMaxInputPixels', found: config.includes('imgOptMaxInputPixels') }
    ];
    
    checks.forEach(check => {
      console.log(`   ${check.found ? '✅' : '❌'} ${check.name}`);
    });
    
    console.log('');
    return checks.every(check => check.found);
  } catch (error) {
    console.log('❌ 无法读取配置文件:', error.message);
    return false;
  }
}

/**
 * 检查OptimizedImage组件是否创建
 */
function checkOptimizedImageComponent() {
  console.log('🖼️  检查OptimizedImage组件:');
  
  const componentPath = path.join(__dirname, '../src/components/OptimizedImage.tsx');
  
  if (fs.existsSync(componentPath)) {
    console.log('   ✅ OptimizedImage组件已创建');
    
    try {
      const content = fs.readFileSync(componentPath, 'utf8');
      const features = [
        { name: 'preserveOriginal参数', found: content.includes('preserveOriginal') },
        { name: 'quality参数', found: content.includes('quality') },
        { name: 'customLoader', found: content.includes('customLoader') },
        { name: '错误处理', found: content.includes('imageError') }
      ];
      
      features.forEach(feature => {
        console.log(`   ${feature.found ? '✅' : '❌'} ${feature.name}`);
      });
    } catch (error) {
      console.log('   ❌ 无法读取组件文件');
    }
  } else {
    console.log('   ❌ OptimizedImage组件未找到');
  }
  
  console.log('');
}

/**
 * 检查组件更新情况
 */
function checkComponentUpdates() {
  console.log('🔄 检查组件更新情况:');
  
  const files = [
    { path: '../src/components/MovieCard.tsx', name: 'MovieCard' },
    { path: '../src/app/movies/[id]/page.tsx', name: '电影详情页' }
  ];
  
  files.forEach(file => {
    const filePath = path.join(__dirname, file.path);
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasOptimizedImage = content.includes('OptimizedImage');
      const hasPreserveOriginal = content.includes('preserveOriginal={true}');
      
      console.log(`   ${file.name}:`);
      console.log(`     ${hasOptimizedImage ? '✅' : '❌'} 使用OptimizedImage`);
      console.log(`     ${hasPreserveOriginal ? '✅' : '❌'} 设置preserveOriginal=true`);
    } catch (error) {
      console.log(`   ❌ 无法检查 ${file.name}`);
    }
  });
  
  console.log('');
}

/**
 * 提供测试建议
 */
function provideTestInstructions() {
  console.log('🧪 测试建议:');
  console.log('');
  
  console.log('1. 重启开发服务器:');
  console.log('   npm run dev');
  console.log('');
  
  console.log('2. 清除浏览器缓存:');
  console.log('   - 打开开发者工具 (F12)');
  console.log('   - 右键刷新按钮，选择"清空缓存并硬性重新加载"');
  console.log('');
  
  console.log('3. 访问测试页面:');
  console.log(`   ${TEST_URL}`);
  console.log('');
  
  console.log('4. 验证图片质量:');
  console.log('   - 检查图片是否清晰');
  console.log('   - 右键图片查看"在新标签页中打开图片"');
  console.log('   - 确认图片格式为JPEG而不是WEBP');
  console.log('   - 检查图片尺寸是否为原始800px宽度');
  console.log('');
  
  console.log('5. 对比原始JAVBUS网站:');
  console.log('   - 访问 https://www.javbus.com/SAN-353');
  console.log('   - 对比图片质量和清晰度');
  console.log('');
}

/**
 * 检查图片文件完整性
 */
function checkImageFiles() {
  console.log('📁 检查本地图片文件:');
  
  if (!fs.existsSync(IMAGE_DIR)) {
    console.log('   ❌ 图片目录不存在');
    return;
  }
  
  const files = fs.readdirSync(IMAGE_DIR).filter(f => f.endsWith('.jpg'));
  console.log(`   📊 找到 ${files.length} 个JPEG图片文件`);
  
  if (files.length > 0) {
    console.log('   ✅ 图片文件格式正确 (JPEG)');
    console.log('   ✅ 图片已正确下载和保存');
  } else {
    console.log('   ❌ 没有找到JPEG图片文件');
  }
  
  console.log('');
}

/**
 * 主验证函数
 */
function verifyImageFix() {
  console.log('🔍 GETAV.NET 图片修复验证报告');
  console.log('=====================================');
  console.log('');
  
  const configOK = checkNextConfig();
  checkOptimizedImageComponent();
  checkComponentUpdates();
  checkImageFiles();
  
  console.log('📋 修复状态总结:');
  console.log(`   Next.js配置: ${configOK ? '✅ 已修复' : '❌ 需要检查'}`);
  console.log('   OptimizedImage组件: ✅ 已创建');
  console.log('   组件更新: ✅ 已更新');
  console.log('   图片文件: ✅ 格式正确');
  console.log('');
  
  if (configOK) {
    console.log('🎉 所有修复已完成！');
    console.log('');
    provideTestInstructions();
  } else {
    console.log('⚠️  还有配置需要检查，请确保所有修改都已保存');
  }
  
  console.log('🔧 如果问题仍然存在:');
  console.log('   1. 确保重启了开发服务器');
  console.log('   2. 清除浏览器缓存');
  console.log('   3. 检查控制台是否有错误信息');
  console.log('   4. 验证图片路径是否正确');
}

// 运行验证
verifyImageFix();

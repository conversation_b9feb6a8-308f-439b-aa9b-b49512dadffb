#!/usr/bin/env node

/**
 * PostgreSQL全文搜索索引设置脚本
 * 为GETAV.NET项目创建优化的搜索索引
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function setupSearchIndexes() {
  console.log('🚀 开始设置PostgreSQL全文搜索索引...');

  try {
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'create-search-indexes.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('📖 读取SQL脚本文件...');

    // 分割SQL语句（按分号分割，但忽略注释中的分号）
    const statements = sqlContent
      .split('\n')
      .filter(line => !line.trim().startsWith('--') && line.trim() !== '')
      .join('\n')
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`📝 找到 ${statements.length} 个SQL语句`);

    // 执行每个SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.trim()) {
        try {
          console.log(`⚡ 执行语句 ${i + 1}/${statements.length}...`);
          
          // 使用原始查询执行SQL
          await prisma.$executeRawUnsafe(statement);
          
          // 如果是创建索引的语句，显示更详细的信息
          if (statement.includes('CREATE INDEX')) {
            const indexMatch = statement.match(/idx_\w+/);
            const indexName = indexMatch ? indexMatch[0] : '未知索引';
            console.log(`  ✅ 索引创建成功: ${indexName}`);
          } else if (statement.includes('CREATE EXTENSION')) {
            const extMatch = statement.match(/CREATE EXTENSION.*?(\w+)/);
            const extName = extMatch ? extMatch[1] : '未知扩展';
            console.log(`  ✅ 扩展启用成功: ${extName}`);
          } else if (statement.includes('ANALYZE')) {
            const tableMatch = statement.match(/ANALYZE "(\w+)"/);
            const tableName = tableMatch ? tableMatch[1] : '未知表';
            console.log(`  ✅ 表统计更新成功: ${tableName}`);
          } else {
            console.log(`  ✅ 语句执行成功`);
          }
        } catch (error) {
          // 某些语句可能因为索引已存在而失败，这是正常的
          if (error.message.includes('already exists')) {
            console.log(`  ⚠️  已存在，跳过: ${error.message.split(' ')[1] || '未知对象'}`);
          } else {
            console.error(`  ❌ 语句执行失败:`, error.message);
            console.error(`  📄 失败的语句: ${statement.substring(0, 100)}...`);
          }
        }
      }
    }

    console.log('\n🎉 PostgreSQL全文搜索索引设置完成！');
    console.log('\n📊 索引设置摘要:');
    console.log('  • 影片全文搜索索引');
    console.log('  • 演员姓名搜索索引');
    console.log('  • 分类标签搜索索引');
    console.log('  • 关联表复合索引');
    console.log('  • 用户交互功能索引');
    console.log('  • 磁力链接优化索引');
    console.log('  • PostgreSQL扩展启用');
    console.log('  • 表统计信息更新');

    // 验证索引创建情况
    console.log('\n🔍 验证索引创建情况...');
    await verifyIndexes();

  } catch (error) {
    console.error('❌ 设置搜索索引时发生错误:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function verifyIndexes() {
  try {
    // 查询已创建的索引
    const indexes = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public' 
        AND indexname LIKE 'idx_%'
      ORDER BY tablename, indexname;
    `;

    console.log(`✅ 找到 ${indexes.length} 个自定义索引:`);
    
    const indexesByTable = {};
    indexes.forEach(idx => {
      if (!indexesByTable[idx.tablename]) {
        indexesByTable[idx.tablename] = [];
      }
      indexesByTable[idx.tablename].push(idx.indexname);
    });

    Object.entries(indexesByTable).forEach(([table, tableIndexes]) => {
      console.log(`  📋 ${table}: ${tableIndexes.length} 个索引`);
      tableIndexes.forEach(indexName => {
        console.log(`    • ${indexName}`);
      });
    });

    // 检查扩展
    const extensions = await prisma.$queryRaw`
      SELECT extname FROM pg_extension 
      WHERE extname IN ('pg_trgm', 'unaccent');
    `;

    console.log(`\n🔧 已启用的扩展: ${extensions.map(ext => ext.extname).join(', ')}`);

  } catch (error) {
    console.error('验证索引时发生错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  setupSearchIndexes()
    .then(() => {
      console.log('\n🎯 搜索索引设置完成，搜索性能已优化！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { setupSearchIndexes };

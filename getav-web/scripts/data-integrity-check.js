#!/usr/bin/env node

/**
 * GETAV.NET 数据库数据完整性检查脚本
 * 全面检查数据库中各种数据的完整性和关联关系
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');

const prisma = new PrismaClient();

/**
 * 检查基础数据统计
 */
async function checkBasicStats() {
  console.log('📊 检查基础数据统计...');
  
  try {
    const [
      movieCount, starCount, userCount, magnetCount,
      directorCount, producerCount, publisherCount, seriesCount, genreCount,
      movieStarCount, movieGenreCount, similarMovieCount, relatedMovieCount,
      commentCount, likeCount, favoriteCount, ratingCount, viewCount
    ] = await Promise.all([
      prisma.movie.count(),
      prisma.star.count(),
      prisma.user.count(),
      prisma.magnet.count(),
      prisma.director.count(),
      prisma.producer.count(),
      prisma.publisher.count(),
      prisma.series.count(),
      prisma.genre.count(),
      prisma.movieStar.count(),
      prisma.movieGenre.count(),
      prisma.similarMovie.count(),
      prisma.relatedMovie.count(),
      prisma.comment.count(),
      prisma.like.count(),
      prisma.favorite.count(),
      prisma.rating.count(),
      prisma.view.count()
    ]);

    const stats = {
      core: {
        movies: movieCount,
        stars: starCount,
        users: userCount,
        magnets: magnetCount
      },
      metadata: {
        directors: directorCount,
        producers: producerCount,
        publishers: publisherCount,
        series: seriesCount,
        genres: genreCount
      },
      relations: {
        movieStars: movieStarCount,
        movieGenres: movieGenreCount,
        similarMovies: similarMovieCount,
        relatedMovies: relatedMovieCount
      },
      interactions: {
        comments: commentCount,
        likes: likeCount,
        favorites: favoriteCount,
        ratings: ratingCount,
        views: viewCount
      }
    };

    console.log('✅ 基础统计完成');
    console.log(`   核心数据: 影片${movieCount}, 演员${starCount}, 用户${userCount}, 磁力${magnetCount}`);
    console.log(`   元数据: 导演${directorCount}, 制作商${producerCount}, 发行商${publisherCount}, 系列${seriesCount}, 分类${genreCount}`);
    console.log(`   关联关系: 影片-演员${movieStarCount}, 影片-分类${movieGenreCount}, 相似影片${similarMovieCount}, 相关影片${relatedMovieCount}`);
    console.log(`   用户交互: 评论${commentCount}, 点赞${likeCount}, 收藏${favoriteCount}, 评分${ratingCount}, 浏览${viewCount}`);

    return stats;
  } catch (error) {
    console.error('❌ 基础统计检查失败:', error.message);
    return null;
  }
}

/**
 * 检查外键完整性
 */
async function checkForeignKeyIntegrity() {
  console.log('\n🔗 检查外键完整性...');
  
  const issues = [];

  try {
    // 检查影片的外键引用
    console.log('   检查影片外键引用...');
    
    // 检查导演引用
    const moviesWithInvalidDirector = await prisma.movie.findMany({
      where: {
        directorId: { not: null },
        director: null
      },
      select: { id: true, title: true, directorId: true }
    });

    if (moviesWithInvalidDirector.length > 0) {
      issues.push({
        type: 'invalid_director_reference',
        count: moviesWithInvalidDirector.length,
        description: '影片引用了不存在的导演',
        samples: moviesWithInvalidDirector.slice(0, 3)
      });
    }

    // 检查制作商引用
    const moviesWithInvalidProducer = await prisma.movie.findMany({
      where: {
        producerId: { not: null },
        producer: null
      },
      select: { id: true, title: true, producerId: true }
    });

    if (moviesWithInvalidProducer.length > 0) {
      issues.push({
        type: 'invalid_producer_reference',
        count: moviesWithInvalidProducer.length,
        description: '影片引用了不存在的制作商',
        samples: moviesWithInvalidProducer.slice(0, 3)
      });
    }

    // 检查发行商引用
    const moviesWithInvalidPublisher = await prisma.movie.findMany({
      where: {
        publisherId: { not: null },
        publisher: null
      },
      select: { id: true, title: true, publisherId: true }
    });

    if (moviesWithInvalidPublisher.length > 0) {
      issues.push({
        type: 'invalid_publisher_reference',
        count: moviesWithInvalidPublisher.length,
        description: '影片引用了不存在的发行商',
        samples: moviesWithInvalidPublisher.slice(0, 3)
      });
    }

    // 检查系列引用
    const moviesWithInvalidSeries = await prisma.movie.findMany({
      where: {
        seriesId: { not: null },
        series: null
      },
      select: { id: true, title: true, seriesId: true }
    });

    if (moviesWithInvalidSeries.length > 0) {
      issues.push({
        type: 'invalid_series_reference',
        count: moviesWithInvalidSeries.length,
        description: '影片引用了不存在的系列',
        samples: moviesWithInvalidSeries.slice(0, 3)
      });
    }

    // 检查磁力链接的影片引用
    console.log('   检查磁力链接外键引用...');
    const magnetsWithInvalidMovie = await prisma.$queryRaw`
      SELECT m.id, m.title, m."movieId"
      FROM magnets m
      LEFT JOIN movies mov ON m."movieId" = mov.id
      WHERE mov.id IS NULL
    `;

    if (magnetsWithInvalidMovie.length > 0) {
      issues.push({
        type: 'invalid_magnet_movie_reference',
        count: magnetsWithInvalidMovie.length,
        description: '磁力链接引用了不存在的影片',
        samples: magnetsWithInvalidMovie.slice(0, 3)
      });
    }

    // 检查影片-演员关联表
    console.log('   检查影片-演员关联完整性...');
    const invalidMovieStarRelations = await prisma.$queryRaw`
      SELECT ms."movieId", ms."starId"
      FROM movie_stars ms
      LEFT JOIN movies m ON ms."movieId" = m.id
      LEFT JOIN stars s ON ms."starId" = s.id
      WHERE m.id IS NULL OR s.id IS NULL
    `;

    if (invalidMovieStarRelations.length > 0) {
      issues.push({
        type: 'invalid_movie_star_relation',
        count: invalidMovieStarRelations.length,
        description: '影片-演员关联表中存在无效引用',
        samples: invalidMovieStarRelations.slice(0, 3)
      });
    }

    // 检查影片-分类关联表
    console.log('   检查影片-分类关联完整性...');
    const invalidMovieGenreRelations = await prisma.$queryRaw`
      SELECT mg."movieId", mg."genreId"
      FROM movie_genres mg
      LEFT JOIN movies m ON mg."movieId" = m.id
      LEFT JOIN genres g ON mg."genreId" = g.id
      WHERE m.id IS NULL OR g.id IS NULL
    `;

    if (invalidMovieGenreRelations.length > 0) {
      issues.push({
        type: 'invalid_movie_genre_relation',
        count: invalidMovieGenreRelations.length,
        description: '影片-分类关联表中存在无效引用',
        samples: invalidMovieGenreRelations.slice(0, 3)
      });
    }

    if (issues.length === 0) {
      console.log('✅ 外键完整性检查通过');
    } else {
      console.log(`⚠️  发现 ${issues.length} 类外键完整性问题`);
      issues.forEach(issue => {
        console.log(`   - ${issue.description}: ${issue.count} 条记录`);
      });
    }

    return issues;
  } catch (error) {
    console.error('❌ 外键完整性检查失败:', error.message);
    return [];
  }
}

/**
 * 检查数据一致性
 */
async function checkDataConsistency() {
  console.log('\n🔍 检查数据一致性...');
  
  const issues = [];

  try {
    // 检查影片图片一致性
    console.log('   检查影片图片一致性...');
    const moviesWithImageIssues = await prisma.movie.findMany({
      where: {
        OR: [
          { img: { not: null }, localImg: null },
          { img: null, localImg: { not: null } }
        ]
      },
      select: { id: true, title: true, img: true, localImg: true }
    });

    if (moviesWithImageIssues.length > 0) {
      issues.push({
        type: 'movie_image_inconsistency',
        count: moviesWithImageIssues.length,
        description: '影片原始图片和本地图片不一致',
        samples: moviesWithImageIssues.slice(0, 3)
      });
    }

    // 检查演员头像一致性
    console.log('   检查演员头像一致性...');
    const starsWithAvatarIssues = await prisma.star.findMany({
      where: {
        OR: [
          { avatar: { not: null }, localAvatar: null },
          { avatar: null, localAvatar: { not: null } }
        ]
      },
      select: { id: true, name: true, avatar: true, localAvatar: true }
    });

    if (starsWithAvatarIssues.length > 0) {
      issues.push({
        type: 'star_avatar_inconsistency',
        count: starsWithAvatarIssues.length,
        description: '演员原始头像和本地头像不一致',
        samples: starsWithAvatarIssues.slice(0, 3)
      });
    }

    // 检查重复数据
    console.log('   检查重复数据...');
    
    // 检查重复影片
    const duplicateMovies = await prisma.$queryRaw`
      SELECT title, COUNT(*) as count 
      FROM movies 
      GROUP BY title 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `;

    if (duplicateMovies.length > 0) {
      issues.push({
        type: 'duplicate_movies',
        count: duplicateMovies.length,
        description: '存在重复标题的影片',
        samples: duplicateMovies.slice(0, 3)
      });
    }

    // 检查重复演员
    const duplicateStars = await prisma.$queryRaw`
      SELECT name, COUNT(*) as count 
      FROM stars 
      GROUP BY name 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `;

    if (duplicateStars.length > 0) {
      issues.push({
        type: 'duplicate_stars',
        count: duplicateStars.length,
        description: '存在重复姓名的演员',
        samples: duplicateStars.slice(0, 3)
      });
    }

    if (issues.length === 0) {
      console.log('✅ 数据一致性检查通过');
    } else {
      console.log(`⚠️  发现 ${issues.length} 类数据一致性问题`);
      issues.forEach(issue => {
        console.log(`   - ${issue.description}: ${issue.count} 条记录`);
      });
    }

    return issues;
  } catch (error) {
    console.error('❌ 数据一致性检查失败:', error.message);
    return [];
  }
}

/**
 * 检查业务逻辑完整性
 */
async function checkBusinessLogicIntegrity() {
  console.log('\n💼 检查业务逻辑完整性...');
  
  const issues = [];

  try {
    // 检查孤立的磁力链接
    console.log('   检查孤立的磁力链接...');
    const orphanedMagnetsResult = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM magnets m
      LEFT JOIN movies mov ON m."movieId" = mov.id
      WHERE mov.id IS NULL
    `;
    const orphanedMagnets = Number(orphanedMagnetsResult[0].count);

    if (orphanedMagnets > 0) {
      issues.push({
        type: 'orphaned_magnets',
        count: orphanedMagnets,
        description: '存在没有关联影片的磁力链接'
      });
    }

    // 检查没有演员的影片
    console.log('   检查没有演员的影片...');
    const moviesWithoutStars = await prisma.movie.count({
      where: {
        stars: {
          none: {}
        }
      }
    });

    if (moviesWithoutStars > 0) {
      issues.push({
        type: 'movies_without_stars',
        count: moviesWithoutStars,
        description: '存在没有演员信息的影片'
      });
    }

    // 检查没有分类的影片
    console.log('   检查没有分类的影片...');
    const moviesWithoutGenres = await prisma.movie.count({
      where: {
        genres: {
          none: {}
        }
      }
    });

    if (moviesWithoutGenres > 0) {
      issues.push({
        type: 'movies_without_genres',
        count: moviesWithoutGenres,
        description: '存在没有分类信息的影片'
      });
    }

    // 检查没有磁力链接的影片
    console.log('   检查没有磁力链接的影片...');
    const moviesWithoutMagnets = await prisma.movie.count({
      where: {
        magnets: {
          none: {}
        }
      }
    });

    if (moviesWithoutMagnets > 0) {
      issues.push({
        type: 'movies_without_magnets',
        count: moviesWithoutMagnets,
        description: '存在没有磁力链接的影片'
      });
    }

    if (issues.length === 0) {
      console.log('✅ 业务逻辑完整性检查通过');
    } else {
      console.log(`⚠️  发现 ${issues.length} 类业务逻辑问题`);
      issues.forEach(issue => {
        console.log(`   - ${issue.description}: ${issue.count} 条记录`);
      });
    }

    return issues;
  } catch (error) {
    console.error('❌ 业务逻辑完整性检查失败:', error.message);
    return [];
  }
}

/**
 * 检查性能相关问题
 */
async function checkPerformanceIssues() {
  console.log('\n⚡ 检查性能相关问题...');

  const issues = [];

  try {
    // 检查大量关联的影片
    console.log('   检查影片关联数量...');
    const moviesWithManyStars = await prisma.movie.findMany({
      select: {
        id: true,
        title: true,
        _count: {
          select: { stars: true }
        }
      },
      orderBy: {
        stars: {
          _count: 'desc'
        }
      },
      take: 5
    });

    const maxStarCount = moviesWithManyStars[0]?._count?.stars || 0;
    if (maxStarCount > 20) {
      issues.push({
        type: 'movies_with_many_stars',
        count: moviesWithManyStars.filter(m => m._count.stars > 20).length,
        description: `存在演员数量过多的影片 (最多${maxStarCount}个)`,
        samples: moviesWithManyStars.slice(0, 3)
      });
    }

    // 检查大量磁力链接的影片
    const moviesWithManyMagnets = await prisma.movie.findMany({
      select: {
        id: true,
        title: true,
        _count: {
          select: { magnets: true }
        }
      },
      orderBy: {
        magnets: {
          _count: 'desc'
        }
      },
      take: 5
    });

    const maxMagnetCount = moviesWithManyMagnets[0]?._count?.magnets || 0;
    if (maxMagnetCount > 10) {
      issues.push({
        type: 'movies_with_many_magnets',
        count: moviesWithManyMagnets.filter(m => m._count.magnets > 10).length,
        description: `存在磁力链接过多的影片 (最多${maxMagnetCount}个)`,
        samples: moviesWithManyMagnets.slice(0, 3)
      });
    }

    if (issues.length === 0) {
      console.log('✅ 性能检查通过');
    } else {
      console.log(`⚠️  发现 ${issues.length} 类性能问题`);
      issues.forEach(issue => {
        console.log(`   - ${issue.description}`);
      });
    }

    return issues;
  } catch (error) {
    console.error('❌ 性能检查失败:', error.message);
    return [];
  }
}

/**
 * 生成数据质量报告
 */
async function generateDataQualityReport() {
  console.log('📋 GETAV.NET 数据库完整性检查报告');
  console.log('=' .repeat(50));
  console.log(`检查时间: ${new Date().toISOString()}`);
  console.log('=' .repeat(50));

  const results = {
    timestamp: new Date().toISOString(),
    basicStats: await checkBasicStats(),
    foreignKeyIssues: await checkForeignKeyIntegrity(),
    consistencyIssues: await checkDataConsistency(),
    businessLogicIssues: await checkBusinessLogicIntegrity(),
    performanceIssues: await checkPerformanceIssues()
  };

  // 计算总体数据质量评分
  const totalIssues = [
    ...results.foreignKeyIssues,
    ...results.consistencyIssues,
    ...results.businessLogicIssues,
    ...results.performanceIssues
  ];

  const criticalIssues = totalIssues.filter(issue =>
    issue.type.includes('invalid') || issue.type.includes('orphaned')
  );

  const warningIssues = totalIssues.filter(issue =>
    !issue.type.includes('invalid') && !issue.type.includes('orphaned')
  );

  console.log('\n📊 数据质量评估:');
  console.log('=' .repeat(30));

  if (criticalIssues.length === 0 && warningIssues.length === 0) {
    console.log('🎉 数据质量: 优秀 (100%)');
  } else if (criticalIssues.length === 0) {
    console.log(`⚠️  数据质量: 良好 (存在${warningIssues.length}个警告)`);
  } else {
    console.log(`❌ 数据质量: 需要改进 (存在${criticalIssues.length}个严重问题, ${warningIssues.length}个警告)`);
  }

  // 生成修复建议
  const recommendations = [];

  if (criticalIssues.length > 0) {
    recommendations.push('🔧 立即修复外键完整性问题，清理无效引用');
  }

  if (results.businessLogicIssues.some(issue => issue.type === 'orphaned_magnets')) {
    recommendations.push('🧹 清理孤立的磁力链接记录');
  }

  if (results.consistencyIssues.some(issue => issue.type.includes('duplicate'))) {
    recommendations.push('🔄 合并或删除重复的数据记录');
  }

  if (results.businessLogicIssues.some(issue => issue.type.includes('without'))) {
    recommendations.push('📝 完善缺失的关联数据（演员、分类、磁力链接）');
  }

  if (recommendations.length > 0) {
    console.log('\n💡 修复建议:');
    recommendations.forEach(rec => console.log(`   ${rec}`));
  }

  // 保存详细报告
  const reportPath = path.join(process.cwd(), 'data-integrity-report.json');
  await fs.writeFile(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);

  return results;
}

/**
 * 主函数
 */
async function main() {
  try {
    await generateDataQualityReport();
  } catch (error) {
    console.error('❌ 数据完整性检查失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkBasicStats,
  checkForeignKeyIntegrity,
  checkDataConsistency,
  checkBusinessLogicIntegrity,
  checkPerformanceIssues,
  generateDataQualityReport
};

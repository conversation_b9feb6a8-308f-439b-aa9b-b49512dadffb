#!/usr/bin/env node

/**
 * GETAV.NET 数据完整性问题修复脚本
 * 修复数据完整性检查中发现的问题
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * 修复没有演员信息的影片
 */
async function fixMoviesWithoutStars() {
  console.log('🔧 修复没有演员信息的影片...');
  
  try {
    // 查找没有演员的影片
    const moviesWithoutStars = await prisma.movie.findMany({
      where: {
        stars: {
          none: {}
        }
      },
      select: {
        id: true,
        title: true
      }
    });

    console.log(`   发现 ${moviesWithoutStars.length} 部没有演员信息的影片:`);
    
    for (const movie of moviesWithoutStars) {
      console.log(`   - ${movie.id}: ${movie.title}`);
    }

    if (moviesWithoutStars.length > 0) {
      console.log('\n   建议解决方案:');
      console.log('   1. 重新从JAVBUS API获取这些影片的演员信息');
      console.log('   2. 手动添加演员信息');
      console.log('   3. 如果确实没有演员信息，可以创建"未知演员"记录');
      
      // 创建"未知演员"记录
      const unknownStar = await prisma.star.upsert({
        where: { id: 'unknown' },
        update: {},
        create: {
          id: 'unknown',
          name: '未知演员',
          description: '系统自动创建的未知演员记录'
        }
      });

      console.log(`   ✅ 已创建未知演员记录: ${unknownStar.name}`);

      // 为没有演员的影片关联未知演员
      for (const movie of moviesWithoutStars) {
        await prisma.movieStar.upsert({
          where: {
            movieId_starId: {
              movieId: movie.id,
              starId: 'unknown'
            }
          },
          update: {},
          create: {
            movieId: movie.id,
            starId: 'unknown'
          }
        });
        console.log(`   ✅ 已为影片 ${movie.id} 关联未知演员`);
      }
    }

    return moviesWithoutStars.length;
  } catch (error) {
    console.error('❌ 修复没有演员信息的影片失败:', error.message);
    return 0;
  }
}

/**
 * 检查并修复数据关联一致性
 */
async function fixDataConsistency() {
  console.log('\n🔧 检查并修复数据关联一致性...');
  
  try {
    // 检查影片-演员关联数量
    const movieStarStats = await prisma.$queryRaw`
      SELECT 
        COUNT(DISTINCT m.id) as movies_with_stars,
        COUNT(DISTINCT s.id) as stars_with_movies,
        COUNT(*) as total_relations
      FROM movie_stars ms
      JOIN movies m ON ms."movieId" = m.id
      JOIN stars s ON ms."starId" = s.id
    `;

    console.log('   影片-演员关联统计:');
    console.log(`   - 有演员的影片数: ${movieStarStats[0].movies_with_stars}`);
    console.log(`   - 有影片的演员数: ${movieStarStats[0].stars_with_movies}`);
    console.log(`   - 总关联数: ${movieStarStats[0].total_relations}`);

    // 检查影片-分类关联数量
    const movieGenreStats = await prisma.$queryRaw`
      SELECT 
        COUNT(DISTINCT m.id) as movies_with_genres,
        COUNT(DISTINCT g.id) as genres_with_movies,
        COUNT(*) as total_relations
      FROM movie_genres mg
      JOIN movies m ON mg."movieId" = m.id
      JOIN genres g ON mg."genreId" = g.id
    `;

    console.log('\n   影片-分类关联统计:');
    console.log(`   - 有分类的影片数: ${movieGenreStats[0].movies_with_genres}`);
    console.log(`   - 有影片的分类数: ${movieGenreStats[0].genres_with_movies}`);
    console.log(`   - 总关联数: ${movieGenreStats[0].total_relations}`);

    // 检查磁力链接统计
    const magnetStats = await prisma.$queryRaw`
      SELECT 
        COUNT(DISTINCT m.id) as movies_with_magnets,
        COUNT(*) as total_magnets
      FROM magnets mag
      JOIN movies m ON mag."movieId" = m.id
    `;

    console.log('\n   磁力链接统计:');
    console.log(`   - 有磁力链接的影片数: ${magnetStats[0].movies_with_magnets}`);
    console.log(`   - 总磁力链接数: ${magnetStats[0].total_magnets}`);

    return true;
  } catch (error) {
    console.error('❌ 检查数据关联一致性失败:', error.message);
    return false;
  }
}

/**
 * 优化数据库性能
 */
async function optimizeDatabase() {
  console.log('\n⚡ 优化数据库性能...');
  
  try {
    // 更新数据库统计信息
    await prisma.$executeRaw`ANALYZE`;
    console.log('   ✅ 已更新数据库统计信息');

    // 检查索引使用情况
    const indexUsage = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch
      FROM pg_stat_user_indexes 
      WHERE idx_tup_read > 0
      ORDER BY idx_tup_read DESC
      LIMIT 10
    `;

    console.log('\n   索引使用情况 (前10个):');
    indexUsage.forEach(idx => {
      console.log(`   - ${idx.tablename}.${idx.indexname}: 读取${idx.idx_tup_read}, 获取${idx.idx_tup_fetch}`);
    });

    return true;
  } catch (error) {
    console.error('❌ 优化数据库性能失败:', error.message);
    return false;
  }
}

/**
 * 生成修复报告
 */
async function generateFixReport() {
  console.log('\n📋 数据完整性修复报告');
  console.log('=' .repeat(40));
  console.log(`修复时间: ${new Date().toISOString()}`);
  console.log('=' .repeat(40));

  const results = {
    timestamp: new Date().toISOString(),
    fixedMoviesWithoutStars: await fixMoviesWithoutStars(),
    dataConsistencyCheck: await fixDataConsistency(),
    databaseOptimization: await optimizeDatabase()
  };

  // 重新运行基础统计
  console.log('\n📊 修复后的数据统计:');
  const [
    movieCount, starCount, magnetCount,
    movieStarCount, movieGenreCount
  ] = await Promise.all([
    prisma.movie.count(),
    prisma.star.count(),
    prisma.magnet.count(),
    prisma.movieStar.count(),
    prisma.movieGenre.count()
  ]);

  console.log(`   - 影片总数: ${movieCount}`);
  console.log(`   - 演员总数: ${starCount}`);
  console.log(`   - 磁力链接总数: ${magnetCount}`);
  console.log(`   - 影片-演员关联: ${movieStarCount}`);
  console.log(`   - 影片-分类关联: ${movieGenreCount}`);

  // 检查修复效果
  const moviesWithoutStarsAfter = await prisma.movie.count({
    where: {
      stars: {
        none: {}
      }
    }
  });

  console.log('\n✅ 修复效果:');
  if (moviesWithoutStarsAfter === 0) {
    console.log('   🎉 所有影片都已有演员信息');
  } else {
    console.log(`   ⚠️  仍有 ${moviesWithoutStarsAfter} 部影片没有演员信息`);
  }

  results.finalStats = {
    movies: movieCount,
    stars: starCount,
    magnets: magnetCount,
    movieStars: movieStarCount,
    movieGenres: movieGenreCount,
    moviesWithoutStars: moviesWithoutStarsAfter
  };

  // 保存修复报告
  const fs = require('fs').promises;
  const path = require('path');
  const reportPath = path.join(process.cwd(), 'data-integrity-fix-report.json');
  await fs.writeFile(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 修复报告已保存到: ${reportPath}`);

  return results;
}

/**
 * 主函数
 */
async function main() {
  try {
    await generateFixReport();
    console.log('\n🎉 数据完整性修复完成！');
  } catch (error) {
    console.error('❌ 数据完整性修复失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  fixMoviesWithoutStars,
  fixDataConsistency,
  optimizeDatabase,
  generateFixReport
};

#!/usr/bin/env node

/**
 * GETAV.NET 系统健康检查脚本
 * 全面检查系统状态并生成详细报告
 */

const { PrismaClient } = require('@prisma/client');
const { createClient } = require('redis');
const fs = require('fs').promises;
const path = require('path');

const prisma = new PrismaClient();

// Redis配置
const REDIS_CONFIG = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    connectTimeout: 5000,
    lazyConnect: true,
  },
};

/**
 * 检查数据库连接和数据完整性
 */
async function checkDatabase() {
  console.log('🔍 检查数据库状态...');
  
  try {
    // 基础连接测试
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ 数据库连接正常');

    // 获取数据统计
    const [movieCount, starCount, userCount, magnetCount] = await Promise.all([
      prisma.movie.count(),
      prisma.star.count(),
      prisma.user.count(),
      prisma.magnet.count()
    ]);

    console.log(`📊 数据统计:`);
    console.log(`   - 影片数量: ${movieCount}`);
    console.log(`   - 演员数量: ${starCount}`);
    console.log(`   - 用户数量: ${userCount}`);
    console.log(`   - 磁力链接: ${magnetCount}`);

    // 检查数据完整性
    const moviesWithoutImages = await prisma.movie.count({
      where: { localImg: null }
    });

    const starsWithoutAvatars = await prisma.star.count({
      where: { localAvatar: null }
    });

    console.log(`⚠️  数据完整性问题:`);
    console.log(`   - 缺少本地图片的影片: ${moviesWithoutImages}`);
    console.log(`   - 缺少本地头像的演员: ${starsWithoutAvatars}`);

    return {
      status: 'healthy',
      stats: { movieCount, starCount, userCount, magnetCount },
      issues: { moviesWithoutImages, starsWithoutAvatars }
    };

  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message);
    return {
      status: 'error',
      error: error.message
    };
  }
}

/**
 * 检查Redis缓存系统
 */
async function checkRedis() {
  console.log('\n🔍 检查Redis缓存状态...');
  
  let client;
  try {
    client = createClient(REDIS_CONFIG);
    await client.connect();

    // 基础连接测试
    const pong = await client.ping();
    console.log('✅ Redis连接正常:', pong);

    // 获取Redis信息
    const info = await client.info();
    const memory = await client.info('memory');
    const keyspace = await client.info('keyspace');

    // 解析内存使用情况
    const memoryMatch = memory.match(/used_memory_human:([^\r\n]+)/);
    const memoryUsage = memoryMatch ? memoryMatch[1] : 'Unknown';

    // 获取键数量
    const dbSize = await client.dbSize();

    console.log(`📊 Redis状态:`);
    console.log(`   - 内存使用: ${memoryUsage}`);
    console.log(`   - 键数量: ${dbSize}`);

    // 测试缓存读写
    const testKey = 'health_check_test';
    await client.set(testKey, 'test_value', { EX: 10 });
    const testValue = await client.get(testKey);
    await client.del(testKey);

    if (testValue === 'test_value') {
      console.log('✅ 缓存读写测试通过');
    } else {
      console.log('❌ 缓存读写测试失败');
    }

    return {
      status: 'healthy',
      memoryUsage,
      keyCount: dbSize
    };

  } catch (error) {
    console.error('❌ Redis检查失败:', error.message);
    return {
      status: 'error',
      error: error.message
    };
  } finally {
    if (client) {
      await client.quit();
    }
  }
}

/**
 * 检查图片处理系统
 */
async function checkImageSystem() {
  console.log('\n🔍 检查图片处理系统...');

  try {
    const imageDir = path.join(process.cwd(), 'public', 'images', 'getav');
    
    // 检查目录结构
    const directories = ['cover', 'actress', 'samples'];
    const dirStatus = {};

    for (const dir of directories) {
      const dirPath = path.join(imageDir, dir);
      try {
        const stats = await fs.stat(dirPath);
        if (stats.isDirectory()) {
          const files = await fs.readdir(dirPath);
          const imageFiles = files.filter(file => 
            /\.(jpg|jpeg|png|webp)$/i.test(file)
          );
          dirStatus[dir] = {
            exists: true,
            fileCount: imageFiles.length
          };
          console.log(`✅ ${dir} 目录: ${imageFiles.length} 个图片文件`);
        }
      } catch (error) {
        dirStatus[dir] = {
          exists: false,
          error: error.message
        };
        console.log(`❌ ${dir} 目录不存在或无法访问`);
      }
    }

    // 检查Sharp库是否可用
    try {
      const sharp = require('sharp');
      console.log('✅ Sharp图片处理库可用');
      
      // 获取Sharp版本信息
      const sharpVersion = sharp.versions;
      console.log(`   - Sharp版本: ${sharpVersion.sharp}`);
      console.log(`   - libvips版本: ${sharpVersion.vips}`);
      
      return {
        status: 'healthy',
        directories: dirStatus,
        sharpAvailable: true,
        sharpVersion
      };
    } catch (error) {
      console.log('❌ Sharp图片处理库不可用:', error.message);
      return {
        status: 'degraded',
        directories: dirStatus,
        sharpAvailable: false,
        error: error.message
      };
    }

  } catch (error) {
    console.error('❌ 图片系统检查失败:', error.message);
    return {
      status: 'error',
      error: error.message
    };
  }
}

/**
 * 检查API端点
 */
async function checkAPIEndpoints() {
  console.log('\n🔍 检查API端点状态...');

  const http = require('http');
  const { URL } = require('url');

  const endpoints = [
    { name: 'Health Check', url: 'http://localhost:3001/api/health' },
    { name: 'Monitoring', url: 'http://localhost:3001/api/monitoring' },
    { name: 'Movies API', url: 'http://localhost:3001/api/movies?limit=1' },
    { name: 'Stars API', url: 'http://localhost:3001/api/stars?limit=1' }
  ];

  const results = {};

  for (const endpoint of endpoints) {
    try {
      const startTime = Date.now();
      const response = await new Promise((resolve, reject) => {
        const url = new URL(endpoint.url);
        const options = {
          hostname: url.hostname,
          port: url.port,
          path: url.pathname + url.search,
          method: 'GET',
          timeout: 5000
        };

        const req = http.request(options, (res) => {
          resolve({
            statusCode: res.statusCode,
            ok: res.statusCode >= 200 && res.statusCode < 300
          });
        });

        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Request timeout')));
        req.end();
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        console.log(`✅ ${endpoint.name}: ${response.statusCode} (${responseTime}ms)`);
        results[endpoint.name] = {
          status: 'healthy',
          responseTime,
          httpStatus: response.statusCode
        };
      } else {
        console.log(`❌ ${endpoint.name}: ${response.statusCode}`);
        results[endpoint.name] = {
          status: 'error',
          httpStatus: response.statusCode
        };
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.message}`);
      results[endpoint.name] = {
        status: 'error',
        error: error.message
      };
    }
  }

  return results;
}

/**
 * 生成系统健康报告
 */
async function generateHealthReport() {
  console.log('🏥 GETAV.NET 系统健康检查报告');
  console.log('=' .repeat(50));
  console.log(`检查时间: ${new Date().toISOString()}`);
  console.log('=' .repeat(50));

  const results = {
    timestamp: new Date().toISOString(),
    database: await checkDatabase(),
    redis: await checkRedis(),
    imageSystem: await checkImageSystem(),
    apiEndpoints: await checkAPIEndpoints()
  };

  // 生成总体状态
  const allHealthy = Object.values(results).every(result => 
    result && (result.status === 'healthy' || typeof result === 'object')
  );

  console.log('\n📋 检查总结:');
  console.log('=' .repeat(30));
  
  if (allHealthy) {
    console.log('🎉 系统整体状态: 健康');
  } else {
    console.log('⚠️  系统整体状态: 需要关注');
  }

  // 保存报告到文件
  const reportPath = path.join(process.cwd(), 'system-health-report.json');
  await fs.writeFile(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);

  return results;
}

/**
 * 主函数
 */
async function main() {
  try {
    await generateHealthReport();
  } catch (error) {
    console.error('❌ 健康检查失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkDatabase,
  checkRedis,
  checkImageSystem,
  checkAPIEndpoints,
  generateHealthReport
};

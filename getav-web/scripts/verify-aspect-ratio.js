const fs = require('fs');
const path = require('path');

/**
 * 验证图片比例修复效果
 * 检查组件是否使用了正确的图片比例
 */

console.log('🔍 验证图片比例修复效果...\n');

// 检查的文件列表
const filesToCheck = [
  {
    path: '../src/components/RecommendedContent.tsx',
    name: '推荐内容轮播图',
    expectedRatio: 'aspect-[800/538]'
  },
  {
    path: '../src/components/MovieCard.tsx', 
    name: '影片卡片',
    expectedRatio: 'aspect-[800/538]'
  },
  {
    path: '../src/app/movies/[id]/page.tsx',
    name: '影片详情页',
    expectedRatio: 'aspect-[800/538]'
  }
];

let allCorrect = true;

filesToCheck.forEach(file => {
  const filePath = path.join(__dirname, file.path);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    console.log(`📄 检查 ${file.name}:`);
    
    // 检查是否包含正确的比例
    const hasCorrectRatio = content.includes(file.expectedRatio);
    
    // 检查是否还有旧的错误比例
    const hasOldRatio16_9 = content.includes('aspect-[16/9]');
    const hasOldRatio3_4 = content.includes('aspect-[3/4]');
    
    if (hasCorrectRatio) {
      console.log(`   ✅ 使用正确比例: ${file.expectedRatio}`);
    } else {
      console.log(`   ❌ 未找到正确比例: ${file.expectedRatio}`);
      allCorrect = false;
    }
    
    if (hasOldRatio16_9) {
      console.log(`   ⚠️  仍包含旧比例: aspect-[16/9]`);
      allCorrect = false;
    }
    
    if (hasOldRatio3_4) {
      console.log(`   ⚠️  仍包含旧比例: aspect-[3/4]`);
      allCorrect = false;
    }
    
    if (hasCorrectRatio && !hasOldRatio16_9 && !hasOldRatio3_4) {
      console.log(`   ✅ 比例设置正确`);
    }
    
  } catch (error) {
    console.log(`   ❌ 无法读取文件: ${error.message}`);
    allCorrect = false;
  }
  
  console.log('');
});

// 显示图片尺寸信息
console.log('📐 图片尺寸信息:');
console.log('   实际图片尺寸: 800x538 像素');
console.log('   宽高比: 1.487 (约 1.49:1)');
console.log('   比例设置: aspect-[800/538]');
console.log('');

// 总结
if (allCorrect) {
  console.log('🎉 所有组件都已正确设置图片比例！');
  console.log('');
  console.log('✅ 修复效果:');
  console.log('   - 轮播图现在使用正确的 800:538 比例');
  console.log('   - 影片卡片使用正确的比例，不再拉伸变形');
  console.log('   - 详情页封面显示比例正确');
  console.log('   - 图片显示更加自然，符合原始比例');
} else {
  console.log('❌ 发现问题，需要进一步修复');
}

console.log('\n🔧 技术说明:');
console.log('   - JAVBUS图片标准尺寸: 800x538px');
console.log('   - Tailwind CSS aspect-[800/538] 确保正确比例');
console.log('   - object-cover 保持图片不变形');
console.log('   - 所有组件统一使用相同比例设置');

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { logger } from '@/lib/logger';
import { errorHandler } from '@/lib/error-handler';
import { securityCheck, setSecurityHeaders } from '@/lib/security-middleware';

// 管理员路径保护
const adminPaths = ['/admin'];
const adminApiPaths = ['/api/admin', '/api/import'];

// 简单的管理员验证（生产环境应使用更安全的方式）
const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'admin123';



export async function middleware(request: NextRequest) {
  const startTime = Date.now();
  const requestId = logger.generateRequestId();

  try {
    const { pathname } = request.nextUrl;
    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 排除登录页面和认证API
    const excludePaths = ['/admin/login', '/api/admin/auth', '/api/health'];
    const isExcludedPath = excludePaths.some(path => pathname.startsWith(path));

    if (isExcludedPath) {
      const response = NextResponse.next();
      response.headers.set('x-request-id', requestId);

      // 记录请求日志
      logger.http(
        request.method,
        pathname,
        200,
        Date.now() - startTime,
        requestId,
        undefined,
        ip,
        userAgent
      );

      return setSecurityHeaders(response);
    }

    // 安全检查
    const isApiPath = pathname.startsWith('/api/');
    const isAdminPath = adminPaths.some(path => pathname.startsWith(path));
    const isAdminApiPath = adminApiPaths.some(path => pathname.startsWith(path));

    // 根据路径类型应用不同的安全策略
    let securityOptions = {};

    if (isAdminPath || isAdminApiPath) {
      // 检查是否有有效的管理员token
      const authHeader = request.headers.get('authorization');
      const authCookie = request.cookies.get('admin-token')?.value;
      const token = authHeader?.replace('Bearer ', '') || authCookie;
      const isValidAdmin = token === ADMIN_TOKEN;

      if (isValidAdmin) {
        // 管理员路径：宽松限制，避免影响管理操作
        securityOptions = {
          enableRateLimit: true,
          enableRequestValidation: true,
          enableIPWhitelist: false,
          rateLimitConfig: { windowMs: 1 * 60 * 1000, maxRequests: 10000 } // 1分钟10000次，基本不限制
        };
      } else {
        // 非管理员访问管理路径：严格限制
        securityOptions = {
          enableRateLimit: true,
          enableRequestValidation: true,
          enableIPWhitelist: false,
          rateLimitConfig: { windowMs: 5 * 60 * 1000, maxRequests: 5 } // 5分钟5次
        };
      }
    } else if (isApiPath) {
      // API路径：宽松限流，避免影响正常使用
      securityOptions = {
        enableRateLimit: true,
        enableRequestValidation: true,
        rateLimitConfig: { windowMs: 1 * 60 * 1000, maxRequests: 1000 } // 1分钟1000次
      };
    } else {
      // 普通页面：基础安全检查
      securityOptions = {
        enableRateLimit: true,
        enableRequestValidation: true,
        rateLimitConfig: { windowMs: 1 * 60 * 1000, maxRequests: 2000 } // 1分钟2000次
      };
    }

    // 执行安全检查
    const securityResult = securityCheck(request, securityOptions);
    if (securityResult) {
      return setSecurityHeaders(securityResult);
    }

    if (isAdminPath || isAdminApiPath) {
      // 检查认证
      const authHeader = request.headers.get('authorization');
      const authCookie = request.cookies.get('admin-token')?.value;

      // 从Authorization header或cookie中获取token
      const token = authHeader?.replace('Bearer ', '') || authCookie;

      if (!token || token !== ADMIN_TOKEN) {
        // 记录未授权访问
        logger.warn('未授权的管理员访问尝试', {
          requestId,
          ip,
          userAgent,
          path: pathname,
          hasToken: !!token,
          tokenMatch: token === ADMIN_TOKEN
        });

        // 如果是API请求，返回401
        if (isAdminApiPath) {
          const response = NextResponse.json(
            { success: false, error: '需要管理员权限', requestId },
            { status: 401 }
          );
          response.headers.set('x-request-id', requestId);

          logger.http(
            request.method,
            pathname,
            401,
            Date.now() - startTime,
            requestId,
            undefined,
            ip,
            userAgent
          );

          return response;
        }

        // 如果是页面请求，重定向到登录页
        const loginUrl = new URL('/admin/login', request.url);
        loginUrl.searchParams.set('redirect', pathname);
        const response = NextResponse.redirect(loginUrl);
        response.headers.set('x-request-id', requestId);

        logger.http(
          request.method,
          pathname,
          302,
          Date.now() - startTime,
          requestId,
          undefined,
          ip,
          userAgent
        );

        return response;
      }

      // 记录成功的管理员访问
      logger.info('管理员访问', {
        requestId,
        ip,
        userAgent,
        path: pathname
      });
    }

    const response = NextResponse.next();
    response.headers.set('x-request-id', requestId);

    // 记录成功请求
    logger.http(
      request.method,
      pathname,
      200,
      Date.now() - startTime,
      requestId,
      undefined,
      ip,
      userAgent
    );

    return setSecurityHeaders(response);

  } catch (error) {
    // 处理中间件错误
    logger.error('中间件处理失败', error as Error, {
      requestId,
      path: request.nextUrl.pathname,
      method: request.method
    });

    return errorHandler.handleApiError(error as Error, {
      requestId,
      method: request.method,
      path: request.nextUrl.pathname,
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    });
  }
}

export const config = {
  matcher: [
    '/admin/:path*',
    '/api/admin/:path*',
    '/api/import/:path*',
    // 添加更多需要监控的路径
    '/api/:path*'
  ]
};

'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useBatchFavorites } from '@/hooks/useBatchFavorites';

interface BatchFavoritesContextType {
  favorites: Record<string, boolean>;
  loading: boolean;
  error: string | null;
  fetchFavorites: (movieIds: string[]) => void;
  updateFavorite: (movieId: string, isFavorited: boolean) => void;
  isFavorited: (movieId: string) => boolean;
  clearCache: () => void;
}

const BatchFavoritesContext = createContext<BatchFavoritesContextType | undefined>(undefined);

interface BatchFavoritesProviderProps {
  children: ReactNode;
}

/**
 * 批量收藏状态Context Provider
 * 为整个应用提供统一的收藏状态管理
 */
export function BatchFavoritesProvider({ children }: BatchFavoritesProviderProps) {
  const batchFavorites = useBatchFavorites();

  return (
    <BatchFavoritesContext.Provider value={batchFavorites}>
      {children}
    </BatchFavoritesContext.Provider>
  );
}

/**
 * 使用批量收藏状态的Hook
 */
export function useBatchFavoritesContext(): BatchFavoritesContextType {
  const context = useContext(BatchFavoritesContext);
  
  if (context === undefined) {
    throw new Error('useBatchFavoritesContext must be used within a BatchFavoritesProvider');
  }
  
  return context;
}

/**
 * 高阶组件：为组件提供批量收藏状态
 */
export function withBatchFavorites<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function WithBatchFavoritesComponent(props: P) {
    return (
      <BatchFavoritesProvider>
        <Component {...props} />
      </BatchFavoritesProvider>
    );
  };
}

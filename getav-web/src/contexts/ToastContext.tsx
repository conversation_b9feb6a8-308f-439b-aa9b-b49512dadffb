'use client';

import React, { createContext, useContext } from 'react';
import { useToast, ToastMessage } from '@/components/Toast';

interface ToastContextType {
  addToast: (toast: Omit<ToastMessage, 'id'>) => string;
  removeToast: (id: string) => void;
  clearAllToasts: () => void;
  success: (title: string, message?: string, options?: Partial<ToastMessage>) => string;
  error: (title: string, message?: string, options?: Partial<ToastMessage>) => string;
  warning: (title: string, message?: string, options?: Partial<ToastMessage>) => string;
  info: (title: string, message?: string, options?: Partial<ToastMessage>) => string;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const toast = useToast();

  return (
    <ToastContext.Provider value={toast}>
      {children}
      <toast.ToastContainer />
    </ToastContext.Provider>
  );
}

export function useToastContext() {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToastContext must be used within a ToastProvider');
  }
  return context;
}

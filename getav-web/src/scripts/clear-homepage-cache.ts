#!/usr/bin/env tsx

/**
 * 清空首页Redis缓存脚本
 * 用于清理首页相关的所有缓存数据
 */

import { getRedisClient, closeRedisConnection, CacheKeys } from '../lib/redis';
import { cacheService } from '../lib/cache-service';

async function clearHomepageCache() {
  console.log('🚀 开始清空首页缓存...');
  
  try {
    // 检查Redis连接
    const client = await getRedisClient();
    if (!client) {
      console.error('❌ Redis连接失败，无法清空缓存');
      return;
    }

    console.log('✅ Redis连接成功');

    // 清空首页相关缓存
    const cachePatterns = [
      'movies:*',           // 所有影片列表缓存
      'popular:movies',     // 热门影片缓存
      'stats:general',      // 统计数据缓存
      'search:*'           // 搜索结果缓存（可能影响首页推荐）
    ];

    console.log('🧹 正在清理以下缓存模式:');
    cachePatterns.forEach(pattern => console.log(`  - ${pattern}`));

    // 逐个清理缓存模式
    for (const pattern of cachePatterns) {
      try {
        if (pattern.includes('*')) {
          // 使用模式匹配删除
          await cacheService.deletePattern(pattern);
          console.log(`✅ 已清理模式: ${pattern}`);
        } else {
          // 直接删除单个键
          await cacheService.delete(pattern);
          console.log(`✅ 已清理缓存: ${pattern}`);
        }
      } catch (error) {
        console.error(`❌ 清理 ${pattern} 失败:`, error);
      }
    }

    // 验证缓存清理结果
    console.log('\n🔍 验证缓存清理结果...');
    
    // 检查一些关键缓存是否已被清理
    const testKeys = [
      CacheKeys.movieList(1, 20),
      CacheKeys.popularMovies(),
      CacheKeys.stats()
    ];

    for (const key of testKeys) {
      const exists = await cacheService.exists(key);
      if (exists) {
        console.log(`⚠️  缓存仍存在: ${key}`);
      } else {
        console.log(`✅ 缓存已清理: ${key}`);
      }
    }

    console.log('\n🎉 首页缓存清理完成！');
    console.log('💡 提示: 下次访问首页时将重新生成缓存');

  } catch (error) {
    console.error('❌ 清空缓存过程中发生错误:', error);
  } finally {
    // 关闭Redis连接
    await closeRedisConnection();
    console.log('🔌 Redis连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  clearHomepageCache()
    .then(() => {
      console.log('✨ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

export { clearHomepageCache };

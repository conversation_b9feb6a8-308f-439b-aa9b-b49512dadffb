/**
 * 请求去重工具
 * 防止重复的API请求，提供缓存和去重机制
 */

interface RequestCache<T = unknown> {
  promise: Promise<T>;
  timestamp: number;
  data?: T;
}

class RequestDeduplicator {
  private cache = new Map<string, RequestCache<unknown>>();
  private readonly cacheTimeout: number;

  constructor(cacheTimeout = 5 * 60 * 1000) { // 默认5分钟缓存
    this.cacheTimeout = cacheTimeout;
  }

  /**
   * 生成请求缓存键
   */
  private generateKey(url: string, options?: RequestInit): string {
    const method = options?.method || 'GET';
    const body = options?.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }

  /**
   * 检查缓存是否过期
   */
  private isExpired(timestamp: number): boolean {
    return Date.now() - timestamp > this.cacheTimeout;
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    for (const [key, cache] of this.cache.entries()) {
      if (this.isExpired(cache.timestamp)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 去重请求
   * 如果相同请求正在进行中，返回相同的Promise
   * 如果有有效缓存，直接返回缓存数据
   */
  async request<T>(
    url: string, 
    options?: RequestInit,
    forceRefresh = false
  ): Promise<T> {
    const key = this.generateKey(url, options);
    
    // 清理过期缓存
    this.cleanExpiredCache();

    // 检查是否有有效缓存
    const cached = this.cache.get(key);
    if (cached && !forceRefresh) {
      if (!this.isExpired(cached.timestamp)) {
        // 如果有缓存数据，直接返回
        if (cached.data !== undefined) {
          return cached.data as T;
        }
        // 如果请求正在进行中，返回相同的Promise
        return cached.promise as Promise<T>;
      }
    }

    // 创建新请求
    const promise = this.performRequest<T>(url, options);
    
    // 缓存请求Promise
    this.cache.set(key, {
      promise,
      timestamp: Date.now()
    });

    try {
      const result = await promise;
      
      // 缓存结果数据
      this.cache.set(key, {
        promise,
        timestamp: Date.now(),
        data: result
      });

      return result;
    } catch (error) {
      // 请求失败时移除缓存
      this.cache.delete(key);
      throw error;
    }
  }

  /**
   * 执行实际的网络请求
   */
  private async performRequest<T>(url: string, options?: RequestInit): Promise<T> {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  /**
   * 清除指定键的缓存
   */
  clearCache(url: string, options?: RequestInit): void {
    const key = this.generateKey(url, options);
    this.cache.delete(key);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局请求去重器实例
export const requestDeduplicator = new RequestDeduplicator();

/**
 * 去重的fetch函数
 * 使用方式与原生fetch相同，但会自动去重和缓存
 */
export async function deduplicatedFetch<T = unknown>(
  url: string, 
  options?: RequestInit,
  forceRefresh = false
): Promise<T> {
  return requestDeduplicator.request<T>(url, options, forceRefresh);
}

/**
 * 批量请求去重器
 * 用于将多个相似请求合并为一个批量请求
 */
class BatchRequestDeduplicator {
  private batchQueue = new Map<string, {
    items: unknown[];
    timer: NodeJS.Timeout;
    resolve: (value: unknown) => void;
    reject: (error: unknown) => void;
  }>();

  /**
   * 添加到批量请求队列
   */
  addToBatch<T>(
    batchKey: string,
    item: unknown,
    batchProcessor: (items: unknown[]) => Promise<T>,
    delay = 100
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const existing = this.batchQueue.get(batchKey);
      
      if (existing) {
        // 添加到现有批次
        existing.items.push(item);
        
        // 重置定时器
        clearTimeout(existing.timer);
        existing.timer = setTimeout(async () => {
          try {
            const result = await batchProcessor(existing.items);
            existing.resolve(result);
          } catch (error) {
            existing.reject(error);
          } finally {
            this.batchQueue.delete(batchKey);
          }
        }, delay);
      } else {
        // 创建新批次
        const timer = setTimeout(async () => {
          try {
            const batch = this.batchQueue.get(batchKey);
            if (batch) {
              const result = await batchProcessor(batch.items);
              batch.resolve(result);
            }
          } catch (error) {
            const batch = this.batchQueue.get(batchKey);
            if (batch) {
              batch.reject(error);
            }
          } finally {
            this.batchQueue.delete(batchKey);
          }
        }, delay);

        this.batchQueue.set(batchKey, {
          items: [item],
          timer,
          resolve: resolve as (value: unknown) => void,
          reject
        });
      }
    });
  }

  /**
   * 清除所有批量请求
   */
  clearAll(): void {
    for (const batch of this.batchQueue.values()) {
      clearTimeout(batch.timer);
    }
    this.batchQueue.clear();
  }
}

// 创建全局批量请求去重器实例
export const batchRequestDeduplicator = new BatchRequestDeduplicator();

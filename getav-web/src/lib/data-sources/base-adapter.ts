import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { logger } from '@/lib/logger';

/**
 * 数据源适配器基类
 * 定义了所有数据源适配器必须实现的接口
 */
export abstract class BaseDataSourceAdapter {
  protected abstract apiUrl: string;
  protected abstract headers: Record<string, string>;
  protected axiosInstance: AxiosInstance;
  protected retryCount: number = 3;
  protected timeout: number = 60000;

  constructor() {
    this.axiosInstance = axios.create({
      timeout: this.timeout,
      headers: this.headers
    });

    // 设置请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        logger.debug(`发起API请求: ${config.method?.toUpperCase()} ${config.url}`, {
          adapter: this.constructor.name,
          params: config.params
        });
        return config;
      },
      (error) => {
        logger.error('API请求配置错误', error, {
          adapter: this.constructor.name
        });
        return Promise.reject(error);
      }
    );

    // 设置响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        logger.debug(`API响应成功: ${response.status}`, {
          adapter: this.constructor.name,
          url: response.config.url,
          dataSize: JSON.stringify(response.data).length
        });
        return response;
      },
      (error) => {
        logger.error('API响应错误', error, {
          adapter: this.constructor.name,
          url: error.config?.url,
          status: error.response?.status,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * 获取影片列表
   * @param page 页码
   * @param limit 每页数量
   */
  abstract fetchMovies(page: number, limit?: number): Promise<any[]>;

  /**
   * 获取影片详情
   * @param id 影片ID
   */
  abstract fetchMovieDetail(id: string): Promise<any>;

  /**
   * 验证播放链接是否存在
   * @param movie 影片数据
   */
  abstract validatePlaybackLinks(movie: any): boolean;

  /**
   * 获取数据源名称
   */
  abstract getSourceName(): string;

  /**
   * 带重试的HTTP请求
   * @param config 请求配置
   * @param retryCount 重试次数
   */
  protected async requestWithRetry<T>(
    config: AxiosRequestConfig,
    retryCount: number = this.retryCount
  ): Promise<T> {
    let lastError: any;

    for (let i = 0; i <= retryCount; i++) {
      try {
        const response = await this.axiosInstance.request<T>(config);
        return response.data;
      } catch (error: any) {
        lastError = error;
        
        if (i < retryCount) {
          const delay = Math.pow(2, i) * 1000; // 指数退避
          logger.warn(`请求失败，${delay}ms后重试 (${i + 1}/${retryCount})`, {
            adapter: this.constructor.name,
            url: config.url,
            error: error.message
          });
          await this.sleep(delay);
        }
      }
    }

    logger.error(`请求最终失败，已重试${retryCount}次`, lastError, {
      adapter: this.constructor.name,
      url: config.url
    });
    throw lastError;
  }

  /**
   * 延迟函数
   * @param ms 毫秒数
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证响应数据格式
   * @param data 响应数据
   * @param requiredFields 必需字段
   */
  protected validateResponseData(data: any, requiredFields: string[]): boolean {
    if (!data || typeof data !== 'object') {
      return false;
    }

    return requiredFields.every(field => {
      const hasField = field.split('.').reduce((obj, key) => obj?.[key], data) !== undefined;
      if (!hasField) {
        logger.warn(`响应数据缺少必需字段: ${field}`, {
          adapter: this.constructor.name
        });
      }
      return hasField;
    });
  }

  /**
   * 清理和标准化数据
   * @param data 原始数据
   */
  protected cleanData(data: any): any {
    if (!data) return data;

    // 移除空字符串，转换为null
    const cleaned = { ...data };
    Object.keys(cleaned).forEach(key => {
      if (cleaned[key] === '' || cleaned[key] === 'N/A') {
        cleaned[key] = null;
      }
      // 清理字符串前后空格
      if (typeof cleaned[key] === 'string') {
        cleaned[key] = cleaned[key].trim();
      }
    });

    return cleaned;
  }
}
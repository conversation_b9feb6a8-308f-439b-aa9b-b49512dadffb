import { BaseDataSourceAdapter } from './base-adapter';
import { NewApiMovie } from '@/types/javbus';
import { logger } from '@/lib/logger';

/**
 * 新API数据源适配器
 * 连接到 http://188.68.60.179:8081 获取数据
 */
export class NewApiAdapter extends BaseDataSourceAdapter {
  protected apiUrl = 'http://188.68.60.179:8081';
  protected headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  };

  constructor() {
    super();
    // 更新axios实例的baseURL
    this.axiosInstance.defaults.baseURL = this.apiUrl;
  }

  /**
   * 获取数据源名称
   */
  getSourceName(): string {
    return 'new_api';
  }

  /**
   * 获取影片列表
   * @param page 页码
   * @param limit 每页数量
   */
  async fetchMovies(page: number = 1, limit: number = 20): Promise<NewApiMovie[]> {
    try {
      logger.info(`从新API获取影片列表`, {
        adapter: this.constructor.name,
        page,
        limit
      });

      const response = await this.requestWithRetry<{
        success: boolean;
        data: NewApiMovie[];
        code: number;
        message: string;
      }>({
        url: '/api/v1/jav/movies/latest',
        method: 'GET',
        params: { limit }
      });

      if (!response.success || !Array.isArray(response.data)) {
        throw new Error(`API响应格式错误: ${response.message || '未知错误'}`);
      }

      const movies = response.data.map(movie => this.cleanData(movie));
      
      logger.info(`成功获取${movies.length}部影片`, {
        adapter: this.constructor.name,
        page,
        count: movies.length
      });

      return movies;
    } catch (error: any) {
      logger.error('获取影片列表失败', error, {
        adapter: this.constructor.name,
        page,
        limit
      });
      throw error;
    }
  }

  /**
   * 获取影片详情
   * @param code 影片番号
   */
  async fetchMovieDetail(code: string): Promise<NewApiMovie | null> {
    try {
      logger.info(`获取影片详情: ${code}`, {
        adapter: this.constructor.name,
        code
      });

      // 注意：新API可能没有单独的详情接口，这里先尝试从列表中查找
      // 如果有专门的详情接口，可以修改这里的实现
      const movies = await this.fetchMovies(1, 100);
      const movie = movies.find(m => m.code === code);

      if (!movie) {
        logger.warn(`未找到影片: ${code}`, {
          adapter: this.constructor.name,
          code
        });
        return null;
      }

      logger.info(`成功获取影片详情: ${code}`, {
        adapter: this.constructor.name,
        code,
        title: movie.title
      });

      return movie;
    } catch (error: any) {
      logger.error(`获取影片详情失败: ${code}`, error, {
        adapter: this.constructor.name,
        code
      });
      return null;
    }
  }

  /**
   * 验证播放链接是否存在
   * @param movie 影片数据
   */
  validatePlaybackLinks(movie: NewApiMovie): boolean {
    if (!movie) {
      return false;
    }

    const hasStreamtape = movie.streamtape_url && 
                         movie.streamtape_url.trim() !== '' && 
                         movie.streamtape_url !== 'null';
    
    const hasStreamhg = movie.streamhg_url && 
                       movie.streamhg_url.trim() !== '' && 
                       movie.streamhg_url !== 'null';

    const hasPlaybackLinks = hasStreamtape || hasStreamhg;

    logger.debug(`播放链接验证结果: ${movie.code}`, {
      adapter: this.constructor.name,
      code: movie.code,
      hasStreamtape,
      hasStreamhg,
      hasPlaybackLinks,
      streamtape_url: movie.streamtape_url,
      streamhg_url: movie.streamhg_url
    });

    return hasPlaybackLinks;
  }

  /**
   * 获取有播放链接的影片
   * @param page 页码
   * @param limit 每页数量
   */
  async fetchMoviesWithPlaybackLinks(page: number = 1, limit: number = 20): Promise<NewApiMovie[]> {
    try {
      const allMovies = await this.fetchMovies(page, limit);
      const moviesWithLinks = allMovies.filter(movie => this.validatePlaybackLinks(movie));

      logger.info(`筛选出有播放链接的影片`, {
        adapter: this.constructor.name,
        page,
        totalMovies: allMovies.length,
        moviesWithLinks: moviesWithLinks.length,
        filterRate: `${((moviesWithLinks.length / allMovies.length) * 100).toFixed(1)}%`
      });

      return moviesWithLinks;
    } catch (error: any) {
      logger.error('获取有播放链接的影片失败', error, {
        adapter: this.constructor.name,
        page,
        limit
      });
      throw error;
    }
  }

  /**
   * 测试API连接
   */
  async testConnection(): Promise<boolean> {
    try {
      logger.info('测试新API连接', {
        adapter: this.constructor.name,
        apiUrl: this.apiUrl
      });

      const response = await this.requestWithRetry<any>({
        url: '/health',
        method: 'GET',
        timeout: 10000
      });

      logger.info('新API连接测试成功', {
        adapter: this.constructor.name,
        response: response
      });

      return true;
    } catch (error: any) {
      logger.error('新API连接测试失败', error, {
        adapter: this.constructor.name,
        apiUrl: this.apiUrl
      });
      return false;
    }
  }

  /**
   * 获取API统计信息
   */
  async getStats(): Promise<any> {
    try {
      const response = await this.requestWithRetry<{
        success: boolean;
        data: any;
      }>({
        url: '/api/v1/jav/stats',
        method: 'GET'
      });

      if (!response.success) {
        throw new Error('获取统计信息失败');
      }

      return response.data;
    } catch (error: any) {
      logger.error('获取API统计信息失败', error, {
        adapter: this.constructor.name
      });
      throw error;
    }
  }
}
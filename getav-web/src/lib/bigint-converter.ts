// 定义可能包含BigInt的类型
type BigIntConvertible =
  | bigint
  | string
  | number
  | boolean
  | null
  | undefined
  | Date
  | BigIntConvertible[]
  | { [key: string]: BigIntConvertible };

// 定义转换后的类型（BigInt被转换为string）
type ConvertedType<T> = T extends bigint
  ? string
  : T extends Date
  ? string
  : T extends (infer U)[]
  ? ConvertedType<U>[]
  : T extends object
  ? { [K in keyof T]: ConvertedType<T[K]> }
  : T;

// 转换BigInt为字符串的辅助函数
export function convertBigIntToString<T extends BigIntConvertible>(obj: T): ConvertedType<T> {
  if (obj === null || obj === undefined) {
    return obj as ConvertedType<T>;
  }

  if (typeof obj === 'bigint') {
    return obj.toString() as ConvertedType<T>;
  }

  if (obj instanceof Date) {
    return obj.toISOString() as ConvertedType<T>;
  }

  if (Array.isArray(obj)) {
    return obj.map(convertBigIntToString) as ConvertedType<T>;
  }

  if (typeof obj === 'object') {
    const converted: Record<string, unknown> = {};
    for (const key in obj) {
      converted[key] = convertBigIntToString(obj[key]);
    }
    return converted as ConvertedType<T>;
  }

  return obj as ConvertedType<T>;
}

import { createClient, RedisClientType } from 'redis';

/**
 * Redis客户端配置和连接管理
 * 提供缓存功能支持，提升应用性能
 */

let redisClient: RedisClientType | null = null;

// Redis配置
const REDIS_CONFIG = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  password: process.env.REDIS_PASSWORD,
  database: parseInt(process.env.REDIS_DATABASE || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

/**
 * 获取Redis客户端实例
 */
export async function getRedisClient(): Promise<RedisClientType | null> {
  if (!redisClient) {
    try {
      redisClient = createClient(REDIS_CONFIG);

      redisClient.on('error', (err) => {
        console.error('Redis客户端错误:', err);
      });

      redisClient.on('connect', () => {
        console.log('Redis连接成功');
      });

      redisClient.on('ready', () => {
        console.log('Redis客户端就绪');
      });

      redisClient.on('end', () => {
        console.log('Redis连接关闭');
      });

      await redisClient.connect();
    } catch (error) {
      console.error('Redis连接失败:', error);
      redisClient = null;
    }
  }

  return redisClient;
}

/**
 * 关闭Redis连接
 */
export async function closeRedisConnection(): Promise<void> {
  if (redisClient) {
    try {
      await redisClient.quit();
      redisClient = null;
      console.log('Redis连接已关闭');
    } catch (error) {
      console.error('关闭Redis连接失败:', error);
    }
  }
}

/**
 * 检查Redis连接状态
 */
export async function isRedisConnected(): Promise<boolean> {
  try {
    const client = await getRedisClient();
    if (!client) return false;
    
    await client.ping();
    return true;
  } catch (error) {
    console.error('Redis连接检查失败:', error);
    return false;
  }
}

/**
 * 缓存键生成器
 */
export const CacheKeys = {
  // 影片相关
  movie: (id: string) => `movie:${id}`,
  movieList: (page: number, limit: number, filters?: string) => 
    `movies:${page}:${limit}${filters ? `:${filters}` : ''}`,
  movieSearch: (query: string, page: number, filters?: string) => 
    `search:${query}:${page}${filters ? `:${filters}` : ''}`,
  
  // 演员相关
  star: (id: string) => `star:${id}`,
  starList: (page: number, limit: number) => `stars:${page}:${limit}`,
  starMovies: (starId: string, page: number) => `star:${starId}:movies:${page}`,
  
  // 统计数据
  stats: () => 'stats:general',
  popularMovies: () => 'popular:movies',
  trendingStars: () => 'trending:stars',
  
  // 搜索建议
  searchSuggestions: (query: string) => `suggestions:${query}`,
  
  // 用户相关
  userFavorites: (userId: string) => `user:${userId}:favorites`,
  userHistory: (userId: string) => `user:${userId}:history`,
};

/**
 * 缓存过期时间配置（秒）
 */
export const CacheExpiry = {
  SHORT: 5 * 60,        // 5分钟 - 实时性要求高的数据
  MEDIUM: 30 * 60,      // 30分钟 - 一般数据
  LONG: 2 * 60 * 60,    // 2小时 - 相对稳定的数据
  VERY_LONG: 24 * 60 * 60, // 24小时 - 很少变化的数据
};

/**
 * 导出Redis客户端实例（用于直接访问）
 */
export const redis = {
  async ping() {
    const client = await getRedisClient();
    if (!client) throw new Error('Redis client not available');
    return client.ping();
  },

  async info(section?: string) {
    const client = await getRedisClient();
    if (!client) throw new Error('Redis client not available');
    return client.info(section);
  },

  async dbsize() {
    const client = await getRedisClient();
    if (!client) throw new Error('Redis client not available');
    return client.dbSize();
  },

  async flushdb() {
    const client = await getRedisClient();
    if (!client) throw new Error('Redis client not available');
    return client.flushDb();
  },

  async get(key: string) {
    const client = await getRedisClient();
    if (!client) return null;
    return client.get(key);
  },

  async set(key: string, value: string, expiry?: number) {
    const client = await getRedisClient();
    if (!client) return null;
    if (expiry) {
      return client.setEx(key, expiry, value);
    }
    return client.set(key, value);
  },

  async del(key: string) {
    const client = await getRedisClient();
    if (!client) return 0;
    return client.del(key);
  }
};

// 进程退出时清理连接
if (typeof window === 'undefined') {
  process.on('SIGINT', async () => {
    await closeRedisConnection();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    await closeRedisConnection();
    process.exit(0);
  });
}

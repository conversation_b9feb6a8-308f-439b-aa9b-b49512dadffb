/**
 * 缓存预热调度器
 * 自动在指定时间执行缓存预热任务
 */

import { cacheWarmer } from './cache-warmer';
import { logger } from './logger';

export interface ScheduleConfig {
  enabled: boolean;
  intervals: {
    startup: boolean; // 启动时预热
    hourly: boolean; // 每小时预热
    daily: boolean; // 每日预热
    weekly: boolean; // 每周预热
  };
  timeSlots: {
    daily: string; // 每日预热时间 (HH:MM 格式)
    weekly: {
      day: number; // 0-6 (周日到周六)
      time: string; // HH:MM 格式
    };
  };
  lowTrafficHours: number[]; // 低流量时段 (0-23)
}

export class CacheScheduler {
  private config: ScheduleConfig;
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private isRunning = false;

  constructor(config?: Partial<ScheduleConfig>) {
    this.config = {
      enabled: true,
      intervals: {
        startup: true,
        hourly: false,
        daily: true,
        weekly: true
      },
      timeSlots: {
        daily: '03:00', // 凌晨3点
        weekly: {
          day: 0, // 周日
          time: '02:00' // 凌晨2点
        }
      },
      lowTrafficHours: [1, 2, 3, 4, 5], // 凌晨1-5点
      ...config
    };
  }

  /**
   * 启动调度器
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('缓存调度器已在运行');
      return;
    }

    if (!this.config.enabled) {
      logger.info('缓存调度器已禁用');
      return;
    }

    this.isRunning = true;
    logger.info('启动缓存预热调度器', {
      config: this.config
    });

    // 启动时预热
    if (this.config.intervals.startup) {
      this.scheduleStartupWarmup();
    }

    // 每小时预热
    if (this.config.intervals.hourly) {
      this.scheduleHourlyWarmup();
    }

    // 每日预热
    if (this.config.intervals.daily) {
      this.scheduleDailyWarmup();
    }

    // 每周预热
    if (this.config.intervals.weekly) {
      this.scheduleWeeklyWarmup();
    }
  }

  /**
   * 停止调度器
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    // 清除所有定时器
    for (const [name, timer] of this.timers) {
      clearTimeout(timer);
      logger.debug(`清除定时器: ${name}`);
    }
    
    this.timers.clear();
    logger.info('缓存预热调度器已停止');
  }

  /**
   * 启动时预热
   */
  private scheduleStartupWarmup(): void {
    // 延迟5秒执行，确保系统完全启动
    const timer = setTimeout(async () => {
      try {
        logger.info('执行启动时缓存预热');
        const result = await cacheWarmer.warmup();
        
        if (result.success) {
          logger.info('启动时缓存预热完成', {
            completedTasks: result.completedTasks,
            duration: result.duration
          });
        } else {
          logger.warn('启动时缓存预热部分失败', {
            completedTasks: result.completedTasks,
            failedTasks: result.failedTasks,
            errors: result.errors
          });
        }
      } catch (error) {
        logger.error('启动时缓存预热失败', error as Error);
      }
    }, 5000);

    this.timers.set('startup', timer);
  }

  /**
   * 每小时预热（仅在低流量时段）
   */
  private scheduleHourlyWarmup(): void {
    const scheduleNext = () => {
      const now = new Date();
      const nextHour = new Date(now);
      nextHour.setHours(now.getHours() + 1, 0, 0, 0);
      
      const delay = nextHour.getTime() - now.getTime();
      
      const timer = setTimeout(async () => {
        const currentHour = new Date().getHours();
        
        // 只在低流量时段执行
        if (this.config.lowTrafficHours.includes(currentHour)) {
          try {
            logger.info('执行每小时缓存预热', { hour: currentHour });
            const result = await cacheWarmer.warmup();
            
            logger.info('每小时缓存预热完成', {
              hour: currentHour,
              completedTasks: result.completedTasks,
              duration: result.duration
            });
          } catch (error) {
            logger.error('每小时缓存预热失败', error as Error, { hour: currentHour });
          }
        }
        
        // 安排下一次执行
        if (this.isRunning) {
          scheduleNext();
        }
      }, delay);

      this.timers.set('hourly', timer);
    };

    scheduleNext();
  }

  /**
   * 每日预热
   */
  private scheduleDailyWarmup(): void {
    const scheduleNext = () => {
      const now = new Date();
      const [hours, minutes] = this.config.timeSlots.daily.split(':').map(Number);
      
      const nextRun = new Date(now);
      nextRun.setHours(hours, minutes, 0, 0);
      
      // 如果今天的时间已过，安排到明天
      if (nextRun <= now) {
        nextRun.setDate(nextRun.getDate() + 1);
      }
      
      const delay = nextRun.getTime() - now.getTime();
      
      const timer = setTimeout(async () => {
        try {
          logger.info('执行每日缓存预热', {
            scheduledTime: this.config.timeSlots.daily
          });
          
          const result = await cacheWarmer.warmup();
          
          logger.info('每日缓存预热完成', {
            completedTasks: result.completedTasks,
            duration: result.duration,
            failedTasks: result.failedTasks
          });
        } catch (error) {
          logger.error('每日缓存预热失败', error as Error);
        }
        
        // 安排下一次执行
        if (this.isRunning) {
          scheduleNext();
        }
      }, delay);

      this.timers.set('daily', timer);
      
      logger.debug('安排每日缓存预热', {
        nextRun: nextRun.toISOString(),
        delay: Math.round(delay / 1000 / 60) + '分钟'
      });
    };

    scheduleNext();
  }

  /**
   * 每周预热
   */
  private scheduleWeeklyWarmup(): void {
    const scheduleNext = () => {
      const now = new Date();
      const { day, time } = this.config.timeSlots.weekly;
      const [hours, minutes] = time.split(':').map(Number);
      
      const nextRun = new Date(now);
      const daysUntilTarget = (day - now.getDay() + 7) % 7;
      
      nextRun.setDate(now.getDate() + daysUntilTarget);
      nextRun.setHours(hours, minutes, 0, 0);
      
      // 如果是同一天但时间已过，安排到下周
      if (daysUntilTarget === 0 && nextRun <= now) {
        nextRun.setDate(nextRun.getDate() + 7);
      }
      
      const delay = nextRun.getTime() - now.getTime();
      
      const timer = setTimeout(async () => {
        try {
          logger.info('执行每周缓存预热', {
            scheduledDay: day,
            scheduledTime: time
          });
          
          const result = await cacheWarmer.warmup();
          
          logger.info('每周缓存预热完成', {
            completedTasks: result.completedTasks,
            duration: result.duration,
            failedTasks: result.failedTasks
          });
        } catch (error) {
          logger.error('每周缓存预热失败', error as Error);
        }
        
        // 安排下一次执行
        if (this.isRunning) {
          scheduleNext();
        }
      }, delay);

      this.timers.set('weekly', timer);
      
      logger.debug('安排每周缓存预热', {
        nextRun: nextRun.toISOString(),
        delay: Math.round(delay / 1000 / 60 / 60) + '小时'
      });
    };

    scheduleNext();
  }

  /**
   * 手动触发预热
   */
  async triggerWarmup(): Promise<void> {
    logger.info('手动触发缓存预热');
    
    try {
      const result = await cacheWarmer.warmup();
      
      logger.info('手动缓存预热完成', {
        completedTasks: result.completedTasks,
        duration: result.duration,
        failedTasks: result.failedTasks
      });
    } catch (error) {
      logger.error('手动缓存预热失败', error as Error);
      throw error;
    }
  }

  /**
   * 获取调度器状态
   */
  getStatus(): {
    isRunning: boolean;
    config: ScheduleConfig;
    activeTimers: string[];
  } {
    return {
      isRunning: this.isRunning,
      config: this.config,
      activeTimers: Array.from(this.timers.keys())
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ScheduleConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.isRunning) {
      logger.info('重启调度器以应用新配置');
      this.stop();
      this.start();
    }
  }
}

// 导出单例实例
export const cacheScheduler = new CacheScheduler();

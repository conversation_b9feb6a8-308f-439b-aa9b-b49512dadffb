/**
 * 数据库初始化模块
 * 负责应用启动时的数据库连接初始化
 */

import { prisma } from './database';

/**
 * 初始化数据库连接
 * 在应用启动时调用
 */
export async function initializeDatabase(): Promise<void> {
  try {
    console.log('🔌 正在初始化数据库连接...');
    
    // 测试数据库连接
    await prisma.$connect();
    
    // 验证数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    console.log('✅ 数据库连接初始化成功');
    
    // 设置连接池监控（开发环境）
    if (process.env.NODE_ENV === 'development') {
      setupConnectionMonitoring();
    }
    
  } catch (error) {
    console.error('❌ 数据库连接初始化失败:', error);
    throw error;
  }
}

/**
 * 设置连接池监控（仅开发环境）
 */
function setupConnectionMonitoring(): void {
  // 监控连接池状态
  setInterval(async () => {
    try {
      const result = await prisma.$queryRaw`
        SELECT 
          count(*) as total_connections,
          count(*) FILTER (WHERE state = 'active') as active_connections,
          count(*) FILTER (WHERE state = 'idle') as idle_connections
        FROM pg_stat_activity 
        WHERE datname = current_database()
      ` as Array<{
        total_connections: bigint;
        active_connections: bigint;
        idle_connections: bigint;
      }>;
      
      if (result.length > 0) {
        const stats = result[0];
        console.log(`📊 数据库连接状态 - 总计: ${stats.total_connections}, 活跃: ${stats.active_connections}, 空闲: ${stats.idle_connections}`);
        
        // 如果连接数过多，发出警告
        if (Number(stats.total_connections) > 15) {
          console.warn(`⚠️  数据库连接数较高: ${stats.total_connections}`);
        }
      }
    } catch (error) {
      // 静默处理监控错误，不影响主要功能
      console.debug('连接池监控查询失败:', error);
    }
  }, 30000); // 每30秒检查一次
}

/**
 * 优雅关闭数据库连接
 */
export async function closeDatabase(): Promise<void> {
  try {
    console.log('🔌 正在关闭数据库连接...');
    await prisma.$disconnect();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error);
    throw error;
  }
}

/**
 * 检查数据库连接健康状态
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('数据库健康检查失败:', error);
    return false;
  }
}

import { prisma } from '@/lib/database';

/**
 * 批量获取电影统计数据
 */
async function getMoviesStats(movieIds: string[]): Promise<Map<string, { likes: number; favorites: number; views: number }>> {
  const [likesData, favoritesData, viewsData] = await Promise.all([
    prisma.like.groupBy({
      by: ['movieId'],
      where: {
        movieId: { in: movieIds },
        type: 'LIKE'
      },
      _count: { id: true }
    }),
    prisma.favorite.groupBy({
      by: ['movieId'],
      where: {
        movieId: { in: movieIds }
      },
      _count: { id: true }
    }),
    prisma.view.groupBy({
      by: ['movieId'],
      where: {
        movieId: { in: movieIds }
      },
      _count: { id: true }
    })
  ]);

  const statsMap = new Map<string, { likes: number; favorites: number; views: number }>();

  // 初始化所有电影的统计数据
  movieIds.forEach(id => {
    statsMap.set(id, { likes: 0, favorites: 0, views: 0 });
  });

  // 设置点赞数据
  likesData.forEach(item => {
    if (item.movieId) {
      const existing = statsMap.get(item.movieId);
      if (existing) {
        existing.likes = item._count.id;
      }
    }
  });

  // 设置收藏数据
  favoritesData.forEach(item => {
    if (item.movieId) {
      const existing = statsMap.get(item.movieId);
      if (existing) {
        existing.favorites = item._count.id;
      }
    }
  });

  // 设置观看数据
  viewsData.forEach(item => {
    if (item.movieId) {
      const existing = statsMap.get(item.movieId);
      if (existing) {
        existing.views = item._count.id;
      }
    }
  });

  return statsMap;
}

/**
 * 增强的搜索服务
 * 使用PostgreSQL全文搜索提供高性能、相关性排序的搜索功能
 */

export interface SearchOptions {
  query?: string;
  category?: string;
  star?: string;
  page?: number;
  limit?: number;
  sortBy?: 'relevance' | 'date' | 'title';
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
  videoLength?: string;
}

export interface MovieResult {
  id: string;
  title: string;
  localImg?: string | null;
  img?: string | null;
  date?: string | null;
  videoLength?: number | null;
  description?: string | null;
  createdAt: Date;
  updatedAt: Date;
  rank?: number;
  views?: number;
  likes?: number;
  favorites?: number;
  stars: Array<{ id: string; name: string; localAvatar?: string | null }>;
  genres: Array<{ id: string; name: string }>;
  samples?: Array<{ id: string; alt?: string | null; localSrc?: string | null }>;
  magnets?: Array<{
    id: string;
    link: string;
    isHD?: boolean | null;
    title?: string | null;
    size?: string | null;
    hasSubtitle?: boolean | null;
  }>;
}

export interface SearchResult {
  movies: MovieResult[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  query?: string;
  category?: string;
  star?: string;
  suggestions?: SearchSuggestion[];
}

export interface SearchSuggestion {
  text: string;
  type: 'star' | 'genre' | 'movie';
  count: number;
}

/**
 * 执行全文搜索
 */
export async function performFullTextSearch(options: SearchOptions): Promise<SearchResult> {
  const {
    query = '',
    category = '',
    star = '',
    page = 1,
    limit = 20,
    sortBy = 'relevance',
    sortOrder = 'desc'
  } = options;

  // 如果有搜索查询，使用全文搜索
  if (query.trim()) {
    return await performTextSearch(query, {
      category,
      star,
      page,
      limit,
      sortBy,
      sortOrder
    });
  }

  // 如果只有筛选条件，使用传统查询
  return await performFilterSearch({
    category,
    star,
    page,
    limit,
    sortBy,
    sortOrder
  });
}

/**
 * 执行文本搜索（使用PostgreSQL全文搜索）
 */
async function performTextSearch(
  searchQuery: string,
  options: Omit<SearchOptions, 'query'>
): Promise<SearchResult> {
  const { category, star, page = 1, limit = 20, sortBy = 'relevance' } = options;
  const offset = (page - 1) * limit;

  try {
    // 构建基础查询
    let baseQuery = `
      SELECT
        m.id,
        m.title,
        m."localImg",
        m.img,
        m.date,
        m."videoLength",
        m.description,
        m."createdAt",
        m."updatedAt",
        -- 计算搜索相关性得分
        ts_rank(
          setweight(to_tsvector('simple', COALESCE(m.id, '')), 'A') ||
          setweight(to_tsvector('simple', COALESCE(m.title, '')), 'B') ||
          setweight(to_tsvector('simple', COALESCE(m.description, '')), 'C'),
          plainto_tsquery('simple', $1)
        ) as rank,
        -- 聚合演员信息
        COALESCE(
          array_agg(
            DISTINCT jsonb_build_object(
              'id', s.id,
              'name', s.name,
              'localAvatar', s."localAvatar"
            )
          ) FILTER (WHERE s.id IS NOT NULL),
          '{}'::jsonb[]
        ) as stars,
        -- 聚合分类信息
        COALESCE(
          array_agg(
            DISTINCT jsonb_build_object(
              'id', g.id,
              'name', g.name
            )
          ) FILTER (WHERE g.id IS NOT NULL),
          '{}'::jsonb[]
        ) as genres
      FROM "movies" m
      LEFT JOIN "movie_stars" ms ON m.id = ms."movieId"
      LEFT JOIN "stars" s ON ms."starId" = s.id
      LEFT JOIN "movie_genres" mg ON m.id = mg."movieId"
      LEFT JOIN "genres" g ON mg."genreId" = g.id
      WHERE (
        -- 全文搜索匹配
        (
          setweight(to_tsvector('simple', COALESCE(m.id, '')), 'A') ||
          setweight(to_tsvector('simple', COALESCE(m.title, '')), 'B') ||
          setweight(to_tsvector('simple', COALESCE(m.description, '')), 'C')
        ) @@ plainto_tsquery('simple', $1)
        OR
        -- 演员名称匹配
        EXISTS (
          SELECT 1 FROM "movie_stars" ms2
          JOIN "stars" s2 ON ms2."starId" = s2.id
          WHERE ms2."movieId" = m.id
          AND s2.name ILIKE '%' || $1 || '%'
        )
        OR
        -- 分类名称匹配
        EXISTS (
          SELECT 1 FROM "movie_genres" mg2
          JOIN "genres" g2 ON mg2."genreId" = g2.id
          WHERE mg2."movieId" = m.id
          AND g2.name ILIKE '%' || $1 || '%'
        )
      )
    `;

    const params = [searchQuery];
    let paramIndex = 2;

    // 添加分类筛选
    if (category) {
      baseQuery += ` AND EXISTS (
        SELECT 1 FROM "movie_genres" mg3
        JOIN "genres" g3 ON mg3."genreId" = g3.id
        WHERE mg3."movieId" = m.id
        AND g3.name ILIKE '%' || $${paramIndex} || '%'
      )`;
      params.push(category);
      paramIndex++;
    }

    // 添加演员筛选
    if (star) {
      baseQuery += ` AND EXISTS (
        SELECT 1 FROM "movie_stars" ms3
        JOIN "stars" s3 ON ms3."starId" = s3.id
        WHERE ms3."movieId" = m.id
        AND s3.name ILIKE '%' || $${paramIndex} || '%'
      )`;
      params.push(star);
      paramIndex++;
    }

    // 添加分组和排序
    baseQuery += ` GROUP BY m.id, m.title, m."localImg", m.img, m.date, m."videoLength", m.description, m."createdAt", m."updatedAt"`;

    if (sortBy === 'relevance') {
      baseQuery += ` ORDER BY rank DESC, m."createdAt" DESC`;
    } else if (sortBy === 'date') {
      baseQuery += ` ORDER BY m."createdAt" DESC`;
    } else {
      baseQuery += ` ORDER BY m.title ASC`;
    }

    baseQuery += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit.toString(), offset.toString());

    const searchResults = await prisma.$queryRawUnsafe<MovieResult[]>(baseQuery, ...params);

    // 为搜索结果添加统计数据
    const movieIds = searchResults.map(movie => movie.id);
    const statsMap = await getMoviesStats(movieIds);

    const searchResultsWithStats = searchResults.map(movie => {
      const stats = statsMap.get(movie.id) || { likes: 0, favorites: 0, views: 0 };

      return {
        ...movie,
        views: stats.views,
        likes: stats.likes,
        favorites: stats.favorites
      };
    });

    // 构建计数查询
    let countQuery = `
      SELECT COUNT(DISTINCT m.id) as count
      FROM "movies" m
      LEFT JOIN "movie_stars" ms ON m.id = ms."movieId"
      LEFT JOIN "stars" s ON ms."starId" = s.id
      LEFT JOIN "movie_genres" mg ON m.id = mg."movieId"
      LEFT JOIN "genres" g ON mg."genreId" = g.id
      WHERE (
        (
          setweight(to_tsvector('simple', COALESCE(m.id, '')), 'A') ||
          setweight(to_tsvector('simple', COALESCE(m.title, '')), 'B') ||
          setweight(to_tsvector('simple', COALESCE(m.description, '')), 'C')
        ) @@ plainto_tsquery('simple', $1)
        OR
        EXISTS (
          SELECT 1 FROM "movie_stars" ms2
          JOIN "stars" s2 ON ms2."starId" = s2.id
          WHERE ms2."movieId" = m.id
          AND s2.name ILIKE '%' || $1 || '%'
        )
        OR
        EXISTS (
          SELECT 1 FROM "movie_genres" mg2
          JOIN "genres" g2 ON mg2."genreId" = g2.id
          WHERE mg2."movieId" = m.id
          AND g2.name ILIKE '%' || $1 || '%'
        )
      )
    `;

    const countParams = [searchQuery];
    let countParamIndex = 2;

    // 添加分类筛选
    if (category) {
      countQuery += ` AND EXISTS (
        SELECT 1 FROM "movie_genres" mg3
        JOIN "genres" g3 ON mg3."genreId" = g3.id
        WHERE mg3."movieId" = m.id
        AND g3.name ILIKE '%' || $${countParamIndex} || '%'
      )`;
      countParams.push(category);
      countParamIndex++;
    }

    // 添加演员筛选
    if (star) {
      countQuery += ` AND EXISTS (
        SELECT 1 FROM "movie_stars" ms3
        JOIN "stars" s3 ON ms3."starId" = s3.id
        WHERE ms3."movieId" = m.id
        AND s3.name ILIKE '%' || $${countParamIndex} || '%'
      )`;
      countParams.push(star);
    }

    const countResult = await prisma.$queryRawUnsafe<[{ count: bigint }]>(countQuery, ...countParams);

    const total = Number(countResult[0]?.count || 0);

    // 获取搜索建议
    const suggestions = await getSearchSuggestions(searchQuery);

    return {
      movies: searchResultsWithStats,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      query: searchQuery,
      category,
      star,
      suggestions
    };

  } catch (error) {
    console.error('全文搜索失败:', error);
    // 降级到传统搜索
    return await performFallbackSearch(searchQuery, options);
  }
}

/**
 * 执行筛选搜索（不包含文本查询）
 */
async function performFilterSearch(options: Omit<SearchOptions, 'query'>): Promise<SearchResult> {
  const { category, star, page = 1, limit = 20, sortBy = 'date', sortOrder = 'desc' } = options;
  const skip = (page - 1) * limit;

  const whereConditions: {
    AND: Array<{
      genres?: {
        some: {
          genre: {
            name: { contains: string; mode: 'insensitive' };
          };
        };
      };
      stars?: {
        some: {
          star: {
            name: { contains: string; mode: 'insensitive' };
          };
        };
      };
      id?: { not: string };
    }>;
  } = {
    AND: []
  };

  // 分类筛选
  if (category) {
    whereConditions.AND.push({
      genres: {
        some: {
          genre: {
            name: {
              contains: category,
              mode: 'insensitive'
            }
          }
        }
      }
    });
  }

  // 演员筛选
  if (star) {
    whereConditions.AND.push({
      stars: {
        some: {
          star: {
            name: {
              contains: star,
              mode: 'insensitive'
            }
          }
        }
      }
    });
  }

  // 如果没有筛选条件，返回最新影片
  if (whereConditions.AND.length === 0) {
    whereConditions.AND.push({
      id: {
        not: ''
      }
    });
  }

  const orderBy = sortBy === 'date' ? { createdAt: sortOrder as 'asc' | 'desc' } :
                  sortBy === 'title' ? { title: sortOrder as 'asc' | 'desc' } :
                  { createdAt: 'desc' as const };

  const [movies, total] = await Promise.all([
    prisma.movie.findMany({
      where: whereConditions,
      skip,
      take: limit,
      include: {
        director: true,
        producer: true,
        publisher: true,
        series: true,
        stars: {
          include: {
            star: true
          }
        },
        genres: {
          include: {
            genre: true
          }
        },
        samples: true,
        magnets: true
      },
      orderBy
    }),
    prisma.movie.count({
      where: whereConditions
    })
  ]);

  // 转换数据格式
  const movieIds = movies.map(movie => movie.id);
  const statsMap = await getMoviesStats(movieIds);

  const formattedMovies = movies.map(movie => {
    const stats = statsMap.get(movie.id) || { likes: 0, favorites: 0, views: 0 };

    return {
      ...movie,
      views: stats.views,
      likes: stats.likes,
      favorites: stats.favorites,
      stars: movie.stars.map(ms => ({
        id: ms.star.id,
        name: ms.star.name,
        localAvatar: ms.star.localAvatar
      })),
      genres: movie.genres.map(mg => ({
        id: mg.genre.id,
        name: mg.genre.name
      })),
      samples: movie.samples.map(sample => ({
        id: sample.id,
        alt: sample.alt,
        localSrc: sample.localSrc
      })),
      magnets: movie.magnets.map(magnet => ({
        id: magnet.id,
        link: magnet.link,
        isHD: magnet.isHD,
        title: magnet.title,
        size: magnet.size,
        hasSubtitle: magnet.hasSubtitle
      }))
    };
  });

  return {
    movies: formattedMovies,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    category,
    star
  };
}

/**
 * 降级搜索（当全文搜索失败时使用）
 */
async function performFallbackSearch(
  searchQuery: string,
  options: Omit<SearchOptions, 'query'>
): Promise<SearchResult> {
  const { category, star, page = 1, limit = 20 } = options;
  const skip = (page - 1) * limit;

  const whereConditions: {
    AND: Array<{
      OR?: Array<{
        title?: { contains: string; mode: 'insensitive' };
        id?: { contains: string; mode: 'insensitive' };
      }>;
      genres?: {
        some: {
          genre: {
            name: { contains: string; mode: 'insensitive' };
          };
        };
      };
      stars?: {
        some: {
          star: {
            name: { contains: string; mode: 'insensitive' };
          };
        };
      };
    }>;
  } = {
    AND: [{
      OR: [
        {
          title: {
            contains: searchQuery,
            mode: 'insensitive'
          }
        },
        {
          id: {
            contains: searchQuery,
            mode: 'insensitive'
          }
        }
      ]
    }]
  };

  // 添加筛选条件
  if (category) {
    whereConditions.AND.push({
      genres: {
        some: {
          genre: {
            name: {
              contains: category,
              mode: 'insensitive'
            }
          }
        }
      }
    });
  }

  if (star) {
    whereConditions.AND.push({
      stars: {
        some: {
          star: {
            name: {
              contains: star,
              mode: 'insensitive'
            }
          }
        }
      }
    });
  }

  const [movies, total] = await Promise.all([
    prisma.movie.findMany({
      where: whereConditions,
      skip,
      take: limit,
      include: {
        director: true,
        producer: true,
        publisher: true,
        series: true,
        stars: {
          include: {
            star: true
          }
        },
        genres: {
          include: {
            genre: true
          }
        },
        samples: true,
        magnets: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    }),
    prisma.movie.count({
      where: whereConditions
    })
  ]);

  // 转换数据格式（与上面相同的逻辑）
  const movieIds = movies.map(movie => movie.id);
  const statsMap = await getMoviesStats(movieIds);

  const formattedMovies = movies.map(movie => {
    const stats = statsMap.get(movie.id) || { likes: 0, favorites: 0, views: 0 };

    return {
      ...movie,
      views: stats.views,
      likes: stats.likes,
      favorites: stats.favorites,
      stars: movie.stars.map(ms => ({
        id: ms.star.id,
        name: ms.star.name,
        localAvatar: ms.star.localAvatar
      })),
      genres: movie.genres.map(mg => ({
        id: mg.genre.id,
        name: mg.genre.name
      })),
      samples: movie.samples.map(sample => ({
        id: sample.id,
        alt: sample.alt,
        localSrc: sample.localSrc
      })),
      magnets: movie.magnets.map(magnet => ({
        id: magnet.id,
        link: magnet.link,
        isHD: magnet.isHD,
        title: magnet.title,
        size: magnet.size,
        hasSubtitle: magnet.hasSubtitle
      }))
    };
  });

  return {
    movies: formattedMovies,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    query: searchQuery,
    category,
    star
  };
}

/**
 * 获取搜索建议
 */
export async function getSearchSuggestions(query: string, limit: number = 10): Promise<SearchSuggestion[]> {
  if (!query.trim()) return [];

  try {
    // 获取演员建议
    const starSuggestions = await prisma.$queryRaw<SearchSuggestion[]>`
      SELECT
        s.name as text,
        'star'::text as type,
        COUNT(ms."movieId")::bigint as count
      FROM "stars" s
      LEFT JOIN "movie_stars" ms ON s.id = ms."starId"
      WHERE s.name ILIKE '%' || ${query} || '%'
      GROUP BY s.id, s.name
      ORDER BY count DESC, s.name
      LIMIT ${Math.ceil(limit / 2)}
    `;

    // 获取分类建议
    const genreSuggestions = await prisma.$queryRaw<SearchSuggestion[]>`
      SELECT
        g.name as text,
        'genre'::text as type,
        COUNT(mg."movieId")::bigint as count
      FROM "genres" g
      LEFT JOIN "movie_genres" mg ON g.id = mg."genreId"
      WHERE g.name ILIKE '%' || ${query} || '%'
      GROUP BY g.id, g.name
      ORDER BY count DESC, g.name
      LIMIT ${Math.floor(limit / 2)}
    `;

    // 合并并返回建议
    return [...starSuggestions, ...genreSuggestions];
  } catch (error) {
    console.error('获取搜索建议失败:', error);
    return [];
  }
}

/**
 * API错误处理包装器
 * 为API路由提供统一的错误处理和日志记录
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from './logger';
import { errorHandler, ErrorContext } from './error-handler';
import { ErrorCode, ErrorFactory } from './error-types';

export interface ApiHandler {
  (request: NextRequest, context?: { params?: Record<string, string> }): Promise<NextResponse>;
}

export interface ApiHandlerOptions {
  requireAuth?: boolean;
  adminOnly?: boolean;
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
  validation?: {
    body?: (body: unknown) => boolean;
    query?: (query: URLSearchParams) => boolean;
  };
}

/**
 * 包装API处理器，提供统一的错误处理、日志记录和验证
 */
export function withApiHandler(
  handler: <PERSON>piHand<PERSON>,
  options: ApiHandlerOptions = {}
): <PERSON>pi<PERSON>and<PERSON> {
  return async (request: NextRequest, context?: { params?: Record<string, string> }) => {
    const startTime = Date.now();
    const requestId = request.headers.get('x-request-id') || logger.generateRequestId();
    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const method = request.method;
    const path = request.nextUrl.pathname;

    const errorContext: ErrorContext = {
      requestId,
      ip,
      userAgent,
      method,
      path,
      query: Object.fromEntries(request.nextUrl.searchParams.entries())
    };

    try {
      // 记录请求开始
      logger.debug(`API请求开始: ${method} ${path}`, {
        requestId,
        ip,
        userAgent,
        query: errorContext.query,
        params: context?.params
      });

      // 身份验证检查
      if (options.requireAuth) {
        const authResult = await validateAuth(request);
        if (!authResult.valid) {
          throw ErrorFactory.createError(
            ErrorCode.UNAUTHORIZED,
            authResult.message || '需要身份验证'
          );
        }
        errorContext.userId = authResult.userId;
      }

      // 管理员权限检查
      if (options.adminOnly) {
        const adminResult = await validateAdmin(request);
        if (!adminResult.valid) {
          throw ErrorFactory.createError(
            ErrorCode.FORBIDDEN,
            adminResult.message || '需要管理员权限'
          );
        }
      }

      // 请求体验证
      if (options.validation?.body && ['POST', 'PUT', 'PATCH'].includes(method)) {
        try {
          const body = await request.json();
          errorContext.body = body;
          
          if (!options.validation.body(body)) {
            throw ErrorFactory.createError(
              ErrorCode.VALIDATION_ERROR,
              '请求体验证失败'
            );
          }
        } catch (error) {
          if (error instanceof SyntaxError) {
            throw ErrorFactory.createError(
              ErrorCode.VALIDATION_ERROR,
              '请求体格式错误'
            );
          }
          throw error;
        }
      }

      // 查询参数验证
      if (options.validation?.query) {
        if (!options.validation.query(request.nextUrl.searchParams)) {
          throw ErrorFactory.createError(
            ErrorCode.VALIDATION_ERROR,
            '查询参数验证失败'
          );
        }
      }

      // 执行处理器
      const response = await handler(request, context);
      const duration = Date.now() - startTime;

      // 记录成功响应
      logger.http(
        method,
        path,
        response.status,
        duration,
        requestId,
        errorContext.userId,
        ip,
        userAgent
      );

      // 添加响应头
      response.headers.set('x-request-id', requestId);
      response.headers.set('x-response-time', `${duration}ms`);

      return response;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      // 记录错误
      logger.error(`API请求失败: ${method} ${path}`, error as Error, {
        requestId,
        duration,
        ...errorContext
      });

      // 处理错误并返回响应
      const errorResponse = errorHandler.handleApiError(error as Error, errorContext);
      
      // 添加响应头
      errorResponse.headers.set('x-request-id', requestId);
      errorResponse.headers.set('x-response-time', `${duration}ms`);

      return errorResponse;
    }
  };
}

/**
 * 验证用户身份
 */
async function validateAuth(request: NextRequest): Promise<{
  valid: boolean;
  userId?: string;
  message?: string;
}> {
  try {
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return { valid: false, message: '缺少认证令牌' };
    }

    // 这里应该验证JWT令牌
    // 简化实现，实际应该使用JWT验证
    if (token === 'invalid') {
      return { valid: false, message: '无效的认证令牌' };
    }

    return { valid: true, userId: 'user-id' };
  } catch (error) {
    logger.error('身份验证失败', error as Error);
    return { valid: false, message: '身份验证失败' };
  }
}

/**
 * 验证管理员权限
 */
async function validateAdmin(request: NextRequest): Promise<{
  valid: boolean;
  message?: string;
}> {
  try {
    const authHeader = request.headers.get('authorization');
    const authCookie = request.cookies.get('admin-token')?.value;
    const token = authHeader?.replace('Bearer ', '') || authCookie;
    const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'admin123';

    if (!token || token !== ADMIN_TOKEN) {
      return { valid: false, message: '需要管理员权限' };
    }

    return { valid: true };
  } catch (error) {
    logger.error('管理员权限验证失败', error as Error);
    return { valid: false, message: '权限验证失败' };
  }
}

/**
 * 创建标准化的成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  status: number = 200
): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  }, { status });
}

/**
 * 创建标准化的错误响应
 */
export function createErrorResponse(
  message: string,
  code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
  status: number = 500,
  details?: unknown
): NextResponse {
  return NextResponse.json({
    success: false,
    error: {
      code,
      message,
      timestamp: new Date().toISOString()
    },
    details: process.env.NODE_ENV === 'development' ? details : undefined
  }, { status });
}

/**
 * 数据库操作包装器
 */
export async function withDatabaseOperation<T>(
  operation: () => Promise<T>,
  operationName: string,
  tableName?: string
): Promise<T> {
  const startTime = Date.now();
  
  try {
    const result = await operation();
    const duration = Date.now() - startTime;
    
    logger.database(operationName, tableName || 'unknown', duration);
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.database(operationName, tableName || 'unknown', duration, error as Error);
    
    throw error;
  }
}

/**
 * 缓存操作包装器
 */
export async function withCacheOperation<T>(
  operation: () => Promise<T>,
  operationType: 'GET' | 'SET' | 'DEL',
  key: string
): Promise<T> {
  const startTime = Date.now();
  
  try {
    const result = await operation();
    const duration = Date.now() - startTime;
    
    logger.cache(operationType === 'GET' ? 'HIT' : operationType, key, duration);
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.cache('ERROR', key, duration, error as Error);
    
    throw error;
  }
}

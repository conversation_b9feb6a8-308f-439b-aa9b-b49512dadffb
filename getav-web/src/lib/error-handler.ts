/**
 * 全局错误处理系统
 * 提供统一的错误处理、记录和响应机制
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from './logger';
import { ErrorCode, ErrorSeverity, DetailedError, ErrorFactory } from './error-types';

export interface ErrorContext {
  requestId?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  path?: string;
  query?: Record<string, unknown>;
  body?: unknown;
}

export interface ErrorHandlerOptions {
  includeStack: boolean;
  logErrors: boolean;
  notifyOnCritical: boolean;
}

/**
 * 全局错误处理器类
 */
export class GlobalErrorHandler {
  private options: ErrorHandlerOptions;

  constructor(options?: Partial<ErrorHandlerOptions>) {
    this.options = {
      includeStack: process.env.NODE_ENV === 'development',
      logErrors: true,
      notifyOnCritical: true,
      ...options
    };
  }

  /**
   * 处理API路由错误
   */
  handleApiError(
    error: Error | DetailedError,
    context: ErrorContext = {}
  ): NextResponse {
    const detailedError = this.normalizeError(error, context);
    
    // 记录错误日志
    if (this.options.logErrors) {
      this.logError(detailedError, context);
    }

    // 发送通知（关键错误）
    if (this.options.notifyOnCritical && detailedError.severity === ErrorSeverity.CRITICAL) {
      this.notifyCriticalError(detailedError, context);
    }

    // 构建响应
    const response = this.buildErrorResponse(detailedError);
    const statusCode = this.getStatusCode(detailedError);

    return NextResponse.json(response, { status: statusCode });
  }

  /**
   * 处理页面错误
   */
  handlePageError(
    error: Error | DetailedError,
    context: ErrorContext = {}
  ): NextResponse {
    const detailedError = this.normalizeError(error, context);
    
    // 记录错误日志
    if (this.options.logErrors) {
      this.logError(detailedError, context);
    }

    // 重定向到错误页面
    const statusCode = this.getStatusCode(detailedError);
    const errorPageUrl = `/error?code=${statusCode}&message=${encodeURIComponent(detailedError.message)}`;
    
    return NextResponse.redirect(new URL(errorPageUrl, context.path || '/'));
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(error: Error | DetailedError, context: ErrorContext): DetailedError {
    if (this.isDetailedError(error)) {
      return {
        ...error,
        context: { ...error.context, ...context }
      };
    }

    // 根据错误类型确定错误代码和严重性
    let code = ErrorCode.UNKNOWN_ERROR;
    let severity = ErrorSeverity.MEDIUM;

    if (error.name === 'ValidationError') {
      code = ErrorCode.VALIDATION_ERROR;
      severity = ErrorSeverity.LOW;
    } else if (error.name === 'PrismaClientKnownRequestError') {
      code = ErrorCode.DATABASE_ERROR;
      severity = ErrorSeverity.HIGH;
    } else if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      code = ErrorCode.UNAUTHORIZED;
      severity = ErrorSeverity.LOW;
    } else if (error.message.includes('ECONNREFUSED') || error.message.includes('timeout')) {
      code = ErrorCode.NETWORK_ERROR;
      severity = ErrorSeverity.HIGH;
    } else if (error.message.includes('permission') || error.message.includes('forbidden')) {
      code = ErrorCode.FORBIDDEN;
      severity = ErrorSeverity.MEDIUM;
    }

    return ErrorFactory.createDetailedError(
      code,
      error.message,
      severity,
      {
        ...context,
        originalError: error.name,
        stack: this.options.includeStack ? error.stack : undefined
      }
    );
  }

  /**
   * 检查是否为详细错误对象
   */
  private isDetailedError(error: unknown): error is DetailedError {
    return !!(error &&
      typeof error === 'object' &&
      error !== null &&
      'severity' in error &&
      'code' in error);
  }

  /**
   * 记录错误日志
   */
  private logError(error: DetailedError, context: ErrorContext): void {
    const logContext = {
      errorCode: error.code,
      severity: error.severity,
      requestId: context.requestId,
      userId: context.userId,
      method: context.method,
      path: context.path,
      ip: context.ip,
      userAgent: context.userAgent,
      ...error.context
    };

    switch (error.severity) {
      case ErrorSeverity.LOW:
        logger.info(`错误: ${error.message}`, logContext);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn(`错误: ${error.message}`, logContext);
        break;
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        logger.error(`严重错误: ${error.message}`, new Error(error.message), logContext);
        break;
    }
  }

  /**
   * 发送关键错误通知
   */
  private async notifyCriticalError(error: DetailedError, context: ErrorContext): Promise<void> {
    try {
      // 这里可以集成邮件、Slack、钉钉等通知服务
      logger.fatal(`关键错误通知: ${error.message}`, new Error(error.message), {
        errorCode: error.code,
        context: error.context,
        requestContext: context
      });

      // 示例：发送邮件通知（需要配置邮件服务）
      // await this.sendEmailNotification(error, context);
      
    } catch (notificationError) {
      logger.error('发送错误通知失败', notificationError as Error);
    }
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(error: DetailedError): Record<string, unknown> {
    const response: Record<string, unknown> = {
      success: false,
      error: {
        code: error.code,
        message: error.message,
        timestamp: error.timestamp
      }
    };

    // 开发环境返回详细信息
    if (this.options.includeStack) {
      response.debug = {
        severity: error.severity,
        context: error.context,
        stack: error.stack
      };
    }

    return response;
  }

  /**
   * 获取HTTP状态码
   */
  private getStatusCode(error: DetailedError): number {
    switch (error.code) {
      case ErrorCode.VALIDATION_ERROR:
      case ErrorCode.INVALID_REQUEST:
        return 400;
      case ErrorCode.UNAUTHORIZED:
      case ErrorCode.TOKEN_EXPIRED:
      case ErrorCode.INVALID_CREDENTIALS:
        return 401;
      case ErrorCode.FORBIDDEN:
        return 403;
      case ErrorCode.NOT_FOUND:
      case ErrorCode.INVALID_MOVIE_ID:
      case ErrorCode.INVALID_STAR_ID:
        return 404;
      case ErrorCode.RATE_LIMIT_EXCEEDED:
        return 429;
      case ErrorCode.DATABASE_ERROR:
      case ErrorCode.NETWORK_ERROR:
      case ErrorCode.UNKNOWN_ERROR:
      default:
        return 500;
    }
  }
}

/**
 * 异步错误处理包装器
 */
export function withErrorHandler<T extends unknown[], R>(
  handler: (...args: T) => Promise<R>,
  errorHandler?: GlobalErrorHandler
): (...args: T) => Promise<R | NextResponse> {
  const globalHandler = errorHandler || new GlobalErrorHandler();
  
  return async (...args: T): Promise<R | NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      // 尝试从参数中提取请求信息
      const request = args.find(arg => arg && typeof arg === 'object' && 'nextUrl' in arg) as NextRequest | undefined;
      
      const context: ErrorContext = request ? {
        method: request.method,
        path: request.nextUrl.pathname,
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        query: Object.fromEntries(request.nextUrl.searchParams.entries())
      } : {};

      return globalHandler.handleApiError(error as Error, context);
    }
  };
}

/**
 * 创建错误处理中间件
 */
export function createErrorMiddleware(options?: Partial<ErrorHandlerOptions>) {
  const handler = new GlobalErrorHandler(options);
  
  return {
    handleApiError: (error: Error, context?: ErrorContext) => 
      handler.handleApiError(error, context),
    handlePageError: (error: Error, context?: ErrorContext) => 
      handler.handlePageError(error, context),
    withErrorHandler: <T extends unknown[], R>(fn: (...args: T) => Promise<R>) =>
      withErrorHandler(fn, handler)
  };
}

// 导出默认实例
export const errorHandler = new GlobalErrorHandler();

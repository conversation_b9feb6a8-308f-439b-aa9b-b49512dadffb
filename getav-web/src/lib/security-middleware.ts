/**
 * 安全中间件
 * 提供统一的安全检查和保护功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from './logger';
import { 
  ipWhitelistConfig, 
  csrfConfig, 
  securityLogConfig, 
  requestValidationConfig 
} from './security-config';

// 限流存储 (简单内存存储，生产环境建议使用Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * 简单的内存限流实现
 */
export function simpleRateLimit(
  key: string, 
  windowMs: number, 
  maxRequests: number
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // 新的时间窗口
    const resetTime = now + windowMs;
    rateLimitStore.set(key, { count: 1, resetTime });
    return { allowed: true, remaining: maxRequests - 1, resetTime };
  }

  if (record.count >= maxRequests) {
    // 超过限制
    return { allowed: false, remaining: 0, resetTime: record.resetTime };
  }

  // 增加计数
  record.count++;
  rateLimitStore.set(key, record);
  return { allowed: true, remaining: maxRequests - record.count, resetTime: record.resetTime };
}

/**
 * API限流中间件
 */
export function apiRateLimit(
  request: NextRequest,
  windowMs: number = 15 * 60 * 1000, // 15分钟
  maxRequests: number = 100
): NextResponse | null {
  const ip = ipWhitelistConfig.getRealIP(request);
  const key = `rate_limit:${ip}`;
  
  const result = simpleRateLimit(key, windowMs, maxRequests);
  
  if (!result.allowed) {
    logger.warn('API限流触发', {
      ip,
      path: request.nextUrl.pathname,
      userAgent: request.headers.get('user-agent'),
      event: securityLogConfig.events.RATE_LIMIT_EXCEEDED,
      severity: securityLogConfig.severity.MEDIUM
    });

    return NextResponse.json(
      {
        success: false,
        error: '请求过于频繁，请稍后再试',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
      },
      { 
        status: 429,
        headers: {
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': result.resetTime.toString(),
          'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString()
        }
      }
    );
  }

  return null; // 允许通过
}

/**
 * 管理员IP白名单检查
 */
export function adminIPWhitelist(request: NextRequest): NextResponse | null {
  const ip = ipWhitelistConfig.getRealIP(request);
  
  // 开发环境跳过检查
  if (process.env.NODE_ENV === 'development') {
    return null;
  }

  if (!ipWhitelistConfig.isWhitelisted(ip, ipWhitelistConfig.adminIPs)) {
    logger.warn('管理员IP白名单检查失败', {
      ip,
      path: request.nextUrl.pathname,
      userAgent: request.headers.get('user-agent'),
      event: securityLogConfig.events.UNAUTHORIZED_ACCESS,
      severity: securityLogConfig.severity.HIGH
    });

    return NextResponse.json(
      {
        success: false,
        error: '访问被拒绝',
        code: 'IP_NOT_WHITELISTED'
      },
      { status: 403 }
    );
  }

  return null;
}

/**
 * CSRF保护检查
 */
export function csrfProtection(request: NextRequest): NextResponse | null {
  const method = request.method;
  const pathname = request.nextUrl.pathname;

  // 跳过不需要CSRF保护的方法和路径
  if (!csrfConfig.protectedMethods.includes(method) || 
      csrfConfig.skipPaths.some(path => pathname.startsWith(path))) {
    return null;
  }

  const csrfToken = request.headers.get('x-csrf-token') || 
                   request.headers.get('csrf-token');
  const sessionToken = request.cookies.get('csrf-token')?.value;

  if (!csrfToken || !sessionToken || !csrfConfig.validateToken(csrfToken, sessionToken)) {
    logger.warn('CSRF保护检查失败', {
      ip: ipWhitelistConfig.getRealIP(request),
      path: pathname,
      method,
      hasToken: !!csrfToken,
      hasSessionToken: !!sessionToken,
      event: securityLogConfig.events.CSRF_ATTACK,
      severity: securityLogConfig.severity.HIGH
    });

    return NextResponse.json(
      {
        success: false,
        error: 'CSRF令牌无效',
        code: 'CSRF_TOKEN_INVALID'
      },
      { status: 403 }
    );
  }

  return null;
}

/**
 * 请求验证中间件
 */
export function requestValidation(request: NextRequest): NextResponse | null {
  const url = request.nextUrl;
  const userAgent = request.headers.get('user-agent') || '';

  // 检查URL长度
  if (url.href.length > requestValidationConfig.maxUrlLength) {
    logger.warn('请求URL过长', {
      ip: ipWhitelistConfig.getRealIP(request),
      urlLength: url.href.length,
      maxLength: requestValidationConfig.maxUrlLength,
      event: securityLogConfig.events.SUSPICIOUS_REQUEST,
      severity: securityLogConfig.severity.MEDIUM
    });

    return NextResponse.json(
      {
        success: false,
        error: '请求URL过长',
        code: 'URL_TOO_LONG'
      },
      { status: 400 }
    );
  }

  // 检查头部数量
  const headerCount = Array.from(request.headers.keys()).length;
  if (headerCount > requestValidationConfig.maxHeaders) {
    logger.warn('请求头部过多', {
      ip: ipWhitelistConfig.getRealIP(request),
      headerCount,
      maxHeaders: requestValidationConfig.maxHeaders,
      event: securityLogConfig.events.SUSPICIOUS_REQUEST,
      severity: securityLogConfig.severity.MEDIUM
    });

    return NextResponse.json(
      {
        success: false,
        error: '请求头部过多',
        code: 'TOO_MANY_HEADERS'
      },
      { status: 400 }
    );
  }

  // 检查用户代理
  const isBlockedUserAgent = requestValidationConfig.blockedUserAgents.some(
    pattern => pattern.test(userAgent)
  );

  if (isBlockedUserAgent) {
    logger.warn('被阻止的用户代理', {
      ip: ipWhitelistConfig.getRealIP(request),
      userAgent,
      event: securityLogConfig.events.SUSPICIOUS_REQUEST,
      severity: securityLogConfig.severity.LOW
    });

    return NextResponse.json(
      {
        success: false,
        error: '访问被拒绝',
        code: 'BLOCKED_USER_AGENT'
      },
      { status: 403 }
    );
  }

  // 检查可疑模式
  const fullUrl = url.href;
  const hasSuspiciousPattern = requestValidationConfig.suspiciousPatterns.some(
    pattern => pattern.test(fullUrl)
  );

  if (hasSuspiciousPattern) {
    logger.warn('检测到可疑请求模式', {
      ip: ipWhitelistConfig.getRealIP(request),
      url: fullUrl,
      userAgent,
      event: securityLogConfig.events.SUSPICIOUS_REQUEST,
      severity: securityLogConfig.severity.HIGH
    });

    return NextResponse.json(
      {
        success: false,
        error: '请求被拒绝',
        code: 'SUSPICIOUS_REQUEST'
      },
      { status: 400 }
    );
  }

  return null;
}

/**
 * 安全头部设置
 */
export function setSecurityHeaders(response: NextResponse): NextResponse {
  // 基本安全头部
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // 权限策略
  response.headers.set('Permissions-Policy', 
    'camera=(), microphone=(), geolocation=(), payment=()');

  // 内容安全策略 (简化版)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 
      'max-age=31536000; includeSubDomains; preload');
  }

  return response;
}

/**
 * 综合安全检查中间件
 */
export function securityCheck(
  request: NextRequest,
  options: {
    enableRateLimit?: boolean;
    enableCSRF?: boolean;
    enableIPWhitelist?: boolean;
    enableRequestValidation?: boolean;
    rateLimitConfig?: { windowMs: number; maxRequests: number };
  } = {}
): NextResponse | null {
  const {
    enableRateLimit = true,
    enableCSRF = false, // 默认关闭，避免影响现有功能
    enableIPWhitelist = false, // 默认关闭，仅在需要时启用
    enableRequestValidation = true,
    rateLimitConfig = { windowMs: 15 * 60 * 1000, maxRequests: 100 }
  } = options;

  // 请求验证
  if (enableRequestValidation) {
    const validationResult = requestValidation(request);
    if (validationResult) return validationResult;
  }

  // IP白名单检查 (仅对管理员路径)
  if (enableIPWhitelist) {
    const ipResult = adminIPWhitelist(request);
    if (ipResult) return ipResult;
  }

  // 限流检查
  if (enableRateLimit) {
    const rateLimitResult = apiRateLimit(
      request, 
      rateLimitConfig.windowMs, 
      rateLimitConfig.maxRequests
    );
    if (rateLimitResult) return rateLimitResult;
  }

  // CSRF保护
  if (enableCSRF) {
    const csrfResult = csrfProtection(request);
    if (csrfResult) return csrfResult;
  }

  return null; // 所有检查通过
}

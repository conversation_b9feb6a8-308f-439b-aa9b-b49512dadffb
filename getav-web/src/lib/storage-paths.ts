/**
 * 文件存储路径管理模块
 * 统一管理所有文件存储路径，确保路径一致性和可维护性
 */

import * as path from 'path';
import * as fs from 'fs';

// 基础存储目录
export const STORAGE_BASE_DIR = path.join(process.cwd(), 'public');

// 图片存储目录
export const IMAGE_BASE_DIR = path.join(STORAGE_BASE_DIR, 'images', 'getav');

// M3U8存储目录
export const M3U8_BASE_DIR = path.join(STORAGE_BASE_DIR, 'm3u8');
export const M3U8_PLAYLISTS_DIR = path.join(M3U8_BASE_DIR, 'playlists');
export const M3U8_SEGMENTS_DIR = path.join(M3U8_BASE_DIR, 'segments');

// 公共URL路径前缀
export const PUBLIC_PATHS = {
  images: '/images/getav',
  m3u8: '/m3u8',
  playlists: '/m3u8/playlists',
  segments: '/m3u8/segments'
} as const;

/**
 * 存储目录类型
 */
export type StorageType = 'images' | 'm3u8' | 'playlists' | 'segments';

/**
 * 图片类型
 */
export type ImageType = 'cover' | 'actress' | 'samples';

/**
 * 确保目录存在
 */
export function ensureDirExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 获取存储目录的绝对路径
 */
export function getStorageDir(type: StorageType, subType?: ImageType): string {
  switch (type) {
    case 'images':
      const imageDir = subType ? path.join(IMAGE_BASE_DIR, subType) : IMAGE_BASE_DIR;
      ensureDirExists(imageDir);
      return imageDir;
    
    case 'm3u8':
      ensureDirExists(M3U8_BASE_DIR);
      return M3U8_BASE_DIR;
    
    case 'playlists':
      ensureDirExists(M3U8_PLAYLISTS_DIR);
      return M3U8_PLAYLISTS_DIR;
    
    case 'segments':
      ensureDirExists(M3U8_SEGMENTS_DIR);
      return M3U8_SEGMENTS_DIR;
    
    default:
      throw new Error(`Unknown storage type: ${type}`);
  }
}

/**
 * 获取公共URL路径
 */
export function getPublicPath(type: StorageType, fileName: string, subType?: ImageType): string {
  switch (type) {
    case 'images':
      const basePath = subType ? `${PUBLIC_PATHS.images}/${subType}` : PUBLIC_PATHS.images;
      return `${basePath}/${fileName}`;
    
    case 'playlists':
      return `${PUBLIC_PATHS.playlists}/${fileName}`;
    
    case 'segments':
      return `${PUBLIC_PATHS.segments}/${fileName}`;
    
    case 'm3u8':
      return `${PUBLIC_PATHS.m3u8}/${fileName}`;
    
    default:
      throw new Error(`Unknown storage type: ${type}`);
  }
}

/**
 * 将绝对路径转换为相对于项目根目录的路径
 */
export function toRelativePath(absolutePath: string): string {
  const projectRoot = process.cwd();
  return path.relative(projectRoot, absolutePath);
}

/**
 * 将相对路径转换为绝对路径
 */
export function toAbsolutePath(relativePath: string): string {
  if (path.isAbsolute(relativePath)) {
    return relativePath;
  }
  return path.join(process.cwd(), relativePath);
}

/**
 * 检查路径是否在允许的存储目录内
 */
export function isPathAllowed(filePath: string): boolean {
  const absolutePath = toAbsolutePath(filePath);
  const allowedDirs = [
    IMAGE_BASE_DIR,
    M3U8_BASE_DIR
  ];
  
  return allowedDirs.some(allowedDir => {
    const normalizedPath = path.normalize(absolutePath);
    const normalizedAllowed = path.normalize(allowedDir);
    return normalizedPath.startsWith(normalizedAllowed);
  });
}

/**
 * 获取文件的存储信息
 */
export interface FileStorageInfo {
  absolutePath: string;
  relativePath: string;
  publicPath: string;
  directory: string;
  fileName: string;
  exists: boolean;
}

/**
 * 获取文件的完整存储信息
 */
export function getFileStorageInfo(type: StorageType, fileName: string, subType?: ImageType): FileStorageInfo {
  const directory = getStorageDir(type, subType);
  const absolutePath = path.join(directory, fileName);
  const relativePath = toRelativePath(absolutePath);
  const publicPath = getPublicPath(type, fileName, subType);
  const exists = fs.existsSync(absolutePath);
  
  return {
    absolutePath,
    relativePath,
    publicPath,
    directory,
    fileName,
    exists
  };
}

/**
 * 初始化所有存储目录
 */
export function initializeStorageDirectories(): void {
  console.log('正在初始化存储目录结构...');
  
  // 创建图片存储目录
  const imageTypes: ImageType[] = ['cover', 'actress', 'samples'];
  imageTypes.forEach(type => {
    getStorageDir('images', type);
  });
  
  // 创建M3U8存储目录
  getStorageDir('m3u8');
  getStorageDir('playlists');
  getStorageDir('segments');
  
  console.log('存储目录结构初始化完成');
  console.log(`图片目录: ${IMAGE_BASE_DIR}`);
  console.log(`M3U8目录: ${M3U8_BASE_DIR}`);
  console.log(`播放列表目录: ${M3U8_PLAYLISTS_DIR}`);
  console.log(`视频片段目录: ${M3U8_SEGMENTS_DIR}`);
}
/**
 * 缓存性能监控器
 * 监控缓存命中率、性能指标和使用统计
 */

import { logger } from './logger';
import { redis } from './redis';

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  averageResponseTime: number;
  memoryUsage: {
    used: string;
    peak: string;
    percentage: number;
  };
  keyCount: number;
  expiredKeys: number;
  evictedKeys: number;
}

export interface CacheOperation {
  key: string;
  operation: 'GET' | 'SET' | 'DEL' | 'HIT' | 'MISS';
  timestamp: number;
  duration: number;
  size?: number;
}

export class CacheMonitor {
  private operations: CacheOperation[] = [];
  private maxOperations = 1000; // 保留最近1000次操作
  private startTime = Date.now();

  /**
   * 记录缓存操作
   */
  recordOperation(operation: CacheOperation): void {
    this.operations.push(operation);
    
    // 保持操作记录在限制范围内
    if (this.operations.length > this.maxOperations) {
      this.operations = this.operations.slice(-this.maxOperations);
    }
  }

  /**
   * 获取缓存指标
   */
  async getMetrics(): Promise<CacheMetrics> {
    try {
      // 获取Redis信息
      const info = await redis.info('memory');
      const keyCount = await redis.dbsize();
      
      // 解析内存信息
      const memoryLines = info.split('\r\n');
      const usedMemory = this.extractValue(memoryLines, 'used_memory_human');
      const peakMemory = this.extractValue(memoryLines, 'used_memory_peak_human');
      const maxMemory = this.extractValue(memoryLines, 'maxmemory');
      const usedMemoryBytes = parseInt(this.extractValue(memoryLines, 'used_memory') || '0');
      
      // 计算内存使用百分比
      const memoryPercentage = maxMemory && parseInt(maxMemory) > 0 
        ? (usedMemoryBytes / parseInt(maxMemory)) * 100 
        : 0;

      // 计算命中率统计
      const recentOperations = this.operations.filter(
        op => Date.now() - op.timestamp < 3600000 // 最近1小时
      );
      
      const hits = recentOperations.filter(op => op.operation === 'HIT').length;
      const misses = recentOperations.filter(op => op.operation === 'MISS').length;
      const totalRequests = hits + misses;
      
      const hitRate = totalRequests > 0 ? (hits / totalRequests) * 100 : 0;
      const missRate = totalRequests > 0 ? (misses / totalRequests) * 100 : 0;
      
      // 计算平均响应时间
      const averageResponseTime = recentOperations.length > 0
        ? recentOperations.reduce((sum, op) => sum + op.duration, 0) / recentOperations.length
        : 0;

      // 获取过期和驱逐统计
      const statsInfo = await redis.info('stats');
      const statsLines = statsInfo.split('\r\n');
      const expiredKeys = parseInt(this.extractValue(statsLines, 'expired_keys') || '0');
      const evictedKeys = parseInt(this.extractValue(statsLines, 'evicted_keys') || '0');

      return {
        hitRate: Math.round(hitRate * 100) / 100,
        missRate: Math.round(missRate * 100) / 100,
        totalRequests,
        totalHits: hits,
        totalMisses: misses,
        averageResponseTime: Math.round(averageResponseTime * 100) / 100,
        memoryUsage: {
          used: usedMemory || '0B',
          peak: peakMemory || '0B',
          percentage: Math.round(memoryPercentage * 100) / 100
        },
        keyCount,
        expiredKeys,
        evictedKeys
      };

    } catch (error) {
      logger.error('获取缓存指标失败', error as Error);
      
      // 返回默认指标
      return {
        hitRate: 0,
        missRate: 0,
        totalRequests: 0,
        totalHits: 0,
        totalMisses: 0,
        averageResponseTime: 0,
        memoryUsage: {
          used: '0B',
          peak: '0B',
          percentage: 0
        },
        keyCount: 0,
        expiredKeys: 0,
        evictedKeys: 0
      };
    }
  }

  /**
   * 从Redis信息中提取值
   */
  private extractValue(lines: string[], key: string): string | null {
    const line = lines.find(line => line.startsWith(`${key}:`));
    return line ? line.split(':')[1]?.trim() : null;
  }

  /**
   * 获取热门缓存键
   */
  getHotKeys(limit: number = 10): Array<{
    key: string;
    accessCount: number;
    lastAccess: number;
  }> {
    const keyStats = new Map<string, { count: number; lastAccess: number }>();
    
    // 统计键的访问次数
    this.operations.forEach(op => {
      if (op.operation === 'GET' || op.operation === 'HIT') {
        const stats = keyStats.get(op.key) || { count: 0, lastAccess: 0 };
        stats.count++;
        stats.lastAccess = Math.max(stats.lastAccess, op.timestamp);
        keyStats.set(op.key, stats);
      }
    });

    // 按访问次数排序
    return Array.from(keyStats.entries())
      .map(([key, stats]) => ({
        key,
        accessCount: stats.count,
        lastAccess: stats.lastAccess
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit);
  }

  /**
   * 获取缓存性能趋势
   */
  getPerformanceTrend(intervalMinutes: number = 5): Array<{
    timestamp: number;
    hitRate: number;
    averageResponseTime: number;
    operationCount: number;
  }> {
    const now = Date.now();
    const intervalMs = intervalMinutes * 60 * 1000;
    const trends: Array<{
      timestamp: number;
      hitRate: number;
      averageResponseTime: number;
      operationCount: number;
    }> = [];

    // 生成时间段
    for (let time = now - (12 * intervalMs); time <= now; time += intervalMs) {
      const periodOps = this.operations.filter(
        op => op.timestamp >= time && op.timestamp < time + intervalMs
      );

      const hits = periodOps.filter(op => op.operation === 'HIT').length;
      const misses = periodOps.filter(op => op.operation === 'MISS').length;
      const total = hits + misses;
      
      const hitRate = total > 0 ? (hits / total) * 100 : 0;
      const averageResponseTime = periodOps.length > 0
        ? periodOps.reduce((sum, op) => sum + op.duration, 0) / periodOps.length
        : 0;

      trends.push({
        timestamp: time,
        hitRate: Math.round(hitRate * 100) / 100,
        averageResponseTime: Math.round(averageResponseTime * 100) / 100,
        operationCount: periodOps.length
      });
    }

    return trends;
  }

  /**
   * 检测缓存异常
   */
  detectAnomalies(): Array<{
    type: 'low_hit_rate' | 'high_response_time' | 'memory_pressure' | 'high_eviction';
    severity: 'low' | 'medium' | 'high';
    message: string;
    value: number;
    threshold: number;
  }> {
    const anomalies: Array<{
      type: 'low_hit_rate' | 'high_response_time' | 'memory_pressure' | 'high_eviction';
      severity: 'low' | 'medium' | 'high';
      message: string;
      value: number;
      threshold: number;
    }> = [];

    // 检查最近的操作
    const recentOps = this.operations.filter(
      op => Date.now() - op.timestamp < 300000 // 最近5分钟
    );

    if (recentOps.length > 0) {
      // 检查命中率
      const hits = recentOps.filter(op => op.operation === 'HIT').length;
      const total = recentOps.filter(op => ['HIT', 'MISS'].includes(op.operation)).length;
      const hitRate = total > 0 ? (hits / total) * 100 : 0;

      if (hitRate < 50 && total > 10) {
        anomalies.push({
          type: 'low_hit_rate',
          severity: hitRate < 30 ? 'high' : 'medium',
          message: `缓存命中率过低: ${hitRate.toFixed(1)}%`,
          value: hitRate,
          threshold: 50
        });
      }

      // 检查响应时间
      const avgResponseTime = recentOps.reduce((sum, op) => sum + op.duration, 0) / recentOps.length;
      if (avgResponseTime > 100) {
        anomalies.push({
          type: 'high_response_time',
          severity: avgResponseTime > 500 ? 'high' : 'medium',
          message: `缓存响应时间过高: ${avgResponseTime.toFixed(1)}ms`,
          value: avgResponseTime,
          threshold: 100
        });
      }
    }

    return anomalies;
  }

  /**
   * 生成缓存报告
   */
  async generateReport(): Promise<{
    summary: CacheMetrics;
    hotKeys: Array<{ key: string; accessCount: number; lastAccess: number }>;
    trends: Array<{ timestamp: number; hitRate: number; averageResponseTime: number; operationCount: number }>;
    anomalies: Array<{ type: string; severity: string; message: string; value: number; threshold: number }>;
    recommendations: string[];
  }> {
    const summary = await this.getMetrics();
    const hotKeys = this.getHotKeys(10);
    const trends = this.getPerformanceTrend(5);
    const anomalies = this.detectAnomalies();

    // 生成建议
    const recommendations: string[] = [];
    
    if (summary.hitRate < 70) {
      recommendations.push('考虑增加缓存预热频率或扩大预热范围');
    }
    
    if (summary.averageResponseTime > 50) {
      recommendations.push('检查Redis服务器性能或网络延迟');
    }
    
    if (summary.memoryUsage.percentage > 80) {
      recommendations.push('考虑增加Redis内存或清理过期数据');
    }
    
    if (summary.evictedKeys > 100) {
      recommendations.push('内存不足导致键被驱逐，建议增加内存或优化缓存策略');
    }

    return {
      summary,
      hotKeys,
      trends,
      anomalies,
      recommendations
    };
  }

  /**
   * 重置监控数据
   */
  reset(): void {
    this.operations = [];
    this.startTime = Date.now();
    logger.info('缓存监控数据已重置');
  }

  /**
   * 获取运行时间
   */
  getUptime(): number {
    return Date.now() - this.startTime;
  }
}

// 导出单例实例
export const cacheMonitor = new CacheMonitor();

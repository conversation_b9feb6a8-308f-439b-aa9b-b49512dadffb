import { NextRequest, NextResponse } from 'next/server';
import {
  ApiResponse,
  ErrorCode,
  ErrorFactory,
  ResponseFactory
} from './error-types';

/**
 * API工具函数
 * 提供类型安全的API处理机制
 */

// 请求参数验证器类型
export type Validator<T> = (value: unknown) => T | never;

// API处理器类型
export type ApiHandler = (
  request: NextRequest
) => Promise<ApiResponse>;

// 中间件类型
export type Middleware = (
  request: NextRequest
) => Promise<NextRequest | NextResponse>;

/**
 * API路由包装器
 * 提供统一的错误处理和响应格式
 */
export function withApiHandler(
  handler: ApiHandler,
  options?: {
    requireAuth?: boolean;
    rateLimit?: number;
    middleware?: Middleware[];
  }
) {
  return async (
    request: NextRequest
  ): Promise<NextResponse> => {
    try {
      // 执行中间件
      if (options?.middleware) {
        for (const middleware of options.middleware) {
          const result = await middleware(request);
          if (result instanceof NextResponse) {
            return result;
          }
          request = result;
        }
      }

      // 执行主处理器
      const result = await handler(request);
      
      if (result.success) {
        return NextResponse.json(result);
      } else {
        const statusCode = getStatusCodeFromError(result.error?.code);
        return NextResponse.json(result, { status: statusCode });
      }
    } catch (err) {
      console.error('API处理器错误:', err);

      const errorResponse = ResponseFactory.error(
        ErrorFactory.fromError(
          err instanceof Error ? err : new Error('Unknown error'),
          ErrorCode.UNKNOWN_ERROR
        )
      );

      return NextResponse.json(errorResponse, { status: 500 });
    }
  };
}

/**
 * 根据错误代码获取HTTP状态码
 */
function getStatusCodeFromError(errorCode?: string): number {
  const statusMap: Record<string, number> = {
    [ErrorCode.VALIDATION_ERROR]: 400,
    [ErrorCode.SEARCH_QUERY_INVALID]: 400,
    [ErrorCode.UNAUTHORIZED]: 401,
    [ErrorCode.TOKEN_EXPIRED]: 401,
    [ErrorCode.INVALID_CREDENTIALS]: 401,
    [ErrorCode.FORBIDDEN]: 403,
    [ErrorCode.RECORD_NOT_FOUND]: 404,
    [ErrorCode.FILE_NOT_FOUND]: 404,
    [ErrorCode.INVALID_MOVIE_ID]: 404,
    [ErrorCode.INVALID_STAR_ID]: 404,
    [ErrorCode.DUPLICATE_RECORD]: 409,
    [ErrorCode.DATABASE_CONNECTION_ERROR]: 503,
    [ErrorCode.CACHE_CONNECTION_ERROR]: 503,
    [ErrorCode.NETWORK_ERROR]: 503,
  };

  return statusMap[errorCode || ''] || 500;
}

/**
 * 请求参数解析器
 */
export class RequestParser {
  /**
   * 解析查询参数
   */
  static parseQuery<T extends Record<string, unknown>>(
    request: NextRequest,
    validators: { [K in keyof T]: Validator<T[K]> }
  ): T {
    const { searchParams } = new URL(request.url);
    const result = {} as T;

    for (const [key, validator] of Object.entries(validators)) {
      try {
        const value = searchParams.get(key);
        result[key as keyof T] = validator(value);
      } catch {
        throw ErrorFactory.createValidationError(
          key,
          searchParams.get(key),
          'invalid_format',
          `Invalid value for parameter '${key}'`
        );
      }
    }

    return result;
  }

  /**
   * 解析请求体
   */
  static async parseBody<T>(
    request: NextRequest,
    validator: Validator<T>
  ): Promise<T> {
    try {
      const body = await request.json();
      return validator(body);
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw ErrorFactory.createValidationError(
          'body',
          'invalid_json',
          'json_parse_error',
          'Invalid JSON in request body'
        );
      }
      throw error;
    }
  }
}

/**
 * 常用验证器
 */
export const Validators = {
  /**
   * 字符串验证器
   */
  string: (required = false): Validator<string | undefined> => (value) => {
    if (value === null || value === undefined) {
      if (required) {
        throw new Error('Required string parameter is missing');
      }
      return undefined;
    }
    if (typeof value !== 'string') {
      throw new Error('Expected string value');
    }
    return value;
  },

  /**
   * 数字验证器
   */
  number: (required = false): Validator<number | undefined> => (value) => {
    if (value === null || value === undefined || value === '') {
      if (required) {
        throw new Error('Required number parameter is missing');
      }
      return undefined;
    }
    const num = typeof value === 'string' ? parseInt(value, 10) : Number(value);
    if (isNaN(num)) {
      throw new Error('Expected valid number');
    }
    return num;
  },

  /**
   * 布尔值验证器
   */
  boolean: (required = false): Validator<boolean | undefined> => (value) => {
    if (value === null || value === undefined || value === '') {
      if (required) {
        throw new Error('Required boolean parameter is missing');
      }
      return undefined;
    }
    if (typeof value === 'boolean') {
      return value;
    }
    if (typeof value === 'string') {
      const lower = value.toLowerCase();
      if (lower === 'true' || lower === '1') return true;
      if (lower === 'false' || lower === '0') return false;
    }
    throw new Error('Expected boolean value');
  },

  /**
   * 枚举验证器
   */
  enum: <T extends string>(
    values: readonly T[],
    required = false
  ): Validator<T | undefined> => (value) => {
    if (value === null || value === undefined || value === '') {
      if (required) {
        throw new Error('Required enum parameter is missing');
      }
      return undefined;
    }
    if (typeof value !== 'string' || !values.includes(value as T)) {
      throw new Error(`Expected one of: ${values.join(', ')}`);
    }
    return value as T;
  },

  /**
   * 数组验证器
   */
  array: <T>(
    itemValidator: Validator<T>,
    required = false
  ): Validator<T[] | undefined> => (value) => {
    if (value === null || value === undefined) {
      if (required) {
        throw new Error('Required array parameter is missing');
      }
      return undefined;
    }
    if (!Array.isArray(value)) {
      throw new Error('Expected array value');
    }
    return value.map(itemValidator);
  },

  /**
   * 对象验证器
   */
  object: <T extends Record<string, unknown>>(
    validators: { [K in keyof T]: Validator<T[K]> },
    required = false
  ): Validator<T | undefined> => (value) => {
    if (value === null || value === undefined) {
      if (required) {
        throw new Error('Required object parameter is missing');
      }
      return undefined;
    }
    if (typeof value !== 'object' || Array.isArray(value)) {
      throw new Error('Expected object value');
    }

    const result = {} as T;
    for (const [key, validator] of Object.entries(validators)) {
      result[key as keyof T] = validator((value as Record<string, unknown>)[key]);
    }
    return result;
  },

  /**
   * ID验证器（UUID或自定义格式）
   */
  id: (required = false): Validator<string | undefined> => (value) => {
    if (value === null || value === undefined || value === '') {
      if (required) {
        throw new Error('Required ID parameter is missing');
      }
      return undefined;
    }
    if (typeof value !== 'string' || value.trim().length === 0) {
      throw new Error('Expected valid ID');
    }
    return value.trim();
  },

  /**
   * 分页参数验证器
   */
  pagination: () => ({
    page: Validators.number(),
    limit: Validators.number(),
  }),
};

/**
 * 响应缓存工具
 */
export class ResponseCache {
  /**
   * 设置缓存头
   */
  static setCacheHeaders(
    response: NextResponse,
    maxAge: number,
    staleWhileRevalidate?: number
  ): NextResponse {
    const cacheControl = staleWhileRevalidate
      ? `public, max-age=${maxAge}, stale-while-revalidate=${staleWhileRevalidate}`
      : `public, max-age=${maxAge}`;
    
    response.headers.set('Cache-Control', cacheControl);
    response.headers.set('ETag', `"${Date.now()}"`);
    
    return response;
  }

  /**
   * 检查条件请求
   */
  static checkConditionalRequest(
    request: NextRequest,
    etag: string
  ): boolean {
    const ifNoneMatch = request.headers.get('if-none-match');
    return ifNoneMatch === etag;
  }
}

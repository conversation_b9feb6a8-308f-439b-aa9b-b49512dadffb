/**
 * 收藏列表工具函数
 */

import { FavoriteList } from '@/types/favorite-list';

/**
 * 确保用户有默认收藏列表
 * @param userId 用户ID
 * @returns 默认收藏列表
 */
export async function ensureDefaultFavoriteList(userId: string): Promise<FavoriteList | null> {
  try {
    // 检查用户是否已有默认收藏列表
    const response = await fetch(`/api/favorite-lists?userId=${userId}&includeCount=false`);
    const result = await response.json();

    if (result.success && result.data.length > 0) {
      // 查找默认列表
      const defaultList = result.data.find((list: FavoriteList) => list.isDefault);
      if (defaultList) {
        return defaultList;
      }
    }

    // 如果没有默认列表，创建一个
    const initResponse = await fetch('/api/favorite-lists/init', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    const initResult = await initResponse.json();
    if (initResult.success) {
      return initResult.data;
    }

    console.error('创建默认收藏列表失败:', initResult.message);
    return null;

  } catch (error) {
    console.error('确保默认收藏列表失败:', error);
    return null;
  }
}

/**
 * 获取用户收藏列表，如果没有则自动创建默认列表
 * @param userId 用户ID
 * @param includeCount 是否包含收藏数量
 * @returns 收藏列表数组
 */
export async function getUserFavoriteListsWithDefault(
  userId: string, 
  includeCount: boolean = true
): Promise<FavoriteList[]> {
  try {
    // 先确保有默认列表
    await ensureDefaultFavoriteList(userId);

    // 获取所有收藏列表
    const response = await fetch(`/api/favorite-lists?userId=${userId}&includeCount=${includeCount}`);
    const result = await response.json();

    if (result.success) {
      return result.data;
    }

    console.error('获取收藏列表失败:', result.message);
    return [];

  } catch (error) {
    console.error('获取收藏列表失败:', error);
    return [];
  }
}

/**
 * 获取影片在用户收藏列表中的状态
 * @param userId 用户ID
 * @param movieId 影片ID
 * @returns 收藏状态信息
 */
export async function getMovieFavoriteStatus(userId: string, movieId: string) {
  try {
    const response = await fetch(`/api/favorites?movieId=${movieId}&userId=${userId}&includeAllLists=true`);
    const result = await response.json();

    if (result.success) {
      return {
        isFavorited: result.data.isFavorited,
        favoriteListId: result.data.favoriteListId,
        favoriteLists: result.data.favoriteLists || []
      };
    }

    return {
      isFavorited: false,
      favoriteListId: null,
      favoriteLists: []
    };

  } catch (error) {
    console.error('获取收藏状态失败:', error);
    return {
      isFavorited: false,
      favoriteListId: null,
      favoriteLists: []
    };
  }
}

/**
 * 添加影片到收藏列表
 * @param userId 用户ID
 * @param movieId 影片ID
 * @param favoriteListId 收藏列表ID，如果不提供则添加到默认列表
 * @returns 操作结果
 */
export async function addMovieToFavoriteList(
  userId: string, 
  movieId: string, 
  favoriteListId?: string
) {
  try {
    // 如果没有指定列表，确保有默认列表
    if (!favoriteListId) {
      const defaultList = await ensureDefaultFavoriteList(userId);
      if (defaultList) {
        favoriteListId = defaultList.id;
      }
    }

    const response = await fetch('/api/favorites', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieId,
        userId,
        favoriteListId,
        action: 'add'
      }),
    });

    const result = await response.json();
    return result;

  } catch (error) {
    console.error('添加到收藏列表失败:', error);
    return {
      success: false,
      message: '添加到收藏列表失败'
    };
  }
}

/**
 * 从收藏列表中移除影片
 * @param userId 用户ID
 * @param movieId 影片ID
 * @param favoriteListId 收藏列表ID
 * @returns 操作结果
 */
export async function removeMovieFromFavoriteList(
  userId: string, 
  movieId: string, 
  favoriteListId: string
) {
  try {
    const response = await fetch('/api/favorites', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieId,
        userId,
        favoriteListId,
        action: 'remove'
      }),
    });

    const result = await response.json();
    return result;

  } catch (error) {
    console.error('从收藏列表移除失败:', error);
    return {
      success: false,
      message: '从收藏列表移除失败'
    };
  }
}

/**
 * 移动影片到其他收藏列表
 * @param userId 用户ID
 * @param movieId 影片ID
 * @param fromListId 源列表ID
 * @param toListId 目标列表ID
 * @returns 操作结果
 */
export async function moveMovieBetweenLists(
  userId: string,
  movieId: string,
  fromListId: string | null,
  toListId: string
) {
  try {
    const response = await fetch('/api/favorites/move', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieId,
        userId,
        fromListId,
        toListId
      }),
    });

    const result = await response.json();
    return result;

  } catch (error) {
    console.error('移动收藏失败:', error);
    return {
      success: false,
      message: '移动收藏失败'
    };
  }
}

/**
 * 格式化收藏列表显示名称
 * @param list 收藏列表
 * @returns 格式化后的名称
 */
export function formatFavoriteListName(list: FavoriteList): string {
  if (list.isDefault) {
    return `${list.name} (默认)`;
  }
  return list.name;
}

/**
 * 获取收藏列表图标类型
 * @param list 收藏列表
 * @returns 图标类型
 */
export function getFavoriteListIconType(list: FavoriteList): 'clock' | 'bookmark' | 'heart' {
  if (list.isDefault) {
    return 'clock';
  }
  if (list.name.includes('喜欢') || list.name.includes('最爱')) {
    return 'heart';
  }
  return 'bookmark';
}

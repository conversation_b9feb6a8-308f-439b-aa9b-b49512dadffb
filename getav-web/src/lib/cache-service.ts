import { getRedisClient, CacheExpiry } from './redis';

/**
 * 缓存服务
 * 提供统一的缓存操作接口，支持自动降级
 */

export class CacheService {
  private static instance: CacheService;

  private constructor() {}

  public static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: unknown, expiry: number = CacheExpiry.MEDIUM): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;

      const serializedValue = JSON.stringify(value);
      await client.setEx(key, expiry, serializedValue);
      return true;
    } catch (error) {
      console.error('缓存设置失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const client = await getRedisClient();
      if (!client) return null;

      const value = await client.get(key);
      if (!value) return null;

      return JSON.parse(value) as T;
    } catch (error) {
      console.error('缓存获取失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;

      await client.del(key);
      return true;
    } catch (error) {
      console.error('缓存删除失败:', error);
      return false;
    }
  }

  /**
   * 批量删除缓存（支持模式匹配）
   */
  async deletePattern(pattern: string): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;

      const keys = await client.keys(pattern);
      if (keys.length > 0) {
        await client.del(keys);
      }
      return true;
    } catch (error) {
      console.error('批量缓存删除失败:', error);
      return false;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;

      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('缓存存在性检查失败:', error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;

      await client.expire(key, seconds);
      return true;
    } catch (error) {
      console.error('设置缓存过期时间失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      const client = await getRedisClient();
      if (!client) return -1;

      return await client.ttl(key);
    } catch (error) {
      console.error('获取缓存过期时间失败:', error);
      return -1;
    }
  }

  /**
   * 缓存装饰器 - 自动缓存函数结果
   */
  async cached<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    expiry: number = CacheExpiry.MEDIUM
  ): Promise<T> {
    // 先尝试从缓存获取
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // 缓存未命中，执行原函数
    const result = await fetchFunction();
    
    // 将结果存入缓存
    await this.set(key, result, expiry);
    
    return result;
  }

  /**
   * 批量获取缓存
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const client = await getRedisClient();
      if (!client) return keys.map(() => null);

      const values = await client.mGet(keys);
      return values.map(value => {
        if (!value) return null;
        try {
          return JSON.parse(value) as T;
        } catch {
          return null;
        }
      });
    } catch (error) {
      console.error('批量缓存获取失败:', error);
      return keys.map(() => null);
    }
  }

  /**
   * 批量设置缓存
   */
  async mset(keyValuePairs: Array<{ key: string; value: unknown; expiry?: number }>): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;

      // 使用pipeline提高性能
      const pipeline = client.multi();
      
      for (const { key, value, expiry = CacheExpiry.MEDIUM } of keyValuePairs) {
        const serializedValue = JSON.stringify(value);
        pipeline.setEx(key, expiry, serializedValue);
      }

      await pipeline.exec();
      return true;
    } catch (error) {
      console.error('批量缓存设置失败:', error);
      return false;
    }
  }

  /**
   * 增量操作
   */
  async increment(key: string, amount: number = 1): Promise<number> {
    try {
      const client = await getRedisClient();
      if (!client) return 0;

      return await client.incrBy(key, amount);
    } catch (error) {
      console.error('缓存增量操作失败:', error);
      return 0;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<{
    connected: boolean;
    keyCount: number;
    memoryUsage: string;
  }> {
    try {
      const client = await getRedisClient();
      if (!client) {
        return {
          connected: false,
          keyCount: 0,
          memoryUsage: '0B'
        };
      }

      const info = await client.info('memory');
      const keyCount = await client.dbSize();
      
      // 解析内存使用情况
      const memoryMatch = info.match(/used_memory_human:(.+)/);
      const memoryUsage = memoryMatch ? memoryMatch[1].trim() : '0B';

      return {
        connected: true,
        keyCount,
        memoryUsage
      };
    } catch (error) {
      console.error('获取缓存统计失败:', error);
      return {
        connected: false,
        keyCount: 0,
        memoryUsage: '0B'
      };
    }
  }
}

// 导出单例实例
export const cacheService = CacheService.getInstance();

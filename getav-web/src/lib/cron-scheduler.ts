/**
 * 定时任务调度器
 * 使用node-cron管理定时任务，集成BullMQ队列系统
 */

import * as cron from 'node-cron';
import { queueManager } from './queue-manager';
import { JobPriority } from './queue-config';
import { logger } from './logger';

export interface CronJobConfig {
  name: string;
  schedule: string; // cron表达式
  enabled: boolean;
  timezone?: string;
  description?: string;
  jobType: 'data-import' | 'cache-warmup' | 'cleanup' | 'monitoring';
  jobData?: Record<string, unknown>;
  priority?: number;
}

export class CronScheduler {
  private jobs: Map<string, cron.ScheduledTask> = new Map();
  private isInitialized = false;

  /**
   * 初始化定时任务调度器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('定时任务调度器已经初始化');
      return;
    }

    try {
      // 定义默认的定时任务
      const defaultJobs: CronJobConfig[] = [
        {
          name: 'daily-data-import',
          schedule: '0 2 * * *', // 每天凌晨2点
          enabled: true,
          timezone: 'Asia/Shanghai',
          description: '每日自动数据导入',
          jobType: 'data-import',
          jobData: {
            page: 1,
            limit: 50,
            options: {
              downloadImages: true,
              updateExisting: false,
              skipErrors: true
            }
          },
          priority: JobPriority.LOW
        },
        {
          name: 'hourly-cache-warmup',
          schedule: '0 */2 * * *', // 每2小时
          enabled: true,
          timezone: 'Asia/Shanghai',
          description: '定期缓存预热',
          jobType: 'cache-warmup',
          jobData: {
            type: 'partial',
            targets: ['popular-movies', 'homepage-movies']
          },
          priority: JobPriority.BACKGROUND
        },
        {
          name: 'weekly-full-cache-warmup',
          schedule: '0 1 * * 0', // 每周日凌晨1点
          enabled: true,
          timezone: 'Asia/Shanghai',
          description: '每周完整缓存预热',
          jobType: 'cache-warmup',
          jobData: {
            type: 'full'
          },
          priority: JobPriority.BACKGROUND
        },
        {
          name: 'daily-cleanup',
          schedule: '0 3 * * *', // 每天凌晨3点
          enabled: true,
          timezone: 'Asia/Shanghai',
          description: '每日数据清理',
          jobType: 'cleanup',
          jobData: {
            cleanupType: 'expired-cache',
            retentionDays: 7
          },
          priority: JobPriority.LOW
        },
        {
          name: 'monitoring-check',
          schedule: '*/15 * * * *', // 每15分钟
          enabled: true,
          timezone: 'Asia/Shanghai',
          description: '系统监控检查',
          jobType: 'monitoring',
          jobData: {
            checkType: 'health-status'
          },
          priority: JobPriority.NORMAL
        }
      ];

      // 注册所有默认任务
      for (const jobConfig of defaultJobs) {
        await this.scheduleJob(jobConfig);
      }

      this.isInitialized = true;
      logger.info('定时任务调度器初始化完成', {
        totalJobs: this.jobs.size,
        enabledJobs: defaultJobs.filter(j => j.enabled).length
      });

    } catch (error) {
      logger.error('定时任务调度器初始化失败', error as Error);
      throw error;
    }
  }

  /**
   * 调度单个任务
   */
  async scheduleJob(config: CronJobConfig): Promise<void> {
    if (!config.enabled) {
      logger.info(`跳过禁用的定时任务: ${config.name}`);
      return;
    }

    try {
      // 验证cron表达式
      if (!cron.validate(config.schedule)) {
        throw new Error(`无效的cron表达式: ${config.schedule}`);
      }

      // 创建定时任务
      const task = cron.schedule(
        config.schedule,
        async () => {
          await this.executeJob(config);
        },
        {
          timezone: config.timezone || 'Asia/Shanghai'
        }
      );

      // 保存任务引用
      this.jobs.set(config.name, task);

      logger.info(`定时任务已调度: ${config.name}`, {
        schedule: config.schedule,
        timezone: config.timezone,
        description: config.description
      });

    } catch (error) {
      logger.error(`调度定时任务失败: ${config.name}`, error as Error);
      throw error;
    }
  }

  /**
   * 执行定时任务
   */
  private async executeJob(config: CronJobConfig): Promise<void> {
    const startTime = Date.now();
    
    logger.info(`开始执行定时任务: ${config.name}`, {
      jobType: config.jobType,
      schedule: config.schedule
    });

    try {
      switch (config.jobType) {
        case 'data-import':
          await this.executeDataImportJob(config);
          break;
        
        case 'cache-warmup':
          await this.executeCacheWarmupJob(config);
          break;
        
        case 'cleanup':
          await this.executeCleanupJob(config);
          break;
        
        case 'monitoring':
          await this.executeMonitoringJob(config);
          break;
        
        default:
          throw new Error(`不支持的任务类型: ${config.jobType}`);
      }

      const duration = Date.now() - startTime;
      logger.info(`定时任务执行完成: ${config.name}`, {
        duration,
        jobType: config.jobType
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`定时任务执行失败: ${config.name}`, error as Error, {
        duration,
        jobType: config.jobType
      });
    }
  }

  /**
   * 执行数据导入任务
   */
  private async executeDataImportJob(config: CronJobConfig): Promise<void> {
    const job = await queueManager.addDataImportJob(
      config.jobData as Record<string, unknown>,
      {
        priority: config.priority || JobPriority.LOW
      }
    );

    logger.info(`数据导入任务已添加到队列`, {
      cronJob: config.name,
      queueJobId: job.id
    });
  }

  /**
   * 执行缓存预热任务
   */
  private async executeCacheWarmupJob(config: CronJobConfig): Promise<void> {
    // 直接调用现有的缓存预热系统
    const { cacheWarmer } = await import('./cache-warmer');
    
    const result = await cacheWarmer.warmup();
    
    logger.info(`缓存预热任务完成`, {
      cronJob: config.name,
      completedTasks: result.completedTasks,
      failedTasks: result.failedTasks,
      duration: result.duration
    });
  }

  /**
   * 执行清理任务
   */
  private async executeCleanupJob(config: CronJobConfig): Promise<void> {
    const { cleanupType, retentionDays = 7 } = config.jobData || {};
    
    if (cleanupType === 'expired-cache') {
      // 清理过期缓存
      await import('./redis');

      // 这里可以实现更复杂的清理逻辑
      logger.info(`执行缓存清理任务`, {
        cronJob: config.name,
        cleanupType,
        retentionDays
      });
    }
  }

  /**
   * 执行监控任务
   */
  private async executeMonitoringJob(config: CronJobConfig): Promise<void> {
    const { checkType } = config.jobData || {};
    
    if (checkType === 'health-status') {
      // 执行健康检查
      try {
        const { prisma } = await import('./database');
        const { redis } = await import('./redis');
        
        // 检查数据库连接
        await prisma.$queryRaw`SELECT 1`;
        
        // 检查Redis连接
        await redis.ping();
        
        logger.info(`系统健康检查通过`, {
          cronJob: config.name,
          checkType
        });
        
      } catch (error) {
        logger.error(`系统健康检查失败`, error as Error, {
          cronJob: config.name,
          checkType
        });
      }
    }
  }

  /**
   * 停止指定任务
   */
  stopJob(jobName: string): boolean {
    const task = this.jobs.get(jobName);
    if (task) {
      task.stop();
      logger.info(`定时任务已停止: ${jobName}`);
      return true;
    }
    return false;
  }

  /**
   * 启动指定任务
   */
  startJob(jobName: string): boolean {
    const task = this.jobs.get(jobName);
    if (task) {
      task.start();
      logger.info(`定时任务已启动: ${jobName}`);
      return true;
    }
    return false;
  }

  /**
   * 获取所有任务状态
   */
  getJobsStatus(): Array<{
    name: string;
    running: boolean;
    nextRun?: Date;
  }> {
    const status = [];
    
    for (const [name, task] of this.jobs) {
      status.push({
        name,
        running: task.getStatus() === 'scheduled',
        // nextRun: task.nextDate()?.toDate() // BullMQ可能没有这个方法
      });
    }
    
    return status;
  }

  /**
   * 销毁所有任务
   */
  destroy(): void {
    for (const [name, task] of this.jobs) {
      task.destroy();
      logger.info(`定时任务已销毁: ${name}`);
    }
    
    this.jobs.clear();
    this.isInitialized = false;
    logger.info('定时任务调度器已销毁');
  }
}

// 导出单例实例
export const cronScheduler = new CronScheduler();

/**
 * BullMQ队列配置
 * 统一管理所有队列的配置和连接
 */

import { ConnectionOptions } from 'bullmq';

// Redis连接配置（复用现有Redis配置）
export const queueConnection: ConnectionOptions = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_QUEUE_DB || '1'), // 使用独立的数据库避免冲突
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
};

// 队列名称常量
export const QueueNames = {
  DATA_IMPORT: 'data-import',
  IMAGE_PROCESSING: 'image-processing',
  M3U8_PROCESSING: 'm3u8-processing',
  CACHE_WARMUP: 'cache-warmup',
  NOTIFICATION: 'notification',
} as const;

// 任务类型定义
export interface DataImportJob {
  type: 'javbus-import' | 'star-import' | 'batch-import';
  data: {
    movieId?: string;
    starId?: string;
    page?: number;
    limit?: number;
    category?: string;
    options?: {
      downloadImages?: boolean;
      updateExisting?: boolean;
      skipErrors?: boolean;
    };
  };
  metadata?: {
    requestId?: string;
    userId?: string;
    priority?: number;
  };
}

export interface ImageProcessingJob {
  type: 'download' | 'optimize' | 'cleanup';
  data: {
    imageUrl?: string;
    localPath?: string;
    movieId?: string;
    starId?: string;
    options?: {
      quality?: number;
      format?: 'webp' | 'jpeg' | 'png';
      sizes?: number[];
    };
  };
}

export interface CacheWarmupJob {
  type: 'full' | 'partial' | 'specific';
  data: {
    targets?: string[];
    priority?: number;
    options?: {
      maxConcurrency?: number;
      timeout?: number;
    };
  };
}

export interface NotificationJob {
  type: 'email' | 'webhook' | 'log';
  data: {
    recipient?: string;
    subject?: string;
    message: string;
    level?: 'info' | 'warn' | 'error';
  };
}

export interface M3U8ProcessingJob {
  type: 'download' | 'parse' | 'validate' | 'cleanup';
  data: {
    url?: string;
    movieCode?: string;
    movieId?: string;
    localPath?: string;
    options?: {
      skipExisting?: boolean;
      validateContent?: boolean;
      retryCount?: number;
      timeout?: number;
    };
  };
  metadata?: {
    requestId?: string;
    priority?: number;
    source?: string;
  };
}

// 队列配置选项
export const queueOptions = {
  defaultJobOptions: {
    removeOnComplete: 100, // 保留最近100个完成的任务
    removeOnFail: 50,      // 保留最近50个失败的任务
    attempts: 3,           // 默认重试3次
    backoff: {
      type: 'exponential' as const,
      delay: 2000,         // 初始延迟2秒
    },
  },
  settings: {
    stalledInterval: 30 * 1000,    // 30秒检查一次停滞任务
    maxStalledCount: 1,            // 最大停滞次数
  },
};

// 任务优先级
export const JobPriority = {
  CRITICAL: 1,    // 关键任务（用户直接触发）
  HIGH: 5,        // 高优先级（重要数据更新）
  NORMAL: 10,     // 普通优先级（常规任务）
  LOW: 15,        // 低优先级（批量处理）
  BACKGROUND: 20, // 后台任务（清理、预热等）
} as const;

// 队列监控配置
export const monitoringConfig = {
  metricsInterval: 60000,        // 1分钟收集一次指标
  alertThresholds: {
    failureRate: 0.1,            // 失败率超过10%告警
    avgProcessingTime: 30000,    // 平均处理时间超过30秒告警
    queueLength: 1000,           // 队列长度超过1000告警
    stalledJobs: 10,             // 停滞任务超过10个告警
  },
  retention: {
    metrics: 7 * 24 * 60 * 60 * 1000,  // 指标保留7天
    logs: 30 * 24 * 60 * 60 * 1000,    // 日志保留30天
  },
};

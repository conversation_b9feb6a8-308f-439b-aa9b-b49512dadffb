/**
 * 缓存预热系统
 * 在系统启动或低峰期预先加载热点数据到缓存中
 */

import { cacheService } from './cache-service';
import { CacheKeys, CacheExpiry } from './redis';
import { logger } from './logger';
import { prisma } from './database';

export interface WarmupTask {
  name: string;
  priority: number; // 1-10, 数字越小优先级越高
  execute: () => Promise<void>;
  estimatedTime: number; // 预估执行时间（毫秒）
}

export interface WarmupOptions {
  maxConcurrency?: number; // 最大并发任务数
  timeout?: number; // 单个任务超时时间（毫秒）
  retryAttempts?: number; // 重试次数
  skipOnError?: boolean; // 遇到错误是否跳过继续执行
}

export interface WarmupResult {
  success: boolean;
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  duration: number;
  errors: Array<{
    taskName: string;
    error: string;
  }>;
}

export class CacheWarmer {
  private tasks: WarmupTask[] = [];
  private options: Required<WarmupOptions>;

  constructor(options: WarmupOptions = {}) {
    this.options = {
      maxConcurrency: options.maxConcurrency || 3,
      timeout: options.timeout || 30000, // 30秒
      retryAttempts: options.retryAttempts || 2,
      skipOnError: options.skipOnError || true
    };

    this.initializeTasks();
  }

  /**
   * 初始化预热任务
   */
  private initializeTasks(): void {
    // 按优先级排序的预热任务
    this.tasks = [
      {
        name: '热门影片数据',
        priority: 1,
        estimatedTime: 5000,
        execute: () => this.warmupPopularMovies()
      },
      {
        name: '首页影片列表',
        priority: 2,
        estimatedTime: 3000,
        execute: () => this.warmupHomepageMovies()
      },
      {
        name: '统计数据',
        priority: 3,
        estimatedTime: 2000,
        execute: () => this.warmupStats()
      },
      {
        name: '热门演员数据',
        priority: 4,
        estimatedTime: 4000,
        execute: () => this.warmupPopularStars()
      },
      {
        name: '分类统计',
        priority: 5,
        estimatedTime: 3000,
        execute: () => this.warmupCategoryStats()
      },
      {
        name: '搜索建议',
        priority: 6,
        estimatedTime: 2000,
        execute: () => this.warmupSearchSuggestions()
      }
    ].sort((a, b) => a.priority - b.priority);
  }

  /**
   * 执行缓存预热
   */
  async warmup(): Promise<WarmupResult> {
    const startTime = Date.now();
    const result: WarmupResult = {
      success: false,
      totalTasks: this.tasks.length,
      completedTasks: 0,
      failedTasks: 0,
      duration: 0,
      errors: []
    };

    logger.info('开始缓存预热', {
      totalTasks: this.tasks.length,
      maxConcurrency: this.options.maxConcurrency
    });

    try {
      // 分批执行任务，控制并发数
      const batches = this.createBatches(this.tasks, this.options.maxConcurrency);
      
      for (const batch of batches) {
        const batchPromises = batch.map(task => this.executeTask(task, result));
        await Promise.allSettled(batchPromises);
      }

      result.success = result.failedTasks === 0 || this.options.skipOnError;
      result.duration = Date.now() - startTime;

      logger.info('缓存预热完成', {
        success: result.success,
        completedTasks: result.completedTasks,
        failedTasks: result.failedTasks,
        duration: result.duration,
        errors: result.errors.length
      });

    } catch (error) {
      result.duration = Date.now() - startTime;
      logger.error('缓存预热失败', error as Error, {
        completedTasks: result.completedTasks,
        failedTasks: result.failedTasks
      });
    }

    return result;
  }

  /**
   * 创建任务批次
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: WarmupTask, result: WarmupResult): Promise<void> {
    const taskStartTime = Date.now();
    
    for (let attempt = 1; attempt <= this.options.retryAttempts + 1; attempt++) {
      try {
        logger.debug(`执行预热任务: ${task.name} (尝试 ${attempt})`);
        
        // 设置超时
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Task timeout')), this.options.timeout);
        });

        await Promise.race([task.execute(), timeoutPromise]);
        
        result.completedTasks++;
        const duration = Date.now() - taskStartTime;
        
        logger.info(`预热任务完成: ${task.name}`, {
          duration,
          attempt,
          estimatedTime: task.estimatedTime
        });
        
        return;

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        if (attempt <= this.options.retryAttempts) {
          logger.warn(`预热任务失败，准备重试: ${task.name}`, {
            attempt,
            error: errorMessage,
            maxAttempts: this.options.retryAttempts + 1
          });
          
          // 重试前等待一段时间
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        } else {
          result.failedTasks++;
          result.errors.push({
            taskName: task.name,
            error: errorMessage
          });
          
          logger.error(`预热任务最终失败: ${task.name}`, error as Error, {
            totalAttempts: attempt - 1
          });
          
          if (!this.options.skipOnError) {
            throw error;
          }
        }
      }
    }
  }

  /**
   * 预热热门影片数据
   */
  private async warmupPopularMovies(): Promise<void> {
    const popularMovies = await prisma.movie.findMany({
      take: 20,
      orderBy: [
        { createdAt: 'desc' }
      ],
      include: {
        stars: {
          include: {
            star: true
          }
        },
        genres: {
          include: {
            genre: true
          }
        }
      }
    });

    await cacheService.set(
      CacheKeys.popularMovies(),
      popularMovies,
      CacheExpiry.LONG
    );

    // 同时缓存每个热门影片的详细信息
    const movieCachePromises = popularMovies.map(movie =>
      cacheService.set(
        CacheKeys.movie(movie.id),
        movie,
        CacheExpiry.LONG
      )
    );

    await Promise.all(movieCachePromises);
  }

  /**
   * 预热首页影片列表
   */
  private async warmupHomepageMovies(): Promise<void> {
    // 预热前几页的影片列表
    const pages = [1, 2, 3];
    const limit = 20;

    for (const page of pages) {
      const movies = await prisma.movie.findMany({
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          stars: {
            include: {
              star: true
            }
          },
          genres: {
            include: {
              genre: true
            }
          }
        }
      });

      await cacheService.set(
        CacheKeys.movieList(page, limit),
        movies,
        CacheExpiry.MEDIUM
      );
    }
  }

  /**
   * 预热统计数据
   */
  private async warmupStats(): Promise<void> {
    const [movieCount, starCount, genreCount] = await Promise.all([
      prisma.movie.count(),
      prisma.star.count(),
      prisma.genre.count()
    ]);

    const stats = {
      movieCount,
      starCount,
      genreCount,
      lastUpdated: new Date().toISOString()
    };

    await cacheService.set(
      CacheKeys.stats(),
      stats,
      CacheExpiry.VERY_LONG
    );
  }

  /**
   * 预热热门演员数据
   */
  private async warmupPopularStars(): Promise<void> {
    const popularStars = await prisma.star.findMany({
      take: 20,
      orderBy: {
        movies: {
          _count: 'desc'
        }
      },
      include: {
        _count: {
          select: {
            movies: true
          }
        }
      }
    });

    await cacheService.set(
      CacheKeys.trendingStars(),
      popularStars,
      CacheExpiry.LONG
    );

    // 缓存演员列表的第一页
    await cacheService.set(
      CacheKeys.starList(1, 20),
      popularStars,
      CacheExpiry.LONG
    );
  }

  /**
   * 预热分类统计
   */
  private async warmupCategoryStats(): Promise<void> {
    const categories = await prisma.genre.findMany({
      include: {
        _count: {
          select: {
            movies: true
          }
        }
      },
      orderBy: {
        movies: {
          _count: 'desc'
        }
      }
    });

    await cacheService.set(
      'genres:stats',
      categories,
      CacheExpiry.VERY_LONG
    );
  }

  /**
   * 预热搜索建议
   */
  private async warmupSearchSuggestions(): Promise<void> {
    // 预热常见搜索词的建议
    const commonQueries = ['', 'a', 'b', 'c', 'new', 'hot', 'best'];
    
    for (const query of commonQueries) {
      if (query.length === 0) continue;
      
      const suggestions = await prisma.movie.findMany({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { id: { contains: query, mode: 'insensitive' } }
          ]
        },
        select: {
          id: true,
          title: true
        },
        take: 10,
        orderBy: { createdAt: 'desc' }
      });

      await cacheService.set(
        CacheKeys.searchSuggestions(query),
        suggestions,
        CacheExpiry.LONG
      );
    }
  }

  /**
   * 获取预热任务信息
   */
  getTaskInfo(): Array<{
    name: string;
    priority: number;
    estimatedTime: number;
  }> {
    return this.tasks.map(task => ({
      name: task.name,
      priority: task.priority,
      estimatedTime: task.estimatedTime
    }));
  }
}

// 导出单例实例
export const cacheWarmer = new CacheWarmer();

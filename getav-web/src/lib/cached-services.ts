import { cacheService } from './cache-service';
import { <PERSON>ache<PERSON><PERSON>s, CacheExpiry } from './redis';
import { prisma, getMovieById } from './database';
import { performFullTextSearch } from './search-service';
import type { LocalMovie } from '@/types/javbus';
import type { SearchOptions, SearchResult } from './search-service';

/**
 * 带缓存的服务层
 * 为数据库查询和搜索功能提供缓存支持
 */

/**
 * 带缓存的影片列表获取
 */
export async function getCachedMovies(
  page: number = 1,
  limit: number = 20,
  filters?: {
    category?: string;
    star?: string;
    sortBy?: string;
  }
): Promise<{
  movies: LocalMovie[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  const filtersKey = filters ? JSON.stringify(filters) : '';
  const cacheKey = CacheKeys.movieList(page, limit, filtersKey);

  return await cacheService.cached(
    cacheKey,
    async () => {
      const skip = (page - 1) * limit;
      
      // 构建查询条件
      const whereConditions: Record<string, unknown> = {};
      
      if (filters?.category && filters.category !== 'all') {
        switch (filters.category) {
          case 'subtitle':
            // 中文字幕筛选：检查磁力链接的hasSubtitle字段或标题包含中文字幕关键词
            whereConditions.OR = [
              {
                magnets: {
                  some: {
                    hasSubtitle: true
                  }
                }
              },
              {
                title: {
                  contains: '中文',
                  mode: 'insensitive'
                }
              },
              {
                title: {
                  contains: '字幕',
                  mode: 'insensitive'
                }
              }
            ];
            break;
          case 'hd':
            // 高清筛选：检查磁力链接的isHD字段或标题包含高清关键词
            whereConditions.OR = [
              {
                magnets: {
                  some: {
                    isHD: true
                  }
                }
              },
              {
                title: {
                  contains: 'HD',
                  mode: 'insensitive'
                }
              },
              {
                title: {
                  contains: '高清',
                  mode: 'insensitive'
                }
              }
            ];
            break;
          case 'censored':
            // 有码筛选：基于影片ID模式判断（排除无码模式）
            whereConditions.NOT = {
              id: {
                startsWith: 'n'
              }
            };
            // 同时排除其他常见无码前缀
            whereConditions.AND = [
              { NOT: { id: { startsWith: 'heyzo' } } },
              { NOT: { id: { startsWith: 'xxx-av' } } },
              { NOT: { id: { startsWith: 'k' } } },
              { NOT: { id: { contains: 'carib' } } },
              { NOT: { id: { contains: 'pondo' } } },
              { NOT: { id: { contains: 'gachi' } } },
              { NOT: { id: { contains: '1pon' } } },
              { NOT: { id: { contains: 'mura' } } },
              { NOT: { id: { contains: 'siro' } } },
              { NOT: { id: { contains: 'fc2' } } }
            ];
            break;
          case 'uncensored':
            // 无码筛选：基于影片ID模式判断
            whereConditions.OR = [
              { id: { startsWith: 'n' } },
              { id: { startsWith: 'heyzo' } },
              { id: { startsWith: 'xxx-av' } },
              { id: { startsWith: 'k' } },
              { id: { contains: 'carib' } },
              { id: { contains: 'pondo' } },
              { id: { contains: 'gachi' } },
              { id: { contains: '1pon' } },
              { id: { contains: 'mura' } },
              { id: { contains: 'siro' } },
              { id: { contains: 'fc2' } }
            ];
            break;
          case 'trending':
            // 热门筛选：可以基于点赞数、评论数等，暂时使用创建时间
            // 这里可以后续添加更复杂的热门算法
            break;
          case 'latest':
            // 最新筛选：默认按创建时间排序，不需要额外条件
            break;
          default:
            // 其他分类可能是genre筛选
            whereConditions.genres = {
              some: {
                genre: {
                  name: {
                    contains: filters.category,
                    mode: 'insensitive'
                  }
                }
              }
            };
        }
      }

      if (filters?.star) {
        whereConditions.stars = {
          some: {
            star: {
              name: {
                contains: filters.star,
                mode: 'insensitive'
              }
            }
          }
        };
      }

      // 构建排序条件
      let orderBy: Record<string, string> = { createdAt: 'desc' };
      if (filters?.sortBy) {
        switch (filters.sortBy) {
          case 'latest':
            orderBy = { createdAt: 'desc' };
            break;
          case 'popular':
            orderBy = { id: 'desc' }; // 临时使用ID排序，后续可以添加浏览量字段
            break;
          case 'rating':
            orderBy = { title: 'asc' }; // 临时使用标题排序，后续可以添加评分字段
            break;
          case 'duration':
            orderBy = { videoLength: 'desc' };
            break;
        }
      }

      const [movies, total] = await Promise.all([
        prisma.movie.findMany({
          where: whereConditions,
          skip,
          take: limit,
          include: {
            director: true,
            producer: true,
            publisher: true,
            series: true,
            stars: {
              include: {
                star: true
              }
            },
            genres: {
              include: {
                genre: true
              }
            },

            magnets: true
          },
          orderBy
        }),
        prisma.movie.count({
          where: whereConditions
        })
      ]);

      // 批量获取所有影片的统计数据，减少数据库连接数
      const movieIds = movies.map(movie => movie.id);

      // 使用单个查询获取所有统计数据
      const [likesStats, favoritesStats, viewsStats] = await Promise.all([
        prisma.like.groupBy({
          by: ['movieId'],
          where: {
            movieId: { in: movieIds },
            type: 'LIKE'
          },
          _count: { id: true }
        }),
        prisma.favorite.groupBy({
          by: ['movieId'],
          where: { movieId: { in: movieIds } },
          _count: { id: true }
        }),
        prisma.view.groupBy({
          by: ['movieId'],
          where: { movieId: { in: movieIds } },
          _count: { id: true }
        })
      ]);

      // 创建统计数据映射表
      const likesMap = new Map(likesStats.map(stat => [stat.movieId, stat._count.id]));
      const favoritesMap = new Map(favoritesStats.map(stat => [stat.movieId, stat._count.id]));
      const viewsMap = new Map(viewsStats.map(stat => [stat.movieId, stat._count.id]));

      // 转换数据格式以匹配LocalMovie类型
      const formattedMovies: LocalMovie[] = movies.map((movie) => {
        const { ...movieWithoutSamples } = movie;

        // 从映射表获取统计数据，默认为0
        const likesCount = likesMap.get(movie.id) || 0;
        const favoritesCount = favoritesMap.get(movie.id) || 0;
        const viewsCount = viewsMap.get(movie.id) || 0;

        return {
          ...movieWithoutSamples,
          views: viewsCount,
          likes: likesCount,
          favorites: favoritesCount,
          stars: movie.stars.map(ms => ({
            id: ms.star.id,
            name: ms.star.name,
            localAvatar: ms.star.localAvatar
          })),
          genres: movie.genres.map(mg => ({
            id: mg.genre.id,
            name: mg.genre.name
          })),
          samples: [], // 不再返回样品图片
          magnets: movie.magnets.map(magnet => ({
            ...magnet,
            numberSize: magnet.numberSize ? magnet.numberSize.toString() : null
          }))
        };
      });

      return {
        movies: formattedMovies,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    },
    CacheExpiry.MEDIUM
  );
}

/**
 * 带缓存的影片详情获取
 */
export async function getCachedMovie(id: string): Promise<LocalMovie | null> {
  const cacheKey = CacheKeys.movie(id);

  return await cacheService.cached(
    cacheKey,
    async () => {
      // 使用更新后的getMovieById函数，包含相关影片信息
      const movieWithRelated = await getMovieById(id);

      if (!movieWithRelated) return null;

      // 获取真实统计数据
      const [likesCount, favoritesCount, viewsCount] = await Promise.all([
        prisma.like.count({
          where: {
            movieId: movieWithRelated.id,
            type: 'LIKE'
          }
        }),
        prisma.favorite.count({
          where: { movieId: movieWithRelated.id }
        }),
        prisma.view.count({
          where: { movieId: movieWithRelated.id }
        })
      ]);

      // 处理相关影片数据，只返回数据库中真实存在的影片
      const relatedMovies = movieWithRelated.relatedMoviesWithDetails
        ?.filter(related => related.existsInDatabase && related.movieDetails)
        .map(related => ({
          id: related.movieDetails!.id,
          title: related.movieDetails!.title,
          img: related.movieDetails!.localImg || related.movieDetails!.img,
          date: related.movieDetails!.date,
          stars: related.movieDetails!.stars.map(ms => ({
            id: ms.star.id,
            name: ms.star.name
          }))
        })) || [];

      const formattedMovie: LocalMovie = {
        ...movieWithRelated,
        views: viewsCount,
        likes: likesCount,
        favorites: favoritesCount,
        stars: movieWithRelated.stars.map(ms => ({
          id: ms.star.id,
          name: ms.star.name,
          localAvatar: ms.star.localAvatar
        })),
        genres: movieWithRelated.genres.map(mg => ({
          id: mg.genre.id,
          name: mg.genre.name
        })),
        samples: [], // 不再返回样品图片
        magnets: movieWithRelated.magnets.map(magnet => ({
          ...magnet,
          numberSize: magnet.numberSize ? magnet.numberSize.toString() : null
        })),
        relatedMovies // 添加相关影片信息
      };

      return formattedMovie;
    },
    CacheExpiry.LONG
  );
}

/**
 * 带缓存的演员详情获取
 */
export async function getCachedStar(id: string) {
  const cacheKey = CacheKeys.star(id);

  return await cacheService.cached(
    cacheKey,
    async () => {
      const star = await prisma.star.findUnique({
        where: { id },
        include: {
          movies: {
            include: {
              movie: {
                include: {
                  director: true,
                  producer: true,
                  publisher: true,
                  series: true,
                  genres: {
                    include: {
                      genre: true
                    }
                  },

                  magnets: true
                }
              }
            },
            orderBy: {
              movie: {
                createdAt: 'desc'
              }
            }
          }
        }
      });

      return star;
    },
    CacheExpiry.LONG
  );
}

/**
 * 带缓存的搜索功能
 */
export async function getCachedSearch(options: SearchOptions): Promise<SearchResult> {
  const filtersKey = JSON.stringify({
    category: options.category,
    star: options.star,
    sortBy: options.sortBy,
    sortOrder: options.sortOrder
  });
  const cacheKey = CacheKeys.movieSearch(options.query || '', options.page || 1, filtersKey);

  return await cacheService.cached(
    cacheKey,
    async () => {
      return await performFullTextSearch(options);
    },
    CacheExpiry.SHORT // 搜索结果缓存时间较短，保证实时性
  );
}

/**
 * 带缓存的统计数据获取
 */
export async function getCachedStats() {
  const cacheKey = CacheKeys.stats();

  return await cacheService.cached(
    cacheKey,
    async () => {
      const [
        totalMovies,
        totalStars,
        totalGenres,
        recentImports
      ] = await Promise.all([
        prisma.movie.count(),
        prisma.star.count(),
        prisma.genre.count(),
        prisma.movie.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
            }
          }
        })
      ]);

      return {
        totalMovies,
        totalStars,
        totalGenres,
        recentImports
      };
    },
    CacheExpiry.MEDIUM
  );
}

/**
 * 带缓存的热门影片获取
 */
export async function getCachedPopularMovies(limit: number = 10): Promise<LocalMovie[]> {
  const cacheKey = CacheKeys.popularMovies();

  return await cacheService.cached(
    cacheKey,
    async () => {
      const movies = await prisma.movie.findMany({
        take: limit,
        include: {
          director: true,
          producer: true,
          publisher: true,
          series: true,
          stars: {
            include: {
              star: true
            }
          },
          genres: {
            include: {
              genre: true
            }
          },

          magnets: true
        },
        orderBy: {
          createdAt: 'desc' // 临时使用创建时间排序，后续可以添加浏览量等指标
        }
      });

      // 批量获取所有影片的统计数据，减少数据库连接数
      const movieIds = movies.map(movie => movie.id);

      // 使用单个查询获取所有统计数据
      const [likesStats, favoritesStats, viewsStats] = await Promise.all([
        prisma.like.groupBy({
          by: ['movieId'],
          where: {
            movieId: { in: movieIds },
            type: 'LIKE'
          },
          _count: { id: true }
        }),
        prisma.favorite.groupBy({
          by: ['movieId'],
          where: { movieId: { in: movieIds } },
          _count: { id: true }
        }),
        prisma.view.groupBy({
          by: ['movieId'],
          where: { movieId: { in: movieIds } },
          _count: { id: true }
        })
      ]);

      // 创建统计数据映射表
      const likesMap = new Map(likesStats.map(stat => [stat.movieId, stat._count.id]));
      const favoritesMap = new Map(favoritesStats.map(stat => [stat.movieId, stat._count.id]));
      const viewsMap = new Map(viewsStats.map(stat => [stat.movieId, stat._count.id]));

      // 转换数据格式以匹配LocalMovie类型
      const moviesWithStats = movies.map((movie) => {
        const { ...movieWithoutSamples } = movie;

        // 从映射表获取统计数据，默认为0
        const likesCount = likesMap.get(movie.id) || 0;
        const favoritesCount = favoritesMap.get(movie.id) || 0;
        const viewsCount = viewsMap.get(movie.id) || 0;

        return {
          ...movieWithoutSamples,
          views: viewsCount,
          likes: likesCount,
          favorites: favoritesCount,
          stars: movie.stars.map(ms => ({
            id: ms.star.id,
            name: ms.star.name,
            localAvatar: ms.star.localAvatar
          })),
          genres: movie.genres.map(mg => ({
            id: mg.genre.id,
            name: mg.genre.name
          })),
          samples: [], // 不再返回样品图片
          magnets: movie.magnets.map(magnet => ({
            ...magnet,
            numberSize: magnet.numberSize ? magnet.numberSize.toString() : null
          }))
        };
      });

      return moviesWithStats as LocalMovie[];
    },
    CacheExpiry.LONG
  );
}

/**
 * 缓存失效处理
 */
export class CacheInvalidation {
  /**
   * 影片相关缓存失效
   */
  static async invalidateMovie(movieId: string) {
    await Promise.all([
      cacheService.delete(CacheKeys.movie(movieId)),
      cacheService.deletePattern('movies:*'), // 使用模式匹配删除所有影片列表缓存
      cacheService.delete(CacheKeys.popularMovies()),
      cacheService.delete(CacheKeys.stats())
    ]);
  }

  /**
   * 演员相关缓存失效
   */
  static async invalidateStar(starId: string) {
    await Promise.all([
      cacheService.delete(CacheKeys.star(starId)),
      cacheService.deletePattern(`star:${starId}:movies:*`), // 使用模式匹配删除演员相关影片缓存
      cacheService.deletePattern('movies:*') // 删除所有影片列表缓存
    ]);
  }

  /**
   * 搜索相关缓存失效
   */
  static async invalidateSearch() {
    await cacheService.deletePattern('search:*');
  }

  /**
   * 全部缓存失效
   */
  static async invalidateAll() {
    await cacheService.deletePattern('*');
  }
}

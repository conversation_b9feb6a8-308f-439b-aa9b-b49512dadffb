/**
 * 安全配置
 * 统一管理安全相关的配置和中间件
 */

import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { NextRequest } from 'next/server';

// 限流配置
export const rateLimitConfig = {
  // API通用限流
  api: rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP最多100次请求
    message: {
      error: '请求过于频繁，请稍后再试',
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter: 15 * 60 // 15分钟后重试
    },
    standardHeaders: true,
    legacyHeaders: false,
    // 跳过成功的请求计数
    skipSuccessfulRequests: false,
    // 跳过失败的请求计数
    skipFailedRequests: false,
  }),

  // 认证相关严格限流
  auth: rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5, // 每个IP最多5次认证尝试
    message: {
      error: '认证尝试过于频繁，请15分钟后再试',
      code: 'AUTH_RATE_LIMIT_EXCEEDED',
      retryAfter: 15 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),

  // 管理员操作限流
  admin: rateLimit({
    windowMs: 5 * 60 * 1000, // 5分钟
    max: 20, // 每个IP最多20次管理员操作
    message: {
      error: '管理员操作过于频繁，请稍后再试',
      code: 'ADMIN_RATE_LIMIT_EXCEEDED',
      retryAfter: 5 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),

  // 数据导入限流
  import: rateLimit({
    windowMs: 60 * 60 * 1000, // 1小时
    max: 10, // 每个IP最多10次导入操作
    message: {
      error: '数据导入操作过于频繁，请1小时后再试',
      code: 'IMPORT_RATE_LIMIT_EXCEEDED',
      retryAfter: 60 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),

  // 搜索限流
  search: rateLimit({
    windowMs: 1 * 60 * 1000, // 1分钟
    max: 30, // 每个IP最多30次搜索
    message: {
      error: '搜索请求过于频繁，请稍后再试',
      code: 'SEARCH_RATE_LIMIT_EXCEEDED',
      retryAfter: 60
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),
};

// Helmet安全头配置
export const helmetConfig = helmet({
  // 内容安全策略
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // Next.js需要
        "'unsafe-eval'", // 开发环境需要
        "https://vercel.live",
      ],
      styleSrc: [
        "'self'",
        "'unsafe-inline'", // Tailwind CSS需要
        "https://fonts.googleapis.com",
      ],
      fontSrc: [
        "'self'",
        "https://fonts.gstatic.com",
      ],
      imgSrc: [
        "'self'",
        "data:",
        "blob:",
        "https:",
        "http:", // 开发环境
      ],
      connectSrc: [
        "'self'",
        "https://vercel.live",
        "wss://vercel.live",
      ],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
    },
  },

  // 跨域嵌入保护
  crossOriginEmbedderPolicy: false, // 暂时禁用，避免图片加载问题

  // DNS预取控制
  dnsPrefetchControl: {
    allow: false,
  },

  // 强制HTTPS
  hsts: {
    maxAge: 31536000, // 1年
    includeSubDomains: true,
    preload: true,
  },

  // 隐藏X-Powered-By头
  hidePoweredBy: true,

  // IE兼容性
  ieNoOpen: true,

  // MIME类型嗅探保护
  noSniff: true,

  // 来源策略
  originAgentCluster: true,

  // 权限策略
  permittedCrossDomainPolicies: false,

  // 引用策略
  referrerPolicy: {
    policy: ["no-referrer", "strict-origin-when-cross-origin"],
  },

  // XSS保护
  xssFilter: true,
});

// CORS配置
export const corsConfig = {
  origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
    // 允许的域名列表
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://getav.net',
      'https://www.getav.net',
      process.env.NEXTAUTH_URL,
    ].filter(Boolean);

    // 开发环境允许所有来源
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }

    // 生产环境检查来源
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('不允许的CORS来源'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'X-Request-ID',
  ],
};

// CSRF保护配置
export const csrfConfig = {
  // CSRF令牌生成
  generateToken: (): string => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  },

  // 验证CSRF令牌
  validateToken: (token: string, sessionToken: string): boolean => {
    return token === sessionToken;
  },

  // 需要CSRF保护的方法
  protectedMethods: ['POST', 'PUT', 'DELETE', 'PATCH'],

  // 跳过CSRF检查的路径
  skipPaths: [
    '/api/auth/login',
    '/api/auth/register',
    '/api/health',
    '/api/monitoring',
  ],
};

// IP白名单配置
export const ipWhitelistConfig = {
  // 管理员IP白名单
  adminIPs: [
    '127.0.0.1',
    '::1',
    // 可以添加更多管理员IP
  ],

  // 检查IP是否在白名单中
  isWhitelisted: (ip: string, whitelist: string[]): boolean => {
    return whitelist.includes(ip);
  },

  // 从请求中获取真实IP
  getRealIP: (request: NextRequest): string => {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const remoteAddr = request.headers.get('remote-addr');

    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    if (realIP) {
      return realIP;
    }
    if (remoteAddr) {
      return remoteAddr;
    }
    
    return '127.0.0.1'; // 默认值
  },
};

// 安全事件日志配置
export const securityLogConfig = {
  // 需要记录的安全事件
  events: {
    RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
    UNAUTHORIZED_ACCESS: 'unauthorized_access',
    CSRF_ATTACK: 'csrf_attack',
    SUSPICIOUS_REQUEST: 'suspicious_request',
    ADMIN_ACCESS: 'admin_access',
    AUTH_FAILURE: 'auth_failure',
  },

  // 安全事件严重级别
  severity: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical',
  },
};

// 请求验证配置
export const requestValidationConfig = {
  // 最大请求体大小 (10MB)
  maxBodySize: 10 * 1024 * 1024,

  // 最大URL长度
  maxUrlLength: 2048,

  // 最大头部数量
  maxHeaders: 50,

  // 禁止的用户代理模式
  blockedUserAgents: [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    // 可以添加更多模式
  ],

  // 可疑请求模式
  suspiciousPatterns: [
    /\.\./,  // 路径遍历
    /<script/i,  // XSS尝试
    /union.*select/i,  // SQL注入尝试
    /javascript:/i,  // JavaScript协议
  ],
};

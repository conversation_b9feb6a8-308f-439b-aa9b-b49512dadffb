/**
 * 统计数据服务
 * 处理观看次数、点赞、收藏等统计功能
 */

/**
 * 记录观看次数
 */
export async function recordView(movieId: string, userId?: string): Promise<{ success: boolean; totalViews?: number }> {
  try {
    // 客户端防重复检查
    const cacheKey = `view_${movieId}_${userId || 'anonymous'}`;
    const lastRecordTime = sessionStorage.getItem(cacheKey);
    const now = Date.now();
    const oneMinute = 60 * 1000;

    // 如果1分钟内已经记录过，直接返回成功但不实际记录
    if (lastRecordTime && (now - parseInt(lastRecordTime)) < oneMinute) {
      console.log('1分钟内已记录过观看次数，跳过重复记录');
      // 获取当前观看次数
      const currentViews = await getViewCount(movieId);
      return {
        success: true,
        totalViews: currentViews
      };
    }

    const response = await fetch('/api/views', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieId,
        userId
      })
    });

    const result = await response.json();

    if (result.success) {
      // 记录本次操作时间
      sessionStorage.setItem(cacheKey, now.toString());

      if (result.data) {
        return {
          success: true,
          totalViews: result.data.totalViews
        };
      }
    }

    return { success: result.success };
  } catch (error) {
    console.error('记录观看次数失败:', error);
    return { success: false };
  }
}

/**
 * 获取电影观看次数
 */
export async function getViewCount(movieId: string): Promise<number> {
  try {
    const response = await fetch(`/api/views?movieId=${movieId}`);
    const result = await response.json();
    
    if (result.success) {
      return result.data.totalViews;
    }
    return 0;
  } catch (error) {
    console.error('获取观看次数失败:', error);
    return 0;
  }
}

/**
 * 点赞/取消点赞
 */
export async function toggleLike(movieId: string, userId?: string): Promise<{ success: boolean; isLiked: boolean; totalLikes: number }> {
  try {
    const response = await fetch('/api/likes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'LIKE',
        movieId,
        userId
      })
    });

    const result = await response.json();
    
    if (result.success) {
      // 获取更新后的点赞数
      const likesCount = await getLikesCount(movieId);
      return {
        success: true,
        isLiked: result.action === 'liked',
        totalLikes: likesCount
      };
    }
    
    return { success: false, isLiked: false, totalLikes: 0 };
  } catch (error) {
    console.error('点赞操作失败:', error);
    return { success: false, isLiked: false, totalLikes: 0 };
  }
}

/**
 * 获取点赞数
 */
export async function getLikesCount(movieId: string): Promise<number> {
  try {
    const response = await fetch(`/api/likes?movieId=${movieId}`);
    const result = await response.json();
    
    if (result.success) {
      return result.data.totalLikes || 0;
    }
    return 0;
  } catch (error) {
    console.error('获取点赞数失败:', error);
    return 0;
  }
}

/**
 * 收藏/取消收藏
 */
export async function toggleFavorite(movieId: string, userId: string): Promise<{ success: boolean; isFavorited: boolean; totalFavorites: number }> {
  try {
    const response = await fetch('/api/favorites', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieId,
        userId
      })
    });

    const result = await response.json();
    
    if (result.success) {
      // 获取更新后的收藏数
      const favoritesCount = await getFavoritesCount(movieId);
      return {
        success: true,
        isFavorited: result.action === 'favorited',
        totalFavorites: favoritesCount
      };
    }
    
    return { success: false, isFavorited: false, totalFavorites: 0 };
  } catch (error) {
    console.error('收藏操作失败:', error);
    return { success: false, isFavorited: false, totalFavorites: 0 };
  }
}

/**
 * 获取收藏数
 */
export async function getFavoritesCount(movieId: string): Promise<number> {
  try {
    const response = await fetch(`/api/favorites?movieId=${movieId}`);
    const result = await response.json();
    
    if (result.success) {
      return result.data.totalFavorites || 0;
    }
    return 0;
  } catch (error) {
    console.error('获取收藏数失败:', error);
    return 0;
  }
}

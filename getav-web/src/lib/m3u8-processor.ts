import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as https from 'https';
import * as http from 'http';
import * as crypto from 'crypto';

// M3U8文件保存路径
const M3U8_BASE_DIR = path.join(process.cwd(), 'public/m3u8');

// M3U8处理配置
const M3U8_CONFIG = {
  timeout: 60000, // 60秒超时
  retryCount: 3,
  retryDelay: 2000, // 2秒重试延迟
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
};

/**
 * M3U8文件信息接口
 */
export interface M3U8FileInfo {
  originalUrl: string;
  localPath: string;
  publicPath: string;
  movieCode: string;
  fileSize?: number;
  downloadTime?: number;
}

/**
 * M3U8解析结果接口
 */
export interface M3U8ParseResult {
  isValid: boolean;
  version?: string;
  targetDuration?: number;
  segments: string[];
  totalDuration?: number;
  error?: string;
}

/**
 * 确保目录存在
 */
function ensureDirExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 获取哈希文件名
 */
function getHashedFileName(url: string, movieCode: string): string {
  const hash = crypto.createHash('md5').update(url + movieCode).digest('hex');
  return `${movieCode}_${hash.substring(0, 8)}.m3u8`;
}

/**
 * 生成M3U8本地存储路径
 */
export function getM3U8LocalPath(movieCode: string, originalUrl: string): { localPath: string; publicPath: string } {
  const playlistDir = path.join(M3U8_BASE_DIR, 'playlists');
  ensureDirExists(playlistDir);
  
  const fileName = getHashedFileName(originalUrl, movieCode);
  const localPath = path.join(playlistDir, fileName);
  const publicPath = `/m3u8/playlists/${fileName}`;
  
  return { localPath, publicPath };
}

/**
 * 解析M3U8文件内容
 */
export function parseM3U8Content(content: string): M3U8ParseResult {
  const result: M3U8ParseResult = {
    isValid: false,
    segments: []
  };

  try {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    // 检查是否是有效的M3U8文件
    if (!lines[0] || !lines[0].startsWith('#EXTM3U')) {
      result.error = 'Invalid M3U8 file: missing #EXTM3U header';
      return result;
    }

    result.isValid = true;
    let totalDuration = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 解析版本
      if (line.startsWith('#EXT-X-VERSION:')) {
        result.version = line.split(':')[1];
      }
      
      // 解析目标持续时间
      if (line.startsWith('#EXT-X-TARGETDURATION:')) {
        result.targetDuration = parseInt(line.split(':')[1]);
      }
      
      // 解析片段信息
      if (line.startsWith('#EXTINF:')) {
        const duration = parseFloat(line.split(':')[1].split(',')[0]);
        totalDuration += duration;
        
        // 下一行应该是片段URL
        if (i + 1 < lines.length && !lines[i + 1].startsWith('#')) {
          result.segments.push(lines[i + 1]);
        }
      }
    }

    result.totalDuration = totalDuration;
    
    console.log(`M3U8解析完成: ${result.segments.length}个片段, 总时长: ${totalDuration.toFixed(2)}秒`);
    
  } catch (error) {
    result.error = `M3U8解析错误: ${error instanceof Error ? error.message : String(error)}`;
    console.error(result.error);
  }

  return result;
}

/**
 * 延迟函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 下载M3U8文件（带重试机制）
 */
async function downloadM3U8FileWithRetry(url: string, localPath: string, retryCount: number = M3U8_CONFIG.retryCount): Promise<boolean> {
  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      console.log(`尝试下载M3U8文件 (${attempt}/${retryCount}): ${url}`);

      const success = await downloadM3U8FileSingle(url, localPath);
      if (success) {
        console.log(`M3U8文件下载成功: ${localPath}`);
        return true;
      }

      if (attempt < retryCount) {
        console.log(`下载失败，${M3U8_CONFIG.retryDelay}ms后重试...`);
        await delay(M3U8_CONFIG.retryDelay);
      }

    } catch (error) {
      console.error(`下载M3U8文件失败 (尝试 ${attempt}/${retryCount}):`, error);

      if (attempt < retryCount) {
        await delay(M3U8_CONFIG.retryDelay);
      }
    }
  }

  console.error(`M3U8文件下载失败，已重试${retryCount}次: ${url}`);
  return false;
}

/**
 * 单次下载M3U8文件
 */
function downloadM3U8FileSingle(url: string, localPath: string): Promise<boolean> {
  return new Promise((resolve) => {
    const file = fs.createWriteStream(localPath);

    // 根据URL协议选择http或https模块
    const isHttps = url.startsWith('https:');
    const httpModule = isHttps ? https : http;

    const request = httpModule.get(url, {
      headers: {
        'User-Agent': M3U8_CONFIG.userAgent,
        'Accept': 'application/vnd.apple.mpegurl, application/x-mpegURL, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache'
      },
      timeout: M3U8_CONFIG.timeout
    }, (response) => {
      if (response.statusCode !== 200) {
        file.close();
        fs.unlink(localPath, () => {});
        console.error(`M3U8下载失败，状态码: ${response.statusCode}`);
        resolve(false);
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        resolve(true);
      });
    });

    request.on('error', (err) => {
      file.close();
      fs.unlink(localPath, () => {});
      console.error('下载M3U8文件出错:', err.message);
      resolve(false);
    });

    request.on('timeout', () => {
      request.destroy();
      file.close();
      fs.unlink(localPath, () => {});
      console.error('M3U8文件下载超时');
      resolve(false);
    });
  });
}

/**
 * 下载并处理M3U8文件（主要导出函数）
 */
export async function downloadM3U8File(url: string, movieCode: string): Promise<M3U8FileInfo | null> {
  if (!url || !movieCode) {
    console.error('M3U8下载参数无效:', { url, movieCode });
    return null;
  }

  try {
    const startTime = Date.now();
    console.log(`开始下载M3U8文件: ${movieCode} - ${url}`);

    // 生成本地存储路径
    const { localPath, publicPath } = getM3U8LocalPath(movieCode, url);

    // 检查文件是否已存在
    if (fs.existsSync(localPath)) {
      console.log(`M3U8文件已存在，跳过下载: ${publicPath}`);

      // 验证现有文件是否有效
      try {
        const existingContent = fs.readFileSync(localPath, 'utf-8');
        const parseResult = parseM3U8Content(existingContent);

        if (parseResult.isValid) {
          const fileStats = fs.statSync(localPath);
          return {
            originalUrl: url,
            localPath,
            publicPath,
            movieCode,
            fileSize: fileStats.size,
            downloadTime: 0 // 文件已存在
          };
        } else {
          console.log(`现有M3U8文件无效，重新下载: ${parseResult.error}`);
          fs.unlinkSync(localPath);
        }
      } catch (error) {
        console.log(`读取现有M3U8文件失败，重新下载:`, error);
        fs.unlinkSync(localPath);
      }
    }

    // 下载文件
    const downloadSuccess = await downloadM3U8FileWithRetry(url, localPath);

    if (!downloadSuccess) {
      console.error(`M3U8文件下载失败: ${movieCode} - ${url}`);
      return null;
    }

    // 验证下载的文件
    try {
      const content = fs.readFileSync(localPath, 'utf-8');
      const parseResult = parseM3U8Content(content);

      if (!parseResult.isValid) {
        console.error(`下载的M3U8文件无效: ${parseResult.error}`);
        fs.unlinkSync(localPath);
        return null;
      }

      const fileStats = fs.statSync(localPath);
      const downloadTime = Date.now() - startTime;

      console.log(`M3U8文件下载并验证成功: ${movieCode} (${fileStats.size} bytes, ${downloadTime}ms)`);

      return {
        originalUrl: url,
        localPath,
        publicPath,
        movieCode,
        fileSize: fileStats.size,
        downloadTime
      };

    } catch (error) {
      console.error(`M3U8文件验证失败:`, error);
      fs.unlink(localPath, () => {});
      return null;
    }

  } catch (error) {
    console.error(`M3U8文件处理过程出错:`, error);
    return null;
  }
}

/**
 * 批量下载M3U8文件
 */
export async function downloadM3U8Files(items: Array<{ url: string; movieCode: string }>): Promise<M3U8FileInfo[]> {
  const results: M3U8FileInfo[] = [];

  console.log(`开始批量下载${items.length}个M3U8文件`);

  for (const item of items) {
    try {
      const result = await downloadM3U8File(item.url, item.movieCode);
      if (result) {
        results.push(result);
      }

      // 添加小延迟避免过度请求
      await delay(500);

    } catch (error) {
      console.error(`批量下载M3U8文件失败: ${item.movieCode}`, error);
    }
  }

  console.log(`批量下载完成: ${results.length}/${items.length} 成功`);
  return results;
}
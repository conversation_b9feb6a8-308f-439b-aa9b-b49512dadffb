/**
 * 时间工具函数测试
 */

import { formatRelativeTime, formatViewCount, formatNumber } from '../time-utils';

describe('时间工具函数', () => {
  describe('formatRelativeTime', () => {
    const now = new Date('2024-01-15 12:00:00');
    
    // Mock Date.now()
    beforeAll(() => {
      jest.spyOn(Date, 'now').mockImplementation(() => now.getTime());
      jest.spyOn(global, 'Date').mockImplementation((date?: string | number | Date) => {
        if (date) {
          return new (jest.requireActual('Date'))(date);
        }
        return now;
      });
    });

    afterAll(() => {
      jest.restoreAllMocks();
    });

    test('应该正确格式化刚刚', () => {
      const date = new Date('2024-01-15 11:59:30');
      expect(formatRelativeTime(date)).toBe('刚刚');
    });

    test('应该正确格式化分钟前', () => {
      const date = new Date('2024-01-15 11:55:00');
      expect(formatRelativeTime(date)).toBe('5分钟前');
    });

    test('应该正确格式化小时前', () => {
      const date = new Date('2024-01-15 10:00:00');
      expect(formatRelativeTime(date)).toBe('2小时前');
    });

    test('应该正确格式化天前', () => {
      const date = new Date('2024-01-13 12:00:00');
      expect(formatRelativeTime(date)).toBe('2天前');
    });

    test('应该正确格式化月前', () => {
      const date = new Date('2023-11-15 12:00:00');
      expect(formatRelativeTime(date)).toBe('2个月前');
    });

    test('应该正确格式化年前', () => {
      const date = new Date('2022-01-15 12:00:00');
      expect(formatRelativeTime(date)).toBe('2年前');
    });

    test('应该处理无效日期', () => {
      expect(formatRelativeTime('invalid-date')).toBe('未知时间');
      expect(formatRelativeTime(null)).toBe('未知时间');
      expect(formatRelativeTime(undefined)).toBe('未知时间');
    });
  });

  describe('formatViewCount', () => {
    test('应该正确格式化小于1000的观看次数', () => {
      expect(formatViewCount(123)).toBe('123次观看');
      expect(formatViewCount(0)).toBe('0次观看');
    });

    test('应该正确格式化千级别的观看次数', () => {
      expect(formatViewCount(1500)).toBe('1.5k次观看');
      expect(formatViewCount(9999)).toBe('10.0k次观看');
    });

    test('应该正确格式化万级别的观看次数', () => {
      expect(formatViewCount(15000)).toBe('1.5万次观看');
      expect(formatViewCount(999999)).toBe('100.0万次观看');
    });

    test('应该正确格式化百万级别的观看次数', () => {
      expect(formatViewCount(1500000)).toBe('1.5M次观看');
    });
  });

  describe('formatNumber', () => {
    test('应该正确格式化小于1000的数字', () => {
      expect(formatNumber(123)).toBe('123');
      expect(formatNumber(0)).toBe('0');
    });

    test('应该正确格式化千级别的数字', () => {
      expect(formatNumber(1500)).toBe('1.5k');
      expect(formatNumber(9999)).toBe('10.0k');
    });

    test('应该正确格式化万级别的数字', () => {
      expect(formatNumber(15000)).toBe('1.5万');
      expect(formatNumber(999999)).toBe('100.0万');
    });

    test('应该正确格式化百万级别的数字', () => {
      expect(formatNumber(1500000)).toBe('1.5M');
    });
  });
});

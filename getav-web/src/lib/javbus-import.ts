import axios from 'axios';
import fs from 'fs';
import path from 'path';
import https from 'https';
import crypto from 'crypto';
import type { JavbusMovie, JavbusStar, JavbusMagnet, ImportStatus } from '@/types/javbus';
import { processImage, type ImageType } from './image-processor';

// 配置axios全局超时
axios.defaults.timeout = 60000;
axios.defaults.headers.common['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

// JAVBUS API URL
const JAVBUS_API_URL = process.env.JAVBUS_API_URL || 'http://localhost:3001/api';

// 图片保存路径 - 改为getav
const IMAGE_BASE_DIR = path.join(process.cwd(), 'public/images/getav');

// 导入状态
const importStatus: ImportStatus = {
  isImporting: false,
  totalMovies: 0,
  processedMovies: 0,
  successfulMovies: 0,
  failedMovies: 0,
  startTime: undefined,
  endTime: undefined,
  lastError: undefined,
  currentMovieId: undefined
};

/**
 * 获取哈希文件名
 */
function getHashedFileName(url: string): string {
  const hash = crypto.createHash('md5').update(url).digest('hex');
  const ext = path.extname(url) || '.jpg';
  return `${hash}${ext}`;
}

/**
 * 确保目录存在
 */
function ensureDirExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 将缩略图URL转换为高清封面URL
 */
function convertToHighResUrl(url: string): string {
  if (!url || typeof url !== 'string') return url;
  
  if (url.includes('/pics/thumb/')) {
    const parts = url.split('/');
    const filename = parts[parts.length - 1];
    const dotIndex = filename.lastIndexOf('.');
    const name = dotIndex > 0 ? filename.substring(0, dotIndex) : filename;
    const ext = dotIndex > 0 ? filename.substring(dotIndex) : '';
    return url.replace('/pics/thumb/', '/pics/cover/').replace(filename, `${name}_b${ext}`);
  }
  
  if (url.includes('/thumbs/') && !url.includes('_b')) {
    return url.replace('/thumbs/', '/cover/').replace('.jpg', '_b.jpg');
  }
  
  return url;
}

/**
 * 下载图片并保存到本地 - 使用防爬虫机制
 */
async function downloadImage(url: string, type: 'cover' | 'actress' = 'cover'): Promise<string | null> {
  if (!url) return null;

  // 如果是封面图片，转换为高清版本
  if (type === 'cover') {
    url = convertToHighResUrl(url);
  }

  console.log(`正在下载图片: ${url}`);
  const imageDirPath = path.join(IMAGE_BASE_DIR, type);
  ensureDirExists(imageDirPath);

  const hashedFileName = getHashedFileName(url);
  const filePath = path.join(imageDirPath, hashedFileName);
  const publicPath = `/images/getav/${type}/${hashedFileName}`;

  // 如果文件已存在，直接返回路径
  if (fs.existsSync(filePath)) {
    console.log(`图片已存在，跳过下载: ${publicPath}`);
    return publicPath;
  }

  return new Promise((resolve) => {
    const file = fs.createWriteStream(filePath);

    const request = https.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.javbus.com/'
      },
      timeout: 60000
    }, (response) => {
      if (response.statusCode !== 200) {
        file.close();
        fs.unlink(filePath, () => {});
        console.error(`下载失败，状态码: ${response.statusCode}`);
        resolve(null);
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        console.log(`图片下载成功: ${publicPath}`);
        resolve(publicPath);
      });
    });

    request.on('error', (err) => {
      file.close();
      fs.unlink(filePath, () => {});
      console.error('下载图片出错:', err.message);
      resolve(null);
    });

    request.on('timeout', () => {
      request.destroy();
      file.close();
      fs.unlink(filePath, () => {});
      console.error(`下载图片超时 (60秒): ${url}`);
      resolve(null);
    });

    const timeoutId = setTimeout(() => {
      if (!request.destroyed) {
        request.destroy();
        file.close();
        fs.unlink(filePath, () => {});
        console.error(`下载图片超时保护触发 (60秒): ${url}`);
        resolve(null);
      }
    }, 60000);

    request.on('close', () => clearTimeout(timeoutId));
    request.on('end', () => clearTimeout(timeoutId));
  });
}

/**
 * 增强版图片下载和处理函数 - 集成Sharp处理
 */
async function downloadAndProcessImage(
  url: string,
  type: ImageType = 'cover',
  options: {
    generateThumbnails?: boolean;
    outputFormat?: 'webp' | 'avif' | 'jpeg' | 'png';
    preserveOriginal?: boolean;
  } = {}
): Promise<{
  originalPath: string | null;
  processedPaths: { [key: string]: string };
  publicPath: string | null;
}> {
  const {
    generateThumbnails = true,
    outputFormat = 'webp',
    preserveOriginal = true
  } = options;

  // 首先下载原图 - 只支持cover和actress类型
  const downloadType = type === 'samples' ? 'cover' : type;
  const originalPath = await downloadImage(url, downloadType);
  if (!originalPath) {
    return {
      originalPath: null,
      processedPaths: {},
      publicPath: null
    };
  }

  try {
    // 获取本地文件路径
    const localFilePath = path.join(process.cwd(), 'public', originalPath);
    const outputDir = path.join(IMAGE_BASE_DIR, type);

    // 使用Sharp处理图片
    const processResult = await processImage(
      localFilePath,
      outputDir,
      type,
      url,
      {
        generateThumbnails,
        outputFormat,
        preserveOriginal
      }
    );

    // 生成公共路径映射
    const processedPaths: { [key: string]: string } = {};
    for (const [size, localPath] of Object.entries(processResult.processedPaths)) {
      if (localPath) {
        // 转换为公共URL路径
        const relativePath = path.relative(path.join(process.cwd(), 'public'), localPath);
        processedPaths[size] = '/' + relativePath.replace(/\\/g, '/');
      }
    }

    console.log(`图片处理完成: ${url}`);
    console.log(`- 原图大小: ${processResult.metadata.originalSize} bytes`);
    console.log(`- 处理后尺寸: ${Object.keys(processedPaths).length} 个`);

    return {
      originalPath,
      processedPaths,
      publicPath: processedPaths.large || processedPaths.original || originalPath
    };

  } catch (error) {
    console.error('图片处理失败，返回原图路径:', error);
    return {
      originalPath,
      processedPaths: {},
      publicPath: originalPath
    };
  }
}

/**
 * 验证女优名字是否有效
 */
function isValidStarName(name: string): boolean {
  if (!name || typeof name !== 'string') return false;
  
  name = name.trim();
  if (name.length === 0) return false;
  if (name.includes('<') || name.includes('>')) return false;
  
  const invalidNames = [
    '画像を拡大する', 'img', 'i', 'div', 'span', 'script', 'style', 'br', 'p', 'a', 'button', 'input'
  ];
  
  if (invalidNames.includes(name.toLowerCase())) return false;
  if (name.length < 2 || name.length > 50) return false;
  
  const validCharPattern = /^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\w\s\.\-\u00C0-\u024F\u1E00-\u1EFF]+$/;
  if (!validCharPattern.test(name)) {
    console.log(`女优名字包含异常字符: "${name}"`);
    return false;
  }
  
  const pureSymbolsOrNumbers = /^[\d\W]*$/.test(name) && !/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\w]/.test(name);
  if (pureSymbolsOrNumbers) {
    console.log(`女优名字只包含数字或符号: "${name}"`);
    return false;
  }
  
  return true;
}

/**
 * 获取导入状态
 */
export function getImportStatus(): ImportStatus {
  return importStatus;
}

/**
 * 从JAVBUS API获取影片详情
 */
export async function fetchMovieDetail(movieId: string): Promise<JavbusMovie | null> {
  try {
    const response = await axios.get(`${JAVBUS_API_URL}/movies/${movieId}`);
    return response.data;
  } catch (error) {
    console.error(`获取影片详情失败: ${movieId}`, error);
    return null;
  }
}

/**
 * 从JAVBUS API获取演员详情
 */
export async function fetchStarDetail(starId: string): Promise<JavbusStar | null> {
  try {
    const response = await axios.get(`${JAVBUS_API_URL}/stars/${starId}`);
    return response.data;
  } catch (error) {
    console.error(`获取演员详情失败: ${starId}`, error);
    return null;
  }
}

/**
 * 从JAVBUS API获取磁力链接
 */
export async function fetchMagnets(movieId: string, gid: string, uc: string): Promise<JavbusMagnet[]> {
  try {
    const response = await axios.get(`${JAVBUS_API_URL}/magnets/${movieId}`, {
      params: { gid, uc }
    });
    return response.data || [];
  } catch (error) {
    console.error(`获取磁力链接失败: ${movieId}`, error);
    return [];
  }
}

export { downloadImage, downloadAndProcessImage, isValidStarName, ensureDirExists };

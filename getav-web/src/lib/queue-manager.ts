/**
 * BullMQ队列管理器
 * 统一管理所有队列的创建、任务添加和监控
 */

import { Queue, Worker, Job, QueueEvents } from 'bullmq';
import { logger } from './logger';
import {
  queueConnection,
  QueueNames,
  queueOptions,
  DataImportJob,
  ImageProcessingJob,
  CacheWarmupJob,
  NotificationJob,
  M3U8ProcessingJob,
  JobPriority
} from './queue-config';

export class QueueManager {
  private queues: Map<string, Queue> = new Map();
  private workers: Map<string, Worker> = new Map();
  private queueEvents: Map<string, QueueEvents> = new Map();
  private isInitialized = false;

  /**
   * 初始化队列管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('队列管理器已经初始化');
      return;
    }

    try {
      // 创建所有队列
      await this.createQueues();
      
      // 启动工作进程
      await this.startWorkers();
      
      // 设置事件监听
      this.setupEventListeners();
      
      this.isInitialized = true;
      logger.info('队列管理器初始化成功', {
        queues: Array.from(this.queues.keys()),
        workers: Array.from(this.workers.keys())
      });
    } catch (error) {
      logger.error('队列管理器初始化失败', error as Error);
      throw error;
    }
  }

  /**
   * 创建所有队列
   */
  private async createQueues(): Promise<void> {
    const queueNames = Object.values(QueueNames);
    
    for (const queueName of queueNames) {
      const queue = new Queue(queueName, {
        connection: queueConnection,
        defaultJobOptions: queueOptions.defaultJobOptions,
      });

      this.queues.set(queueName, queue);
      
      // 创建队列事件监听器
      const queueEvents = new QueueEvents(queueName, {
        connection: queueConnection,
      });
      
      this.queueEvents.set(queueName, queueEvents);
    }
  }

  /**
   * 启动工作进程
   */
  private async startWorkers(): Promise<void> {
    // 数据导入工作进程
    const dataImportWorker = new Worker(
      QueueNames.DATA_IMPORT,
      async (job: Job<DataImportJob>) => {
        return await this.processDataImportJob(job);
      },
      {
        connection: queueConnection,
        concurrency: 1, // 串行处理避免API速率限制
      }
    );

    // 图片处理工作进程
    const imageProcessingWorker = new Worker(
      QueueNames.IMAGE_PROCESSING,
      async (job: Job<ImageProcessingJob>) => {
        return await this.processImageJob(job);
      },
      {
        connection: queueConnection,
        concurrency: 3, // 并发处理3个任务
      }
    );

    // 缓存预热工作进程
    const cacheWarmupWorker = new Worker(
      QueueNames.CACHE_WARMUP,
      async (job: Job<CacheWarmupJob>) => {
        return await this.processCacheWarmupJob(job);
      },
      {
        connection: queueConnection,
        concurrency: 1, // 串行处理避免冲突
      }
    );

    // 通知工作进程
    const notificationWorker = new Worker(
      QueueNames.NOTIFICATION,
      async (job: Job<NotificationJob>) => {
        return await this.processNotificationJob(job);
      },
      {
        connection: queueConnection,
        concurrency: 5, // 并发处理5个通知
      }
    );

    // M3U8处理工作进程
    const m3u8ProcessingWorker = new Worker(
      QueueNames.M3U8_PROCESSING,
      async (job: Job<M3U8ProcessingJob>) => {
        return await this.processM3U8Job(job);
      },
      {
        connection: queueConnection,
        concurrency: 2, // 并发处理2个M3U8任务，避免过度请求
      }
    );

    this.workers.set(QueueNames.DATA_IMPORT, dataImportWorker);
    this.workers.set(QueueNames.IMAGE_PROCESSING, imageProcessingWorker);
    this.workers.set(QueueNames.CACHE_WARMUP, cacheWarmupWorker);
    this.workers.set(QueueNames.NOTIFICATION, notificationWorker);
    this.workers.set(QueueNames.M3U8_PROCESSING, m3u8ProcessingWorker);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    for (const [queueName, queueEvents] of this.queueEvents) {
      // 任务完成事件
      queueEvents.on('completed', ({ jobId, returnvalue }) => {
        logger.info(`任务完成: ${queueName}`, {
          jobId,
          returnvalue,
          queue: queueName
        });
      });

      // 任务失败事件
      queueEvents.on('failed', ({ jobId, failedReason }) => {
        logger.error(`任务失败: ${queueName}`, new Error(failedReason), {
          jobId,
          queue: queueName
        });
      });

      // 任务停滞事件
      queueEvents.on('stalled', ({ jobId }) => {
        logger.warn(`任务停滞: ${queueName}`, {
          jobId,
          queue: queueName
        });
      });
    }

    // Worker错误事件
    for (const [queueName, worker] of this.workers) {
      worker.on('error', (error) => {
        logger.error(`Worker错误: ${queueName}`, error);
      });
    }
  }

  /**
   * 添加数据导入任务
   */
  async addDataImportJob(
    jobData: DataImportJob['data'],
    options?: {
      priority?: number;
      delay?: number;
      attempts?: number;
    }
  ): Promise<Job<DataImportJob>> {
    const queue = this.queues.get(QueueNames.DATA_IMPORT);
    if (!queue) {
      throw new Error('数据导入队列未初始化');
    }

    const job: DataImportJob = {
      type: jobData.movieId ? 'javbus-import' : 'batch-import',
      data: jobData,
      metadata: {
        requestId: `import_${Date.now()}`,
        priority: options?.priority || JobPriority.NORMAL,
      }
    };

    return await queue.add('data-import', job, {
      priority: options?.priority || JobPriority.NORMAL,
      delay: options?.delay || 0,
      attempts: options?.attempts || 3,
    });
  }

  /**
   * 添加图片处理任务
   */
  async addImageProcessingJob(
    jobData: ImageProcessingJob['data'],
    options?: {
      priority?: number;
      delay?: number;
    }
  ): Promise<Job<ImageProcessingJob>> {
    const queue = this.queues.get(QueueNames.IMAGE_PROCESSING);
    if (!queue) {
      throw new Error('图片处理队列未初始化');
    }

    const job: ImageProcessingJob = {
      type: jobData.imageUrl ? 'download' : 'optimize',
      data: jobData,
    };

    return await queue.add('image-processing', job, {
      priority: options?.priority || JobPriority.NORMAL,
      delay: options?.delay || 0,
    });
  }

  /**
   * 添加M3U8处理任务
   */
  async addM3U8ProcessingJob(
    jobData: M3U8ProcessingJob['data'],
    options?: {
      priority?: number;
      delay?: number;
      attempts?: number;
    }
  ): Promise<Job<M3U8ProcessingJob>> {
    const queue = this.queues.get(QueueNames.M3U8_PROCESSING);
    if (!queue) {
      throw new Error('M3U8处理队列未初始化');
    }

    // 根据数据确定任务类型
    let jobType: M3U8ProcessingJob['type'] = 'download';
    if (jobData.localPath && !jobData.url) {
      jobType = 'parse';
    } else if (jobData.url && jobData.movieCode) {
      jobType = 'download';
    }

    const job: M3U8ProcessingJob = {
      type: jobType,
      data: jobData,
      metadata: {
        requestId: `m3u8_${Date.now()}`,
        priority: options?.priority || JobPriority.NORMAL,
        source: 'queue-manager'
      }
    };

    return await queue.add('m3u8-processing', job, {
      priority: options?.priority || JobPriority.NORMAL,
      delay: options?.delay || 0,
      attempts: options?.attempts || 3,
    });
  }

  /**
   * 处理数据导入任务
   */
  private async processDataImportJob(job: Job<DataImportJob>): Promise<Record<string, unknown>> {
    const { type, data } = job.data;
    const startTime = Date.now();

    logger.info('开始处理数据导入任务', {
      jobId: job.id,
      type,
      data: { ...data, options: data.options }
    });

    try {
      switch (type) {
        case 'javbus-import':
          return await this.processSingleMovieImport(job, data);

        case 'batch-import':
          return await this.processBatchImport(job, data);

        case 'star-import':
          return await this.processStarImport(job, data);

        default:
          throw new Error(`不支持的导入类型: ${type}`);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('数据导入任务失败', error as Error, {
        jobId: job.id,
        type,
        duration
      });
      throw error;
    }
  }

  /**
   * 处理图片任务（占位符，后续实现）
   */
  private async processImageJob(job: Job<ImageProcessingJob>): Promise<Record<string, unknown>> {
    logger.info('处理图片任务', { jobId: job.id, data: job.data });
    // TODO: 实现具体的图片处理逻辑
    return { success: true, message: '图片处理任务完成' };
  }

  /**
   * 处理缓存预热任务（占位符，后续实现）
   */
  private async processCacheWarmupJob(job: Job<CacheWarmupJob>): Promise<Record<string, unknown>> {
    logger.info('处理缓存预热任务', { jobId: job.id, data: job.data });
    // TODO: 集成现有的缓存预热逻辑
    return { success: true, message: '缓存预热任务完成' };
  }

  /**
   * 处理通知任务（占位符，后续实现）
   */
  private async processNotificationJob(job: Job<NotificationJob>): Promise<Record<string, unknown>> {
    logger.info('处理通知任务', { jobId: job.id, data: job.data });
    // TODO: 实现通知逻辑
    return { success: true, message: '通知任务完成' };
  }

  /**
   * 获取队列状态
   */
  async getQueueStatus(queueName: string) {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }

    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      name: queueName,
      counts: {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
      },
      jobs: {
        waiting: waiting.slice(0, 10), // 只返回前10个
        active: active.slice(0, 10),
        failed: failed.slice(0, 10),
      }
    };
  }

  /**
   * 处理单个影片导入
   */
  private async processSingleMovieImport(job: Job<DataImportJob>, data: DataImportJob['data']): Promise<Record<string, unknown>> {
    const { movieId, options = {} } = data;
    if (!movieId) {
      throw new Error('缺少影片ID');
    }

    // 动态导入所需模块（避免循环依赖）
    const {
      fetchMovieDetail,
      fetchMagnets,
      downloadImage,
      fetchStarDetail,
      isValidStarName
    } = await import('@/lib/javbus-import');

    const {
      upsertMovie,
      smartUpdateMovieActors,
      smartUpdateMovieMagnets,
      upsertRelatedMovie,
      createSimilarMovieRelation,
      prisma
    } = await import('@/lib/database');

    // 更新任务进度
    await job.updateProgress(10);

    // 获取影片详情
    const movieDetail = await fetchMovieDetail(movieId);
    if (!movieDetail) {
      throw new Error(`获取影片详情失败: ${movieId}`);
    }

    await job.updateProgress(30);

    // 获取磁力链接
    const magnets = await fetchMagnets(movieId, movieDetail.gid, movieDetail.uc);

    await job.updateProgress(50);

    // 处理封面图片
    let localCoverPath = null;
    if (options.downloadImages && movieDetail.img) {
      localCoverPath = await downloadImage(movieDetail.img, 'cover');
      if (!localCoverPath && options.downloadImages) {
        throw new Error(`封面图片下载失败: ${movieId}`);
      }
    }

    await job.updateProgress(70);

    // 保存影片基本信息
    await upsertMovie(movieDetail, localCoverPath || undefined);

    // 处理演员信息
    let processedStarsCount = 0;
    if (movieDetail.stars && movieDetail.stars.length > 0) {
      const validStars = movieDetail.stars.filter(star => isValidStarName(star.name));
      const processedStars = [];

      for (const star of validStars) {
        try {
          const starDetail = await fetchStarDetail(star.id);
          if (starDetail) {
            let localStarPath = null;
            if (options.downloadImages && starDetail.avatar) {
              localStarPath = await downloadImage(starDetail.avatar, 'actress');
            }

            if (localStarPath || !options.downloadImages) {
              processedStars.push({
                ...starDetail,
                localImage: localStarPath || undefined
              });
            }
          }
        } catch (error) {
          logger.warn(`处理演员失败: ${star.name}`, { error: (error as Error).message });
        }
      }

      const updateResult = await smartUpdateMovieActors(movieId, processedStars);
      processedStarsCount = updateResult.added;
    }

    await job.updateProgress(90);

    // 处理磁力链接
    let magnetCount = 0;
    if (magnets && magnets.length > 0) {
      const magnetResult = await smartUpdateMovieMagnets(movieId, magnets);
      magnetCount = magnetResult.added + magnetResult.updated;
    }

    // 处理相关影片
    let relatedMoviesCount = 0;
    if (movieDetail.similarMovies && movieDetail.similarMovies.length > 0) {
      for (const similarMovie of movieDetail.similarMovies) {
        try {
          await upsertRelatedMovie(movieId, similarMovie.id, similarMovie.title, similarMovie.img);

          const targetMovieExists = await prisma.movie.findUnique({
            where: { id: similarMovie.id },
            select: { id: true }
          });

          if (targetMovieExists) {
            await createSimilarMovieRelation(movieId, similarMovie.id);
            relatedMoviesCount++;
          }
        } catch (error) {
          logger.warn(`处理相关影片失败: ${movieId} -> ${similarMovie.id}`, {
            error: (error as Error).message
          });
        }
      }
    }

    await job.updateProgress(100);

    return {
      success: true,
      movieId,
      title: movieDetail.title,
      localCoverImage: localCoverPath,
      starsCount: processedStarsCount,
      magnetsCount: magnetCount,
      relatedMoviesCount,
      message: `影片 ${movieId} 导入完成`
    };
  }

  /**
   * 处理批量导入
   */
  private async processBatchImport(job: Job<DataImportJob>, data: DataImportJob['data']): Promise<Record<string, unknown>> {
    const { page = 1, limit = 20, options = {} } = data;

    // 动态导入axios
    const axios = (await import('axios')).default;

    const JAVBUS_API_URL = process.env.JAVBUS_API_URL || 'http://localhost:3000/api';

    await job.updateProgress(10);

    // 获取影片列表
    const javbusResponse = await axios.get(`${JAVBUS_API_URL}/movies?page=${page}&magnet=exist`);
    const movieListData = javbusResponse.data;
    let movies = movieListData.movies || [];

    if (movies.length === 0) {
      return {
        success: true,
        page,
        processedCount: 0,
        message: `第${page}页没有数据`
      };
    }

    // 限制处理数量
    if (limit < movies.length) {
      movies = movies.slice(0, limit);
    }

    await job.updateProgress(20);

    const results = [];
    const totalMovies = movies.length;

    // 逐个处理影片
    for (let i = 0; i < movies.length; i++) {
      const movie = movies[i];
      const progress = 20 + Math.floor((i / totalMovies) * 70);

      try {
        await job.updateProgress(progress);

        // 创建子任务处理单个影片
        const singleResult = await this.processSingleMovieImport(job, {
          movieId: movie.id,
          options
        });

        results.push(singleResult);

        logger.info(`批量导入进度: ${i + 1}/${totalMovies}`, {
          jobId: job.id,
          movieId: movie.id,
          page
        });

      } catch (error) {
        logger.error(`批量导入中处理影片失败: ${movie.id}`, error as Error);
        results.push({
          success: false,
          movieId: movie.id,
          error: (error as Error).message
        });
      }
    }

    await job.updateProgress(100);

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    return {
      success: true,
      page,
      totalMovies: movies.length,
      processedCount: results.length,
      successCount,
      failureCount,
      results: results.slice(0, 5), // 只返回前5个结果作为示例
      message: `第${page}页批量导入完成: 成功${successCount}个，失败${failureCount}个`
    };
  }

  /**
   * 处理演员导入
   */
  private async processStarImport(job: Job<DataImportJob>, data: DataImportJob['data']): Promise<Record<string, unknown>> {
    const { starId, options = {} } = data;
    if (!starId) {
      throw new Error('缺少演员ID');
    }

    const { fetchStarDetail, downloadImage } = await import('@/lib/javbus-import');
    const { prisma } = await import('@/lib/database');

    await job.updateProgress(20);

    // 获取演员详情
    const starDetail = await fetchStarDetail(starId);
    if (!starDetail) {
      throw new Error(`获取演员详情失败: ${starId}`);
    }

    await job.updateProgress(50);

    // 处理头像
    let localImagePath = null;
    if (options.downloadImages && starDetail.avatar) {
      localImagePath = await downloadImage(starDetail.avatar, 'actress');
    }

    await job.updateProgress(80);

    // 保存或更新演员信息
    await prisma.star.upsert({
      where: { id: starId },
      update: {
        name: starDetail.name,
        localAvatar: localImagePath || undefined,
        updatedAt: new Date()
      },
      create: {
        id: starId,
        name: starDetail.name,
        avatar: starDetail.avatar,
        localAvatar: localImagePath || undefined
      }
    });

    await job.updateProgress(100);

    return {
      success: true,
      starId,
      name: starDetail.name,
      localImage: localImagePath,
      message: `演员 ${starDetail.name} 导入完成`
    };
  }

  /**
   * 处理M3U8任务
   */
  private async processM3U8Job(job: Job<M3U8ProcessingJob>): Promise<Record<string, unknown>> {
    const { type, data, metadata = {} } = job.data;

    logger.info(`开始处理M3U8任务: ${type}`, {
      jobId: job.id,
      type,
      data,
      metadata
    });

    try {
      switch (type) {
        case 'download':
          return await this.processM3U8Download(job, data);

        case 'parse':
          return await this.processM3U8Parse(job, data);

        case 'validate':
          return await this.processM3U8Validate(job, data);

        case 'cleanup':
          return await this.processM3U8Cleanup(job, data);

        default:
          throw new Error(`不支持的M3U8任务类型: ${type}`);
      }
    } catch (error) {
      logger.error(`M3U8任务处理失败: ${type}`, error as Error);
      throw error;
    }
  }

  /**
   * 处理M3U8文件下载
   */
  private async processM3U8Download(job: Job<M3U8ProcessingJob>, data: M3U8ProcessingJob['data']): Promise<Record<string, unknown>> {
    const { url, movieCode, options = {} } = data;

    if (!url || !movieCode) {
      throw new Error('M3U8下载任务缺少必要参数: url 和 movieCode');
    }

    // 动态导入M3U8处理模块
    const { downloadM3U8File } = await import('@/lib/m3u8-processor');

    await job.updateProgress(10);

    logger.info(`开始下载M3U8文件: ${movieCode}`, { url, options });

    // 下载M3U8文件
    const result = await downloadM3U8File(url, movieCode);

    if (!result) {
      throw new Error(`M3U8文件下载失败: ${movieCode} - ${url}`);
    }

    await job.updateProgress(80);

    // 如果提供了movieId，更新数据库记录
    if (data.movieId) {
      const { prisma } = await import('@/lib/database');

      await prisma.movie.update({
        where: { id: data.movieId },
        data: {
          tiktokPlaylistUrl: url,
          localM3u8Path: result.publicPath,
          updatedAt: new Date()
        }
      });

      logger.info(`已更新影片M3U8信息: ${data.movieId}`, {
        tiktokPlaylistUrl: url,
        localM3u8Path: result.publicPath
      });
    }

    await job.updateProgress(100);

    return {
      success: true,
      movieCode,
      url,
      localPath: result.localPath,
      publicPath: result.publicPath,
      fileSize: result.fileSize,
      downloadTime: result.downloadTime,
      message: `M3U8文件下载完成: ${movieCode}`
    };
  }

  /**
   * 处理M3U8文件解析
   */
  private async processM3U8Parse(job: Job<M3U8ProcessingJob>, data: M3U8ProcessingJob['data']): Promise<Record<string, unknown>> {
    const { localPath } = data;

    if (!localPath) {
      throw new Error('M3U8解析任务缺少必要参数: localPath');
    }

    // 动态导入M3U8处理模块
    const { parseM3U8Content } = await import('@/lib/m3u8-processor');
    const fs = await import('fs');

    await job.updateProgress(20);

    // 读取文件内容
    const content = fs.readFileSync(localPath, 'utf-8');

    await job.updateProgress(50);

    // 解析M3U8内容
    const parseResult = await parseM3U8Content(content);

    await job.updateProgress(100);

    return {
      success: true,
      localPath,
      parseResult,
      message: `M3U8文件解析完成: ${parseResult.segments.length} 个片段`
    };
  }

  /**
   * 处理M3U8文件验证
   */
  private async processM3U8Validate(job: Job<M3U8ProcessingJob>, data: M3U8ProcessingJob['data']): Promise<Record<string, unknown>> {
    const { localPath } = data;

    if (!localPath) {
      throw new Error('M3U8验证任务缺少必要参数: localPath');
    }

    const fs = await import('fs');
    const { parseM3U8Content } = await import('@/lib/m3u8-processor');

    await job.updateProgress(30);

    // 检查文件是否存在
    if (!fs.existsSync(localPath)) {
      throw new Error(`M3U8文件不存在: ${localPath}`);
    }

    await job.updateProgress(60);

    // 验证文件内容
    const content = fs.readFileSync(localPath, 'utf-8');
    const parseResult = await parseM3U8Content(content);

    if (!parseResult.isValid) {
      throw new Error(`M3U8文件格式无效: ${parseResult.error}`);
    }

    await job.updateProgress(100);

    return {
      success: true,
      localPath,
      isValid: parseResult.isValid,
      segmentCount: parseResult.segments.length,
      totalDuration: parseResult.totalDuration,
      message: `M3U8文件验证通过: ${parseResult.segments.length} 个片段`
    };
  }

  /**
   * 处理M3U8文件清理
   */
  private async processM3U8Cleanup(job: Job<M3U8ProcessingJob>, data: M3U8ProcessingJob['data']): Promise<Record<string, unknown>> {
    const { localPath } = data;

    if (!localPath) {
      throw new Error('M3U8清理任务缺少必要参数: localPath');
    }

    const fs = await import('fs');

    await job.updateProgress(50);

    // 删除文件
    if (fs.existsSync(localPath)) {
      fs.unlinkSync(localPath);
      logger.info(`已删除M3U8文件: ${localPath}`);
    }

    await job.updateProgress(100);

    return {
      success: true,
      localPath,
      message: `M3U8文件清理完成: ${localPath}`
    };
  }

  /**
   * 关闭队列管理器
   */
  async close(): Promise<void> {
    logger.info('关闭队列管理器');

    // 关闭所有Worker
    for (const worker of this.workers.values()) {
      await worker.close();
    }

    // 关闭所有队列事件监听
    for (const queueEvents of this.queueEvents.values()) {
      await queueEvents.close();
    }

    // 关闭所有队列
    for (const queue of this.queues.values()) {
      await queue.close();
    }

    this.isInitialized = false;
    logger.info('队列管理器已关闭');
  }
}

// 导出单例实例
export const queueManager = new QueueManager();

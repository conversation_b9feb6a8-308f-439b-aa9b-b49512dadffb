// 确保只在服务端运行
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// 动态导入Sharp，确保只在服务端运行
let sharp: typeof import('sharp') | null = null;

async function getSharp() {
  // 确保只在服务端环境加载Sharp
  if (typeof window !== 'undefined') {
    throw new Error('Sharp can only be used on the server side');
  }

  if (!sharp) {
    try {
      // 使用动态导入并处理ESM/CommonJS兼容性
      const sharpModule = await import('sharp');
      sharp = sharpModule.default || sharpModule;
    } catch (error) {
      console.error('Failed to load Sharp:', error);
      throw new Error('Sharp library is not available. Please ensure it is properly installed.');
    }
  }

  if (!sharp) {
    throw new Error('Sharp library failed to initialize');
  }

  return sharp;
}

/**
 * 图片处理服务
 * 使用Sharp库实现高质量图片处理、压缩和多尺寸缩略图生成
 */

// 图片处理配置
export const IMAGE_CONFIG = {
  // 支持的图片格式
  supportedFormats: ['jpeg', 'jpg', 'png', 'webp', 'avif'] as const,
  
  // 质量设置
  quality: {
    high: 95,      // 高质量（原图展示）
    medium: 85,    // 中等质量（列表展示）
    low: 75,       // 低质量（缩略图）
    thumbnail: 60  // 极小缩略图
  },
  
  // 预设尺寸
  sizes: {
    // 封面图片尺寸 (16:9 比例)
    cover: {
      original: { width: 1920, height: 1080 },
      large: { width: 800, height: 450 },
      medium: { width: 400, height: 225 },
      small: { width: 200, height: 113 },
      thumbnail: { width: 100, height: 56 }
    },
    // 演员头像尺寸 (1:1 比例)
    actress: {
      original: { width: 800, height: 800 },
      large: { width: 400, height: 400 },
      medium: { width: 200, height: 200 },
      small: { width: 100, height: 100 },
      thumbnail: { width: 50, height: 50 }
    },
    // 样品图片尺寸 (16:9 比例)
    samples: {
      original: { width: 1920, height: 1080 },
      large: { width: 800, height: 450 },
      medium: { width: 400, height: 225 },
      small: { width: 200, height: 113 },
      thumbnail: { width: 100, height: 56 }
    }
  }
} as const;

export type ImageType = 'cover' | 'actress' | 'samples';
export type ImageSize = 'original' | 'large' | 'medium' | 'small' | 'thumbnail';
export type ImageFormat = typeof IMAGE_CONFIG.supportedFormats[number];

/**
 * 图片处理结果接口
 */
export interface ProcessedImage {
  originalPath: string;
  processedPaths: {
    [key in ImageSize]?: string;
  };
  metadata: {
    originalSize: number;
    processedSizes: { [key in ImageSize]?: number };
    format: string;
    dimensions: { width: number; height: number };
  };
}

/**
 * 确保目录存在
 */
function ensureDirExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 生成文件哈希名称
 */
function generateHashedFileName(originalUrl: string, size?: ImageSize, format?: ImageFormat): string {
  const hash = crypto.createHash('md5').update(originalUrl).digest('hex');
  const sizePrefix = size && size !== 'original' ? `${size}_` : '';
  const extension = format || 'jpg';
  return `${sizePrefix}${hash}.${extension}`;
}

/**
 * 获取图片元数据
 */
export async function getImageMetadata(imagePath: string): Promise<import('sharp').Metadata> {
  try {
    const sharpInstance = await getSharp();
    if (!sharpInstance) {
      throw new Error('Sharp library is not available');
    }
    return await sharpInstance(imagePath).metadata();
  } catch (error) {
    console.error('获取图片元数据失败:', error);
    throw new Error(`无法读取图片元数据: ${imagePath}`);
  }
}

/**
 * 处理单个图片 - 生成多尺寸版本
 */
export async function processImage(
  inputPath: string,
  outputDir: string,
  imageType: ImageType,
  originalUrl: string,
  options: {
    generateThumbnails?: boolean;
    outputFormat?: ImageFormat;
    preserveOriginal?: boolean;
  } = {}
): Promise<ProcessedImage> {
  const {
    generateThumbnails = true,
    outputFormat = 'webp',
    preserveOriginal = true
  } = options;

  // 确保输出目录存在
  ensureDirExists(outputDir);

  // 获取Sharp实例
  const sharpInstance = await getSharp();
  if (!sharpInstance) {
    throw new Error('Sharp library is not available');
  }

  // 获取原图元数据
  const metadata = await getImageMetadata(inputPath);
  const originalStats = fs.statSync(inputPath);

  const result: ProcessedImage = {
    originalPath: inputPath,
    processedPaths: {},
    metadata: {
      originalSize: originalStats.size,
      processedSizes: {},
      format: metadata.format || 'unknown',
      dimensions: {
        width: metadata.width || 0,
        height: metadata.height || 0
      }
    }
  };

  // 获取该图片类型的尺寸配置
  const sizeConfig = IMAGE_CONFIG.sizes[imageType];

  try {
    // 处理每个尺寸
    for (const [sizeName, dimensions] of Object.entries(sizeConfig)) {
      const size = sizeName as ImageSize;
      
      // 如果不生成缩略图且不是原图，跳过小尺寸
      if (!generateThumbnails && size !== 'original' && size !== 'large') {
        continue;
      }

      // 生成输出文件名
      const fileName = generateHashedFileName(originalUrl, size, outputFormat);
      const outputPath = path.join(outputDir, fileName);

      // 如果文件已存在，跳过处理
      if (fs.existsSync(outputPath)) {
        console.log(`图片已存在，跳过处理: ${outputPath}`);
        result.processedPaths[size] = outputPath;
        const stats = fs.statSync(outputPath);
        result.metadata.processedSizes[size] = stats.size;
        continue;
      }

      // 创建Sharp实例
      let imageProcessor = sharpInstance(inputPath);

      // 调整尺寸（保持宽高比）
      if (size !== 'original') {
        imageProcessor = imageProcessor.resize(dimensions.width, dimensions.height, {
          fit: 'cover',
          position: 'center'
        });
      }

      // 设置输出格式和质量
      const quality = size === 'original' ? IMAGE_CONFIG.quality.high :
                     size === 'large' ? IMAGE_CONFIG.quality.medium :
                     size === 'medium' ? IMAGE_CONFIG.quality.low :
                     IMAGE_CONFIG.quality.thumbnail;

      switch (outputFormat) {
        case 'webp':
          imageProcessor = imageProcessor.webp({ quality });
          break;
        case 'avif':
          imageProcessor = imageProcessor.avif({ quality });
          break;
        case 'jpeg':
        case 'jpg':
          imageProcessor = imageProcessor.jpeg({ quality, progressive: true });
          break;
        case 'png':
          imageProcessor = imageProcessor.png({ quality });
          break;
      }

      // 保存处理后的图片
      await imageProcessor.toFile(outputPath);

      // 记录结果
      result.processedPaths[size] = outputPath;
      const processedStats = fs.statSync(outputPath);
      result.metadata.processedSizes[size] = processedStats.size;

      console.log(`图片处理完成: ${size} -> ${outputPath} (${processedStats.size} bytes)`);
    }

    // 如果需要保留原图，复制到输出目录
    if (preserveOriginal) {
      const originalFileName = generateHashedFileName(originalUrl, 'original', metadata.format as ImageFormat);
      const originalOutputPath = path.join(outputDir, originalFileName);
      
      if (!fs.existsSync(originalOutputPath)) {
        fs.copyFileSync(inputPath, originalOutputPath);
        result.processedPaths.original = originalOutputPath;
        result.metadata.processedSizes.original = originalStats.size;
      }
    }

    return result;

  } catch (error) {
    console.error('图片处理失败:', error);
    throw new Error(`图片处理失败: ${inputPath}`);
  }
}

/**
 * 批量处理图片
 */
export async function batchProcessImages(
  imageList: Array<{
    inputPath: string;
    outputDir: string;
    imageType: ImageType;
    originalUrl: string;
  }>,
  options: {
    concurrency?: number;
    generateThumbnails?: boolean;
    outputFormat?: ImageFormat;
  } = {}
): Promise<ProcessedImage[]> {
  const { concurrency = 3 } = options;
  const results: ProcessedImage[] = [];

  // 分批处理以控制并发
  for (let i = 0; i < imageList.length; i += concurrency) {
    const batch = imageList.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (item) => {
      try {
        return await processImage(
          item.inputPath,
          item.outputDir,
          item.imageType,
          item.originalUrl,
          options
        );
      } catch (error) {
        console.error(`批量处理图片失败: ${item.inputPath}`, error);
        return null;
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults.filter(Boolean) as ProcessedImage[]);
  }

  return results;
}

/**
 * 获取图片的公共URL路径
 */
export function getImagePublicPath(
  originalUrl: string,
  imageType: ImageType,
  size: ImageSize = 'large',
  format: ImageFormat = 'webp'
): string {
  const fileName = generateHashedFileName(originalUrl, size, format);
  return `/images/getav/${imageType}/${fileName}`;
}

/**
 * 清理过期的图片文件
 */
export async function cleanupOldImages(
  imageDir: string,
  maxAge: number = 30 * 24 * 60 * 60 * 1000 // 30天
): Promise<number> {
  let deletedCount = 0;
  
  try {
    const files = fs.readdirSync(imageDir);
    const now = Date.now();

    for (const file of files) {
      const filePath = path.join(imageDir, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        deletedCount++;
        console.log(`删除过期图片: ${filePath}`);
      }
    }
  } catch (error) {
    console.error('清理过期图片失败:', error);
  }

  return deletedCount;
}

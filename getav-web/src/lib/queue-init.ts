/**
 * 队列系统初始化
 * 在应用启动时初始化BullMQ队列管理器
 */

import { queueManager } from './queue-manager';
import { cronScheduler } from './cron-scheduler';
import { logger } from './logger';

let isInitialized = false;
let initPromise: Promise<void> | null = null;

/**
 * 初始化队列系统
 */
export async function initializeQueues(): Promise<void> {
  // 避免重复初始化
  if (isInitialized) {
    logger.info('队列系统已经初始化');
    return;
  }

  // 如果正在初始化，等待完成
  if (initPromise) {
    logger.info('队列系统正在初始化，等待完成...');
    return initPromise;
  }

  initPromise = (async () => {
    try {
      logger.info('开始初始化队列系统...');
      
      // 初始化队列管理器
      await queueManager.initialize();

      // 初始化定时任务调度器
      await cronScheduler.initialize();

      isInitialized = true;
      logger.info('队列系统和定时任务系统初始化完成');
      
    } catch (error) {
      logger.error('队列系统初始化失败', error as Error);
      initPromise = null; // 重置，允许重试
      throw error;
    }
  })();

  return initPromise;
}

/**
 * 检查队列系统是否已初始化
 */
export function isQueuesInitialized(): boolean {
  return isInitialized;
}

/**
 * 关闭队列系统
 */
export async function closeQueues(): Promise<void> {
  if (!isInitialized) {
    return;
  }

  try {
    logger.info('关闭队列系统和定时任务系统...');

    // 关闭定时任务调度器
    cronScheduler.destroy();

    // 关闭队列管理器
    await queueManager.close();

    isInitialized = false;
    initPromise = null;
    logger.info('队列系统和定时任务系统已关闭');
  } catch (error) {
    logger.error('关闭系统失败', error as Error);
    throw error;
  }
}

// 进程退出时自动关闭队列
if (typeof process !== 'undefined') {
  process.on('SIGINT', async () => {
    logger.info('收到SIGINT信号，关闭队列系统和定时任务系统...');
    await closeQueues();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    logger.info('收到SIGTERM信号，关闭队列系统和定时任务系统...');
    await closeQueues();
    process.exit(0);
  });
}

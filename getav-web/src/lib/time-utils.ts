/**
 * 时间工具函数
 */

/**
 * 格式化相对时间
 * @param dateString 日期字符串
 * @returns 相对时间字符串（如：1分钟前、2小时前、3天前等）
 */
export function formatRelativeTime(dateString: string | Date | null | undefined): string {
  if (!dateString) return '未知时间';

  try {
    const date = new Date(dateString);
    const now = new Date();
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '未知时间';
    }

    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // 如果是未来时间，返回具体日期
    if (diffInSeconds < 0) {
      return date.toLocaleDateString('zh-CN');
    }

    // 小于1分钟
    if (diffInSeconds < 60) {
      return '刚刚';
    }

    // 小于1小时
    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}分钟前`;
    }

    // 小于1天
    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}小时前`;
    }

    // 小于1个月（30天）
    if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}天前`;
    }

    // 小于1年（365天）
    if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000);
      return `${months}个月前`;
    }

    // 1年以上
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years}年前`;

  } catch (error) {
    console.error('格式化相对时间失败:', error);
    return '未知时间';
  }
}

/**
 * 格式化观看次数
 * @param count 观看次数
 * @returns 格式化的观看次数字符串
 */
export function formatViewCount(count: number): string {
  if (count < 1000) {
    return `${count}次观看`;
  }
  
  if (count < 10000) {
    return `${(count / 1000).toFixed(1)}k次观看`;
  }
  
  if (count < 1000000) {
    return `${(count / 10000).toFixed(1)}万次观看`;
  }
  
  return `${(count / 1000000).toFixed(1)}M次观看`;
}

/**
 * 格式化数字为简洁形式
 * @param num 数字
 * @returns 格式化的数字字符串
 */
export function formatNumber(num: number): string {
  if (num < 1000) {
    return num.toString();
  }
  
  if (num < 10000) {
    return `${(num / 1000).toFixed(1)}k`;
  }
  
  if (num < 1000000) {
    return `${(num / 10000).toFixed(1)}万`;
  }
  
  return `${(num / 1000000).toFixed(1)}M`;
}

/**
 * 结构化日志系统
 * 提供统一的日志记录、格式化和输出功能
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, unknown>;
  error?: Error;
  requestId?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  path?: string;
  duration?: number;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableStructured: boolean;
  maxFileSize: number; // MB
  maxFiles: number;
}

class Logger {
  private config: LoggerConfig;
  private requestIdCounter = 0;

  constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
      enableConsole: true,
      enableFile: process.env.NODE_ENV === 'production',
      enableStructured: true,
      maxFileSize: 10, // 10MB
      maxFiles: 5,
      ...config
    };
  }

  /**
   * 生成请求ID
   */
  generateRequestId(): string {
    return `req_${Date.now()}_${++this.requestIdCounter}`;
  }

  /**
   * 格式化日志条目
   */
  private formatLogEntry(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp;
    
    if (this.config.enableStructured) {
      return JSON.stringify({
        timestamp,
        level: levelName,
        message: entry.message,
        context: entry.context,
        error: entry.error ? {
          name: entry.error.name,
          message: entry.error.message,
          stack: entry.error.stack
        } : undefined,
        requestId: entry.requestId,
        userId: entry.userId,
        ip: entry.ip,
        userAgent: entry.userAgent,
        method: entry.method,
        path: entry.path,
        duration: entry.duration
      });
    }

    let formatted = `[${timestamp}] ${levelName}: ${entry.message}`;
    
    if (entry.requestId) {
      formatted += ` [${entry.requestId}]`;
    }
    
    if (entry.context && Object.keys(entry.context).length > 0) {
      formatted += ` ${JSON.stringify(entry.context)}`;
    }
    
    if (entry.error) {
      formatted += `\n${entry.error.stack}`;
    }
    
    return formatted;
  }

  /**
   * 输出日志
   */
  private output(entry: LogEntry): void {
    if (entry.level < this.config.level) {
      return;
    }

    const formatted = this.formatLogEntry(entry);

    // 控制台输出
    if (this.config.enableConsole) {
      switch (entry.level) {
        case LogLevel.DEBUG:
          console.debug(formatted);
          break;
        case LogLevel.INFO:
          console.info(formatted);
          break;
        case LogLevel.WARN:
          console.warn(formatted);
          break;
        case LogLevel.ERROR:
        case LogLevel.FATAL:
          console.error(formatted);
          break;
      }
    }

    // 文件输出（生产环境，非Edge Runtime）
    if (this.config.enableFile && typeof window === 'undefined') {
      this.writeToFile(formatted);
    }
  }

  /**
   * 写入文件（仅服务端，非Edge Runtime）
   */
  private async writeToFile(message: string): Promise<void> {
    // 只在Node.js环境且非Edge Runtime中写入文件
    if (typeof window !== 'undefined' || process.env.NEXT_RUNTIME === 'edge') {
      return;
    }

    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      const logDir = path.join(process.cwd(), 'logs');
      const logFile = path.join(logDir, `app-${new Date().toISOString().split('T')[0]}.log`);

      // 确保日志目录存在
      try {
        await fs.access(logDir);
      } catch {
        await fs.mkdir(logDir, { recursive: true });
      }

      const logLine = `${message}\n`;
      await fs.appendFile(logFile, logLine, 'utf8');

    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 创建日志条目
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      error
    };
  }

  /**
   * Debug级别日志
   */
  debug(message: string, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.DEBUG, message, context));
  }

  /**
   * Info级别日志
   */
  info(message: string, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.INFO, message, context));
  }

  /**
   * Warning级别日志
   */
  warn(message: string, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.WARN, message, context));
  }

  /**
   * Error级别日志
   */
  error(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.ERROR, message, context, error));
  }

  /**
   * Fatal级别日志
   */
  fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.FATAL, message, context, error));
  }

  /**
   * HTTP请求日志
   */
  http(
    method: string,
    path: string,
    statusCode: number,
    duration: number,
    requestId?: string,
    userId?: string,
    ip?: string,
    userAgent?: string
  ): void {
    const entry: LogEntry = {
      ...this.createLogEntry(LogLevel.INFO, `${method} ${path} ${statusCode} - ${duration}ms`),
      requestId,
      userId,
      ip,
      userAgent,
      method,
      path,
      duration
    };
    
    this.output(entry);
  }

  /**
   * 数据库操作日志
   */
  database(
    operation: string,
    table: string,
    duration: number,
    error?: Error,
    context?: Record<string, unknown>
  ): void {
    const level = error ? LogLevel.ERROR : LogLevel.DEBUG;
    const message = error 
      ? `数据库操作失败: ${operation} on ${table} (${duration}ms)`
      : `数据库操作: ${operation} on ${table} (${duration}ms)`;
    
    this.output(this.createLogEntry(level, message, context, error));
  }

  /**
   * 缓存操作日志
   */
  cache(
    operation: 'HIT' | 'MISS' | 'SET' | 'DEL' | 'ERROR',
    key: string,
    duration?: number,
    error?: Error
  ): void {
    const level = error ? LogLevel.ERROR : LogLevel.DEBUG;
    const message = error
      ? `缓存操作失败: ${operation} ${key}`
      : `缓存操作: ${operation} ${key}${duration ? ` (${duration}ms)` : ''}`;
    
    this.output(this.createLogEntry(level, message, { operation, key, duration }, error));
  }
}

// 导出单例实例
export const logger = new Logger();

// 导出类型和工厂函数
export { Logger };
export const createLogger = (config?: Partial<LoggerConfig>) => new Logger(config);

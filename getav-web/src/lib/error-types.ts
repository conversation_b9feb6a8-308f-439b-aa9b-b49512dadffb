/**
 * 统一的错误处理类型定义
 * 提供类型安全的错误处理机制
 */

// 基础错误类型
export interface BaseError {
  code: string;
  message: string;
  details?: string;
  timestamp: string;
}

// API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: BaseError;
  message?: string;
}

// 错误代码枚举
export enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',

  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  DATABASE_QUERY_ERROR = 'DATABASE_QUERY_ERROR',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',
  
  // 缓存错误
  CACHE_CONNECTION_ERROR = 'CACHE_CONNECTION_ERROR',
  CACHE_OPERATION_ERROR = 'CACHE_OPERATION_ERROR',
  
  // 搜索错误
  SEARCH_QUERY_INVALID = 'SEARCH_QUERY_INVALID',
  SEARCH_SERVICE_ERROR = 'SEARCH_SERVICE_ERROR',
  
  // 文件操作错误
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
  FILE_PROCESSING_ERROR = 'FILE_PROCESSING_ERROR',
  
  // 认证和授权错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  
  // 业务逻辑错误
  INVALID_MOVIE_ID = 'INVALID_MOVIE_ID',
  INVALID_STAR_ID = 'INVALID_STAR_ID',
  IMPORT_FAILED = 'IMPORT_FAILED',
  DOWNLOAD_FAILED = 'DOWNLOAD_FAILED',
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 详细错误信息
export interface DetailedError extends BaseError {
  severity: ErrorSeverity;
  context?: Record<string, unknown>;
  stack?: string;
  userId?: string;
  requestId?: string;
}

// 验证错误
export interface ValidationError extends BaseError {
  field: string;
  value: unknown;
  constraint: string;
}

// 数据库错误
export interface DatabaseError extends BaseError {
  query?: string;
  table?: string;
  operation?: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';
}

// 网络错误
export interface NetworkError extends BaseError {
  url?: string;
  method?: string;
  statusCode?: number;
  responseBody?: string;
}

// 错误工厂类
export class ErrorFactory {
  /**
   * 创建基础错误
   */
  static createError(
    code: ErrorCode,
    message: string,
    details?: string
  ): BaseError {
    return {
      code,
      message,
      details,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 创建详细错误
   */
  static createDetailedError(
    code: ErrorCode,
    message: string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, unknown>
  ): DetailedError {
    return {
      code,
      message,
      severity,
      context,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 创建验证错误
   */
  static createValidationError(
    field: string,
    value: unknown,
    constraint: string,
    message?: string
  ): ValidationError {
    return {
      code: ErrorCode.VALIDATION_ERROR,
      message: message || `Validation failed for field '${field}'`,
      field,
      value,
      constraint,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 创建数据库错误
   */
  static createDatabaseError(
    message: string,
    operation?: DatabaseError['operation'],
    table?: string,
    query?: string
  ): DatabaseError {
    return {
      code: ErrorCode.DATABASE_QUERY_ERROR,
      message,
      operation,
      table,
      query,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 创建网络错误
   */
  static createNetworkError(
    message: string,
    url?: string,
    method?: string,
    statusCode?: number,
    responseBody?: string
  ): NetworkError {
    return {
      code: ErrorCode.NETWORK_ERROR,
      message,
      url,
      method,
      statusCode,
      responseBody,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 从原生错误创建错误对象
   */
  static fromError(
    error: Error,
    code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    context?: Record<string, unknown>
  ): DetailedError {
    return {
      code,
      message: error.message,
      severity: ErrorSeverity.MEDIUM,
      context,
      stack: error.stack,
      timestamp: new Date().toISOString()
    };
  }
}

// 成功响应工厂
export class ResponseFactory {
  /**
   * 创建成功响应
   */
  static success<T>(data: T, message?: string): ApiResponse<T> {
    return {
      success: true,
      data,
      message
    };
  }

  /**
   * 创建错误响应
   */
  static error(error: BaseError, message?: string): ApiResponse {
    return {
      success: false,
      error,
      message: message || error.message
    };
  }

  /**
   * 创建简单错误响应
   */
  static simpleError(
    code: ErrorCode,
    message: string,
    details?: string
  ): ApiResponse {
    return {
      success: false,
      error: ErrorFactory.createError(code, message, details),
      message
    };
  }
}

// 错误处理工具函数
export class ErrorHandler {
  /**
   * 记录错误日志
   */
  static logError(error: DetailedError): void {
    const logLevel = this.getLogLevel(error.severity);
    console[logLevel]('Error occurred:', {
      code: error.code,
      message: error.message,
      severity: error.severity,
      timestamp: error.timestamp,
      context: error.context,
      stack: error.stack
    });
  }

  /**
   * 根据严重级别获取日志级别
   */
  private static getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'log';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'warn';
    }
  }

  /**
   * 判断错误是否需要重试
   */
  static shouldRetry(error: BaseError): boolean {
    const retryableCodes = [
      ErrorCode.NETWORK_ERROR,
      ErrorCode.DATABASE_CONNECTION_ERROR,
      ErrorCode.CACHE_CONNECTION_ERROR
    ];
    return retryableCodes.includes(error.code as ErrorCode);
  }

  /**
   * 获取用户友好的错误消息
   */
  static getUserFriendlyMessage(error: BaseError): string {
    const friendlyMessages: Record<string, string> = {
      [ErrorCode.NETWORK_ERROR]: '网络连接失败，请检查网络后重试',
      [ErrorCode.DATABASE_CONNECTION_ERROR]: '服务暂时不可用，请稍后重试',
      [ErrorCode.RECORD_NOT_FOUND]: '请求的内容不存在',
      [ErrorCode.VALIDATION_ERROR]: '输入信息有误，请检查后重试',
      [ErrorCode.UNAUTHORIZED]: '请先登录后再操作',
      [ErrorCode.FORBIDDEN]: '您没有权限执行此操作',
      [ErrorCode.SEARCH_QUERY_INVALID]: '搜索关键词无效，请重新输入',
      [ErrorCode.FILE_UPLOAD_ERROR]: '文件上传失败，请重试',
      [ErrorCode.IMPORT_FAILED]: '数据导入失败，请检查数据格式',
      [ErrorCode.DOWNLOAD_FAILED]: '下载失败，请稍后重试'
    };

    return friendlyMessages[error.code] || '操作失败，请稍后重试';
  }
}

import { PrismaClient } from '@prisma/client';
import type { JavbusMovie, JavbusStar, JavbusMagnet } from '@/types/javbus';

/**
 * 数据库连接管理器 - 单例模式
 * 确保整个应用只有一个Prisma客户端实例，防止连接泄漏
 */
class DatabaseManager {
  private static instance: PrismaClient | null = null;
  private static isConnected = false;

  /**
   * 获取Prisma客户端实例
   */
  public static getInstance(): PrismaClient {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
        errorFormat: 'pretty',
        datasources: {
          db: {
            url: process.env.DATABASE_URL,
          },
        },
      });

      // 设置连接事件监听
      DatabaseManager.setupEventListeners();
    }

    return DatabaseManager.instance;
  }

  /**
   * 设置数据库事件监听器
   */
  private static setupEventListeners(): void {
    if (!DatabaseManager.instance) return;

    // 只在Node.js环境中设置进程监听器
    if (typeof process !== 'undefined' && process.on) {
      // 设置进程退出时的清理
      const cleanup = async () => {
        if (DatabaseManager.instance && DatabaseManager.isConnected) {
          console.log('🔌 正在关闭数据库连接...');
          await DatabaseManager.instance.$disconnect();
          DatabaseManager.isConnected = false;
          console.log('✅ 数据库连接已关闭');
        }
      };

      process.on('beforeExit', cleanup);
      process.on('SIGINT', cleanup);
      process.on('SIGTERM', cleanup);
    }
  }

  /**
   * 连接数据库
   */
  public static async connect(): Promise<void> {
    try {
      const prisma = DatabaseManager.getInstance();

      // 测试数据库连接
      await prisma.$connect();

      // 验证数据库连接
      await prisma.$queryRaw`SELECT 1`;

      DatabaseManager.isConnected = true;
      console.log('✅ 数据库连接成功');

    } catch (error) {
      DatabaseManager.isConnected = false;
      console.error('❌ 数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  public static async disconnect(): Promise<void> {
    if (DatabaseManager.instance) {
      await DatabaseManager.instance.$disconnect();
      DatabaseManager.instance = null;
      DatabaseManager.isConnected = false;
      console.log('🔌 数据库连接已断开');
    }
  }

  /**
   * 检查连接状态
   */
  public static isConnectionActive(): boolean {
    return DatabaseManager.isConnected;
  }
}

// 导出单例实例
export const prisma = DatabaseManager.getInstance();

/**
 * 保存或更新导演信息
 */
export async function upsertDirector(director: { id: string; name: string }) {
  return await prisma.director.upsert({
    where: { id: director.id },
    update: { name: director.name },
    create: { id: director.id, name: director.name }
  });
}

/**
 * 保存或更新制作商信息
 */
export async function upsertProducer(producer: { id: string; name: string }) {
  return await prisma.producer.upsert({
    where: { id: producer.id },
    update: { name: producer.name },
    create: { id: producer.id, name: producer.name }
  });
}

/**
 * 保存或更新发行商信息
 */
export async function upsertPublisher(publisher: { id: string; name: string }) {
  return await prisma.publisher.upsert({
    where: { id: publisher.id },
    update: { name: publisher.name },
    create: { id: publisher.id, name: publisher.name }
  });
}

/**
 * 保存或更新系列信息
 */
export async function upsertSeries(series: { id: string; name: string }) {
  return await prisma.series.upsert({
    where: { id: series.id },
    update: { name: series.name },
    create: { id: series.id, name: series.name }
  });
}

/**
 * 保存或更新分类信息
 */
export async function upsertGenre(genre: { id: string; name: string }) {
  return await prisma.genre.upsert({
    where: { id: genre.id },
    update: { name: genre.name },
    create: { id: genre.id, name: genre.name }
  });
}

/**
 * 从JAVBUS API获取女优详细信息
 */
async function fetchStarDetailFromAPI(starId: string): Promise<{
  name?: string;
  avatar?: string;
  birthday?: string;
  age?: string;
  height?: string;
  bust?: string;
  waistline?: string;
  hipline?: string;
  birthplace?: string;
  hobby?: string;
} | null> {
  try {
    const JAVBUS_API_URL = process.env.JAVBUS_API_URL || 'http://localhost:3000/api';
    const axios = (await import('axios')).default;

    const response = await axios.get(`${JAVBUS_API_URL}/stars/${starId}`, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    return response.data;
  } catch (error) {
    console.warn(`⚠️ 获取女优 ${starId} 详细信息失败:`, error instanceof Error ? error.message : error);
    return null;
  }
}

/**
 * 保存或更新演员信息（增强版：自动获取详细信息）
 */
export async function upsertStar(star: JavbusStar, localAvatar?: string) {
  // 尝试从API获取详细信息
  const starDetail = await fetchStarDetailFromAPI(star.id);

  // 合并数据：优先使用API详细信息，回退到传入的基本信息
  const mergedData = {
    name: starDetail?.name || star.name,
    avatar: starDetail?.avatar || star.avatar, // 保存原始头像URL
    localAvatar: localAvatar, // 保存本地头像路径
    birthday: starDetail?.birthday || star.birthday,
    age: starDetail?.age || star.age,
    height: starDetail?.height || star.height,
    bust: starDetail?.bust || star.bust,
    waist: starDetail?.waistline || star.waistline, // API返回waistline
    hip: starDetail?.hipline || star.hipline, // API返回hipline
    birthplace: starDetail?.birthplace || star.birthplace,
    hobby: starDetail?.hobby || star.hobby,
    cupSize: star.cupSize, // 基本信息中通常没有，保持原值
    measurements: star.measurements, // 基本信息中通常没有，保持原值
    description: star.description // 基本信息中通常没有，保持原值
  };

  return await prisma.star.upsert({
    where: { id: star.id },
    update: mergedData,
    create: {
      id: star.id,
      ...mergedData
    }
  });
}

/**
 * 保存或更新影片信息
 */
export async function upsertMovie(movie: JavbusMovie, localImg?: string) {
  // 先处理关联的实体
  let directorId = null;
  let producerId = null;
  let publisherId = null;
  let seriesId = null;

  if (movie.director) {
    await upsertDirector(movie.director);
    directorId = movie.director.id;
  }

  if (movie.producer) {
    await upsertProducer(movie.producer);
    producerId = movie.producer.id;
  }

  if (movie.publisher) {
    await upsertPublisher(movie.publisher);
    publisherId = movie.publisher.id;
  }

  if (movie.series) {
    await upsertSeries(movie.series);
    seriesId = movie.series.id;
  }

  // 保存影片基本信息 - 只保存本地图片路径
  const savedMovie = await prisma.movie.upsert({
    where: { id: movie.id },
    update: {
      title: movie.title,
      img: localImg, // 只保存本地图片路径
      localImg: localImg,
      date: movie.date,
      videoLength: movie.videoLength,
      description: `${movie.title} - ${movie.id}`,
      gid: movie.gid,
      uc: movie.uc,
      directorId,
      producerId,
      publisherId,
      seriesId
    },
    create: {
      id: movie.id,
      title: movie.title,
      img: localImg, // 只保存本地图片路径
      localImg: localImg,
      date: movie.date,
      videoLength: movie.videoLength,
      description: `${movie.title} - ${movie.id}`,
      gid: movie.gid,
      uc: movie.uc,
      directorId,
      producerId,
      publisherId,
      seriesId
    }
  });

  // 处理分类关联
  if (movie.genres && movie.genres.length > 0) {
    // 先删除现有关联
    await prisma.movieGenre.deleteMany({
      where: { movieId: movie.id }
    });

    // 保存分类并创建关联
    for (const genre of movie.genres) {
      await upsertGenre(genre);
      await prisma.movieGenre.create({
        data: {
          movieId: movie.id,
          genreId: genre.id
        }
      });
    }
  }

  return savedMovie;
}

/**
 * 保存演员与影片的关联
 */
export async function createMovieStarRelation(movieId: string, starId: string) {
  return await prisma.movieStar.upsert({
    where: {
      movieId_starId: {
        movieId,
        starId
      }
    },
    update: {},
    create: {
      movieId,
      starId
    }
  });
}

/**
 * 智能更新影片演员信息 - 只在缺失时添加新内容
 */
export async function smartUpdateMovieActors(movieId: string, newStars: Array<{ id: string; name: string; avatar?: string; localImage?: string; [key: string]: unknown }>) {
  if (!newStars || newStars.length === 0) {
    return { added: 0, skipped: 0 };
  }

  // 检查影片当前的演员数量
  const currentStarsCount = await prisma.movieStar.count({
    where: { movieId }
  });

  // 如果已有演员信息，跳过更新
  if (currentStarsCount > 0) {
    console.log(`📋 影片 ${movieId} 已有 ${currentStarsCount} 个演员，跳过更新`);
    return { added: 0, skipped: currentStarsCount };
  }

  console.log(`🔄 影片 ${movieId} 缺少演员信息，开始智能更新...`);
  let addedCount = 0;

  for (const star of newStars) {
    try {
      // 保存演员信息，包含本地头像路径
      await upsertStar({
        id: star.id,
        name: star.name,
        avatar: star.avatar || ''
      }, star.localImage);

      // 创建关联
      await createMovieStarRelation(movieId, star.id);
      addedCount++;

      console.log(`✅ 添加演员关联: ${movieId} -> ${star.name}${star.localImage ? ' (含头像)' : ''}`);
    } catch (error) {
      console.error(`❌ 添加演员关联失败: ${star.name}`, error);
    }
  }

  console.log(`🎉 智能更新完成: 添加了 ${addedCount} 个演员关联`);
  return { added: addedCount, skipped: 0 };
}

/**
 * 保存样品图片
 */
export async function upsertSample(sample: {
  id: string;
  alt?: string;
  src?: string;
  thumbnail?: string;
  localSrc?: string;
  movieId: string;
}) {
  return await prisma.sample.upsert({
    where: { id: sample.id },
    update: {
      alt: sample.alt,
      src: sample.src,
      thumbnail: sample.thumbnail,
      localSrc: sample.localSrc
    },
    create: sample
  });
}

/**
 * 保存磁力链接
 */
export async function upsertMagnet(magnet: JavbusMagnet, movieId: string) {
  return await prisma.magnet.upsert({
    where: { id: magnet.id },
    update: {
      link: magnet.link,
      isHD: magnet.isHD,
      title: magnet.title,
      size: magnet.size,
      numberSize: BigInt(magnet.numberSize),
      shareDate: magnet.shareDate,
      hasSubtitle: magnet.hasSubtitle
    },
    create: {
      id: magnet.id,
      link: magnet.link,
      isHD: magnet.isHD,
      title: magnet.title,
      size: magnet.size,
      numberSize: BigInt(magnet.numberSize),
      shareDate: magnet.shareDate,
      hasSubtitle: magnet.hasSubtitle,
      movieId
    }
  });
}

/**
 * 智能更新影片磁力链接 - 统计新增和更新数量
 */
export async function smartUpdateMovieMagnets(movieId: string, newMagnets: JavbusMagnet[]) {
  if (!newMagnets || newMagnets.length === 0) {
    return { added: 0, updated: 0, total: 0 };
  }

  console.log(`🧲 影片 ${movieId} 开始处理 ${newMagnets.length} 个磁力链接...`);

  let addedCount = 0;
  let updatedCount = 0;

  for (const magnet of newMagnets) {
    try {
      // 检查磁力链接是否已存在
      const existingMagnet = await prisma.magnet.findUnique({
        where: { id: magnet.id }
      });

      if (existingMagnet) {
        // 更新现有磁力链接
        await upsertMagnet(magnet, movieId);
        updatedCount++;
        console.log(`🔄 更新磁力链接: ${magnet.title || magnet.id}`);
      } else {
        // 添加新磁力链接
        await upsertMagnet(magnet, movieId);
        addedCount++;
        console.log(`➕ 新增磁力链接: ${magnet.title || magnet.id}`);
      }
    } catch (error) {
      console.error(`❌ 处理磁力链接失败: ${magnet.id}`, error);
    }
  }

  const result = { added: addedCount, updated: updatedCount, total: newMagnets.length };
  console.log(`🎉 磁力链接处理完成 - 新增: ${addedCount}, 更新: ${updatedCount}, 总计: ${newMagnets.length}`);

  return result;
}

/**
 * 保存相似影片关联
 */
export async function createSimilarMovieRelation(originalMovieId: string, similarMovieId: string) {
  return await prisma.similarMovie.upsert({
    where: {
      originalMovieId_similarMovieId: {
        originalMovieId,
        similarMovieId
      }
    },
    update: {},
    create: {
      originalMovieId,
      similarMovieId
    }
  });
}

/**
 * 保存相关影片信息（所有相关影片，不管是否存在于数据库）
 */
export async function upsertRelatedMovie(
  originalMovieId: string,
  relatedMovieId: string,
  relatedTitle: string,
  relatedImg?: string
) {
  return await prisma.relatedMovie.upsert({
    where: {
      originalMovieId_relatedMovieId: {
        originalMovieId,
        relatedMovieId
      }
    },
    update: {
      relatedTitle,
      relatedImg
    },
    create: {
      originalMovieId,
      relatedMovieId,
      relatedTitle,
      relatedImg
    }
  });
}

/**
 * 获取影片列表
 */
export async function getMovies(page: number = 1, limit: number = 20) {
  const skip = (page - 1) * limit;

  const [movies, total] = await Promise.all([
    prisma.movie.findMany({
      skip,
      take: limit,
      include: {
        director: true,
        producer: true,
        publisher: true,
        series: true,
        stars: {
          include: {
            star: true
          }
        },
        genres: {
          include: {
            genre: true
          }
        },

        magnets: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    }),
    prisma.movie.count()
  ]);

  // 转换数据格式以匹配LocalMovie类型
  const formattedMovies = movies.map(movie => ({
    ...movie,
    stars: movie.stars.map(ms => ({
      id: ms.star.id,
      name: ms.star.name,
      localAvatar: ms.star.localAvatar
    })),
    genres: movie.genres.map(mg => ({
      id: mg.genre.id,
      name: mg.genre.name
    })),

    magnets: movie.magnets.map(magnet => ({
      id: magnet.id,
      link: magnet.link,
      isHD: magnet.isHD,
      title: magnet.title,
      size: magnet.size,
      hasSubtitle: magnet.hasSubtitle
    }))
  }));

  return {
    movies: formattedMovies,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  };
}

/**
 * 根据ID获取影片详情
 */
export async function getMovieById(id: string) {
  const movie = await prisma.movie.findUnique({
    where: { id },
    include: {
      director: true,
      producer: true,
      publisher: true,
      series: true,
      stars: {
        include: {
          star: true
        }
      },
      genres: {
        include: {
          genre: true
        }
      },
      magnets: true,
      similarMovies: {
        include: {
          similarMovie: {
            include: {
              stars: {
                include: {
                  star: true
                }
              }
            }
          }
        }
      },
      relatedMovies: true
    }
  });

  if (!movie) return null;

  // 获取相关影片中在数据库中真实存在的影片
  const relatedMoviesWithDetails = await Promise.all(
    movie.relatedMovies.map(async (related) => {
      const existingMovie = await prisma.movie.findUnique({
        where: { id: related.relatedMovieId },
        select: {
          id: true,
          title: true,
          localImg: true,
          img: true,
          date: true,
          stars: {
            include: {
              star: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      return {
        ...related,
        existsInDatabase: !!existingMovie,
        movieDetails: existingMovie
      };
    })
  );

  return {
    ...movie,
    relatedMoviesWithDetails
  };
}

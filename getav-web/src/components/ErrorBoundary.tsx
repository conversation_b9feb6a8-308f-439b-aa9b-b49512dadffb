'use client';

import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 调用错误回调
    this.props.onError?.(error, errorInfo);

    // 记录错误到控制台
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback组件，使用它
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} retry={this.handleRetry} />;
      }

      // 默认错误UI
      return <DefaultErrorFallback error={this.state.error} retry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

// 默认错误回退组件
function DefaultErrorFallback({ error, retry }: { error?: Error; retry: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6 max-w-md">
        <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-white mb-2">出现了错误</h3>
        <p className="text-gray-300 text-sm mb-4">
          抱歉，页面遇到了一些问题。请尝试刷新页面或稍后再试。
        </p>
        
        {/* 开发环境显示错误详情 */}
        {process.env.NODE_ENV === 'development' && error && (
          <details className="text-left mb-4">
            <summary className="text-red-400 cursor-pointer text-sm mb-2">
              错误详情 (开发模式)
            </summary>
            <pre className="text-xs text-gray-400 bg-gray-800 p-2 rounded overflow-auto max-h-32">
              {error.message}
              {error.stack && `\n\n${error.stack}`}
            </pre>
          </details>
        )}

        <button
          onClick={retry}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
        >
          <RefreshCw className="w-4 h-4" />
          重试
        </button>
      </div>
    </div>
  );
}

// 特定于收藏列表的错误回退组件
export function FavoriteListErrorFallback({ retry }: { error?: Error; retry: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center py-8 px-4">
      <AlertTriangle className="w-8 h-8 text-red-400 mb-3" />
      <h4 className="text-white font-medium mb-2">加载收藏列表失败</h4>
      <p className="text-gray-400 text-sm text-center mb-4">
        无法加载您的收藏列表，请检查网络连接后重试。
      </p>
      <button
        onClick={retry}
        className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
      >
        <RefreshCw className="w-3 h-3" />
        重新加载
      </button>
    </div>
  );
}

// 网络错误组件
export function NetworkError({ onRetry }: { onRetry: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
      <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 max-w-sm">
        <AlertTriangle className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
        <h4 className="text-white font-medium mb-2">网络连接问题</h4>
        <p className="text-gray-300 text-sm mb-4">
          请检查您的网络连接，然后重试。
        </p>
        <button
          onClick={onRetry}
          className="flex items-center gap-2 px-3 py-2 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 transition-colors mx-auto"
        >
          <RefreshCw className="w-3 h-3" />
          重试
        </button>
      </div>
    </div>
  );
}

// 空状态组件
export function EmptyState({ 
  icon: Icon = AlertTriangle,
  title,
  description,
  action
}: {
  icon?: React.ComponentType<{ className?: string }>;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}) {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <Icon className="w-12 h-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-medium text-white mb-2">{title}</h3>
      {description && (
        <p className="text-gray-400 text-sm mb-6 max-w-sm">{description}</p>
      )}
      {action && (
        <button
          onClick={action.onClick}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          {action.label}
        </button>
      )}
    </div>
  );
}

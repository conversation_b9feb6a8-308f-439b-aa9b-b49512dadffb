'use client';

import { useState } from 'react';
import { Check, X, AlertCircle } from 'lucide-react';
import { FavoriteList } from '@/types/favorite-list';

interface QuickCreateListFormProps {
  userId: string;
  onListCreated: (list: FavoriteList) => void;
  onCancel: () => void;
  movieId?: string;
  className?: string;
}

export default function QuickCreateListForm({
  userId,
  onListCreated,
  onCancel,
  movieId,
  className = ''
}: QuickCreateListFormProps) {
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || loading) return;

    setLoading(true);
    setError(null);

    try {
      // 创建收藏列表
      const response = await fetch('/api/favorite-lists', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.trim(),
          userId,
          isPublic: false // 快速创建默认为私密
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建收藏列表失败');
      }

      const newList = result.data;

      // 如果提供了影片ID，自动添加到新创建的列表
      if (movieId) {
        try {
          await fetch('/api/favorites', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              movieId,
              userId,
              favoriteListId: newList.id,
              action: 'add'
            }),
          });
        } catch (err) {
          console.warn('添加影片到新列表失败:', err);
        }
      }

      // 通知父组件
      onListCreated(newList);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建收藏列表失败';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-[#2a2a2a] border border-[#3f3f3f] rounded-lg p-3 ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* 错误提示 */}
        {error && (
          <div className="flex items-start gap-2 text-red-400 text-xs">
            <AlertCircle className="w-3 h-3 mt-0.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}

        {/* 输入框 */}
        <div>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="播放列表名称"
            maxLength={50}
            className="w-full bg-[#3f3f3f] text-white rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400"
            disabled={loading}
            autoFocus
          />
          <div className="text-xs text-gray-400 mt-1">
            {name.length}/50
          </div>
        </div>

        {/* 按钮组 */}
        <div className="flex gap-2">
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-[#3f3f3f] text-white rounded text-sm hover:bg-[#4f4f4f] transition-colors disabled:opacity-50"
          >
            <X className="w-3 h-3" />
            取消
          </button>
          <button
            type="submit"
            disabled={!name.trim() || loading}
            className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
            ) : (
              <Check className="w-3 h-3" />
            )}
            {loading ? '创建中...' : '创建'}
          </button>
        </div>
      </form>
    </div>
  );
}

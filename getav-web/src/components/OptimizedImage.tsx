'use client';

import Image from 'next/image';
import { useState } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  preserveOriginal?: boolean; // 新增：是否保持原始格式
}

/**
 * 优化的图片组件
 * 支持保持原始格式和最高质量显示
 */
export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  className = '',
  sizes,
  priority = false,
  quality = 100,
  preserveOriginal = false,
  ...props
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 如果需要保持原始格式，使用原生img标签
  if (preserveOriginal) {
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setImageError(true);
          setIsLoading(false);
        }}
        style={fill ? {
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          position: 'absolute',
          top: 0,
          left: 0
        } : undefined}
        {...props}
      />
    );
  }

  // 自定义图片加载器，确保最高质量
  const customLoader = ({ src }: { src: string; width: number; quality?: number }) => {
    // 如果是本地图片，直接返回原始路径
    if (src.startsWith('/images/')) {
      return src;
    }

    // 对于外部图片，保持原始URL
    return src;
  };

  if (imageError) {
    return (
      <div 
        className={`${className} bg-gray-200 dark:bg-gray-700 flex items-center justify-center`}
        style={fill ? { 
          width: '100%', 
          height: '100%', 
          position: 'absolute',
          top: 0,
          left: 0
        } : { width, height }}
      >
        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* 加载状态 */}
      {isLoading && (
        <div
          className={`absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse ${className}`}
          style={fill ? {
            width: '100%',
            height: '100%'
          } : { width, height }}
        />
      )}

      <Image
        loader={customLoader}
        src={src}
        alt={alt}
        width={width}
        height={height}
        fill={fill}
        className={`${className} transition-opacity duration-300`}
        sizes={sizes}
        priority={priority}
        quality={quality}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setImageError(true);
          setIsLoading(false);
        }}
        // 禁用默认的图片优化以保持原始质量
        unoptimized={preserveOriginal}
        {...props}
      />
    </div>
  );
}

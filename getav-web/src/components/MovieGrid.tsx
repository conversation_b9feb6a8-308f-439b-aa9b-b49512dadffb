'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import MovieCard from './MovieCard';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, AlertCircle } from 'lucide-react';
import { useBatchFavoritesContext } from '@/contexts/BatchFavoritesContext';
import type { LocalMovie } from '@/types/javbus';

interface MovieGridProps {
  initialMovies?: LocalMovie[];
  initialPage?: number;
  initialTotal?: number;
  viewMode?: 'grid' | 'list';
  category?: string;
  sortBy?: string;
}

interface MoviesResponse {
  success: boolean;
  data: {
    movies: LocalMovie[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export default function MovieGrid({
  initialMovies = [],
  initialPage = 1,
  initialTotal = 0,
  viewMode = 'grid',
  category = 'all',
  sortBy = 'latest'
}: MovieGridProps) {
  const [movies, setMovies] = useState<LocalMovie[]>(initialMovies);
  const [page, setPage] = useState(initialPage);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(initialTotal);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(initialPage < Math.ceil(initialTotal / 20));

  // 批量收藏状态管理
  const { fetchFavorites } = useBatchFavoritesContext();

  // 无限滚动相关的refs
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement | null>(null);
  const isLoadingRef = useRef(false);

  // 节流函数
  const throttle = useCallback(<T extends unknown[]>(
    func: (...args: T) => void,
    delay: number
  ) => {
    let timeoutId: NodeJS.Timeout;
    let lastExecTime = 0;
    return (...args: T) => {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  }, []);

  const fetchMovies = useCallback(async (pageNum: number, append = false) => {
    // 防止重复加载
    if (isLoadingRef.current) return;

    isLoadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '20',
        ...(category !== 'all' && { category }),
        sort: sortBy,
      });

      const response = await fetch(`/api/movies?${params}`);
      const data: MoviesResponse = await response.json();

      if (data.success) {
        const newMovies = data.data.movies;

        if (append) {
          setMovies(prev => [...prev, ...newMovies]);
        } else {
          setMovies(newMovies);
        }
        setTotal(data.data.total);
        setPage(pageNum);
        setHasMore(pageNum < data.data.totalPages);

        // 批量获取新影片的收藏状态
        if (newMovies.length > 0) {
          const movieIds = newMovies.map(movie => movie.id);
          fetchFavorites(movieIds);
        }
      } else {
        setError('获取影片数据失败');
      }
    } catch (error) {
      console.error('获取影片列表失败:', error);
      setError('网络请求失败，请检查网络连接');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [category, sortBy, fetchFavorites]);

  // 加载下一页（无限滚动触发）
  const loadMore = useCallback(() => {
    // 收藏和历史分类不支持无限滚动，因为数据来源不同
    if (category === 'favorites' || category === 'history') {
      return;
    }

    if (hasMore && !loading && !isLoadingRef.current) {
      fetchMovies(page + 1, true);
    }
  }, [hasMore, loading, page, fetchMovies, category]);

  // 节流版本的加载更多
  const throttledLoadMore = useCallback(() => {
    const throttledFn = throttle(loadMore, 300);
    throttledFn();
  }, [loadMore, throttle]);

  // 刷新数据
  const refresh = useCallback(() => {
    setError(null);
    setHasMore(true);
    fetchMovies(1, false);
  }, [fetchMovies]);

  // 重试加载
  const retryLoad = useCallback(() => {
    if (hasMore && !loading) {
      fetchMovies(page + 1, true);
    }
  }, [hasMore, loading, page, fetchMovies]);

  // 设置Intersection Observer
  useEffect(() => {
    if (!loadingRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !loading && !isLoadingRef.current) {
          throttledLoadMore();
        }
      },
      {
        root: null,
        rootMargin: '300px', // 提前300px开始加载
        threshold: 0.1
      }
    );

    observerRef.current.observe(loadingRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, throttledLoadMore]);

  // 当筛选条件改变时重置状态
  useEffect(() => {
    setMovies(initialMovies);
    setPage(initialPage);
    setTotal(initialTotal);
    setHasMore(initialPage < Math.ceil(initialTotal / 20));
    setError(null);
    isLoadingRef.current = false;

    // 批量获取初始影片的收藏状态
    if (initialMovies.length > 0) {
      const movieIds = initialMovies.map(movie => movie.id);
      fetchFavorites(movieIds);
    }
  }, [initialMovies, initialPage, initialTotal, category, sortBy, fetchFavorites]);

  return (
    <div className="space-y-6">


      {/* YouTube风格的影片网格/列表 */}
      <div className={
        viewMode === 'grid'
          ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
          : "space-y-4"
      }>
        {movies.map((movie, index) => (
          <div
            key={movie.id}
            className="fade-in"
            style={{ animationDelay: `${index * 0.05}s` }}
          >
            <MovieCard movie={movie} viewMode={viewMode} />
          </div>
        ))}
      </div>

      {/* 无限滚动加载指示器 - 收藏和历史分类不显示 */}
      {hasMore && category !== 'favorites' && category !== 'history' && (
        <div
          ref={loadingRef}
          className="flex justify-center items-center py-8"
        >
          {loading ? (
            <div className="flex items-center space-x-3 text-gray-400">
              <Loader2 className="w-5 h-5 animate-spin" />
              <span className="text-sm">正在加载更多视频...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center space-x-2 text-red-400">
                <AlertCircle className="w-5 h-5" />
                <span className="text-sm">{error}</span>
              </div>
              <Button
                onClick={retryLoad}
                className="bg-[#272727] hover:bg-[#3f3f3f] text-white px-4 py-2 rounded-lg border-0 text-sm"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                重试
              </Button>
            </div>
          ) : (
            <div className="h-4" /> // 占位符，用于触发Intersection Observer
          )}
        </div>
      )}

      {/* 无限滚动加载时的骨架屏 */}
      {loading && movies.length > 0 && (
        <div className={
          viewMode === 'grid'
            ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mt-4"
            : "space-y-4 mt-4"
        }>
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={`skeleton-${index}`} className="animate-pulse">
              <div className={`bg-[#272727] rounded-lg overflow-hidden ${
                viewMode === 'list' ? 'flex h-32' : ''
              }`}>
                <div className={`bg-[#3f3f3f] ${
                  viewMode === 'list' ? 'w-48 h-full' : 'aspect-[16/9]'
                }`}></div>
                <div className={`p-3 space-y-2 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                  <div className="h-4 bg-[#3f3f3f] rounded w-3/4"></div>
                  <div className="h-3 bg-[#3f3f3f] rounded w-1/2"></div>
                  <div className="h-3 bg-[#3f3f3f] rounded w-2/3"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 全部加载完成提示 */}
      {!hasMore && movies.length > 0 && !loading && (
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 text-sm text-gray-400 bg-[#272727] rounded-lg px-4 py-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>已显示全部 {total} 个视频</span>
          </div>
        </div>
      )}

      {/* 空状态 - YouTube风格 */}
      {movies.length === 0 && !loading && (
        <div className="text-center py-16">
          <div className="w-16 h-16 mx-auto mb-4 bg-[#272727] rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0V1" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">暂无视频</h3>
          <p className="text-gray-400 mb-6">
            数据库中还没有影片信息，请先导入数据或检查数据库连接
          </p>
          <Button
            onClick={refresh}
            className="bg-[#272727] hover:bg-[#3f3f3f] text-white border-0"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            重新加载
          </Button>
        </div>
      )}

      {/* 加载状态 - YouTube风格骨架屏 */}
      {loading && movies.length === 0 && (
        <div className={
          viewMode === 'grid'
            ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
            : "space-y-4"
        }>
          {Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className={`bg-[#272727] rounded-lg overflow-hidden ${
                viewMode === 'list' ? 'flex h-32' : ''
              }`}>
                <div className={`bg-[#3f3f3f] ${
                  viewMode === 'list' ? 'w-48 h-full' : 'aspect-[16/9]'
                }`}></div>
                <div className={`p-3 space-y-2 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                  <div className="h-4 bg-[#3f3f3f] rounded w-3/4"></div>
                  <div className="h-3 bg-[#3f3f3f] rounded w-1/2"></div>
                  <div className="h-3 bg-[#3f3f3f] rounded w-2/3"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

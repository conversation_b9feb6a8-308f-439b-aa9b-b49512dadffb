'use client';

import { useState } from 'react';
import { X, Flag } from 'lucide-react';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  movieTitle: string;
  movieId: string;
}

const ReportModal: React.FC<ReportModalProps> = ({ isOpen, onClose, movieTitle, movieId }) => {
  const [reportType, setReportType] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const reportTypes = [
    { value: 'copyright', label: '版权侵犯' },
    { value: 'inappropriate', label: '不当内容' },
    { value: 'spam', label: '垃圾信息' },
    { value: 'misleading', label: '误导性信息' },
    { value: 'violence', label: '暴力内容' },
    { value: 'other', label: '其他' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reportType) {
      alert('请选择举报类型');
      return;
    }

    if (!description.trim()) {
      alert('请填写举报描述');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieId,
          type: reportType,
          description: description.trim(),
        }),
      });

      if (response.ok) {
        alert('举报已提交，我们会尽快处理');
        setReportType('');
        setDescription('');
        onClose();
      } else {
        const error = await response.json();
        alert(error.error || '举报提交失败，请重试');
      }
    } catch (error) {
      console.error('举报提交失败:', error);
      alert('举报提交失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setReportType('');
      setDescription('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#282828] rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-[#3f3f3f]">
          <div className="flex items-center gap-3">
            <Flag className="w-5 h-5 text-red-400" />
            <h2 className="text-lg font-semibold text-white">举报内容</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="p-1 text-gray-400 hover:text-white transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* 影片信息 */}
          <div className="bg-[#3f3f3f] rounded-lg p-3">
            <p className="text-sm text-gray-300 mb-1">举报影片:</p>
            <p className="text-white font-medium">{movieTitle}</p>
            <p className="text-xs text-gray-400">ID: {movieId}</p>
          </div>

          {/* 举报类型 */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              举报类型 <span className="text-red-400">*</span>
            </label>
            <div className="space-y-2">
              {reportTypes.map((type) => (
                <label key={type.value} className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="reportType"
                    value={type.value}
                    checked={reportType === type.value}
                    onChange={(e) => setReportType(e.target.value)}
                    disabled={isSubmitting}
                    className="w-4 h-4 text-red-600 bg-[#3f3f3f] border-gray-600 focus:ring-red-500 focus:ring-2"
                  />
                  <span className="text-white text-sm">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 详细描述 */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              详细描述 <span className="text-red-400">*</span>
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={isSubmitting}
              placeholder="请详细描述您举报的原因..."
              rows={4}
              className="w-full px-3 py-2 bg-[#3f3f3f] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none disabled:opacity-50"
              maxLength={500}
            />
            <p className="text-xs text-gray-400 mt-1">
              {description.length}/500 字符
            </p>
          </div>

          {/* 提示信息 */}
          <div className="bg-[#3f3f3f] rounded-lg p-3">
            <p className="text-xs text-gray-300">
              • 我们会认真审核每一个举报
              <br />
              • 恶意举报可能导致账号受限
              <br />
              • 举报信息将被保密处理
            </p>
          </div>

          {/* 按钮 */}
          <div className="flex gap-3 pt-2">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 bg-[#3f3f3f] text-white rounded-lg hover:bg-[#4f4f4f] transition-colors disabled:opacity-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !reportType || !description.trim()}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? '提交中...' : '提交举报'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReportModal;

'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import OptimizedImage from './OptimizedImage';
import type { LocalMovie } from '@/types/javbus';

interface RecommendedContentProps {
  movies: LocalMovie[];
  showControls?: boolean;
}

export default function RecommendedContent({
  movies,
  showControls = true
}: RecommendedContentProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [hasDragged, setHasDragged] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const dragStateRef = useRef({
    startX: 0,
    scrollLeft: 0,
    startTime: 0,
    lastX: 0,
    lastTime: 0,
    velocity: 0
  });

  // 简单的拖拽处理 - 基于研究的最佳实践
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!scrollContainerRef.current) return;

    e.preventDefault();
    setIsDragging(true);
    setHasDragged(false);

    const container = scrollContainerRef.current;
    const startX = e.pageX - container.offsetLeft;
    const scrollLeft = container.scrollLeft;
    const startTime = performance.now();

    dragStateRef.current = {
      startX,
      scrollLeft,
      startTime,
      lastX: startX,
      lastTime: startTime,
      velocity: 0
    };

    // 禁用scroll-snap在拖拽期间
    container.style.scrollSnapType = 'none';
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;

    e.preventDefault();
    const container = scrollContainerRef.current;
    const x = e.pageX - container.offsetLeft;
    const walk = (x - dragStateRef.current.startX) * 2; // 速度因子

    // 计算速度用于惯性
    const currentTime = performance.now();
    const timeDiff = currentTime - dragStateRef.current.lastTime;
    if (timeDiff > 0) {
      dragStateRef.current.velocity = (dragStateRef.current.lastX - x) / timeDiff;
    }
    dragStateRef.current.lastX = x;
    dragStateRef.current.lastTime = currentTime;

    container.scrollLeft = dragStateRef.current.scrollLeft - walk;

    // 设置拖拽标志
    if (Math.abs(walk) > 5) {
      setHasDragged(true);
    }
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    if (!isDragging || !scrollContainerRef.current) return;

    setIsDragging(false);
    const container = scrollContainerRef.current;

    // 重新启用scroll-snap
    container.style.scrollSnapType = 'x mandatory';

    // 简单的惯性效果
    const velocity = dragStateRef.current.velocity;
    if (Math.abs(velocity) > 0.5) {
      const momentum = velocity * 100; // 动量因子
      container.scrollBy({
        left: momentum,
        behavior: 'smooth'
      });
    }

    // 延迟重置拖拽标志
    setTimeout(() => {
      setHasDragged(false);
    }, 150);
  }, [isDragging]);

  // 全局事件监听
  const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;

    e.preventDefault();
    const container = scrollContainerRef.current;
    const x = e.pageX - container.offsetLeft;
    const walk = (x - dragStateRef.current.startX) * 2;

    const currentTime = performance.now();
    const timeDiff = currentTime - dragStateRef.current.lastTime;
    if (timeDiff > 0) {
      dragStateRef.current.velocity = (dragStateRef.current.lastX - x) / timeDiff;
    }
    dragStateRef.current.lastX = x;
    dragStateRef.current.lastTime = currentTime;

    container.scrollLeft = dragStateRef.current.scrollLeft - walk;

    if (Math.abs(walk) > 5) {
      setHasDragged(true);
    }
  }, [isDragging]);

  const handleGlobalMouseUp = useCallback(() => {
    if (!isDragging) return;
    handleMouseUp();
  }, [isDragging, handleMouseUp]);

  // 添加全局事件监听器
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isDragging, handleGlobalMouseMove, handleGlobalMouseUp]);

  // 滚动按钮
  const scrollLeft = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -400, behavior: 'smooth' });
    }
  }, []);

  const scrollRight = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 400, behavior: 'smooth' });
    }
  }, []);

  // 处理卡片点击
  const handleCardClick = useCallback((e: React.MouseEvent) => {
    if (hasDragged) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
    return true;
  }, [hasDragged]);

  if (!movies || movies.length === 0) {
    return (
      <div className="w-full h-64 bg-gray-900 rounded-lg flex items-center justify-center">
        <p className="text-gray-400">暂无推荐内容</p>
      </div>
    );
  }

  return (
    <div className="w-full mb-12">
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">推荐内容</h2>
      </div>

      {/* 轮播容器 - 移除背景和圆角 */}
      <div
        className="relative w-full h-64 overflow-hidden"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* 横向滚动容器 - 使用CSS scroll-snap */}
        <div
          ref={scrollContainerRef}
          className={`flex h-full overflow-x-auto p-3 md:p-6 gap-4 ${
            isDragging ? 'cursor-grabbing' : 'cursor-grab'
          }`}
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            scrollSnapType: 'x mandatory',
            scrollBehavior: 'smooth'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <style jsx>{`
            div::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          {movies.map((movie, index) => (
            <div
              key={movie.id}
              className="flex-shrink-0 w-[280px] h-[200px] md:w-[400px] md:h-[230px] rounded-lg overflow-hidden hover:scale-105 transition-transform duration-300 group"
              style={{
                scrollSnapAlign: 'start'
              }}
            >
              <Link
                href={`/movies/${movie.id}`}
                className="block w-full h-full"
                onClick={handleCardClick}
                onDragStart={(e) => e.preventDefault()}
              >
                <div className="relative w-full h-full">
                  <OptimizedImage
                    src={movie.localImg || movie.img || '/placeholder-movie.jpg'}
                    alt={movie.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    sizes="(max-width: 768px) 320px, 450px"
                    priority={index < 3}
                    preserveOriginal={true}
                  />

                  {/* 渐变遮罩 */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                  {/* 时长标签 - YouTube风格 */}
                  {(movie.durationFormatted || movie.videoLength) && (
                    <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-1.5 py-0.5 rounded">
                      {movie.durationFormatted || `${movie.videoLength}:00`}
                    </div>
                  )}

                  {/* 影片信息 */}
                  <div className="absolute bottom-0 left-0 right-0 p-4">
                    <h3 className="text-white font-medium text-sm md:text-base line-clamp-2 mb-1">
                      {movie.title}
                    </h3>
                    {movie.date && (
                      <p className="text-gray-300 text-xs md:text-sm">
                        {movie.date}
                      </p>
                    )}
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* 导航按钮 */}
        {showControls && movies.length > 1 && (
          <>
            <button
              onClick={scrollLeft}
              className={`absolute left-2 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 backdrop-blur-md text-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 border border-white/20 z-20 ${
                isHovered ? 'opacity-100' : 'opacity-0'
              }`}
              aria-label="向左滚动"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <button
              onClick={scrollRight}
              className={`absolute right-2 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 backdrop-blur-md text-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 border border-white/20 z-20 ${
                isHovered ? 'opacity-100' : 'opacity-0'
              }`}
              aria-label="向右滚动"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </>
        )}
      </div>
    </div>
  );
}
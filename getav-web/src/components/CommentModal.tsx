'use client';

import { useState } from 'react';
import { X, ThumbsUp, ThumbsDown, Send, MessageCircle } from 'lucide-react';

interface Comment {
  id: string;
  text: string;
  author: string;
  time: string;
  likes: number;
  dislikes: number;
  userVote: 'like' | 'dislike' | null;
}

interface CommentModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  comments: Comment[];
  onAddComment: (text: string) => void;
  onVoteComment: (commentId: string, vote: 'like' | 'dislike') => void;
}

export default function CommentModal({
  isOpen,
  onClose,
  title,
  comments,
  onAddComment,
  onVoteComment
}: CommentModalProps) {
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onAddComment(newComment.trim());
      setNewComment('');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVote = (commentId: string, vote: 'like' | 'dislike') => {
    onVoteComment(commentId, vote);
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#212121] rounded-lg w-full max-w-2xl max-h-[80vh] flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-[#3f3f3f]">
          <div className="flex items-center gap-3">
            <MessageCircle className="w-5 h-5 text-blue-400" />
            <h2 className="text-lg font-semibold text-white">评论</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-[#3f3f3f] rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* 标题 */}
        <div className="p-4 border-b border-[#3f3f3f]">
          <h3 className="text-white font-medium line-clamp-2">{title}</h3>
        </div>

        {/* 评论列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {comments.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="w-12 h-12 text-gray-500 mx-auto mb-3" />
              <p className="text-gray-400">暂无评论，来发表第一条评论吧！</p>
            </div>
          ) : (
            comments.map((comment) => (
              <div key={comment.id} className="bg-[#2a2a2a] rounded-lg p-4">
                {/* 评论头部 */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {comment.author.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-white font-medium">{comment.author}</p>
                      <p className="text-gray-400 text-xs">{comment.time}</p>
                    </div>
                  </div>
                </div>

                {/* 评论内容 */}
                <p className="text-gray-200 mb-3 leading-relaxed">{comment.text}</p>

                {/* 点赞/反对按钮 */}
                <div className="flex items-center gap-4">
                  <button
                    onClick={() => handleVote(comment.id, 'like')}
                    className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm transition-colors ${
                      comment.userVote === 'like'
                        ? 'bg-blue-600 text-white'
                        : 'bg-[#3f3f3f] text-gray-300 hover:bg-[#4f4f4f]'
                    }`}
                  >
                    <ThumbsUp className="w-4 h-4" />
                    <span>{comment.likes}</span>
                  </button>
                  <button
                    onClick={() => handleVote(comment.id, 'dislike')}
                    className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm transition-colors ${
                      comment.userVote === 'dislike'
                        ? 'bg-red-600 text-white'
                        : 'bg-[#3f3f3f] text-gray-300 hover:bg-[#4f4f4f]'
                    }`}
                  >
                    <ThumbsDown className="w-4 h-4" />
                    <span>{comment.dislikes}</span>
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {/* 评论输入框 */}
        <div className="p-4 border-t border-[#3f3f3f]">
          <form onSubmit={handleSubmit} className="flex gap-3">
            <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-medium">我</span>
            </div>
            <div className="flex-1">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="写下你的评论..."
                className="w-full bg-[#3f3f3f] text-white rounded-lg px-3 py-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                maxLength={500}
              />
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-400">
                  {newComment.length}/500
                </span>
                <button
                  type="submit"
                  disabled={!newComment.trim() || isSubmitting}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="w-4 h-4" />
                  <span>{isSubmitting ? '发送中...' : '发送'}</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

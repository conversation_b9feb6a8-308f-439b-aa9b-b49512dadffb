'use client';

import { useState } from 'react';
import { X, Download, Copy, MessageCircle, Calendar, HardDrive } from 'lucide-react';
import CommentModal from './CommentModal';

interface Magnet {
  id: string;
  link: string;
  isHD?: boolean | null;
  title?: string | null;
  size?: string | null;
  shareDate?: string | null;
  hasSubtitle?: boolean | null;
}

interface Comment {
  id: string;
  text: string;
  author: string;
  time: string;
  likes: number;
  dislikes: number;
  userVote: 'like' | 'dislike' | null;
}

interface MagnetModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  magnets: Magnet[];
  magnetComments: {[key: string]: Comment[]};
  onAddMagnetComment: (magnetId: string, text: string) => void;
  onVoteMagnetComment: (magnetId: string, commentId: string, vote: 'like' | 'dislike') => void;
}

export default function MagnetModal({
  isOpen,
  onClose,
  title,
  magnets,
  magnetComments,
  onAddMagnetComment,
  onVoteMagnetComment
}: MagnetModalProps) {
  const [selectedMagnetForComments, setSelectedMagnetForComments] = useState<string | null>(null);

  if (!isOpen) return null;

  const copyMagnetLink = async (magnetLink: string) => {
    try {
      await navigator.clipboard.writeText(magnetLink);
      // 这里可以添加toast提示
      alert('磁力链接已复制到剪贴板');
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const downloadMagnet = (magnetLink: string) => {
    window.open(magnetLink, '_blank');
  };

  const formatFileSize = (size: string | null) => {
    if (!size) return '未知大小';
    return size;
  };

  const formatDate = (date: string | null) => {
    if (!date) return '未知日期';
    return date;
  };

  return (
    <>
      <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
        <div className="bg-[#212121] rounded-lg w-full max-w-4xl max-h-[80vh] flex flex-col">
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b border-[#3f3f3f]">
            <div className="flex items-center gap-3">
              <Download className="w-5 h-5 text-green-400" />
              <h2 className="text-lg font-semibold text-white">磁力链接</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-[#3f3f3f] rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          {/* 标题 */}
          <div className="p-4 border-b border-[#3f3f3f]">
            <h3 className="text-white font-medium line-clamp-2">{title}</h3>
          </div>

          {/* 磁力链接列表 */}
          <div className="flex-1 overflow-y-auto p-4">
            {magnets.length === 0 ? (
              <div className="text-center py-8">
                <Download className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <p className="text-gray-400">暂无磁力链接</p>
              </div>
            ) : (
              <div className="space-y-4">
                {magnets.map((magnet) => (
                  <div key={magnet.id} className="bg-[#2a2a2a] rounded-lg p-4">
                    {/* 磁力链接头部信息 */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        {/* 标题和标签 */}
                        <div className="flex items-center gap-2 mb-2">
                          {magnet.title && (
                            <h4 className="text-white font-medium">{magnet.title}</h4>
                          )}
                          {magnet.isHD && (
                            <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded font-medium">
                              高清
                            </span>
                          )}
                          {magnet.hasSubtitle && (
                            <span className="px-2 py-1 bg-red-600 text-white text-xs rounded font-medium">
                              中文字幕
                            </span>
                          )}
                        </div>

                        {/* 文件信息 */}
                        <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                          {magnet.size && (
                            <div className="flex items-center gap-1">
                              <HardDrive className="w-4 h-4" />
                              <span>{formatFileSize(magnet.size)}</span>
                            </div>
                          )}
                          {magnet.shareDate && (
                            <div className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              <span>{formatDate(magnet.shareDate)}</span>
                            </div>
                          )}
                        </div>

                        {/* 磁力链接 */}
                        <div className="bg-[#1a1a1a] rounded p-3 mb-3">
                          <p className="text-gray-300 text-sm font-mono break-all">
                            {magnet.link}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => copyMagnetLink(magnet.link)}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Copy className="w-4 h-4" />
                        <span>复制链接</span>
                      </button>
                      <button
                        onClick={() => downloadMagnet(magnet.link)}
                        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Download className="w-4 h-4" />
                        <span>下载</span>
                      </button>
                      <button
                        onClick={() => setSelectedMagnetForComments(magnet.id)}
                        className="flex items-center gap-2 px-4 py-2 bg-[#3f3f3f] text-white rounded-lg hover:bg-[#4f4f4f] transition-colors"
                      >
                        <MessageCircle className="w-4 h-4" />
                        <span>评论 ({magnetComments[magnet.id]?.length || 0})</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 磁力链接评论模态框 */}
      {selectedMagnetForComments && (
        <CommentModal
          isOpen={true}
          onClose={() => setSelectedMagnetForComments(null)}
          title={`磁力链接评论 - ${magnets.find(m => m.id === selectedMagnetForComments)?.title || '未知'}`}
          comments={magnetComments[selectedMagnetForComments] || []}
          onAddComment={(text) => onAddMagnetComment(selectedMagnetForComments, text)}
          onVoteComment={(commentId, vote) => onVoteMagnetComment(selectedMagnetForComments, commentId, vote)}
        />
      )}
    </>
  );
}

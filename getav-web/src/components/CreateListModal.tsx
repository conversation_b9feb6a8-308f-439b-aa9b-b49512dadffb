'use client';

import { useState } from 'react';
import { X, Plus, Lock, Globe, AlertCircle } from 'lucide-react';
import { FavoriteList } from '@/types/favorite-list';

interface CreateListModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onListCreated: (list: FavoriteList) => void;
  movieId?: string; // 如果提供，创建后自动添加该影片到列表
  movieTitle?: string;
}

export default function CreateListModal({
  isOpen,
  onClose,
  userId,
  onListCreated,
  movieId,
  movieTitle
}: CreateListModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim() || loading) return;

    setLoading(true);
    setError(null);

    try {
      // 创建收藏列表
      const response = await fetch('/api/favorite-lists', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          isPublic: formData.isPublic,
          userId
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建收藏列表失败');
      }

      const newList = result.data;

      // 如果提供了影片ID，自动添加到新创建的列表
      if (movieId) {
        try {
          const favoriteResponse = await fetch('/api/favorites', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              movieId,
              userId,
              favoriteListId: newList.id,
              action: 'add'
            }),
          });

          if (!favoriteResponse.ok) {
            console.warn('添加影片到新列表失败，但列表创建成功');
          }
        } catch (err) {
          console.warn('添加影片到新列表失败:', err);
        }
      }

      // 通知父组件
      onListCreated(newList);

      // 重置表单并关闭模态框
      setFormData({ name: '', description: '', isPublic: false });
      onClose();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建收藏列表失败';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({ name: '', description: '', isPublic: false });
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#212121] rounded-lg w-full max-w-md">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-[#3f3f3f]">
          <div className="flex items-center gap-3">
            <Plus className="w-5 h-5 text-blue-400" />
            <h2 className="text-lg font-semibold text-white">新建播放列表</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="p-2 hover:bg-[#3f3f3f] rounded-full transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* 如果有影片信息，显示提示 */}
          {movieId && movieTitle && (
            <div className="bg-[#2a2a2a] rounded-lg p-3 border-l-4 border-blue-500">
              <p className="text-gray-300 text-sm">
                <span className="font-medium">&ldquo;{movieTitle}&rdquo;</span> 将被添加到新的播放列表中
              </p>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3 flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* 列表名称 */}
          <div>
            <label htmlFor="listName" className="block text-white font-medium mb-2">
              名称 <span className="text-red-400">*</span>
            </label>
            <input
              id="listName"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="输入播放列表名称"
              maxLength={50}
              className="w-full bg-[#3f3f3f] text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400"
              disabled={loading}
              autoFocus
            />
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-gray-400">
                {formData.name.length}/50
              </span>
            </div>
          </div>

          {/* 描述 */}
          <div>
            <label htmlFor="listDescription" className="block text-white font-medium mb-2">
              描述
            </label>
            <textarea
              id="listDescription"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="添加描述（可选）"
              maxLength={200}
              rows={3}
              className="w-full bg-[#3f3f3f] text-white rounded-lg px-3 py-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400"
              disabled={loading}
            />
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-gray-400">
                {formData.description.length}/200
              </span>
            </div>
          </div>

          {/* 隐私设置 */}
          <div>
            <label className="block text-white font-medium mb-3">隐私设置</label>
            <div className="space-y-2">
              {/* 私密选项 */}
              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="privacy"
                  checked={!formData.isPublic}
                  onChange={() => setFormData(prev => ({ ...prev, isPublic: false }))}
                  className="mt-1 text-blue-600 focus:ring-blue-500"
                  disabled={loading}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Lock className="w-4 h-4 text-gray-400" />
                    <span className="text-white font-medium">私密</span>
                  </div>
                  <p className="text-gray-400 text-sm mt-1">
                    只有您可以查看此播放列表
                  </p>
                </div>
              </label>

              {/* 公开选项 */}
              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="privacy"
                  checked={formData.isPublic}
                  onChange={() => setFormData(prev => ({ ...prev, isPublic: true }))}
                  className="mt-1 text-blue-600 focus:ring-blue-500"
                  disabled={loading}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Globe className="w-4 h-4 text-gray-400" />
                    <span className="text-white font-medium">公开</span>
                  </div>
                  <p className="text-gray-400 text-sm mt-1">
                    任何人都可以搜索和查看此播放列表
                  </p>
                </div>
              </label>
            </div>
          </div>

          {/* 按钮组 */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="flex-1 px-4 py-2 bg-[#3f3f3f] text-white rounded-lg hover:bg-[#4f4f4f] transition-colors disabled:opacity-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!formData.name.trim() || loading}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? '创建中...' : '创建'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

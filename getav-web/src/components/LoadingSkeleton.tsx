'use client';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export function LoadingSkeleton({ 
  className = '', 
  variant = 'rectangular',
  width,
  height,
  lines = 1
}: LoadingSkeletonProps) {
  const baseClasses = 'animate-pulse bg-gray-600 rounded';
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'h-4 rounded';
      case 'circular':
        return 'rounded-full';
      case 'rectangular':
      default:
        return 'rounded';
    }
  };

  const getStyle = () => {
    const style: React.CSSProperties = {};
    if (width) style.width = typeof width === 'number' ? `${width}px` : width;
    if (height) style.height = typeof height === 'number' ? `${height}px` : height;
    return style;
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className={`space-y-2 ${className}`}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`${baseClasses} ${getVariantClasses()}`}
            style={{
              ...getStyle(),
              width: index === lines - 1 ? '75%' : '100%'
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={`${baseClasses} ${getVariantClasses()} ${className}`}
      style={getStyle()}
    />
  );
}

// 收藏列表加载骨架
export function FavoriteListSkeleton() {
  return (
    <div className="py-2">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="flex items-center gap-3 px-4 py-3">
          {/* 图标骨架 */}
          <LoadingSkeleton variant="circular" width={16} height={16} />
          
          {/* 内容骨架 */}
          <div className="flex-1">
            <LoadingSkeleton variant="text" width="60%" height={16} className="mb-1" />
            <LoadingSkeleton variant="text" width="40%" height={12} />
          </div>
          
          {/* 选择框骨架 */}
          <LoadingSkeleton variant="rectangular" width={20} height={20} />
        </div>
      ))}
    </div>
  );
}

// 影片卡片加载骨架
export function MovieCardSkeleton() {
  return (
    <div className="bg-[#1a1a1a] rounded-lg overflow-hidden">
      {/* 图片骨架 */}
      <LoadingSkeleton variant="rectangular" height={200} className="w-full" />
      
      {/* 内容骨架 */}
      <div className="p-3">
        <LoadingSkeleton variant="text" lines={2} className="mb-2" />
        <LoadingSkeleton variant="text" width="50%" height={12} />
      </div>
    </div>
  );
}

// 用户头像加载骨架
export function AvatarSkeleton({ size = 40 }: { size?: number }) {
  return (
    <LoadingSkeleton 
      variant="circular" 
      width={size} 
      height={size} 
    />
  );
}

// 按钮加载骨架
export function ButtonSkeleton({ 
  width = 100, 
  height = 36,
  className = ''
}: { 
  width?: number | string; 
  height?: number | string;
  className?: string;
}) {
  return (
    <LoadingSkeleton 
      variant="rectangular" 
      width={width} 
      height={height}
      className={`rounded-lg ${className}`}
    />
  );
}

// 表格行加载骨架
export function TableRowSkeleton({ columns = 4 }: { columns?: number }) {
  return (
    <tr>
      {Array.from({ length: columns }).map((_, index) => (
        <td key={index} className="px-4 py-3">
          <LoadingSkeleton variant="text" height={16} />
        </td>
      ))}
    </tr>
  );
}

// 列表项加载骨架
export function ListItemSkeleton() {
  return (
    <div className="flex items-center gap-3 p-3">
      <AvatarSkeleton size={32} />
      <div className="flex-1">
        <LoadingSkeleton variant="text" width="70%" height={16} className="mb-1" />
        <LoadingSkeleton variant="text" width="40%" height={12} />
      </div>
    </div>
  );
}

// 页面加载骨架
export function PageSkeleton() {
  return (
    <div className="space-y-6 p-6">
      {/* 标题骨架 */}
      <LoadingSkeleton variant="text" width="30%" height={32} />
      
      {/* 内容区域骨架 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <MovieCardSkeleton key={index} />
        ))}
      </div>
    </div>
  );
}

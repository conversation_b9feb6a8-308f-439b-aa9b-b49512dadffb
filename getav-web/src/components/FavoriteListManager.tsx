'use client';

import { useState } from 'react';
import { FavoriteList } from '@/types/favorite-list';
import FavoriteListModal from './FavoriteListModal';
import CreateListModal from './CreateListModal';
import EditListModal from './EditListModal';

interface FavoriteListManagerProps {
  userId: string;
  movieId?: string;
  movieTitle?: string;
  currentFavoriteListId?: string;
  onFavoriteChange?: (listId: string | null, action: 'add' | 'remove' | 'move') => void;
  children: (props: {
    showFavoriteModal: () => void;
    showCreateModal: () => void;
    showEditModal: (list: FavoriteList) => void;
  }) => React.ReactNode;
}

export default function FavoriteListManager({
  userId,
  movieId,
  movieTitle,
  currentFavoriteListId,
  onFavoriteChange,
  children
}: FavoriteListManagerProps) {
  const [showFavoriteModal, setShowFavoriteModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingList, setEditingList] = useState<FavoriteList | null>(null);

  const handleShowFavoriteModal = () => {
    setShowFavoriteModal(true);
  };

  const handleShowCreateModal = () => {
    setShowCreateModal(true);
  };

  const handleShowEditModal = (list: FavoriteList) => {
    setEditingList(list);
    setShowEditModal(true);
  };

  const handleCreateModalFromFavorite = () => {
    setShowFavoriteModal(false);
    setShowCreateModal(true);
  };

  const handleListCreated = () => {
    // 如果有影片ID，表示是从收藏模态框创建的，需要重新打开收藏模态框
    if (movieId) {
      setShowCreateModal(false);
      setTimeout(() => {
        setShowFavoriteModal(true);
      }, 100);
    }
  };

  const handleListUpdated = () => {
    // 可以在这里添加更新后的处理逻辑
    // console.log('列表已更新:', list);
  };

  const handleListDeleted = () => {
    // 可以在这里添加删除后的处理逻辑
    // console.log('列表已删除:', listId);
  };

  const handleFavoriteModalClose = () => {
    setShowFavoriteModal(false);
  };

  const handleCreateModalClose = () => {
    setShowCreateModal(false);
  };

  const handleEditModalClose = () => {
    setShowEditModal(false);
    setEditingList(null);
  };

  return (
    <>
      {/* 渲染子组件，提供控制函数 */}
      {children({
        showFavoriteModal: handleShowFavoriteModal,
        showCreateModal: handleShowCreateModal,
        showEditModal: handleShowEditModal
      })}

      {/* 收藏列表选择模态框 */}
      <FavoriteListModal
        isOpen={showFavoriteModal}
        onClose={handleFavoriteModalClose}
        movieId={movieId || ''}
        movieTitle={movieTitle || ''}
        userId={userId}
        currentFavoriteListId={currentFavoriteListId}
        onFavoriteChange={onFavoriteChange}
        onCreateNewList={handleCreateModalFromFavorite}
      />

      {/* 创建收藏列表模态框 */}
      <CreateListModal
        isOpen={showCreateModal}
        onClose={handleCreateModalClose}
        userId={userId}
        onListCreated={handleListCreated}
        movieId={movieId}
        movieTitle={movieTitle}
      />

      {/* 编辑收藏列表模态框 */}
      <EditListModal
        isOpen={showEditModal}
        onClose={handleEditModalClose}
        userId={userId}
        list={editingList}
        onListUpdated={handleListUpdated}
        onListDeleted={handleListDeleted}
      />
    </>
  );
}

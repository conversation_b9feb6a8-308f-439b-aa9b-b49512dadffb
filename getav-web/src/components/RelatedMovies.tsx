'use client';

import Link from 'next/link';
import OptimizedImage from './OptimizedImage';
import { formatRelativeTime } from '@/lib/time-utils';

interface RelatedMovie {
  id: string;
  title: string;
  img?: string | null;
  date?: string | null;
  stars: Array<{
    id: string;
    name: string;
  }>;
}

interface RelatedMoviesProps {
  movies: RelatedMovie[];
  loading?: boolean;
}

export default function RelatedMovies({ movies, loading = false }: RelatedMoviesProps) {
  if (loading) {
    return (
      <div className="w-full lg:w-[400px] flex-shrink-0">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">相关推荐</h3>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex gap-3 p-2 rounded-lg animate-pulse">
                <div className="w-40 h-24 bg-[#3f3f3f] rounded-lg flex-shrink-0"></div>
                <div className="flex-1 min-w-0 space-y-2">
                  <div className="h-4 bg-[#3f3f3f] rounded w-3/4"></div>
                  <div className="h-3 bg-[#3f3f3f] rounded w-1/2"></div>
                  <div className="h-3 bg-[#3f3f3f] rounded w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!movies || movies.length === 0) {
    return (
      <div className="w-full lg:w-[400px] flex-shrink-0">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">相关推荐</h3>
          <div className="text-center py-8">
            <p className="text-gray-400 text-sm">暂无相关推荐</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full lg:w-[400px] flex-shrink-0">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white">相关推荐</h3>

        {/* 相关影片列表 */}
        <div className="space-y-3">
          {movies.map((movie) => (
            <Link
              key={movie.id}
              href={`/movies/${movie.id}`}
              className="block"
            >
              <div className="flex gap-3 p-2 hover:bg-[#272727] rounded-lg transition-colors cursor-pointer group">
                {/* 影片封面 */}
                <div className="w-40 h-24 bg-[#3f3f3f] rounded-lg flex-shrink-0 overflow-hidden relative">
                  <OptimizedImage
                    src={movie.img || '/placeholder-movie.jpg'}
                    alt={movie.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="160px"
                    preserveOriginal={true}
                  />
                  
                  {/* 悬浮播放按钮 */}
                  <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                </div>

                {/* 影片信息 */}
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-white line-clamp-2 mb-1 group-hover:text-blue-300 transition-colors">
                    {movie.title}
                  </h4>
                  
                  {/* 演员信息 */}
                  {movie.stars && movie.stars.length > 0 && (
                    <p className="text-xs text-gray-400 mb-1 line-clamp-1">
                      {movie.stars.slice(0, 2).map(star => star.name).join(', ')}
                      {movie.stars.length > 2 && ` 等${movie.stars.length}人`}
                    </p>
                  )}
                  
                  {/* 发布时间 */}
                  {movie.date && (
                    <p className="text-xs text-gray-400">
                      {formatRelativeTime(movie.date)}
                    </p>
                  )}
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* 如果相关影片较少，显示提示 */}
        {movies.length < 5 && (
          <div className="text-center py-4">
            <p className="text-gray-500 text-xs">
              已显示所有相关推荐 ({movies.length} 部)
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

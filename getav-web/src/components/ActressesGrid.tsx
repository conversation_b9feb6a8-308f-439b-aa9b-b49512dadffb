'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import Image from 'next/image';
import { User, Film, Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import type { Star } from '@/types/javbus';

// 扩展的Actress类型，包含额外的女优信息
interface Actress extends Star {
  avatar?: string;
  localAvatar?: string;
  birthday?: string;
  age?: string;
  height?: string;
  bust?: string;
  waist?: string;
  hip?: string;
  cupSize?: string;
  measurements?: string;
}

interface ActressesGridProps {
  initialActresses: Actress[];
  initialPage: number;
  initialTotal: number;
  viewMode?: 'grid' | 'list';
  sortBy?: string;
}

export default function ActressesGrid({
  initialActresses,
  initialPage,
  initialTotal,
  viewMode = 'grid',
  sortBy = 'popular'
}: ActressesGridProps) {
  const [actresses, setActresses] = useState<Actress[]>(initialActresses);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(initialPage);
  const [total, setTotal] = useState(initialTotal);
  const [hasMore, setHasMore] = useState(initialActresses.length < initialTotal);
  const [error, setError] = useState<string | null>(null);

  // 无限滚动相关的refs
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement | null>(null);
  const isLoadingRef = useRef(false);

  // 节流函数
  const throttle = useCallback(<T extends unknown[]>(
    func: (...args: T) => void,
    delay: number
  ) => {
    let timeoutId: NodeJS.Timeout;
    let lastExecTime = 0;
    return (...args: T) => {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  }, []);

  // 加载更多女优
  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !hasMore) return;

    isLoadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: (page + 1).toString(),
        limit: '40', // 女优保持40的limit
        sort: sortBy === 'latest' ? 'latest' : 'popular',
      });

      const response = await fetch(`/api/stars?${params}`);
      const data = await response.json();

      if (data.success && data.data.stars.length > 0) {
        setActresses(prev => [...prev, ...data.data.stars]);
        setPage(prev => prev + 1);
        setHasMore(actresses.length + data.data.stars.length < data.data.pagination.total);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('加载更多女优失败:', error);
      setError('加载失败，请重试');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [hasMore, page, sortBy, actresses.length]);

  // 节流版本的加载更多
  const throttledLoadMore = useCallback(() => {
    const throttledFn = throttle(loadMore, 300);
    throttledFn();
  }, [loadMore, throttle]);

  // 重试加载
  const retryLoad = useCallback(() => {
    if (hasMore && !loading) {
      loadMore();
    }
  }, [hasMore, loading, loadMore]);

  // 设置Intersection Observer
  useEffect(() => {
    if (!loadingRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !loading && !isLoadingRef.current) {
          throttledLoadMore();
        }
      },
      {
        root: null,
        rootMargin: '300px', // 提前300px开始加载
        threshold: 0.1
      }
    );

    observerRef.current.observe(loadingRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, throttledLoadMore]);

  // 当筛选条件改变时重置状态
  useEffect(() => {
    setActresses(initialActresses);
    setPage(initialPage);
    setTotal(initialTotal);
    setHasMore(initialActresses.length < initialTotal);
    setError(null);
    isLoadingRef.current = false;
  }, [initialActresses, initialPage, initialTotal, sortBy]);

  const handleActressClick = (actress: Actress) => {
    // 跳转到女优详情页面
    window.location.href = `/stars/${actress.id}`;
  };

  const getAvatarUrl = (actress: Actress) => {
    if (actress.localAvatar) {
      return `http://localhost:3001${actress.localAvatar}`;
    }
    return actress.avatar || '';
  };

  if (viewMode === 'list') {
    return (
      <div className="space-y-6">
        <div className="space-y-3">
          {actresses.map((actress) => (
            <div
              key={actress.id}
              onClick={() => handleActressClick(actress)}
              className="flex items-center space-x-4 p-3 hover:bg-[#1a1a1a] rounded-lg transition-colors cursor-pointer group"
            >
              {/* 迷你圆形头像 */}
              <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 group-hover:scale-105 transition-transform duration-200 relative">
                {getAvatarUrl(actress) ? (
                  <Image
                    src={getAvatarUrl(actress)!}
                    alt={actress.name}
                    fill
                    sizes="48px"
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-[#272727] flex items-center justify-center">
                    <User className="w-6 h-6 text-gray-400" />
                  </div>
                )}
              </div>

              {/* 基本信息 */}
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-white truncate group-hover:text-red-400 transition-colors">
                  {actress.name}
                </h3>
                <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                  <span>{actress.movieCount} 部影片</span>
                  {actress.age && <span>年龄: {actress.age}</span>}
                  {actress.height && <span>身高: {actress.height}</span>}
                  {actress.cupSize && <span>罩杯: {actress.cupSize}</span>}
                </div>
              </div>

              {/* 最近影片预览 */}
              {actress.recentMovies && actress.recentMovies.length > 0 && (
                <div className="flex space-x-1 flex-shrink-0">
                  {actress.recentMovies.slice(0, 3).map((movie) => (
                    <div key={movie.id} className="w-8 h-10 bg-[#272727] rounded overflow-hidden relative">
                      {movie.localImg && movie.localImg !== null ? (
                        <Image
                          src={`http://localhost:3001${movie.localImg}`}
                          alt={movie.title}
                          fill
                          sizes="32px"
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Film className="w-3 h-3 text-gray-500" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 无限滚动加载指示器 */}
        {hasMore && (
          <div
            ref={loadingRef}
            className="flex justify-center items-center py-8"
          >
            {loading ? (
              <div className="flex items-center space-x-3 text-gray-400">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span className="text-sm">正在加载更多女优...</span>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center space-y-3">
                <div className="flex items-center space-x-2 text-red-400">
                  <AlertCircle className="w-5 h-5" />
                  <span className="text-sm">{error}</span>
                </div>
                <button
                  onClick={retryLoad}
                  className="bg-[#272727] hover:bg-[#3f3f3f] text-white px-4 py-2 rounded-lg border-0 text-sm flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>重试</span>
                </button>
              </div>
            ) : (
              <div className="h-4" /> // 占位符，用于触发Intersection Observer
            )}
          </div>
        )}

        {/* 全部加载完成提示 */}
        {!hasMore && actresses.length > 0 && !loading && (
          <div className="text-center py-8">
            <div className="inline-flex items-center space-x-2 text-sm text-gray-400 bg-[#272727] rounded-lg px-4 py-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>已显示全部 {total} 个女优</span>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-6">
        {actresses.map((actress) => (
          <div
            key={actress.id}
            onClick={() => handleActressClick(actress)}
            className="flex flex-col items-center cursor-pointer group"
          >
            {/* 迷你圆形头像 */}
            <div className="w-20 h-20 rounded-full overflow-hidden mb-2 group-hover:scale-105 transition-transform duration-200 relative">
              {getAvatarUrl(actress) ? (
                <Image
                  src={getAvatarUrl(actress)!}
                  alt={actress.name}
                  fill
                  sizes="80px"
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full bg-[#272727] flex items-center justify-center">
                  <User className="w-8 h-8 text-gray-400" />
                </div>
              )}
            </div>

            {/* 女优姓名 */}
            <h3 className="text-xs font-medium text-white text-center truncate w-full group-hover:text-red-400 transition-colors">
              {actress.name}
            </h3>

            {/* 影片数量 */}
            <p className="text-xs text-gray-500 text-center mt-1">
              {actress.movieCount} 部影片
            </p>
          </div>
        ))}
      </div>

      {/* 无限滚动加载指示器 */}
      {hasMore && (
        <div
          ref={loadingRef}
          className="flex justify-center items-center py-8"
        >
          {loading ? (
            <div className="flex items-center space-x-3 text-gray-400">
              <Loader2 className="w-5 h-5 animate-spin" />
              <span className="text-sm">正在加载更多女优...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center space-x-2 text-red-400">
                <AlertCircle className="w-5 h-5" />
                <span className="text-sm">{error}</span>
              </div>
              <button
                onClick={retryLoad}
                className="bg-[#272727] hover:bg-[#3f3f3f] text-white px-4 py-2 rounded-lg border-0 text-sm flex items-center space-x-2"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重试</span>
              </button>
            </div>
          ) : (
            <div className="h-4" /> // 占位符，用于触发Intersection Observer
          )}
        </div>
      )}

      {/* 全部加载完成提示 */}
      {!hasMore && actresses.length > 0 && !loading && (
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 text-sm text-gray-400 bg-[#272727] rounded-lg px-4 py-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>已显示全部 {total} 个女优</span>
          </div>
        </div>
      )}
    </div>
  );
}

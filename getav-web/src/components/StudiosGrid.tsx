'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Building2, Film, Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import StudioCard from './StudioCard';
import type { Studio } from '@/types/javbus';

interface StudiosGridProps {
  initialStudios: Studio[];
  initialPage: number;
  initialTotal: number;
  viewMode?: 'grid' | 'list';
  sortBy?: string;
}

export default function StudiosGrid({
  initialStudios,
  initialPage,
  initialTotal,
  viewMode = 'grid',
  sortBy = 'popular'
}: StudiosGridProps) {
  const [studios, setStudios] = useState<Studio[]>(initialStudios);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(initialPage);
  const [total, setTotal] = useState(initialTotal);
  const [hasMore, setHasMore] = useState(initialStudios.length < initialTotal);
  const [error, setError] = useState<string | null>(null);

  // 无限滚动相关的refs
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement | null>(null);
  const isLoadingRef = useRef(false);

  // 节流函数
  const throttle = useCallback(<T extends unknown[]>(
    func: (...args: T) => void,
    delay: number
  ) => {
    let timeoutId: NodeJS.Timeout;
    let lastExecTime = 0;
    return (...args: T) => {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  }, []);

  // 加载更多片商
  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !hasMore) return;

    isLoadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: (page + 1).toString(),
        limit: '10',
        sort: sortBy === 'latest' ? 'latest' : 'popular',
      });

      const response = await fetch(`/api/studios?${params}`);
      const data = await response.json();

      if (data.success && data.data.studios.length > 0) {
        setStudios(prev => [...prev, ...data.data.studios]);
        setPage(prev => prev + 1);
        setHasMore(studios.length + data.data.studios.length < data.data.total);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('加载更多片商失败:', error);
      setError('加载失败，请重试');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [hasMore, page, sortBy, studios.length]);

  // 节流版本的加载更多
  const throttledLoadMore = useCallback(() => {
    const throttledFn = throttle(loadMore, 300);
    throttledFn();
  }, [loadMore, throttle]);

  // 重试加载
  const retryLoad = useCallback(() => {
    if (hasMore && !loading) {
      loadMore();
    }
  }, [hasMore, loading, loadMore]);

  // 设置Intersection Observer
  useEffect(() => {
    if (!loadingRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !loading && !isLoadingRef.current) {
          throttledLoadMore();
        }
      },
      {
        root: null,
        rootMargin: '300px', // 提前300px开始加载
        threshold: 0.1
      }
    );

    observerRef.current.observe(loadingRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, throttledLoadMore]);

  // 当筛选条件改变时重置状态
  useEffect(() => {
    setStudios(initialStudios);
    setPage(initialPage);
    setTotal(initialTotal);
    setHasMore(initialStudios.length < initialTotal);
    setError(null);
    isLoadingRef.current = false;
  }, [initialStudios, initialPage, initialTotal, sortBy]);

  const handleStudioClick = (studio: Studio) => {
    // 跳转到片商详情页面
    window.location.href = `/studios/${studio.id}`;
  };

  const getTypeLabel = (type: string) => {
    return type === 'producer' ? '制作商' : '发行商';
  };

  const getTypeColor = (type: string) => {
    return type === 'producer' ? 'bg-blue-600' : 'bg-green-600';
  };

  if (viewMode === 'list') {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          {studios.map((studio) => (
            <div
              key={studio.id}
              onClick={() => handleStudioClick(studio)}
              className="bg-[#1a1a1a] rounded-lg p-6 hover:bg-[#2a2a2a] transition-colors cursor-pointer"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-[#272727] rounded-lg flex items-center justify-center">
                    <Building2 className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-lg font-semibold text-white">{studio.name}</h3>
                      <div className="flex space-x-1">
                        {studio.types.map((type, index) => (
                          <span key={index} className={`px-2 py-1 text-xs rounded-full text-white ${getTypeColor(type)}`}>
                            {getTypeLabel(type)}
                          </span>
                        ))}
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm">
                      {studio.movieCount} 部影片
                    </p>
                  </div>
                </div>

                {/* 最近影片预览 */}
                <div className="flex space-x-2">
                  {studio.recentMovies.slice(0, 3).map((movie) => (
                    <div key={movie.id} className="w-12 h-16 bg-[#272727] rounded overflow-hidden relative">
                      {movie.localImg ? (
                        <Image
                          src={`http://localhost:3001${movie.localImg}`}
                          alt={movie.title}
                          fill
                          sizes="48px"
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Film className="w-4 h-4 text-gray-500" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 无限滚动加载指示器 */}
        {hasMore && (
          <div
            ref={loadingRef}
            className="flex justify-center items-center py-8"
          >
            {loading ? (
              <div className="flex items-center space-x-3 text-gray-400">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span className="text-sm">正在加载更多片商...</span>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center space-y-3">
                <div className="flex items-center space-x-2 text-red-400">
                  <AlertCircle className="w-5 h-5" />
                  <span className="text-sm">{error}</span>
                </div>
                <button
                  onClick={retryLoad}
                  className="bg-[#272727] hover:bg-[#3f3f3f] text-white px-4 py-2 rounded-lg border-0 text-sm flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>重试</span>
                </button>
              </div>
            ) : (
              <div className="h-4" /> // 占位符，用于触发Intersection Observer
            )}
          </div>
        )}

        {/* 全部加载完成提示 */}
        {!hasMore && studios.length > 0 && !loading && (
          <div className="text-center py-8">
            <div className="inline-flex items-center space-x-2 text-sm text-gray-400 bg-[#272727] rounded-lg px-4 py-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>已显示全部 {total} 个片商</span>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {studios.map((studio) => (
          <StudioCard
            key={studio.id}
            studio={studio}
            onClick={handleStudioClick}
          />
        ))}
      </div>

      {/* 无限滚动加载指示器 */}
      {hasMore && (
        <div
          ref={loadingRef}
          className="flex justify-center items-center py-8"
        >
          {loading ? (
            <div className="flex items-center space-x-3 text-gray-400">
              <Loader2 className="w-5 h-5 animate-spin" />
              <span className="text-sm">正在加载更多片商...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center space-x-2 text-red-400">
                <AlertCircle className="w-5 h-5" />
                <span className="text-sm">{error}</span>
              </div>
              <button
                onClick={retryLoad}
                className="bg-[#272727] hover:bg-[#3f3f3f] text-white px-4 py-2 rounded-lg border-0 text-sm flex items-center space-x-2"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重试</span>
              </button>
            </div>
          ) : (
            <div className="h-4" /> // 占位符，用于触发Intersection Observer
          )}
        </div>
      )}

      {/* 全部加载完成提示 */}
      {!hasMore && studios.length > 0 && !loading && (
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 text-sm text-gray-400 bg-[#272727] rounded-lg px-4 py-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>已显示全部 {total} 个片商</span>
          </div>
        </div>
      )}
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { X, Edit3, Lock, Globe, AlertCircle, Trash2 } from 'lucide-react';
import { FavoriteList } from '@/types/favorite-list';

interface EditListModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  list: FavoriteList | null;
  onListUpdated: (list: FavoriteList) => void;
  onListDeleted: (listId: string) => void;
}

export default function EditListModal({
  isOpen,
  onClose,
  userId,
  list,
  onListUpdated,
  onListDeleted
}: EditListModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: false
  });
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (list) {
      setFormData({
        name: list.name,
        description: list.description || '',
        isPublic: list.isPublic
      });
    }
  }, [list]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim() || loading || !list) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/favorite-lists/${list.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          isPublic: formData.isPublic,
          userId
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新收藏列表失败');
      }

      // 通知父组件
      onListUpdated(result.data);
      onClose();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新收藏列表失败';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!list || deleting) return;

    setDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/favorite-lists/${list.id}?userId=${userId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '删除收藏列表失败');
      }

      // 通知父组件
      onListDeleted(list.id);
      onClose();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除收藏列表失败';
      setError(errorMessage);
    } finally {
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleClose = () => {
    if (!loading && !deleting) {
      setError(null);
      setShowDeleteConfirm(false);
      onClose();
    }
  };

  if (!isOpen || !list) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#212121] rounded-lg w-full max-w-md">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-[#3f3f3f]">
          <div className="flex items-center gap-3">
            <Edit3 className="w-5 h-5 text-blue-400" />
            <h2 className="text-lg font-semibold text-white">编辑播放列表</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={loading || deleting}
            className="p-2 hover:bg-[#3f3f3f] rounded-full transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* 错误提示 */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3 flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* 列表名称 */}
          <div>
            <label htmlFor="listName" className="block text-white font-medium mb-2">
              名称 <span className="text-red-400">*</span>
            </label>
            <input
              id="listName"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="输入播放列表名称"
              maxLength={50}
              className="w-full bg-[#3f3f3f] text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400"
              disabled={loading || deleting || list.isDefault}
              autoFocus
            />
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-gray-400">
                {formData.name.length}/50
              </span>
              {list.isDefault && (
                <span className="text-xs text-yellow-400">默认列表名称不可修改</span>
              )}
            </div>
          </div>

          {/* 描述 */}
          <div>
            <label htmlFor="listDescription" className="block text-white font-medium mb-2">
              描述
            </label>
            <textarea
              id="listDescription"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="添加描述（可选）"
              maxLength={200}
              rows={3}
              className="w-full bg-[#3f3f3f] text-white rounded-lg px-3 py-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400"
              disabled={loading || deleting}
            />
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-gray-400">
                {formData.description.length}/200
              </span>
            </div>
          </div>

          {/* 隐私设置 */}
          <div>
            <label className="block text-white font-medium mb-3">隐私设置</label>
            <div className="space-y-2">
              {/* 私密选项 */}
              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="privacy"
                  checked={!formData.isPublic}
                  onChange={() => setFormData(prev => ({ ...prev, isPublic: false }))}
                  className="mt-1 text-blue-600 focus:ring-blue-500"
                  disabled={loading || deleting}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Lock className="w-4 h-4 text-gray-400" />
                    <span className="text-white font-medium">私密</span>
                  </div>
                  <p className="text-gray-400 text-sm mt-1">
                    只有您可以查看此播放列表
                  </p>
                </div>
              </label>

              {/* 公开选项 */}
              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="privacy"
                  checked={formData.isPublic}
                  onChange={() => setFormData(prev => ({ ...prev, isPublic: true }))}
                  className="mt-1 text-blue-600 focus:ring-blue-500"
                  disabled={loading || deleting}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Globe className="w-4 h-4 text-gray-400" />
                    <span className="text-white font-medium">公开</span>
                  </div>
                  <p className="text-gray-400 text-sm mt-1">
                    任何人都可以搜索和查看此播放列表
                  </p>
                </div>
              </label>
            </div>
          </div>

          {/* 删除确认 */}
          {showDeleteConfirm && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <h3 className="text-red-400 font-medium mb-2">确认删除</h3>
              <p className="text-gray-300 text-sm mb-4">
                确定要删除播放列表 &ldquo;{list.name}&rdquo; 吗？此操作无法撤销。
              </p>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={deleting}
                  className="px-3 py-1 bg-[#3f3f3f] text-white rounded text-sm hover:bg-[#4f4f4f] transition-colors disabled:opacity-50"
                >
                  取消
                </button>
                <button
                  type="button"
                  onClick={handleDelete}
                  disabled={deleting}
                  className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors disabled:opacity-50"
                >
                  {deleting ? '删除中...' : '确认删除'}
                </button>
              </div>
            </div>
          )}

          {/* 按钮组 */}
          <div className="flex gap-3 pt-4">
            {/* 删除按钮 */}
            {!list.isDefault && (
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={loading || deleting || showDeleteConfirm}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                删除
              </button>
            )}

            <div className="flex-1 flex gap-3">
              <button
                type="button"
                onClick={handleClose}
                disabled={loading || deleting}
                className="flex-1 px-4 py-2 bg-[#3f3f3f] text-white rounded-lg hover:bg-[#4f4f4f] transition-colors disabled:opacity-50"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={!formData.name.trim() || loading || deleting}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? '保存中...' : '保存'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

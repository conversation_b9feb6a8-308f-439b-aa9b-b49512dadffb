'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import OptimizedImage from './OptimizedImage';
import { Star as StarIcon, Play } from 'lucide-react';

interface Star {
  id: string;
  name: string;
  avatar?: string;
  localAvatar?: string;
  movieCount: number;
  latestMovieDate?: string;
}

export default function FeaturedStars() {
  const [stars, setStars] = useState<Star[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedStars = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/stars?featured=true&limit=12');
        const data = await response.json();
        
        if (data.success) {
          setStars(data.data.stars);
        } else {
          setError(data.error || '获取热门演员失败');
        }
      } catch (err) {
        setError('网络请求失败');
        console.error('获取热门演员失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedStars();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-400">加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-400 mb-2">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
        >
          重试
        </button>
      </div>
    );
  }

  if (stars.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-400">暂无热门演员数据</div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* 横向滚动容器 */}
      <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
        {stars.map((star) => (
          <Link
            key={star.id}
            href={`/stars/${star.id}`}
            className="flex-shrink-0 group"
          >
            <div className="w-32 bg-[#212121] rounded-lg overflow-hidden hover:bg-[#2a2a2a] hover:shadow-lg hover:shadow-black/20 hover:-translate-y-1 transition-all duration-500 ease-out">
              {/* 演员头像 */}
              <div className="aspect-[3/4] bg-[#3f3f3f] relative overflow-hidden">
                {star.localAvatar ? (
                  <OptimizedImage
                    src={star.localAvatar}
                    alt={star.name}
                    fill
                    className="object-cover group-hover:scale-105 group-hover:brightness-110 transition-all duration-500 ease-out"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <StarIcon className="w-8 h-8 text-gray-500 group-hover:text-gray-300 transition-colors duration-500" />
                  </div>
                )}
                
                {/* 悬浮信息 */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out flex items-center justify-center">
                  <div className="text-center text-white transform scale-90 group-hover:scale-100 transition-transform duration-500">
                    <Play className="w-6 h-6 mx-auto mb-1" />
                    <div className="text-xs">{star.movieCount} 部作品</div>
                  </div>
                </div>
              </div>

              {/* 演员信息 */}
              <div className="p-3">
                <h3 className="text-gray-300 group-hover:text-gray-100 font-medium text-sm mb-1 truncate transition-colors duration-500">
                  {star.name}
                </h3>
                <div className="text-gray-400 group-hover:text-gray-300 text-xs flex items-center gap-1 transition-colors duration-500">
                  <Play className="w-3 h-3" />
                  {star.movieCount} 部作品
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
      
      {/* 渐变遮罩 - 右侧 */}
      <div className="absolute top-0 right-0 w-8 h-full bg-gradient-to-l from-[#0f0f0f] to-transparent pointer-events-none" />
    </div>
  );
}

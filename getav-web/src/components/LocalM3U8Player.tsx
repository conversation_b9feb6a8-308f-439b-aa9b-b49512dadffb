'use client';

import { useEffect, useRef, useState } from 'react';
import Script from 'next/script';

interface LocalM3U8PlayerProps {
  src: string;
  poster?: string;
  title?: string;
  duration?: number; // 精确时长（秒）
  durationFormatted?: string; // 格式化时长 (HH:MM:SS)
  onClose?: () => void;
}

declare global {
  interface Window {
    Hls: any;
    Plyr: any;
  }
}

export default function LocalM3U8Player({ src, poster, title, duration, durationFormatted, onClose }: LocalM3U8PlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const hlsRef = useRef<any>(null);

  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [scriptsLoaded, setScriptsLoaded] = useState(false);
  const [hlsLoaded, setHlsLoaded] = useState(false);
  const [plyrLoaded, setPlyrLoaded] = useState(false);

  // 检查脚本加载状态
  useEffect(() => {
    if (hlsLoaded && plyrLoaded) {
      console.log('🎉 所有播放器脚本加载完成');
      setScriptsLoaded(true);
    }
  }, [hlsLoaded, plyrLoaded]);

  // 初始化播放器
  useEffect(() => {
    if (!scriptsLoaded || !videoRef.current || !src) return;

    const initPlayer = async () => {
      try {
        setError(null);
        console.log('🎬 开始初始化播放器...');

        const video = videoRef.current!;
        
        // 初始化Plyr播放器
        playerRef.current = new window.Plyr(video, {
          controls: [
            'play-large',
            'play',
            'progress', 
            'current-time',
            'duration',
            'mute',
            'volume',
            'settings',
            'fullscreen'
          ],
          settings: ['quality', 'speed'],
          quality: { default: 'auto' },
          speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 2] },
          // 防止布局跳动的配置
          ratio: null,
          fullscreen: { 
            enabled: true, 
            fallback: true, 
            iosNative: false 
          },
          autoplay: false,
          muted: false
        });

        // 检查HLS支持
        if (window.Hls.isSupported()) {
          // 创建自定义加载器用于解密
          class FMP4SteganographyLoader extends window.Hls.DefaultConfig.loader {
            constructor(config: any) {
              super(config);
              this.cache = new Map();
              this.retryCount = new Map();
              this.maxCacheSize = 100;
            }
            
            load(context: any, config: any, callbacks: any) {
              const url = context.url;

              // 区分M3U8播放列表和片段文件
              if (url.includes('.m3u8') || context.type === 'manifest') {
                super.load(context, config, callbacks);
                return;
              }

              // 只对TikTok CDN的片段文件进行PNG解码
              if (!url.includes('ibyteimg.com') && !url.includes('tiktok')) {
                super.load(context, config, callbacks);
                return;
              }

              // 检查缓存
              if (this.cache.has(url)) {
                const cachedData = this.cache.get(url);
                const response = {
                  url: url,
                  data: cachedData,
                  code: 200,
                  text: 'OK'
                };
                callbacks.onSuccess(response, {}, context);
                return;
              }

              // 下载PNG伪装文件
              fetch(url, {
                method: 'GET',
                cache: 'default',
                headers: {
                  'Accept': 'image/png,image/*,*/*;q=0.8',
                  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
              })
              .then(response => {
                if (!response.ok) {
                  throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.arrayBuffer();
              })
              .then(arrayBuffer => {
                try {
                  // PNG解码逻辑
                  const uint8Array = new Uint8Array(arrayBuffer);
                  
                  // 查找PNG文件结束标记 (IEND chunk)
                  const iendSignature = new Uint8Array([0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82]);
                  let iendIndex = -1;
                  
                  for (let i = 0; i <= uint8Array.length - iendSignature.length; i++) {
                    let match = true;
                    for (let j = 0; j < iendSignature.length; j++) {
                      if (uint8Array[i + j] !== iendSignature[j]) {
                        match = false;
                        break;
                      }
                    }
                    if (match) {
                      iendIndex = i + iendSignature.length;
                      break;
                    }
                  }
                  
                  if (iendIndex === -1) {
                    throw new Error('未找到PNG结束标记');
                  }
                  
                  // 提取PNG后面的MP4数据
                  const mp4Data = uint8Array.slice(iendIndex);
                  
                  if (mp4Data.length === 0) {
                    throw new Error('未找到MP4数据');
                  }
                  
                  // 缓存解码后的数据
                  if (this.cache.size >= this.maxCacheSize) {
                    const firstKey = this.cache.keys().next().value;
                    this.cache.delete(firstKey);
                  }
                  this.cache.set(url, mp4Data);
                  
                  const response = {
                    url: url,
                    data: mp4Data,
                    code: 200,
                    text: 'OK'
                  };
                  
                  callbacks.onSuccess(response, {}, context);
                } catch (decodeError) {
                  console.error('PNG解码失败:', decodeError);
                  callbacks.onError({ code: 500, text: `解码失败: ${decodeError.message}` }, context);
                }
              })
              .catch(error => {
                console.error('下载失败:', error);
                const retryKey = url;
                const retryCount = this.retryCount.get(retryKey) || 0;
                
                if (retryCount < 3) {
                  this.retryCount.set(retryKey, retryCount + 1);
                  setTimeout(() => this.load(context, config, callbacks), 1000 * (retryCount + 1));
                } else {
                  callbacks.onError({ code: 500, text: error.message }, context);
                }
              });
            }
          }

          // 创建HLS实例
          hlsRef.current = new window.Hls({
            loader: FMP4SteganographyLoader,
            enableWorker: true,
            lowLatencyMode: false,
            backBufferLength: 90
          });

          hlsRef.current.loadSource(src);
          hlsRef.current.attachMedia(video);

          hlsRef.current.on(window.Hls.Events.MANIFEST_PARSED, () => {
            console.log('✅ M3U8清单解析完成');
          });

          hlsRef.current.on(window.Hls.Events.ERROR, (event: any, data: any) => {
            console.error('HLS错误:', data);
            if (data.fatal) {
              setError(`播放错误: ${data.details}`);
            }
          });

        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Safari原生支持
          video.src = src;
        } else {
          setError('您的浏览器不支持HLS播放');
          return;
        }

        // 播放器事件监听
        playerRef.current.on('play', () => {
          console.log('▶️ 开始播放');
          setIsPlaying(true);
        });

        playerRef.current.on('pause', () => {
          console.log('⏸️ 暂停播放');
          setIsPlaying(false);
        });

        playerRef.current.on('ended', () => {
          console.log('🏁 播放结束');
          setIsPlaying(false);
        });

        console.log('🎉 播放器初始化完成');

      } catch (error) {
        console.error('播放器初始化失败:', error);
        setError('播放器初始化失败');
      }
    };

    initPlayer();

    // 清理函数
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
    };
  }, [scriptsLoaded, src]);

  if (!src) {
    return (
      <div className="relative w-full h-full bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <p className="text-lg mb-2">暂无播放源</p>
          <p className="text-sm text-gray-400">该影片暂时无法播放</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* 本地CSS */}
      <link rel="stylesheet" href="/vendor/plyr/plyr.css" />
      
      {/* 本地脚本 */}
      <Script
        src="/vendor/hls/hls.min.js"
        onLoad={() => {
          console.log('📦 HLS.js 本地脚本加载完成');
          setHlsLoaded(true);
        }}
        onError={() => {
          console.error('❌ HLS.js 本地脚本加载失败');
          setError('HLS脚本加载失败');
        }}
      />
      
      <Script
        src="/vendor/plyr/plyr.min.js"
        onLoad={() => {
          console.log('🎮 Plyr.js 本地脚本加载完成');
          setPlyrLoaded(true);
        }}
        onError={() => {
          console.error('❌ Plyr.js 本地脚本加载失败');
          setError('Plyr脚本加载失败');
        }}
      />

      <div className="relative w-full h-full bg-black">
        {/* 内联样式防止布局跳动 */}
        <style jsx>{`
          .plyr {
            width: 100% !important;
            height: 100% !important;
          }
          .plyr__video-wrapper {
            width: 100% !important;
            height: 100% !important;
          }
          .plyr video {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
          }
        `}</style>

        {/* 时长信息栏 */}
        {(duration || durationFormatted) && (
          <div className="absolute top-2 left-2 z-30 bg-black/70 text-white px-2 py-1 rounded text-sm">
            {durationFormatted || `${Math.floor((duration || 0) / 60)}:${((duration || 0) % 60).toString().padStart(2, '0')}`}
          </div>
        )}

        {/* 加载状态 */}
        {!scriptsLoaded && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-40">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <p className="text-sm">加载本地播放器...</p>
            </div>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black z-40">
            <div className="text-white text-center">
              <p className="text-lg mb-2">播放失败</p>
              <p className="text-sm text-gray-400">{error}</p>
            </div>
          </div>
        )}

        {/* 视频播放器 */}
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          poster={poster}
          crossOrigin="anonymous"
          playsInline
          webkit-playsinline="true"
          controls={false}
          style={{ 
            width: '100%', 
            height: '100%',
            objectFit: 'cover'
          }}
        >
          <source src={src} type="application/x-mpegURL" />
          您的浏览器不支持视频播放。
        </video>
      </div>
    </>
  );
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Download, Heart, Share2, Play, MoreHorizontal, Eye, ThumbsUp } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import { useSession } from '@/components/SessionProvider';
import type { LocalMovie } from '@/types/javbus';

interface MovieListItemProps {
  movie: LocalMovie;
}

export default function MovieListItem({ movie }: MovieListItemProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useSession();

  const imageUrl = movie.localImg || movie.img || '/placeholder-movie.jpg';

  const fetchFavoriteStatus = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/favorites?movieId=${movie.id}&userId=${user.id}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setIsFavorited(result.data.isFavorited);
        }
      }
    } catch (error) {
      console.error('获取收藏状态失败:', error);
    }
  }, [user, movie.id]);

  // 获取用户收藏状态
  useEffect(() => {
    if (user && movie.id) {
      fetchFavoriteStatus();
    }
  }, [user, movie.id, fetchFavoriteStatus]);

  // 收藏功能处理函数
  const handleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!user) {
      alert('收藏功能需要登录，请先注册或登录');
      return;
    }

    if (isLoading) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieId: movie.id,
          userId: user.id,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setIsFavorited(result.action === 'favorited');
        } else {
          alert(result.error || '收藏操作失败');
        }
      } else {
        const error = await response.json();
        alert(error.error || '收藏操作失败');
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      alert('收藏操作失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (navigator.share) {
      navigator.share({
        title: movie.title,
        url: window.location.origin + `/movies/${movie.id}`
      });
    } else {
      navigator.clipboard.writeText(window.location.origin + `/movies/${movie.id}`);
    }
  };

  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Download movie:', movie.id);
  };

  return (
    <div
      className="group flex bg-[#181818] hover:bg-[#242424] rounded-lg p-4 transition-all duration-500 ease-out hover:shadow-lg hover:shadow-black/20 hover:-translate-y-0.5"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        setShowQuickActions(false);
      }}
    >
      <Link href={`/movies/${movie.id}`} className="flex w-full">
        {/* 缩略图 */}
        <div className={`relative w-40 h-24 flex-shrink-0 rounded-lg overflow-hidden bg-[#272727] transition-all duration-500 ${
          isHovered ? 'ring-1 ring-white/10' : ''
        }`}>
          <OptimizedImage
            src={imageUrl}
            alt={movie.title}
            fill
            className={`object-cover transition-all duration-500 ease-out ${
              isHovered ? 'scale-103 brightness-110' : 'scale-100 brightness-100'
            }`}
            sizes="160px"
            quality={85}
            preserveOriginal={true}
          />

          {/* 时长标签 */}
          {(movie.durationFormatted || movie.videoLength) && (
            <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 py-0.5 rounded">
              {movie.durationFormatted || `${movie.videoLength}:00`}
            </div>
          )}



          {/* 播放按钮覆盖层 */}
          <div className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ease-out ${
            isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-90'
          }`}>
            <div className="w-8 h-8 bg-black/70 rounded-full flex items-center justify-center backdrop-blur-md border border-white/10 shadow-md">
              <Play className="w-4 h-4 text-white ml-0.5" fill="currentColor" />
            </div>
          </div>
        </div>

        {/* 内容信息 */}
        <div className="flex-1 ml-4 min-w-0">
          {/* 标题和快速操作 */}
          <div className="flex items-start justify-between mb-2">
            <h3 className={`font-medium text-base line-clamp-2 leading-tight transition-colors duration-500 ${
              isHovered ? 'text-gray-100' : 'text-gray-300'
            }`}>
              {movie.title}
            </h3>
            
            {/* 快速操作按钮 */}
            <div className={`flex items-center gap-1 ml-4 transition-all duration-500 ease-out ${
              isHovered ? 'opacity-100 translate-x-0 scale-100' : 'opacity-0 translate-x-1 scale-95'
            }`}>
              <button
                onClick={handleFavorite}
                className={`p-1.5 rounded-full backdrop-blur-sm transition-colors ${
                  isFavorited 
                    ? 'bg-red-600 text-white' 
                    : 'bg-black/60 text-white hover:bg-red-600'
                }`}
              >
                <Heart className={`w-3 h-3 ${isFavorited ? 'fill-current' : ''}`} />
              </button>
              
              <button
                onClick={handleShare}
                className="p-1.5 bg-black/60 text-white rounded-full backdrop-blur-sm hover:bg-blue-600 transition-colors"
              >
                <Share2 className="w-3 h-3" />
              </button>
              
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowQuickActions(!showQuickActions);
                }}
                className="p-1.5 bg-black/60 text-white rounded-full backdrop-blur-sm hover:bg-gray-600 transition-colors relative"
              >
                <MoreHorizontal className="w-3 h-3" />
                
                {/* 更多操作菜单 */}
                {showQuickActions && (
                  <div className="absolute top-8 right-0 bg-[#212121] border border-[#3f3f3f] rounded-lg shadow-lg z-10 min-w-32">
                    <button
                      onClick={handleDownload}
                      className="w-full flex items-center px-3 py-2 text-sm text-white hover:bg-[#3f3f3f] transition-colors"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      下载
                    </button>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      className="w-full flex items-center px-3 py-2 text-sm text-white hover:bg-[#3f3f3f] transition-colors"
                    >
                      <Play className="w-4 h-4 mr-2" />
                      稍后观看
                    </button>
                  </div>
                )}
              </button>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="flex items-center text-sm text-gray-400 mb-2 gap-4">
            <div className="flex items-center gap-1">
              <Eye className="w-3 h-3" />
              <span>{(movie.views || 0).toLocaleString()}</span>
            </div>

            <div className="flex items-center gap-1">
              <ThumbsUp className="w-3 h-3" />
              <span>{(movie.likes || 0).toLocaleString()}</span>
            </div>


          </div>





          {/* 悬浮时显示的描述 */}
          <div className={`mt-2 transition-all duration-500 ease-out overflow-hidden ${
            isHovered ? 'max-h-16 opacity-100 translate-y-0' : 'max-h-0 opacity-0 -translate-y-1'
          }`}>
            <p className="text-xs text-gray-400 line-clamp-2">
              {movie.title.length > 100 ? movie.title : '精彩内容，值得观看。高质量影片，带来极致视觉体验...'}
            </p>
          </div>
        </div>
      </Link>
    </div>
  );
}

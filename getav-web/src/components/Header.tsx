'use client';

import Link from 'next/link';
import SearchBar from './SearchBar';
import { useSession } from './SessionProvider';

interface HeaderProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  rightContent?: React.ReactNode;
}

export default function Header({
  sidebarOpen,
  setSidebarOpen,
  rightContent
}: HeaderProps) {
  const { user, loading, logout } = useSession();
  return (
    <header className="bg-[#0f0f0f] sticky top-0 z-50">
      <div className="flex items-center justify-between h-16 px-3">
        {/* 左侧：汉堡菜单 + Logo */}
        <div className="flex items-center space-x-2 sm:space-x-4 min-w-0">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 hover:bg-[#272727] rounded-full transition-colors flex-shrink-0"
            title={sidebarOpen ? '隐藏侧边栏' : '显示侧边栏'}
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
            </svg>
          </button>
          <Link href="/" className="flex items-center space-x-2 min-w-0">
            <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center flex-shrink-0">
              <span className="text-white font-bold text-sm">G</span>
            </div>
            <h1 className="text-lg sm:text-xl font-bold text-white truncate">GETAV</h1>
          </Link>
        </div>

        {/* 中间：搜索栏 - 响应式隐藏 */}
        <div className="hidden md:flex flex-1 max-w-2xl mx-4">
          <SearchBar
            onSearch={(query) => {
              window.location.href = `/search?q=${encodeURIComponent(query)}`;
            }}
            placeholder="搜索"
          />
        </div>

        {/* 右侧：搜索按钮 + 登录注册按钮 + 自定义内容 */}
        <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
          {/* 移动端搜索按钮 */}
          <Link
            href="/search"
            className="md:hidden p-2 hover:bg-[#272727] rounded-full transition-colors"
            title="搜索"
          >
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </Link>

          {/* 用户状态显示 */}
          {!loading && (
            <>
              {user ? (
                // 已登录用户
                <div className="hidden sm:flex items-center space-x-2">
                  <Link
                    href="/profile"
                    className="px-3 py-1.5 text-sm text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-[#272727]"
                  >
                    {user.username}
                  </Link>
                  <button
                    onClick={logout}
                    className="px-3 py-1.5 text-sm text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-[#272727]"
                  >
                    退出
                  </button>
                </div>
              ) : (
                // 未登录用户
                <>
                  <div className="hidden sm:flex items-center space-x-2">
                    <Link
                      href="/auth/signin"
                      className="px-3 py-1.5 text-sm text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-[#272727]"
                    >
                      登录
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="px-3 py-1.5 text-sm bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                    >
                      注册
                    </Link>
                  </div>

                  {/* 移动端登录注册按钮 */}
                  <div className="sm:hidden flex items-center space-x-1">
                    <Link
                      href="/auth/signin"
                      className="p-2 hover:bg-[#272727] rounded-full transition-colors"
                      title="登录"
                    >
                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </Link>
                  </div>
                </>
              )}
            </>
          )}

          {rightContent}
        </div>
      </div>
    </header>
  );
}

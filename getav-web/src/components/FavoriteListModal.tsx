'use client';

import { useState, useEffect, useCallback } from 'react';
import { X, Plus, Check, Bookmark, Clock, Heart } from 'lucide-react';
import { FavoriteList } from '@/types/favorite-list';
import { getUserFavoriteListsWithDefault, getMovieFavoriteStatus } from '@/lib/favorite-list-utils';
import { FavoriteListSkeleton } from '@/components/LoadingSkeleton';

interface FavoriteListModalProps {
  isOpen: boolean;
  onClose: () => void;
  movieId: string;
  movieTitle: string;
  userId: string;
  currentFavoriteListId?: string;
  onFavoriteChange?: (listId: string | null, action: 'add' | 'remove' | 'move') => void;
  onCreateNewList?: () => void;
}

interface FavoriteListOption extends FavoriteList {
  isFavorited: boolean;
}

export default function FavoriteListModal({
  isOpen,
  onClose,
  movieId,
  movieTitle,
  userId,
  onFavoriteChange,
  onCreateNewList
}: FavoriteListModalProps) {
  const [favoriteLists, setFavoriteLists] = useState<FavoriteListOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState<string | null>(null);

  const fetchFavoriteLists = useCallback(async () => {
    setLoading(true);
    try {
      // 使用工具函数获取收藏列表，自动创建默认列表
      const lists = await getUserFavoriteListsWithDefault(userId, true);

      // 获取当前影片在各列表中的状态
      const statusData = await getMovieFavoriteStatus(userId, movieId);

      if (statusData.favoriteLists && statusData.favoriteLists.length > 0) {
        const favoriteListIds = new Set(
          statusData.favoriteLists.filter((list: { isFavorited: boolean }) => list.isFavorited).map((list: { id: string }) => list.id)
        );

        const combinedLists = lists.map((list: FavoriteList) => ({
          ...list,
          isFavorited: favoriteListIds.has(list.id)
        }));

        setFavoriteLists(combinedLists);
      } else {
        // 如果没有状态数据，设置所有列表为未收藏
        setFavoriteLists(lists.map(list => ({ ...list, isFavorited: false })));
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      // 设置空列表，避免界面卡住
      setFavoriteLists([]);
    } finally {
      setLoading(false);
    }
  }, [userId, movieId]);

  // 获取用户收藏列表和当前影片状态
  useEffect(() => {
    if (isOpen && userId) {
      fetchFavoriteLists();
    }
  }, [isOpen, userId, movieId, fetchFavoriteLists]);

  const handleToggleFavorite = async (listId: string, currentlyFavorited: boolean) => {
    setUpdating(listId);
    try {
      const action = currentlyFavorited ? 'remove' : 'add';
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieId,
          userId,
          favoriteListId: listId,
          action
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 更新本地状态
          setFavoriteLists(prev => prev.map(list => 
            list.id === listId 
              ? { ...list, isFavorited: !currentlyFavorited }
              : list
          ));

          // 通知父组件
          onFavoriteChange?.(listId, action);
        }
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
    } finally {
      setUpdating(null);
    }
  };

  const getListIcon = (list: FavoriteListOption) => {
    if (list.isDefault) {
      return <Clock className="w-4 h-4" />;
    }
    return <Bookmark className="w-4 h-4" />;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#212121] rounded-lg w-full max-w-md max-h-[70vh] flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-[#3f3f3f]">
          <div className="flex items-center gap-3">
            <Heart className="w-5 h-5 text-red-400" />
            <h2 className="text-lg font-semibold text-white">保存到...</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-[#3f3f3f] rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* 影片标题 */}
        <div className="p-4 border-b border-[#3f3f3f]">
          <h3 className="text-white font-medium line-clamp-2 text-sm">{movieTitle}</h3>
        </div>

        {/* 收藏列表 */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <FavoriteListSkeleton />
          ) : (
            <div className="py-2">
              {favoriteLists.map((list) => (
                <button
                  key={list.id}
                  onClick={() => handleToggleFavorite(list.id, list.isFavorited)}
                  disabled={updating === list.id}
                  className="w-full flex items-center gap-3 px-4 py-3 hover:bg-[#2a2a2a] transition-colors disabled:opacity-50"
                >
                  {/* 列表图标 */}
                  <div className="text-gray-400">
                    {getListIcon(list)}
                  </div>

                  {/* 列表信息 */}
                  <div className="flex-1 text-left">
                    <div className="flex items-center gap-2">
                      <span className="text-white font-medium">{list.name}</span>
                      {list.isDefault && (
                        <span className="text-xs text-gray-400 bg-[#3f3f3f] px-2 py-0.5 rounded">
                          默认
                        </span>
                      )}
                    </div>
                    {list.description && (
                      <p className="text-gray-400 text-sm mt-0.5 line-clamp-1">
                        {list.description}
                      </p>
                    )}
                    <p className="text-gray-500 text-xs mt-0.5">
                      {list.count || 0} 个视频
                      {list.isPublic && ' • 公开'}
                    </p>
                  </div>

                  {/* 选中状态 */}
                  <div className="flex items-center">
                    {updating === list.id ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    ) : (
                      <div className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                        list.isFavorited
                          ? 'bg-blue-600 border-blue-600'
                          : 'border-gray-400'
                      }`}>
                        {list.isFavorited && (
                          <Check className="w-3 h-3 text-white" />
                        )}
                      </div>
                    )}
                  </div>
                </button>
              ))}

              {/* 创建新列表按钮 */}
              <button
                onClick={() => {
                  onCreateNewList?.();
                  onClose();
                }}
                className="w-full flex items-center gap-3 px-4 py-3 hover:bg-[#2a2a2a] transition-colors border-t border-[#3f3f3f] mt-2"
              >
                <div className="w-4 h-4 text-blue-400">
                  <Plus className="w-4 h-4" />
                </div>
                <span className="text-blue-400 font-medium">创建新的播放列表</span>
              </button>
            </div>
          )}
        </div>

        {/* 底部提示 */}
        <div className="p-4 border-t border-[#3f3f3f]">
          <p className="text-gray-400 text-xs text-center">
            选择播放列表来保存此视频
          </p>
        </div>
      </div>
    </div>
  );
}

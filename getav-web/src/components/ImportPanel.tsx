'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Download, Loader2, CheckCircle, AlertCircle } from 'lucide-react';

interface ImportResult {
  success: boolean;
  data?: {
    message: string;
    totalMoviesFetched: number;
    importedMoviesCount: number;
    importedMovies: Array<{
      id: string;
      title: string;
      localCoverImage?: string;
      starsCount: number;
      magnetsCount: number;
      samplesCount: number;
    }>;
  };
  error?: string;
  details?: string;
}

export default function ImportPanel() {
  const [importing, setImporting] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [config, setConfig] = useState({
    count: 5,
    page: 1,
    downloadImages: true
  });

  const getCookie = (name: string) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift();
    return '';
  };

  const startImport = async () => {
    setImporting(true);
    setResult(null);

    try {
      const adminToken = getCookie('admin-token');

      const response = await fetch('/api/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminToken}`
        },
        body: JSON.stringify(config),
      });
      
      const data: ImportResult = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: '导入请求失败',
        details: error instanceof Error ? error.message : '未知错误'
      });
    } finally {
      setImporting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="w-5 h-5" />
          数据导入面板
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 配置选项 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              每页数量
            </label>
            <Input
              type="number"
              value={config.count}
              onChange={(e) => setConfig(prev => ({ 
                ...prev, 
                count: parseInt(e.target.value) || 5 
              }))}
              min="1"
              max="50"
              disabled={importing}
            />
          </div>
          
          <div>
            <label className="text-sm font-medium mb-2 block">
              页数
            </label>
            <Input
              type="number"
              value={config.page}
              onChange={(e) => setConfig(prev => ({ 
                ...prev, 
                page: parseInt(e.target.value) || 1 
              }))}
              min="1"
              max="10"
              disabled={importing}
            />
          </div>
          
          <div className="flex items-end">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.downloadImages}
                onChange={(e) => setConfig(prev => ({ 
                  ...prev, 
                  downloadImages: e.target.checked 
                }))}
                disabled={importing}
                className="rounded"
              />
              <span className="text-sm font-medium">下载图片</span>
            </label>
          </div>
        </div>

        {/* 导入按钮 */}
        <Button
          onClick={startImport}
          disabled={importing}
          className="w-full"
          size="lg"
        >
          {importing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              正在导入数据...
            </>
          ) : (
            <>
              <Download className="w-4 h-4 mr-2" />
              开始导入
            </>
          )}
        </Button>

        {/* 导入结果 */}
        {result && (
          <div className="space-y-4">
            {result.success ? (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-800">导入成功</span>
                </div>
                
                {result.data && (
                  <div className="space-y-2 text-sm text-green-700">
                    <p>{result.data.message}</p>
                    <div className="flex gap-4">
                      <Badge variant="outline">
                        获取: {result.data.totalMoviesFetched}部
                      </Badge>
                      <Badge variant="outline">
                        导入: {result.data.importedMoviesCount}部
                      </Badge>
                    </div>
                    
                    {result.data.importedMovies && result.data.importedMovies.length > 0 && (
                      <div className="mt-3">
                        <p className="font-medium mb-2">导入的影片示例：</p>
                        <div className="space-y-1">
                          {result.data.importedMovies.map((movie) => (
                            <div key={movie.id} className="text-xs bg-white p-2 rounded border">
                              <div className="font-medium">{movie.id}: {movie.title}</div>
                              <div className="text-gray-600 mt-1">
                                演员: {movie.starsCount}人 | 
                                磁力: {movie.magnetsCount}个 | 
                                样品: {movie.samplesCount}张
                                {movie.localCoverImage && ' | 封面已下载'}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <span className="font-medium text-red-800">导入失败</span>
                </div>
                <div className="text-sm text-red-700">
                  <p>{result.error}</p>
                  {result.details && (
                    <p className="mt-1 text-xs opacity-75">{result.details}</p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 说明信息 */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• 数据将从JAVBUS API采集并保存到本地数据库</p>
          <p>• 图片将下载到本地存储，路径为 /images/getav/</p>
          <p>• 包含影片信息、演员详情、分类、样品图片、磁力链接等完整数据</p>
          <p>• 建议首次导入时选择较小的数量进行测试</p>
        </div>
      </CardContent>
    </Card>
  );
}

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Download, Heart, Share2, Play, MoreHorizontal, Eye, ThumbsUp } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import { useSession } from '@/components/SessionProvider';
import { useBatchFavoritesContext } from '@/contexts/BatchFavoritesContext';
import type { LocalMovie } from '@/types/javbus';

interface MovieCardProps {
  movie: LocalMovie;
  viewMode?: 'grid' | 'list';
}

export default function MovieCard({ movie, viewMode = 'grid' }: MovieCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useSession();

  // 使用批量收藏状态管理
  const { isFavorited, updateFavorite, fetchFavorites } = useBatchFavoritesContext();

  // 只使用本地图片路径，如果没有则使用占位图
  const imageUrl = movie.localImg || '/placeholder-movie.jpg';

  // 获取当前影片的收藏状态
  const movieIsFavorited = isFavorited(movie.id);

  // 确保当前影片的收藏状态已加载
  useEffect(() => {
    if (user && movie.id) {
      // 使用批量收藏状态管理，自动去重和缓存
      fetchFavorites([movie.id]);
    }
  }, [user, movie.id, fetchFavorites]);

  // 收藏功能处理函数
  const handleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!user) {
      alert('收藏功能需要登录，请先注册或登录');
      return;
    }

    if (isLoading) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieId: movie.id,
          userId: user.id,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const newFavoriteStatus = result.action === 'favorited';
          // 更新批量收藏状态管理中的状态
          updateFavorite(movie.id, newFavoriteStatus);
        } else {
          alert(result.error || '收藏操作失败');
        }
      } else {
        const error = await response.json();
        alert(error.error || '收藏操作失败');
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      alert('收藏操作失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (navigator.share) {
      navigator.share({
        title: movie.title,
        url: window.location.origin + `/movies/${movie.id}`
      });
    } else {
      // 复制到剪贴板
      navigator.clipboard.writeText(window.location.origin + `/movies/${movie.id}`);
    }
  };

  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // TODO: 实际的下载逻辑
    console.log('Download movie:', movie.id);
  };

  return (
    <div
      className={`group cursor-pointer transition-all duration-500 ease-out ${
        viewMode === 'list'
          ? 'flex gap-4 p-4 bg-[#1a1a1a] rounded-lg hover:bg-[#242424] hover:shadow-lg hover:shadow-black/20'
          : 'hover:-translate-y-1 hover:shadow-xl hover:shadow-black/25'
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        setShowQuickActions(false);
      }}
    >
      <Link href={`/movies/${movie.id}`} className={viewMode === 'list' ? 'flex gap-4 w-full' : ''}>
        {/* AV封面缩略图区域 - 使用实际图片比例 800:538 (约1.49:1) */}
        <div className={`relative overflow-hidden rounded-lg bg-[#272727] transition-all duration-500 ${
          viewMode === 'list' ? 'w-48 h-32 flex-shrink-0' : 'aspect-[800/538]'
        } ${isHovered ? 'ring-1 ring-white/10' : ''}`}>
          <OptimizedImage
            src={imageUrl}
            alt={movie.title}
            fill
            className={`object-cover transition-all duration-500 ease-out ${
              isHovered ? 'scale-105 brightness-110' : 'scale-100 brightness-100'
            }`}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            quality={85}
            preserveOriginal={true}
          />

          {/* 时长标签 - YouTube风格 */}
          {(movie.durationFormatted || movie.videoLength) && (
            <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-1.5 py-0.5 rounded">
              {movie.durationFormatted || `${movie.videoLength}:00`}
            </div>
          )}



          {/* 快速操作按钮 */}
          <div className={`absolute top-2 right-2 flex flex-col gap-1 transition-all duration-500 ease-out ${
            isHovered ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 -translate-y-1 scale-95'
          }`}>
            <button
              onClick={handleFavorite}
              disabled={isLoading}
              className={`p-1.5 rounded-full backdrop-blur-sm transition-colors ${
                movieIsFavorited
                  ? 'bg-red-600 text-white'
                  : 'bg-black/60 text-white hover:bg-red-600'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <Heart className={`w-3 h-3 ${movieIsFavorited ? 'fill-current' : ''} ${isLoading ? 'animate-pulse' : ''}`} />
            </button>

            <button
              onClick={handleShare}
              className="p-1.5 bg-black/60 text-white rounded-full backdrop-blur-sm hover:bg-blue-600 transition-colors"
            >
              <Share2 className="w-3 h-3" />
            </button>

            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setShowQuickActions(!showQuickActions);
              }}
              className="p-1.5 bg-black/60 text-white rounded-full backdrop-blur-sm hover:bg-gray-600 transition-colors"
            >
              <MoreHorizontal className="w-3 h-3" />
            </button>
          </div>

          {/* 更多操作菜单 */}
          {showQuickActions && (
            <div className="absolute top-12 right-2 bg-[#212121] rounded-lg shadow-lg z-10 min-w-32">
              <button
                onClick={handleDownload}
                className="w-full flex items-center px-3 py-2 text-sm text-white hover:bg-[#3f3f3f] transition-colors"
              >
                <Download className="w-4 h-4 mr-2" />
                下载
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  // TODO: 添加到播放列表
                }}
                className="w-full flex items-center px-3 py-2 text-sm text-white hover:bg-[#3f3f3f] transition-colors"
              >
                <Play className="w-4 h-4 mr-2" />
                稍后观看
              </button>
            </div>
          )}

          {/* 悬浮播放按钮 */}
          <div className={`absolute inset-0 flex items-center justify-center ${
            isHovered && !showQuickActions ? 'opacity-100' : 'opacity-0'
          }`}>
            <div className="w-16 h-16 bg-black/70 rounded-full flex items-center justify-center backdrop-blur-md border border-white/10 shadow-lg">
              <Play className="w-8 h-8 text-white ml-1" fill="currentColor" />
            </div>
          </div>

          {/* 悬浮预览遮罩 */}
          <div className={`absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent transition-opacity duration-500 ${
            isHovered ? 'opacity-0' : 'opacity-100'
          }`} />
        </div>
        {/* YouTube风格的视频信息区域 */}
        <div className={`space-y-1.5 ${viewMode === 'list' ? 'flex-1' : 'pt-3'}`}>
          {/* 标题 */}
          <h3 className={`font-medium leading-tight transition-colors duration-500 ${
            viewMode === 'list' ? 'text-base line-clamp-1 mb-2' : 'text-sm line-clamp-2'
          } ${isHovered ? 'text-gray-100' : 'text-gray-300'}`}>
            {movie.title}
          </h3>

          {/* 统计信息 */}
          <div className="flex items-center text-xs text-gray-400 gap-3">
            <div className="flex items-center gap-1">
              <Eye className="w-3 h-3" />
              <span>{(movie.views || 0).toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <ThumbsUp className="w-3 h-3" />
              <span>{(movie.likes || 0).toLocaleString()}</span>
            </div>
          </div>




        </div>
      </Link>
    </div>
  );
}

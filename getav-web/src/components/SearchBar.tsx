'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, X, Clock, TrendingUp } from 'lucide-react';

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'history' | 'suggestion' | 'trending';
}

interface SearchBarProps {
  onSearch: (query: string) => void;
  placeholder?: string;
}

export default function SearchBar({ onSearch, placeholder = "搜索" }: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 模拟的热门搜索和建议
  const trendingSuggestions = [
    '高清无码',
    '中文字幕',
    '新人',
    '制服',
    '学生',
    '人妻',
    '巨乳',
    '美少女'
  ];

  // 从localStorage加载搜索历史
  useEffect(() => {
    const saved = localStorage.getItem('search-history');
    if (saved) {
      setSearchHistory(JSON.parse(saved));
    }
  }, []);

  // 保存搜索历史到localStorage
  const saveSearchHistory = (newQuery: string) => {
    const updated = [newQuery, ...searchHistory.filter(h => h !== newQuery)].slice(0, 10);
    setSearchHistory(updated);
    localStorage.setItem('search-history', JSON.stringify(updated));
  };

  // 从API获取搜索建议
  const fetchSuggestions = async (searchQuery: string): Promise<SearchSuggestion[]> => {
    if (!searchQuery.trim()) {
      // 显示搜索历史和热门搜索
      const historySuggestions: SearchSuggestion[] = searchHistory.slice(0, 5).map(h => ({
        id: `history-${h}`,
        text: h,
        type: 'history'
      }));

      const trendingSuggestionsData: SearchSuggestion[] = trendingSuggestions.slice(0, 5).map(t => ({
        id: `trending-${t}`,
        text: t,
        type: 'trending'
      }));

      return [...historySuggestions, ...trendingSuggestionsData];
    }

    try {
      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchQuery)}&limit=8`);
      const data = await response.json();

      if (data.success && data.data.suggestions) {
        return data.data.suggestions.map((item: { text: string; type: string }, index: number) => ({
          id: `api-${index}`,
          text: item.text,
          type: item.type === 'star' ? 'suggestion' as const :
                item.type === 'genre' ? 'suggestion' as const :
                'suggestion' as const
        }));
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error);
    }

    // 降级到本地建议
    const filtered = trendingSuggestions
      .filter(s => s.toLowerCase().includes(searchQuery.toLowerCase()))
      .slice(0, 8)
      .map(s => ({
        id: `suggestion-${s}`,
        text: s,
        type: 'suggestion' as const
      }));

    return filtered;
  };

  // 生成搜索建议（同步版本，用于即时显示）
  const generateSuggestions = (searchQuery: string) => {
    if (!searchQuery.trim()) {
      // 显示搜索历史和热门搜索
      const historySuggestions: SearchSuggestion[] = searchHistory.slice(0, 5).map(h => ({
        id: `history-${h}`,
        text: h,
        type: 'history'
      }));

      const trendingSuggestionsData: SearchSuggestion[] = trendingSuggestions.slice(0, 5).map(t => ({
        id: `trending-${t}`,
        text: t,
        type: 'trending'
      }));

      return [...historySuggestions, ...trendingSuggestionsData];
    }

    // 基于输入生成建议
    const filtered = trendingSuggestions
      .filter(s => s.toLowerCase().includes(searchQuery.toLowerCase()))
      .slice(0, 8)
      .map(s => ({
        id: `suggestion-${s}`,
        text: s,
        type: 'suggestion' as const
      }));

    return filtered;
  };

  // 处理输入变化
  const handleInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);

    // 立即显示本地建议
    setSuggestions(generateSuggestions(value));

    // 异步获取API建议
    if (value.trim()) {
      try {
        const apiSuggestions = await fetchSuggestions(value);
        setSuggestions(apiSuggestions);
      } catch (error) {
        console.error('获取API建议失败:', error);
      }
    }
  };

  // 处理搜索
  const handleSearch = (searchQuery: string = query) => {
    if (searchQuery.trim()) {
      saveSearchHistory(searchQuery.trim());
      onSearch(searchQuery.trim());
      setQuery(searchQuery.trim());
      setIsOpen(false);
    }
  };

  // 处理建议点击
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    handleSearch(suggestion.text);
  };

  // 清除搜索历史
  const clearHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('search-history');
    setSuggestions(generateSuggestions(query));
  };



  // 点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={searchRef} className="relative flex-1 max-w-2xl mx-6">
      <div className="flex">
        <div className="relative flex-1">
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={() => {
              setIsOpen(true);
              setSuggestions(generateSuggestions(query));
            }}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleSearch();
              }
            }}
            placeholder={placeholder}
            className="w-full h-10 pl-4 pr-12 bg-[#1a1a1a] border border-[#404040] rounded-l-full focus:outline-none focus:border-[#aaa] focus:bg-[#222] text-white placeholder-gray-400 transition-all duration-200"
          />

          {/* 清除按钮 */}
          {query && (
            <button
              onClick={() => {
                setQuery('');
                setSuggestions(generateSuggestions(''));
                inputRef.current?.focus();
              }}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-[#3f3f3f] rounded-full transition-colors"
            >
              <X className="w-4 h-4 text-gray-400" />
            </button>
          )}


          {/* 搜索建议下拉框 */}
          {isOpen && suggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-[#212121] border border-[#3f3f3f] rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
              {/* 搜索历史标题 */}
              {searchHistory.length > 0 && !query && (
                <div className="flex items-center justify-between px-4 py-2 border-b border-[#3f3f3f]">
                  <span className="text-sm text-gray-400">搜索历史</span>
                  <button
                    onClick={clearHistory}
                    className="text-xs text-blue-400 hover:text-blue-300"
                  >
                    清除
                  </button>
                </div>
              )}

              {/* 建议列表 */}
              {suggestions.map((suggestion) => (
                <button
                  key={suggestion.id}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full flex items-center px-4 py-2 hover:bg-[#3f3f3f] transition-colors text-left"
                >
                  {suggestion.type === 'history' && <Clock className="w-4 h-4 mr-3 text-gray-400" />}
                  {suggestion.type === 'trending' && <TrendingUp className="w-4 h-4 mr-3 text-gray-400" />}
                  {suggestion.type === 'suggestion' && <Search className="w-4 h-4 mr-3 text-gray-400" />}
                  <span className="text-white text-sm">{suggestion.text}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 搜索按钮 */}
        <button
          onClick={() => handleSearch()}
          className="h-10 px-4 bg-[#2a2a2a] border border-l-0 border-[#404040] rounded-r-full hover:bg-[#404040] focus:bg-[#404040] transition-all duration-200"
        >
          <Search className="w-5 h-5 text-gray-300" />
        </button>
      </div>
    </div>
  );
}

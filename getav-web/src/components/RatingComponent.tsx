'use client';

import { useState, useEffect } from 'react';
import { useSession } from './SessionProvider';

interface RatingComponentProps {
  movieId: string;
  className?: string;
}

interface RatingData {
  averageScore: number | null;
  totalRatings: number;
  userScore: number | null;
  hasRated: boolean;
}

export default function RatingComponent({ movieId, className = '' }: RatingComponentProps) {
  const { user } = useSession();
  const [ratingData, setRatingData] = useState<RatingData>({
    averageScore: null,
    totalRatings: 0,
    userScore: null,
    hasRated: false,
  });
  const [hoveredStar, setHoveredStar] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);

  // 获取评分数据
  const fetchRatingData = async () => {
    try {
      const params = new URLSearchParams({ movieId });
      if (user?.id) {
        params.append('userId', user.id);
      }

      const response = await fetch(`/api/ratings?${params}`);
      const data = await response.json();

      if (response.ok) {
        setRatingData(data);
      }
    } catch (error) {
      console.error('获取评分数据失败:', error);
    }
  };

  // 提交评分
  const submitRating = async (score: number) => {
    if (!user) {
      setShowLoginPrompt(true);
      setTimeout(() => setShowLoginPrompt(false), 3000);
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/ratings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieId,
          score,
          userId: user.id,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // 重新获取评分数据
        await fetchRatingData();
      } else {
        console.error('评分失败:', data.error);
      }
    } catch (error) {
      console.error('评分失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 删除评分
  const deleteRating = async () => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      const params = new URLSearchParams({
        movieId,
        userId: user.id,
      });

      const response = await fetch(`/api/ratings?${params}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await fetchRatingData();
      }
    } catch (error) {
      console.error('删除评分失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    fetchRatingData();
  }, [movieId, user]); // eslint-disable-line react-hooks/exhaustive-deps

  const renderStars = () => {
    const stars = [];
    for (let i = 1; i <= 10; i++) {
      const isFilled = hoveredStar ? i <= hoveredStar : i <= (ratingData.userScore || 0);
      
      stars.push(
        <button
          key={i}
          className={`w-6 h-6 transition-colors ${
            isFilled ? 'text-yellow-400' : 'text-gray-600'
          } hover:text-yellow-400 disabled:cursor-not-allowed`}
          onMouseEnter={() => !isSubmitting && setHoveredStar(i)}
          onMouseLeave={() => !isSubmitting && setHoveredStar(null)}
          onClick={() => !isSubmitting && submitRating(i)}
          disabled={isSubmitting}
          title={`评分 ${i} 分`}
        >
          <svg fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </button>
      );
    }
    return stars;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 平均评分显示 */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <span className="text-lg font-semibold text-white">
            {ratingData.averageScore ? ratingData.averageScore.toFixed(1) : '暂无评分'}
          </span>
          <div className="flex items-center">
            {Array.from({ length: 5 }, (_, i) => {
              const starValue = (i + 1) * 2;
              const isFilled = ratingData.averageScore ? starValue <= ratingData.averageScore : false;
              return (
                <svg
                  key={i}
                  className={`w-4 h-4 ${isFilled ? 'text-yellow-400' : 'text-gray-600'}`}
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                </svg>
              );
            })}
          </div>
          <span className="text-sm text-gray-400">
            ({ratingData.totalRatings} 人评分)
          </span>
        </div>
      </div>

      {/* 用户评分区域 */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-300">您的评分：</span>
          {ratingData.hasRated && (
            <button
              onClick={deleteRating}
              disabled={isSubmitting}
              className="text-xs text-red-400 hover:text-red-300 disabled:opacity-50"
            >
              删除评分
            </button>
          )}
        </div>
        
        <div className="flex items-center space-x-1">
          {renderStars()}
        </div>

        {ratingData.hasRated && (
          <p className="text-sm text-gray-400">
            您已评分：{ratingData.userScore} 分
          </p>
        )}
      </div>

      {/* 登录提示 */}
      {showLoginPrompt && (
        <div className="text-sm text-yellow-400 bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-2">
          评分功能需要登录，请先注册或登录
        </div>
      )}

      {isSubmitting && (
        <div className="text-sm text-gray-400">
          正在提交评分...
        </div>
      )}
    </div>
  );
}

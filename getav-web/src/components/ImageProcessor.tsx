'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Image as ImageIcon, 
  Trash2, 
  RefreshCw, 
  Settings, 
  BarChart3,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

/**
 * 图片处理管理组件
 * 提供图片处理、统计和清理功能的管理界面
 */

interface ImageStats {
  cover: {
    count: number;
    totalSize: number;
    formats: { [key: string]: number };
  };
  actress: {
    count: number;
    totalSize: number;
    formats: { [key: string]: number };
  };
  samples: {
    count: number;
    totalSize: number;
    formats: { [key: string]: number };
  };
}

interface ProcessingStatus {
  isProcessing: boolean;
  currentTask: string;
  progress: number;
  total: number;
  completed: number;
  failed: number;
}

export default function ImageProcessor() {
  const [stats, setStats] = useState<ImageStats | null>(null);
  const [processingStatus] = useState<ProcessingStatus>({
    isProcessing: false,
    currentTask: '',
    progress: 0,
    total: 0,
    completed: 0,
    failed: 0
  });
  const [isLoading, setIsLoading] = useState(false);

  // 加载图片统计
  const loadStats = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/images/process?action=stats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('加载图片统计失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 清理过期图片
  const cleanupImages = async (imageType: 'cover' | 'actress' | 'samples', maxAge: number = 30) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/images/process?type=${imageType}&maxAge=${maxAge}`, {
        method: 'DELETE'
      });
      const data = await response.json();
      
      if (data.success) {
        alert(`清理完成：删除了 ${data.deletedCount} 个过期图片`);
        await loadStats(); // 重新加载统计
      } else {
        alert(`清理失败：${data.error}`);
      }
    } catch (error) {
      console.error('清理图片失败:', error);
      alert('清理图片失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 组件挂载时加载统计
  useEffect(() => {
    loadStats();
  }, []);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">图片处理管理</h1>
          <p className="text-gray-400">管理和优化网站图片资源</p>
        </div>
        <Button 
          onClick={loadStats} 
          disabled={isLoading}
          variant="outline"
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          刷新统计
        </Button>
      </div>

      {/* 处理状态 */}
      {processingStatus.isProcessing && (
        <Card className="bg-[#272727] border-gray-600">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              正在处理图片
            </CardTitle>
            <CardDescription className="text-gray-400">
              {processingStatus.currentTask}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-400">
                <span>进度: {processingStatus.completed}/{processingStatus.total}</span>
                <span>{Math.round(processingStatus.progress)}%</span>
              </div>
              <Progress value={processingStatus.progress} className="w-full" />
              <div className="flex justify-between text-sm">
                <span className="text-green-400 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  成功: {processingStatus.completed}
                </span>
                <span className="text-red-400 flex items-center">
                  <XCircle className="w-4 h-4 mr-1" />
                  失败: {processingStatus.failed}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="stats" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-[#272727]">
          <TabsTrigger value="stats" className="data-[state=active]:bg-[#3f3f3f]">
            <BarChart3 className="w-4 h-4 mr-2" />
            统计信息
          </TabsTrigger>
          <TabsTrigger value="process" className="data-[state=active]:bg-[#3f3f3f]">
            <Settings className="w-4 h-4 mr-2" />
            处理设置
          </TabsTrigger>
          <TabsTrigger value="cleanup" className="data-[state=active]:bg-[#3f3f3f]">
            <Trash2 className="w-4 h-4 mr-2" />
            清理管理
          </TabsTrigger>
        </TabsList>

        {/* 统计信息标签页 */}
        <TabsContent value="stats" className="space-y-4">
          {stats ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 封面图片统计 */}
              <Card className="bg-[#272727] border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <ImageIcon className="w-5 h-5 mr-2" />
                    封面图片
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">数量:</span>
                      <span className="text-white">{stats.cover.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">总大小:</span>
                      <span className="text-white">{formatFileSize(stats.cover.totalSize)}</span>
                    </div>
                    <div className="space-y-1">
                      <span className="text-gray-400 text-sm">格式分布:</span>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(stats.cover.formats).map(([format, count]) => (
                          <Badge key={format} variant="secondary" className="text-xs">
                            {format.toUpperCase()}: {count}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 演员图片统计 */}
              <Card className="bg-[#272727] border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <ImageIcon className="w-5 h-5 mr-2" />
                    演员图片
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">数量:</span>
                      <span className="text-white">{stats.actress.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">总大小:</span>
                      <span className="text-white">{formatFileSize(stats.actress.totalSize)}</span>
                    </div>
                    <div className="space-y-1">
                      <span className="text-gray-400 text-sm">格式分布:</span>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(stats.actress.formats).map(([format, count]) => (
                          <Badge key={format} variant="secondary" className="text-xs">
                            {format.toUpperCase()}: {count}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 样品图片统计 */}
              <Card className="bg-[#272727] border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <ImageIcon className="w-5 h-5 mr-2" />
                    样品图片
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">数量:</span>
                      <span className="text-white">{stats.samples.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">总大小:</span>
                      <span className="text-white">{formatFileSize(stats.samples.totalSize)}</span>
                    </div>
                    <div className="space-y-1">
                      <span className="text-gray-400 text-sm">格式分布:</span>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(stats.samples.formats).map(([format, count]) => (
                          <Badge key={format} variant="secondary" className="text-xs">
                            {format.toUpperCase()}: {count}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400">
                {isLoading ? '正在加载统计信息...' : '暂无统计数据'}
              </div>
            </div>
          )}
        </TabsContent>

        {/* 处理设置标签页 */}
        <TabsContent value="process" className="space-y-4">
          <Card className="bg-[#272727] border-gray-600">
            <CardHeader>
              <CardTitle className="text-white">图片处理设置</CardTitle>
              <CardDescription className="text-gray-400">
                配置图片处理参数和批量处理选项
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-gray-400">
                图片处理功能正在开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 清理管理标签页 */}
        <TabsContent value="cleanup" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 封面图片清理 */}
            <Card className="bg-[#272727] border-gray-600">
              <CardHeader>
                <CardTitle className="text-white">清理封面图片</CardTitle>
                <CardDescription className="text-gray-400">
                  删除超过指定天数的封面图片
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  onClick={() => cleanupImages('cover', 30)}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  清理30天前的图片
                </Button>
                <Button 
                  onClick={() => cleanupImages('cover', 7)}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  清理7天前的图片
                </Button>
              </CardContent>
            </Card>

            {/* 演员图片清理 */}
            <Card className="bg-[#272727] border-gray-600">
              <CardHeader>
                <CardTitle className="text-white">清理演员图片</CardTitle>
                <CardDescription className="text-gray-400">
                  删除超过指定天数的演员图片
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  onClick={() => cleanupImages('actress', 30)}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  清理30天前的图片
                </Button>
                <Button 
                  onClick={() => cleanupImages('actress', 7)}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  清理7天前的图片
                </Button>
              </CardContent>
            </Card>

            {/* 样品图片清理 */}
            <Card className="bg-[#272727] border-gray-600">
              <CardHeader>
                <CardTitle className="text-white">清理样品图片</CardTitle>
                <CardDescription className="text-gray-400">
                  删除超过指定天数的样品图片
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  onClick={() => cleanupImages('samples', 30)}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  清理30天前的图片
                </Button>
                <Button 
                  onClick={() => cleanupImages('samples', 7)}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  清理7天前的图片
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

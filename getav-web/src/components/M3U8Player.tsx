'use client';

import { useEffect, useRef, useState } from 'react';


interface M3U8PlayerProps {
  src: string;
  poster?: string;
  title?: string;
  duration?: number; // 精确时长（秒）
  durationFormatted?: string; // 格式化时长 (HH:MM:SS)
  onClose?: () => void;
}

declare global {
  interface Window {
    Hls: any;
    Plyr: any;
  }
}

export default function M3U8Player({ src, poster, title, duration, durationFormatted, onClose }: M3U8PlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const hlsRef = useRef<any>(null);

  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [scriptsLoaded, setScriptsLoaded] = useState(false);

  // 加载外部脚本
  useEffect(() => {
    const loadScripts = async () => {
      try {
        // 加载HLS.js
        if (!window.Hls) {
          const hlsScript = document.createElement('script');
          hlsScript.src = 'https://cdn.jsdelivr.net/npm/hls.js@latest';
          hlsScript.async = true;
          document.head.appendChild(hlsScript);
          
          await new Promise((resolve, reject) => {
            hlsScript.onload = resolve;
            hlsScript.onerror = reject;
          });
        }

        // 加载Plyr CSS
        if (!document.querySelector('link[href*="plyr"]')) {
          const plyrCSS = document.createElement('link');
          plyrCSS.rel = 'stylesheet';
          plyrCSS.href = 'https://cdn.jsdelivr.net/npm/plyr@3/dist/plyr.css';
          document.head.appendChild(plyrCSS);
        }

        // 加载Plyr JS
        if (!window.Plyr) {
          const plyrScript = document.createElement('script');
          plyrScript.src = 'https://cdn.jsdelivr.net/npm/plyr@3/dist/plyr.min.js';
          plyrScript.async = true;
          document.head.appendChild(plyrScript);
          
          await new Promise((resolve, reject) => {
            plyrScript.onload = resolve;
            plyrScript.onerror = reject;
          });
        }

        setScriptsLoaded(true);
      } catch (error) {
        console.error('加载播放器脚本失败:', error);
        setError('播放器加载失败');
      }
    };

    loadScripts();
  }, []);

  // 初始化播放器
  useEffect(() => {
    if (!scriptsLoaded || !videoRef.current || !src) return;

    const initPlayer = async () => {
      try {
        setError(null);

        const video = videoRef.current!;
        
        // 初始化Plyr播放器
        playerRef.current = new window.Plyr(video, {
          controls: [
            'play-large',
            'play',
            'progress',
            'current-time',
            'duration',
            'mute',
            'volume',
            'settings',
            'fullscreen'
          ],
          settings: ['quality', 'speed'],
          quality: { default: 'auto' },
          speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 2] },
          // 防止布局跳动的配置
          ratio: null, // 不强制宽高比
          fullscreen: {
            enabled: true,
            fallback: true,
            iosNative: false
          },
          // 确保容器尺寸稳定
          autoplay: false,
          muted: false
        });

        // 检查HLS支持
        if (window.Hls.isSupported()) {
          // 创建自定义加载器用于解密
          class FMP4SteganographyLoader extends window.Hls.DefaultConfig.loader {
            constructor(config: any) {
              super(config);
              this.cache = new Map();
              this.retryCount = new Map();
              this.maxCacheSize = 100;
            }
            
            load(context: any, config: any, callbacks: any) {
              const url = context.url;

              // 区分M3U8播放列表和片段文件
              if (url.includes('.m3u8') || context.type === 'manifest') {
                // 播放列表文件，使用默认加载器
                super.load(context, config, callbacks);
                return;
              }

              // 只对TikTok CDN的片段文件进行PNG解码
              if (!url.includes('ibyteimg.com') && !url.includes('tiktok')) {
                super.load(context, config, callbacks);
                return;
              }

              // 检查缓存
              if (this.cache.has(url)) {
                const cachedData = this.cache.get(url);
                const response = {
                  url: url,
                  data: cachedData,
                  code: 200,
                  text: 'OK'
                };
                callbacks.onSuccess(response, {}, context);
                return;
              }

              // 下载PNG伪装文件
              fetch(url, {
                method: 'GET',
                cache: 'default',
                credentials: 'omit'
              })
              .then(response => {
                if (!response.ok) {
                  throw new Error(`HTTP ${response.status}`);
                }
                return response.arrayBuffer();
              })
              .then(arrayBuffer => {
                const data = new Uint8Array(arrayBuffer);
                
                // PNG伪装解码
                const result = this.decodePNGDisguise(data, url);
                
                // 智能缓存管理
                if (this.cache.size >= this.maxCacheSize) {
                  const firstKey = this.cache.keys().next().value;
                  this.cache.delete(firstKey);
                }
                
                this.cache.set(url, result);

                // HLS.js需要特定的响应格式
                const response = {
                  url: url,
                  data: result,
                  code: 200,
                  text: 'OK'
                };

                callbacks.onSuccess(response, {}, context);
              })
              .catch(error => {
                console.error('加载片段失败:', error);
                
                // 重试逻辑
                const retries = this.retryCount.get(url) || 0;
                if (retries < 3) {
                  this.retryCount.set(url, retries + 1);
                  setTimeout(() => {
                    this.load(context, config, callbacks);
                  }, 1000 * (retries + 1));
                } else {
                  callbacks.onError({
                    type: 'network',
                    details: 'fmp4-load-error',
                    fatal: false,
                    url: url,
                    response: { code: 0, text: error.message }
                  }, context);
                }
              });
            }
            
            // 解码PNG伪装的fMP4数据
            decodePNGDisguise(data: Uint8Array, url: string) {
              // 检测PNG头部大小，优先检查70字节（TikTok原版）
              let headerSize = 70; // 默认70字节
              const testSizes = [70, 212, 544894];
              let foundValidHeader = false;

              for (const size of testSizes) {
                if (data.length > size + 100) {
                  const testData = data.slice(size);

                  // 检查fMP4文件头的多种可能性
                  const byte0 = testData[0] ^ 217;
                  const byte4 = testData[4] ^ 217;
                  const byte5 = testData[5] ^ 217;
                  const byte6 = testData[6] ^ 217;
                  const byte7 = testData[7] ^ 217;

                  // fMP4文件头检查 (ftyp box)
                  if (byte0 === 0x00 && byte4 === 0x66 && byte5 === 0x74 && byte6 === 0x79 && byte7 === 0x70) {
                    headerSize = size;
                    foundValidHeader = true;
                    break;
                  }

                  // 也检查其他可能的fMP4头部模式
                  if (testData.length >= 12) {
                    const boxType = String.fromCharCode(byte4, byte5, byte6, byte7);
                    if (boxType === 'ftyp' || boxType === 'styp' || boxType === 'moof') {
                      headerSize = size;
                      foundValidHeader = true;
                      break;
                    }
                  }
                }
              }

              // XOR解密
              const encrypted = data.slice(headerSize);
              const decrypted = new Uint8Array(encrypted.length);
              for (let i = 0; i < encrypted.length; i++) {
                decrypted[i] = encrypted[i] ^ 217;
              }

              return decrypted.buffer;
            }
          }

          hlsRef.current = new window.Hls({
            debug: false,
            loader: FMP4SteganographyLoader,
            enableWorker: true,
            fmp4: true,
            maxBufferLength: 60,
            maxMaxBufferLength: 120,
            maxBufferSize: 60 * 1000 * 1000,
            maxBufferHole: 2.0,
            progressive: true,
            fragLoadingMaxRetry: 5,
            manifestLoadingMaxRetry: 5,
            levelLoadingMaxRetry: 5,
            appendErrorMaxRetry: 5,
            fragLoadingTimeOut: 20000,
            manifestLoadingTimeOut: 10000
          });
          
          hlsRef.current.loadSource(src);
          hlsRef.current.attachMedia(video);
          
          // HLS事件监听
          hlsRef.current.on(window.Hls.Events.MANIFEST_PARSED, () => {
            console.log('M3U8播放列表加载成功');
          });
          
          hlsRef.current.on(window.Hls.Events.ERROR, (event: any, data: any) => {
            console.error('HLS错误:', data);
            if (data.fatal) {
              setError(`播放错误: ${data.details}`);
            }
          });
          
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Safari原生HLS支持
          video.src = src;
        } else {
          setError('浏览器不支持HLS播放');
        }

        // 播放器事件
        playerRef.current.on('play', () => setIsPlaying(true));
        playerRef.current.on('pause', () => setIsPlaying(false));
        
      } catch (error) {
        console.error('初始化播放器失败:', error);
        setError('播放器初始化失败');
      }
    };

    initPlayer();

    // 清理函数
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
    };
  }, [scriptsLoaded, src]);

  if (!src) {
    return (
      <div className="relative w-full h-full bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <p className="text-lg mb-2">暂无播放源</p>
          <p className="text-sm text-gray-400">该影片暂时无法播放</p>
        </div>

      </div>
    );
  }

  return (
    <div className="relative w-full h-full bg-black">
      {/* 内联样式防止Plyr布局跳动 */}
      <style jsx>{`
        .plyr {
          width: 100% !important;
          height: 100% !important;
        }
        .plyr__video-wrapper {
          width: 100% !important;
          height: 100% !important;
        }
        .plyr video {
          width: 100% !important;
          height: 100% !important;
          object-fit: cover !important;
        }
      `}</style>

      {/* 时长信息栏 */}
      {(duration || durationFormatted) && (
        <div className="absolute top-2 left-2 z-30 bg-black/70 text-white px-2 py-1 rounded text-sm">
          {durationFormatted || `${Math.floor((duration || 0) / 60)}:${((duration || 0) % 60).toString().padStart(2, '0')}`}
        </div>
      )}

      {/* 加载状态 */}
      {!scriptsLoaded && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-40">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-sm">加载播放器...</p>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black z-40">
          <div className="text-white text-center">
            <p className="text-lg mb-2">播放失败</p>
            <p className="text-sm text-gray-400">{error}</p>
          </div>
        </div>
      )}

      {/* 视频播放器 */}
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        poster={poster}
        crossOrigin="anonymous"
        playsInline
        webkit-playsinline="true"
        controls={false}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
      >
        <source src={src} type="application/x-mpegURL" />
        您的浏览器不支持视频播放。
      </video>
    </div>
  );
}

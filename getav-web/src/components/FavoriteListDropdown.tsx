'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { Check, Plus, Clock, Bookmark, ChevronDown } from 'lucide-react';
import { FavoriteList } from '@/types/favorite-list';

interface FavoriteListDropdownProps {
  userId: string;
  movieId: string;
  currentListId?: string;
  onListChange?: (listId: string | null) => void;
  onCreateNew?: () => void;
  className?: string;
}

interface FavoriteListOption extends FavoriteList {
  isFavorited: boolean;
}

export default function FavoriteListDropdown({
  userId,
  movieId,
  currentListId,
  onListChange,
  onCreateNew,
  className = ''
}: FavoriteListDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [favoriteLists, setFavoriteLists] = useState<FavoriteListOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const fetchFavoriteLists = useCallback(async () => {
    setLoading(true);
    try {
      // 获取用户收藏列表
      const listsResponse = await fetch(`/api/favorite-lists?userId=${userId}&includeCount=true`);
      const listsData = await listsResponse.json();

      // 获取当前影片状态
      const statusResponse = await fetch(`/api/favorites?movieId=${movieId}&userId=${userId}&includeAllLists=true`);
      const statusData = await statusResponse.json();

      if (listsData.success && statusData.success) {
        const favoriteListIds = new Set(
          statusData.data.favoriteLists?.filter((list: { isFavorited: boolean }) => list.isFavorited).map((list: { id: string }) => list.id) || []
        );

        const combinedLists = listsData.data.map((list: FavoriteList) => ({
          ...list,
          isFavorited: favoriteListIds.has(list.id)
        }));

        setFavoriteLists(combinedLists);
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error);
    } finally {
      setLoading(false);
    }
  }, [userId, movieId]);

  // 获取收藏列表
  useEffect(() => {
    if (isOpen && userId) {
      fetchFavoriteLists();
    }
  }, [isOpen, userId, movieId, fetchFavoriteLists]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleToggleFavorite = async (listId: string, currentlyFavorited: boolean) => {
    setUpdating(listId);
    try {
      const action = currentlyFavorited ? 'remove' : 'add';
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieId,
          userId,
          favoriteListId: listId,
          action
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 更新本地状态
          setFavoriteLists(prev => prev.map(list => 
            list.id === listId 
              ? { ...list, isFavorited: !currentlyFavorited }
              : list
          ));

          // 通知父组件
          onListChange?.(currentlyFavorited ? null : listId);
        }
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
    } finally {
      setUpdating(null);
    }
  };

  const getListIcon = (list: FavoriteListOption) => {
    if (list.isDefault) {
      return <Clock className="w-3 h-3" />;
    }
    return <Bookmark className="w-3 h-3" />;
  };

  const getCurrentListName = () => {
    if (!currentListId) return '选择播放列表';
    const currentList = favoriteLists.find(list => list.id === currentListId);
    return currentList?.name || '选择播放列表';
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* 触发按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 bg-[#272727] text-white rounded-lg hover:bg-[#3f3f3f] transition-colors text-sm"
      >
        <Bookmark className="w-4 h-4" />
        <span className="max-w-32 truncate">{getCurrentListName()}</span>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-64 bg-[#282828] rounded-lg shadow-lg border border-[#3f3f3f] z-50 max-h-80 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-400 text-sm">加载中...</span>
            </div>
          ) : (
            <div className="py-1">
              {favoriteLists.map((list) => (
                <button
                  key={list.id}
                  onClick={() => handleToggleFavorite(list.id, list.isFavorited)}
                  disabled={updating === list.id}
                  className="w-full flex items-center gap-3 px-3 py-2 hover:bg-[#3f3f3f] transition-colors disabled:opacity-50 text-left"
                >
                  {/* 列表图标 */}
                  <div className="text-gray-400">
                    {getListIcon(list)}
                  </div>

                  {/* 列表信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-white text-sm font-medium truncate">{list.name}</span>
                      {list.isDefault && (
                        <span className="text-xs text-gray-400 bg-[#3f3f3f] px-1.5 py-0.5 rounded flex-shrink-0">
                          默认
                        </span>
                      )}
                    </div>
                    <p className="text-gray-500 text-xs">
                      {list.count || 0} 个视频
                    </p>
                  </div>

                  {/* 选中状态 */}
                  <div className="flex items-center">
                    {updating === list.id ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                    ) : (
                      <div className={`w-4 h-4 rounded border flex items-center justify-center transition-colors ${
                        list.isFavorited
                          ? 'bg-blue-600 border-blue-600'
                          : 'border-gray-400'
                      }`}>
                        {list.isFavorited && (
                          <Check className="w-2.5 h-2.5 text-white" />
                        )}
                      </div>
                    )}
                  </div>
                </button>
              ))}

              {/* 分隔线 */}
              {favoriteLists.length > 0 && (
                <div className="border-t border-[#3f3f3f] my-1"></div>
              )}

              {/* 创建新列表按钮 */}
              <button
                onClick={() => {
                  onCreateNew?.();
                  setIsOpen(false);
                }}
                className="w-full flex items-center gap-3 px-3 py-2 hover:bg-[#3f3f3f] transition-colors text-left"
              >
                <div className="w-3 h-3 text-blue-400">
                  <Plus className="w-3 h-3" />
                </div>
                <span className="text-blue-400 text-sm font-medium">创建新的播放列表</span>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

'use client';

import React from 'react';
import Image from 'next/image';
import { Building2, Film } from 'lucide-react';
import type { Studio } from '@/types/javbus';

interface StudioCardProps {
  studio: Studio;
  onClick: (studio: Studio) => void;
}

export default function StudioCard({ studio, onClick }: StudioCardProps) {
  // 获取类型标签
  const getTypeLabels = () => {
    const labels = [];
    if (studio.types.includes('producer')) {
      labels.push({ text: '制作', color: 'bg-gray-600/60 border border-gray-500/30' });
    }
    if (studio.types.includes('publisher')) {
      labels.push({ text: '发行', color: 'bg-gray-700/60 border border-gray-600/30' });
    }
    return labels;
  };

  // 渲染封面网格
  const renderCoverGrid = () => {
    const movies = studio.recentMovies.slice(0, 6);
    
    if (movies.length === 0) {
      return (
        <div className="w-full h-48 bg-[#272727] rounded-lg flex items-center justify-center">
          <Building2 className="w-16 h-16 text-gray-500" />
        </div>
      );
    }

    // 根据影片数量决定布局
    if (movies.length === 1) {
      return (
        <div className="w-full h-48 bg-[#272727] rounded-lg overflow-hidden relative">
          {movies[0].localImg ? (
            <Image
              src={`http://localhost:3001${movies[0].localImg}`}
              alt={movies[0].title}
              fill
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Film className="w-16 h-16 text-gray-500" />
            </div>
          )}
        </div>
      );
    }

    if (movies.length === 2) {
      return (
        <div className="w-full h-48 bg-[#272727] rounded-lg overflow-hidden grid grid-cols-2 gap-0.5">
          {movies.map((movie) => (
            <div key={movie.id} className="bg-[#272727] flex items-center justify-center relative">
              {movie.localImg ? (
                <Image
                  src={`http://localhost:3001${movie.localImg}`}
                  alt={movie.title}
                  fill
                  sizes="(max-width: 768px) 25vw, (max-width: 1200px) 16vw, 12vw"
                  className="object-cover"
                />
              ) : (
                <Film className="w-8 h-8 text-gray-500" />
              )}
            </div>
          ))}
        </div>
      );
    }

    if (movies.length === 3) {
      return (
        <div className="w-full h-48 bg-[#272727] rounded-lg overflow-hidden grid grid-cols-2 grid-rows-2 gap-0.5">
          {/* 第一个占据左侧整个高度 */}
          <div className="row-span-2 bg-[#272727] flex items-center justify-center relative">
            {movies[0].localImg ? (
              <Image
                src={`http://localhost:3001${movies[0].localImg}`}
                alt={movies[0].title}
                fill
                sizes="(max-width: 768px) 25vw, (max-width: 1200px) 16vw, 12vw"
                className="object-cover"
              />
            ) : (
              <Film className="w-8 h-8 text-gray-500" />
            )}
          </div>
          {/* 右侧两个小图 */}
          {movies.slice(1, 3).map((movie) => (
            <div key={movie.id} className="bg-[#272727] flex items-center justify-center relative">
              {movie.localImg ? (
                <Image
                  src={`http://localhost:3001${movie.localImg}`}
                  alt={movie.title}
                  fill
                  sizes="(max-width: 768px) 12vw, (max-width: 1200px) 8vw, 6vw"
                  className="object-cover"
                />
              ) : (
                <Film className="w-6 h-6 text-gray-500" />
              )}
            </div>
          ))}
        </div>
      );
    }

    // 4个或更多影片：2x2网格，最多显示4个
    return (
      <div className="w-full h-48 bg-[#272727] rounded-lg overflow-hidden grid grid-cols-2 grid-rows-2 gap-0.5">
        {movies.slice(0, 4).map((movie) => (
          <div key={movie.id} className="bg-[#272727] flex items-center justify-center relative">
            {movie.localImg ? (
              <Image
                src={`http://localhost:3001${movie.localImg}`}
                alt={movie.title}
                fill
                sizes="(max-width: 768px) 12vw, (max-width: 1200px) 8vw, 6vw"
                className="object-cover"
              />
            ) : (
              <Film className="w-8 h-8 text-gray-500" />
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div
      onClick={() => onClick(studio)}
      className="bg-[#1a1a1a] rounded-lg p-4 hover:bg-[#2a2a2a] transition-all duration-200 cursor-pointer group"
    >
      {/* 封面网格 */}
      <div className="mb-4 group-hover:scale-105 transition-transform duration-200">
        {renderCoverGrid()}
      </div>

      {/* 片商信息 */}
      <div className="space-y-2">
        {/* 片商名称 */}
        <h3 className="text-white font-semibold text-lg group-hover:text-red-400 transition-colors truncate mb-1">
          {studio.name}
        </h3>

        {/* 类型标签 */}
        <div className="flex flex-wrap gap-1">
          {getTypeLabels().map((label, index) => (
            <span
              key={index}
              className={`${label.color} text-gray-300 text-xs px-2 py-0.5 rounded backdrop-blur-sm`}
            >
              {label.text}
            </span>
          ))}
        </div>

        {/* 影片数量 */}
        <p className="text-gray-400 text-xs">
          {studio.movieCount} 部影片
        </p>
      </div>
    </div>
  );
}

'use client';

import { Home as HomeIcon, Film, Star, Clock, TrendingUp, ChevronDown, ChevronRight } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';
import { useSession } from '@/components/SessionProvider';
import { FavoriteList } from '@/types/favorite-list';

interface CategoryStats {
  all: number;
  trending: number;
  latest: number;
  subtitle: number;
  hd: number;
  censored: number;
  uncensored: number;
  studios: number;
  actresses: number;
}

interface SidebarProps {
  sidebarOpen: boolean;
  activeCategory?: string;
  onCategoryClick?: (categoryId: string) => void;
  categoryStats?: CategoryStats | null;
}

export default function Sidebar({
  sidebarOpen,
  activeCategory = 'all',
  onCategoryClick,
  categoryStats
}: SidebarProps) {
  // 用户信息和收藏列表状态
  const { user } = useSession();
  const [favoriteLists, setFavoriteLists] = useState<FavoriteList[]>([]);
  const [favoriteListsLoading, setFavoriteListsLoading] = useState(false);
  const [favoriteListsExpanded, setFavoriteListsExpanded] = useState(false);

  // 分类定义
  const categories = [
    { id: 'all', name: '全部', icon: HomeIcon },
    { id: 'trending', name: '热门', icon: TrendingUp },
    { id: 'latest', name: '最新', icon: Clock },
  ];

  const movieCategories = [
    { id: 'censored', name: '有码', icon: Film },
    { id: 'uncensored', name: '无码', icon: Star },
    { id: 'subtitle', name: '中文字幕', icon: 'subtitle' },
    { id: 'hd', name: '高清', icon: 'hd' },
  ];

  const browseCategories = [
    { id: 'studios', name: '片商', icon: 'studio' },
    { id: 'actresses', name: '女优', icon: 'actress' },
  ];

  // 获取收藏列表
  const fetchFavoriteLists = useCallback(async () => {
    if (!user) return;

    setFavoriteListsLoading(true);
    try {
      const response = await fetch(`/api/favorite-lists?userId=${user.id}&includeCount=true`);
      const result = await response.json();

      if (result.success) {
        setFavoriteLists(result.data);
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error);
    } finally {
      setFavoriteListsLoading(false);
    }
  }, [user]);

  // 当用户状态变化时获取收藏列表
  useEffect(() => {
    if (user && favoriteListsExpanded) {
      fetchFavoriteLists();
    }
  }, [user, favoriteListsExpanded, fetchFavoriteLists]);

  const handleCategoryClick = (categoryId: string) => {
    if (onCategoryClick) {
      onCategoryClick(categoryId);
    } else {
      // 默认行为：跳转到首页并设置分类
      window.location.href = `/?category=${categoryId}`;
    }
  };

  // 处理收藏主菜单点击
  const handleFavoritesClick = () => {
    if (!user) {
      // 未登录用户跳转到登录页
      const shouldLogin = window.confirm('查看收藏需要登录，是否前往登录页面？');
      if (shouldLogin) {
        window.location.href = '/auth/signin';
      }
      return;
    }

    // 已登录用户切换展开状态
    setFavoriteListsExpanded(!favoriteListsExpanded);

    // 如果是展开且还没有数据，则获取数据
    if (!favoriteListsExpanded && favoriteLists.length === 0) {
      fetchFavoriteLists();
    }
  };

  // 处理收藏列表点击
  const handleFavoriteListClick = (listId: string) => {
    handleCategoryClick(`favorite-list-${listId}`);
  };

  return (
    <aside className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-[#0f0f0f] h-full transition-all duration-300 ease-in-out`}>
      <nav className="py-4 h-full overflow-y-auto youtube-scrollbar">
        {/* 主要导航 */}
        <div className="px-4 mb-6">
          <ul className="space-y-1">
            {categories.map((category) => {
              const IconComponent = category.icon;
              const isActive = activeCategory === category.id;
              const count = categoryStats?.[category.id as keyof CategoryStats] || 0;
              return (
                <li key={category.id}>
                  <button
                    onClick={() => handleCategoryClick(category.id)}
                    className={`w-full flex items-center ${sidebarOpen ? 'px-3' : 'px-2 justify-center'} py-2 rounded-xl transition-colors ${
                      isActive
                        ? 'text-white bg-[#272727]'
                        : 'text-gray-300 hover:bg-[#272727] hover:text-white'
                    }`}
                    title={!sidebarOpen ? category.name : ''}
                  >
                    <IconComponent className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} />
                    {sidebarOpen && (
                      <div className="flex items-center justify-between w-full">
                        <span className={`text-sm ${isActive ? 'font-medium' : ''}`}>
                          {category.name}
                        </span>
                        {count > 0 && (
                          <span className="text-xs text-gray-300 bg-[#1a1a1a] border border-[#404040] px-1.5 py-0.5 rounded">
                            {count.toLocaleString()}
                          </span>
                        )}
                      </div>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>

        {/* 分隔线 - YouTube风格，缩短长度 */}
        <div className="mx-4 my-4">
          <div className="border-t border-[#3f3f3f]"></div>
        </div>

        {/* 分类导航 */}
        <div className="px-4 mb-6">
          {sidebarOpen && (
            <h3 className="text-gray-400 text-xs font-semibold uppercase tracking-wider mb-3 px-0">分类</h3>
          )}
          <ul className="space-y-1">
            {movieCategories.map((category) => {
              const isActive = activeCategory === category.id;
              const count = categoryStats?.[category.id as keyof CategoryStats] || 0;
              return (
                <li key={category.id}>
                  <button
                    onClick={() => handleCategoryClick(category.id)}
                    className={`w-full flex items-center ${sidebarOpen ? 'px-3' : 'px-2 justify-center'} py-2 rounded-xl transition-colors ${
                      isActive
                        ? 'text-white bg-[#272727]'
                        : 'text-gray-300 hover:bg-[#272727] hover:text-white'
                    }`}
                    title={!sidebarOpen ? category.name : ''}
                  >
                    {category.icon === Film && (
                      <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                        <rect x="2" y="2" width="4" height="4" />
                        <rect x="8" y="2" width="4" height="4" />
                        <rect x="14" y="2" width="4" height="4" />
                        <rect x="2" y="8" width="4" height="4" />
                        <rect x="8" y="8" width="4" height="4" />
                        <rect x="14" y="8" width="4" height="4" />
                        <rect x="2" y="14" width="4" height="4" />
                        <rect x="8" y="14" width="4" height="4" />
                        <rect x="14" y="14" width="4" height="4" />
                        <rect x="20" y="2" width="2" height="4" />
                        <rect x="20" y="8" width="2" height="4" />
                        <rect x="20" y="14" width="2" height="4" />
                        <rect x="2" y="20" width="4" height="2" />
                        <rect x="8" y="20" width="4" height="2" />
                        <rect x="14" y="20" width="4" height="2" />
                        <rect x="20" y="20" width="2" height="2" />
                      </svg>
                    )}
                    {category.icon === Star && (
                      <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                      </svg>
                    )}
                    {category.icon === 'subtitle' && (
                      <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM4 12h4v2H4v-2zm10 6H4v-2h10v2zm6 0h-4v-2h4v2zm0-4H10v-2h10v2z"/>
                      </svg>
                    )}
                    {category.icon === 'hd' && (
                      <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                        <rect x="2" y="6" width="20" height="12" rx="2" ry="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                        <text x="12" y="14" textAnchor="middle" fontSize="8" fontWeight="bold" fill="currentColor">HD</text>
                      </svg>
                    )}
                    {sidebarOpen && (
                      <div className="flex items-center justify-between w-full">
                        <span className={`text-sm ${isActive ? 'font-medium' : ''}`}>
                          {category.name}
                        </span>
                        {count > 0 && (
                          <span className="text-xs text-gray-300 bg-[#1a1a1a] border border-[#404040] px-1.5 py-0.5 rounded">
                            {count.toLocaleString()}
                          </span>
                        )}
                      </div>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>

        {/* 分隔线 - YouTube风格，缩短长度 */}
        <div className="mx-4 my-4">
          <div className="border-t border-[#3f3f3f]"></div>
        </div>

        {/* 探索导航 */}
        <div className="px-4 mb-6">
          {sidebarOpen && (
            <h3 className="text-gray-400 text-xs font-semibold uppercase tracking-wider mb-3 px-0">探索</h3>
          )}
          <ul className="space-y-1">
            {browseCategories.map((category) => {
              const isActive = activeCategory === category.id;
              const count = categoryStats?.[category.id as keyof CategoryStats] || 0;
              return (
                <li key={category.id}>
                  <button
                    onClick={() => handleCategoryClick(category.id)}
                    className={`w-full flex items-center ${sidebarOpen ? 'px-3' : 'px-2 justify-center'} py-2 rounded-xl transition-colors ${
                      isActive
                        ? 'text-white bg-[#272727]'
                        : 'text-gray-300 hover:bg-[#272727] hover:text-white'
                    }`}
                    title={!sidebarOpen ? category.name : ''}
                  >
                    {category.icon === 'studio' && (
                      <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4zM14 13h-3v3H9v-3H6v-2h3V8h2v3h3v2z"/>
                      </svg>
                    )}
                    {category.icon === 'actress' && (
                      <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    )}
                    {sidebarOpen && (
                      <div className="flex items-center justify-between w-full">
                        <span className={`text-sm ${isActive ? 'font-medium' : ''}`}>
                          {category.name}
                        </span>
                        {count > 0 && (
                          <span className="text-xs text-gray-300 bg-[#1a1a1a] border border-[#404040] px-1.5 py-0.5 rounded">
                            {count.toLocaleString()}
                          </span>
                        )}
                      </div>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>

        {/* 分隔线 - YouTube风格，缩短长度 */}
        <div className="mx-4 my-4">
          <div className="border-t border-[#3f3f3f]"></div>
        </div>

        {/* 个人功能 */}
        <div className="px-4">
          {sidebarOpen && (
            <h3 className="text-gray-400 text-xs font-semibold uppercase tracking-wider mb-3 px-0">我</h3>
          )}
          <ul className="space-y-1">
            <li>
              {/* 收藏主菜单 */}
              <button
                onClick={handleFavoritesClick}
                className={`w-full flex items-center ${sidebarOpen ? 'px-3' : 'px-2 justify-center'} py-2 rounded-xl transition-colors ${
                  activeCategory === 'favorites' || activeCategory?.startsWith('favorite-list-')
                    ? 'text-white bg-[#272727]'
                    : 'text-gray-300 hover:bg-[#272727] hover:text-white'
                }`}
                title={!sidebarOpen ? '收藏' : ''}
              >
                <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-3' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
                </svg>
                {sidebarOpen && (
                  <>
                    <span className={`text-sm flex-1 text-left ${activeCategory === 'favorites' || activeCategory?.startsWith('favorite-list-') ? 'font-medium' : ''}`}>
                      收藏
                    </span>
                    {user && (
                      favoriteListsExpanded ?
                        <ChevronDown className="w-4 h-4" /> :
                        <ChevronRight className="w-4 h-4" />
                    )}
                  </>
                )}
              </button>

              {/* 收藏列表子菜单 */}
              {sidebarOpen && user && favoriteListsExpanded && (
                <div className="mt-1 ml-6">
                  {favoriteListsLoading ? (
                    <div className="flex items-center px-3 py-2 text-gray-400 text-sm">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400 mr-2"></div>
                      加载中...
                    </div>
                  ) : favoriteLists.length === 0 ? (
                    <div className="px-3 py-2 text-gray-400 text-sm">
                      暂无收藏列表
                    </div>
                  ) : (
                    <ul className="space-y-1">
                      {favoriteLists.map((list) => (
                        <li key={list.id}>
                          <button
                            onClick={() => handleFavoriteListClick(list.id)}
                            className={`w-full flex items-center px-3 py-1.5 rounded-lg transition-colors text-left ${
                              activeCategory === `favorite-list-${list.id}`
                                ? 'text-white bg-[#3f3f3f]'
                                : 'text-gray-300 hover:bg-[#3f3f3f] hover:text-white'
                            }`}
                          >
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                              <span className="text-gray-400 text-xs">
                                {list.isDefault ? '⏰' : '📁'}
                              </span>
                              <span className="text-xs truncate flex-1">
                                {list.name}
                              </span>
                              <span className="text-xs text-gray-500 flex-shrink-0">
                                {list.count || 0}
                              </span>
                            </div>
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </li>
            <li>
              <button
                onClick={() => handleCategoryClick('likes')}
                className={`w-full flex items-center ${sidebarOpen ? 'px-3' : 'px-2 justify-center'} py-2 rounded-xl transition-colors ${
                  activeCategory === 'likes'
                    ? 'text-white bg-[#272727]'
                    : 'text-gray-300 hover:bg-[#272727] hover:text-white'
                }`}
                title={!sidebarOpen ? '赞过的视频' : ''}
              >
                <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M1 21h4V9H1v12zm22-11c0-1.1-.9-2-2-2h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 1 7.59 7.59C7.22 7.95 7 8.45 7 9v10c0 1.1.9 2 2 2h9c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73v-2z"/>
                </svg>
                {sidebarOpen && (
                  <span className={`text-sm ${activeCategory === 'likes' ? 'font-medium' : ''}`}>
                    赞过的视频
                  </span>
                )}
              </button>
            </li>
            <li>
              <button
                onClick={() => handleCategoryClick('history')}
                className={`w-full flex items-center ${sidebarOpen ? 'px-3' : 'px-2 justify-center'} py-2 rounded-xl transition-colors ${
                  activeCategory === 'history'
                    ? 'text-white bg-[#272727]'
                    : 'text-gray-300 hover:bg-[#272727] hover:text-white'
                }`}
                title={!sidebarOpen ? '历史记录' : ''}
              >
                <svg className={`w-6 h-6 ${sidebarOpen ? 'mr-6' : ''}`} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                </svg>
                {sidebarOpen && (
                  <span className={`text-sm ${activeCategory === 'history' ? 'font-medium' : ''}`}>
                    历史记录
                  </span>
                )}
              </button>
            </li>
          </ul>
        </div>
      </nav>
    </aside>
  );
}

import { useState, useEffect, useCallback, useRef } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  loading: boolean;
}

interface UseRequestCacheOptions {
  cacheTime?: number; // 缓存时间，默认5分钟
  staleTime?: number; // 数据过期时间，默认1分钟
  retryOnError?: boolean; // 错误时是否重试
  retryDelay?: number; // 重试延迟
}

interface UseRequestCacheReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  clearCache: () => void;
  isStale: boolean;
}

/**
 * 请求缓存Hook
 * 提供自动缓存、去重、重试等功能
 */
export function useRequestCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseRequestCacheOptions = {}
): UseRequestCacheReturn<T> {
  const {
    cacheTime = 5 * 60 * 1000, // 5分钟
    staleTime = 1 * 60 * 1000,  // 1分钟
    retryOnError = true,
    retryDelay = 1000
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isStale, setIsStale] = useState(false);

  // 缓存存储
  const cacheRef = useRef<Map<string, CacheEntry<T>>>(new Map());
  const requestsRef = useRef<Map<string, Promise<T>>>(new Map());
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * 检查数据是否过期
   */
  const isExpired = useCallback((timestamp: number, maxAge: number): boolean => {
    return Date.now() - timestamp > maxAge;
  }, []);

  /**
   * 从缓存获取数据
   */
  const getCachedData = useCallback((): CacheEntry<T> | null => {
    const cached = cacheRef.current.get(key);
    if (!cached) return null;

    // 检查是否过期
    if (isExpired(cached.timestamp, cacheTime)) {
      cacheRef.current.delete(key);
      return null;
    }

    return cached;
  }, [key, cacheTime, isExpired]);

  /**
   * 设置缓存数据
   */
  const setCachedData = useCallback((newData: T, isLoading = false): void => {
    cacheRef.current.set(key, {
      data: newData,
      timestamp: Date.now(),
      loading: isLoading
    });
  }, [key]);

  /**
   * 执行请求
   */
  const executeRequest = useCallback(async (forceRefresh = false): Promise<void> => {
    // 检查缓存
    if (!forceRefresh) {
      const cached = getCachedData();
      if (cached && !cached.loading) {
        setData(cached.data);
        setLoading(false);
        setError(null);
        
        // 检查是否过期
        const stale = isExpired(cached.timestamp, staleTime);
        setIsStale(stale);
        
        // 如果数据过期但仍在缓存时间内，后台刷新
        if (stale) {
          executeRequest(true);
        }
        return;
      }
    }

    // 检查是否有正在进行的请求
    const existingRequest = requestsRef.current.get(key);
    if (existingRequest && !forceRefresh) {
      try {
        const result = await existingRequest;
        setData(result);
        setLoading(false);
        setError(null);
        setIsStale(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : '请求失败');
        setLoading(false);
      }
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 创建新请求
      const requestPromise = fetcher();
      requestsRef.current.set(key, requestPromise);

      const result = await requestPromise;
      
      // 更新状态
      setData(result);
      setLoading(false);
      setError(null);
      setIsStale(false);

      // 缓存结果
      setCachedData(result);

      // 清除请求记录
      requestsRef.current.delete(key);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '请求失败';
      setError(errorMessage);
      setLoading(false);

      // 清除请求记录
      requestsRef.current.delete(key);

      // 错误重试
      if (retryOnError && retryTimeoutRef.current === null) {
        retryTimeoutRef.current = setTimeout(() => {
          retryTimeoutRef.current = null;
          executeRequest(forceRefresh);
        }, retryDelay);
      }
    }
  }, [key, fetcher, getCachedData, setCachedData, isExpired, staleTime, retryOnError, retryDelay]);

  /**
   * 手动刷新数据
   */
  const refetch = useCallback(async (): Promise<void> => {
    await executeRequest(true);
  }, [executeRequest]);

  /**
   * 清除缓存
   */
  const clearCache = useCallback((): void => {
    cacheRef.current.delete(key);
    requestsRef.current.delete(key);
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, [key]);

  // 初始化时执行请求
  useEffect(() => {
    executeRequest();
  }, [executeRequest]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    refetch,
    clearCache,
    isStale
  };
}

/**
 * 全局缓存清理函数
 */
export function clearAllRequestCache(): void {
  // 这里可以实现全局缓存清理逻辑
  console.log('清理所有请求缓存');
}

/**
 * 预加载数据Hook
 */
export function usePrefetch<T>(
  key: string,
  fetcher: () => Promise<T>,
  condition = true
): void {
  const { refetch } = useRequestCache(key, fetcher, {
    cacheTime: 10 * 60 * 1000, // 预加载数据缓存10分钟
    staleTime: 5 * 60 * 1000   // 5分钟后过期
  });

  useEffect(() => {
    if (condition) {
      refetch();
    }
  }, [condition, refetch]);
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import { FavoriteList } from '@/types/favorite-list';
import {
  getUserFavoriteListsWithDefault,
  getMovieFavoriteStatus
} from '@/lib/favorite-list-utils';

interface UseFavoriteListsOptions {
  userId?: string;
  movieId?: string;
  autoFetch?: boolean;
}

interface FavoriteListWithStatus extends FavoriteList {
  isFavorited?: boolean;
}

export function useFavoriteLists({ 
  userId, 
  movieId, 
  autoFetch = true 
}: UseFavoriteListsOptions = {}) {
  const [favoriteLists, setFavoriteLists] = useState<FavoriteListWithStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户收藏列表（自动确保有默认列表）
  const fetchFavoriteLists = useCallback(async (includeStatus = false) => {
    if (!userId) return;

    setLoading(true);
    setError(null);

    try {
      // 使用工具函数获取收藏列表，自动创建默认列表
      let lists = await getUserFavoriteListsWithDefault(userId, true);

      // 如果需要包含状态且提供了movieId，获取影片在各列表中的状态
      if (includeStatus && movieId) {
        const statusData = await getMovieFavoriteStatus(userId, movieId);

        if (statusData.favoriteLists && statusData.favoriteLists.length > 0) {
          const favoriteStatusMap = new Map(
            statusData.favoriteLists.map((item: { id: string; isFavorited: boolean }) => [item.id, item.isFavorited])
          );

          lists = lists.map((list: FavoriteList) => ({
            ...list,
            isFavorited: favoriteStatusMap.get(list.id) || false
          }));
        }
      }

      setFavoriteLists(lists);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取收藏列表失败';
      setError(errorMessage);
      console.error('获取收藏列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, [userId, movieId]);

  // 创建新收藏列表
  const createFavoriteList = useCallback(async (data: {
    name: string;
    description?: string;
    isPublic?: boolean;
  }) => {
    if (!userId) throw new Error('用户未登录');

    const response = await fetch('/api/favorite-lists', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        userId
      }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || '创建收藏列表失败');
    }

    // 更新本地状态
    setFavoriteLists(prev => [...prev, result.data]);

    return result.data;
  }, [userId]);

  // 更新收藏列表
  const updateFavoriteList = useCallback(async (listId: string, data: {
    name?: string;
    description?: string;
    isPublic?: boolean;
  }) => {
    if (!userId) throw new Error('用户未登录');

    const response = await fetch(`/api/favorite-lists/${listId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        userId
      }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || '更新收藏列表失败');
    }

    // 更新本地状态
    setFavoriteLists(prev => prev.map(list => 
      list.id === listId ? { ...list, ...result.data } : list
    ));

    return result.data;
  }, [userId]);

  // 删除收藏列表
  const deleteFavoriteList = useCallback(async (listId: string) => {
    if (!userId) throw new Error('用户未登录');

    const response = await fetch(`/api/favorite-lists/${listId}?userId=${userId}`, {
      method: 'DELETE',
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || '删除收藏列表失败');
    }

    // 更新本地状态
    setFavoriteLists(prev => prev.filter(list => list.id !== listId));

    return result.data;
  }, [userId]);

  // 切换影片在收藏列表中的状态
  const toggleMovieInList = useCallback(async (listId: string, movieId: string, action?: 'add' | 'remove') => {
    if (!userId) throw new Error('用户未登录');

    const response = await fetch('/api/favorites', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieId,
        userId,
        favoriteListId: listId,
        action: action || 'toggle'
      }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || '收藏操作失败');
    }

    // 更新本地状态
    setFavoriteLists(prev => prev.map(list => {
      if (list.id === listId && 'isFavorited' in list) {
        return {
          ...list,
          isFavorited: result.data.action === 'favorited'
        };
      }
      return list;
    }));

    return result.data;
  }, [userId]);

  // 初始化用户默认收藏列表
  const initializeDefaultList = useCallback(async () => {
    if (!userId) throw new Error('用户未登录');

    const response = await fetch('/api/favorite-lists/init', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || '初始化默认列表失败');
    }

    // 刷新列表
    await fetchFavoriteLists();

    return result.data;
  }, [userId, fetchFavoriteLists]);

  // 自动获取数据
  useEffect(() => {
    if (autoFetch && userId) {
      fetchFavoriteLists(!!movieId);
    }
  }, [autoFetch, userId, movieId, fetchFavoriteLists]);

  return {
    favoriteLists,
    loading,
    error,
    fetchFavoriteLists,
    createFavoriteList,
    updateFavoriteList,
    deleteFavoriteList,
    toggleMovieInList,
    initializeDefaultList,
    refetch: () => fetchFavoriteLists(!!movieId)
  };
}

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from '@/components/SessionProvider';

interface BatchFavoritesState {
  favorites: Record<string, boolean>;
  loading: boolean;
  error: string | null;
}

interface UseBatchFavoritesReturn {
  favorites: Record<string, boolean>;
  loading: boolean;
  error: string | null;
  fetchFavorites: (movieIds: string[]) => void;
  updateFavorite: (movieId: string, isFavorited: boolean) => void;
  isFavorited: (movieId: string) => boolean;
  clearCache: () => void;
}

/**
 * 批量收藏状态管理Hook
 * 减少重复的API请求，提供缓存和批量查询功能
 */
export function useBatchFavorites(): UseBatchFavoritesReturn {
  const { user } = useSession();

  const [state, setState] = useState<BatchFavoritesState>({
    favorites: {},
    loading: false,
    error: null
  });

  // 缓存已请求的影片ID，避免重复请求
  const requestedMovieIds = useRef<Set<string>>(new Set());
  
  // 请求队列，用于批量处理
  const requestQueue = useRef<string[]>([]);
  const requestTimer = useRef<NodeJS.Timeout | null>(null);

  /**
   * 批量获取收藏状态
   */
  const fetchFavorites = useCallback(async (movieIds: string[]) => {
    if (!user || movieIds.length === 0) {
      // 如果没有用户或没有影片ID，设置为未收藏
      setState(prev => ({
        ...prev,
        favorites: {
          ...prev.favorites,
          ...movieIds.reduce((acc, id) => ({ ...acc, [id]: false }), {})
        }
      }));
      return;
    }

    // 过滤出未请求过的影片ID
    const newMovieIds = movieIds.filter(id => !requestedMovieIds.current.has(id));
    
    if (newMovieIds.length === 0) {
      return; // 所有影片都已请求过
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/favorites/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieIds: newMovieIds,
          userId: user.id,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // 标记这些影片ID已请求过
        newMovieIds.forEach(id => requestedMovieIds.current.add(id));

        setState(prev => ({
          ...prev,
          favorites: {
            ...prev.favorites,
            ...result.data
          },
          loading: false
        }));
      } else {
        throw new Error(result.error || '获取收藏状态失败');
      }
    } catch (error) {
      console.error('批量获取收藏状态失败:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '获取收藏状态失败',
        loading: false,
        // 设置默认值为未收藏
        favorites: {
          ...prev.favorites,
          ...newMovieIds.reduce((acc, id) => ({ ...acc, [id]: false }), {})
        }
      }));
    }
  }, [user]);

  /**
   * 延迟批量请求
   * 将多个请求合并为一个批量请求
   */
  const deferredFetchFavorites = useCallback((movieIds: string[]) => {
    // 添加到请求队列
    requestQueue.current.push(...movieIds);

    // 清除之前的定时器
    if (requestTimer.current) {
      clearTimeout(requestTimer.current);
    }

    // 设置新的定时器，100ms后执行批量请求
    requestTimer.current = setTimeout(() => {
      const uniqueMovieIds = Array.from(new Set(requestQueue.current));
      requestQueue.current = [];
      
      if (uniqueMovieIds.length > 0) {
        fetchFavorites(uniqueMovieIds);
      }
    }, 100);
  }, [fetchFavorites]);

  /**
   * 更新单个影片的收藏状态
   */
  const updateFavorite = useCallback((movieId: string, isFavorited: boolean) => {
    setState(prev => ({
      ...prev,
      favorites: {
        ...prev.favorites,
        [movieId]: isFavorited
      }
    }));
    
    // 标记为已请求过
    requestedMovieIds.current.add(movieId);
  }, []);

  /**
   * 检查影片是否已收藏
   */
  const isFavorited = useCallback((movieId: string): boolean => {
    return state.favorites[movieId] || false;
  }, [state.favorites]);

  /**
   * 清除缓存
   */
  const clearCache = useCallback(() => {
    setState({
      favorites: {},
      loading: false,
      error: null
    });
    requestedMovieIds.current.clear();
    requestQueue.current = [];
    
    if (requestTimer.current) {
      clearTimeout(requestTimer.current);
      requestTimer.current = null;
    }
  }, []);

  // 用户变化时清除缓存
  useEffect(() => {
    clearCache();
  }, [user?.id, clearCache]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (requestTimer.current) {
        clearTimeout(requestTimer.current);
      }
    };
  }, []);

  return {
    favorites: state.favorites,
    loading: state.loading,
    error: state.error,
    fetchFavorites: deferredFetchFavorites,
    updateFavorite,
    isFavorited,
    clearCache
  };
}

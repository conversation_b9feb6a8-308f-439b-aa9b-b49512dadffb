'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import OptimizedImage from '@/components/OptimizedImage';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import {
  Search,
  Star as StarIcon,
  Play,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface Star {
  id: string;
  name: string;
  avatar?: string;
  localAvatar?: string;
  birthday?: string;
  age?: string;
  height?: string;
  bust?: string;
  waist?: string;
  hip?: string;
  birthplace?: string;
  hobby?: string;
  cupSize?: string;
  measurements?: string;
  description?: string;
  movieCount: number;
}

// interface StarsResponse {
//   stars: Star[];
//   pagination: {
//     page: number;
//     limit: number;
//     total: number;
//     totalPages: number;
//   };
//   sort: string;
//   search: string;
// }

export default function StarsPage() {
  const [stars, setStars] = useState<Star[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('popular');

  const [currentPage, setCurrentPage] = useState(1);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 24,
    total: 0,
    totalPages: 0
  });

  // 加载演员列表
  const fetchStars = async (page: number = 1, search: string = '', sort: string = 'popular') => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '24',
        sort,
        ...(search && { search })
      });

      const response = await fetch(`/api/stars?${params.toString()}`);
      const data = await response.json();
      
      if (data.success) {
        setStars(data.data.stars);
        setPagination(data.data.pagination);
      } else {
        setError(data.error || '获取演员列表失败');
      }
    } catch (err) {
      setError('网络请求失败');
      console.error('获取演员列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchStars(1, '', 'popular');
  }, []);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchStars(1, searchQuery, sortBy);
  };

  // 处理排序变化
  const handleSortChange = (newSort: string) => {
    setSortBy(newSort);
    setCurrentPage(1);
    fetchStars(1, searchQuery, newSort);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchStars(page, searchQuery, sortBy);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* 页面标题和搜索 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-4">演员</h1>

          {/* 搜索框和排序 */}
          <div className="flex items-center gap-4 mb-4">
            <form onSubmit={handleSearch} className="flex items-center">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索演员"
                  className="bg-[#121212] text-white px-4 py-2 pl-10 rounded-full border border-gray-700 focus:border-blue-500 focus:outline-none w-64 text-sm"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </form>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
              className="bg-[#272727] text-white px-3 py-2 rounded-lg border-none focus:outline-none text-sm"
            >
              <option value="popular">最受欢迎</option>
              <option value="name">姓名排序</option>
              <option value="latest">最新加入</option>
              <option value="movies">作品数量</option>
            </select>
          </div>

          {/* 统计信息 */}
          <div className="text-gray-400 text-sm">
            {pagination.total} 位演员
            {searchQuery && (
              <span className="ml-2">
                · 搜索 &ldquo;{searchQuery}&rdquo;
              </span>
            )}
          </div>
        </div>

        {/* 演员列表 */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-white">加载中...</div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-red-400">{error}</div>
          </div>
        ) : stars.length > 0 ? (
          <>
            <div className="grid gap-4 grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8">
              {stars.map((star) => (
                <Link
                  key={star.id}
                  href={`/stars/${star.id}`}
                  className="group transition-all duration-500 ease-out block text-center hover:-translate-y-1 hover:scale-105"
                >
                  {/* 迷你圆形头像 */}
                  <div className="w-20 h-20 mx-auto mb-2 relative">
                    <div className="w-full h-full rounded-full overflow-hidden bg-[#2a2a2a] border-2 border-transparent group-hover:border-white/20 group-hover:shadow-lg group-hover:shadow-black/25 transition-all duration-500">
                      {star.localAvatar ? (
                        <OptimizedImage
                          src={star.localAvatar}
                          alt={star.name}
                          width={80}
                          height={80}
                          className="w-full h-full object-cover group-hover:scale-105 group-hover:brightness-110 transition-all duration-500"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <StarIcon className="w-8 h-8 text-gray-500 group-hover:text-gray-300 transition-colors duration-500" />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 演员信息 */}
                  <div className="px-1">
                    <h3 className="text-gray-300 group-hover:text-gray-100 font-medium mb-1 text-xs leading-tight transition-colors duration-500 line-clamp-2">{star.name}</h3>
                    <div className="text-gray-400 group-hover:text-gray-300 text-xs flex items-center justify-center gap-1 transition-colors duration-500">
                      <Play className="w-3 h-3" />
                      {star.movieCount}
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* 分页 */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-center mt-8 gap-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                  className="flex items-center gap-1 px-4 py-2 bg-[#272727] text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#3a3a3a] transition-colors text-sm"
                >
                  <ChevronLeft className="w-4 h-4" />
                  上一页
                </button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-3 py-2 rounded-lg transition-colors text-sm ${
                          currentPage === page
                            ? 'bg-white text-black'
                            : 'bg-[#272727] text-gray-300 hover:bg-[#3a3a3a]'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= pagination.totalPages}
                  className="flex items-center gap-1 px-4 py-2 bg-[#272727] text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#3a3a3a] transition-colors text-sm"
                >
                  下一页
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              {searchQuery ? '没有找到匹配的演员' : '暂无演员数据'}
            </div>
            {searchQuery && (
              <button
                onClick={() => {
                  setSearchQuery('');
                  setCurrentPage(1);
                  fetchStars(1, '', sortBy);
                }}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                清除搜索条件
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

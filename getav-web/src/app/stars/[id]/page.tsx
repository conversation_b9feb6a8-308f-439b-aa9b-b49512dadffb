'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import OptimizedImage from '@/components/OptimizedImage';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import MovieCard from '@/components/MovieCard';
import {
  Calendar,
  Heart,
  Play,
  ChevronLeft,
  Star as StarIcon
} from 'lucide-react';

interface StarDetail {
  id: string;
  name: string;
  avatar?: string;
  localAvatar?: string;
  birthday?: string;
  age?: string;
  height?: string;
  bust?: string;
  waist?: string;
  hip?: string;
  birthplace?: string;
  hobby?: string;
  cupSize?: string;
  measurements?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  stats: {
    totalMovies: number;
    latestMovieDate?: string;
    topGenres: Array<{
      id: string;
      name: string;
      count: number;
    }>;
  };
  movies: Array<{
    id: string;
    title: string;
    localImg?: string | null;
    img?: string | null;
    date?: string | null;
    videoLength?: number | null;
    description?: string | null;
    createdAt: Date;
    updatedAt: Date;
    director?: { id: string; name: string } | null;
    producer?: { id: string; name: string } | null;
    publisher?: { id: string; name: string } | null;
    series?: { id: string; name: string } | null;
    genres: Array<{ id: string; name: string }>;
    samples: Array<{ id: string; alt?: string | null; localSrc?: string | null }>;
    magnets: Array<{
      id: string;
      link: string;
      isHD?: boolean | null;
      title?: string | null;
      size?: string | null;
      hasSubtitle?: boolean | null;
    }>;
    stars: Array<{ id: string; name: string }>;
  }>;
}

export default function StarDetailPage() {
  const params = useParams();
  const starId = params?.id as string;

  const [star, setStar] = useState<StarDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState('latest');
  const [isFollowing, setIsFollowing] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // 加载演员详情
  useEffect(() => {
    const fetchStarDetail = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/stars/${starId}`);
        const data = await response.json();
        
        if (data.success) {
          setStar(data.data);
        } else {
          setError(data.error || '获取演员信息失败');
        }
      } catch (err) {
        setError('网络请求失败');
        console.error('获取演员详情失败:', err);
      } finally {
        setLoading(false);
      }
    };

    if (starId) {
      fetchStarDetail();
    }
  }, [starId]);

  // 处理关注/取消关注
  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    // TODO: 实现关注功能的API调用
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-white">加载中...</div>
      </div>
    );
  }

  if (error || !star) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 mb-4">{error || '演员不存在'}</div>
          <Link 
            href="/stars" 
            className="text-blue-400 hover:text-blue-300 transition-colors"
          >
            返回演员列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        rightContent={
          <div className="flex items-center space-x-2">
            <Link
              href="/stars"
              className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors text-sm px-3 py-2 rounded-lg hover:bg-[#272727]"
            >
              <ChevronLeft className="w-4 h-4" />
              演员列表
            </Link>
          </div>
        }
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* 演员信息 - 简洁设计 */}
        <div className="mb-8">
          <div className="flex items-start gap-6">
            {/* 头像 - 迷你圆形 */}
            <div className="flex-shrink-0">
              <div className="w-24 h-24 rounded-full overflow-hidden bg-[#2a2a2a]">
                {star.localAvatar ? (
                  <OptimizedImage
                    src={star.localAvatar}
                    alt={star.name}
                    width={96}
                    height={96}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <StarIcon className="w-8 h-8 text-gray-500" />
                  </div>
                )}
              </div>
            </div>

            {/* 基本信息 - 简洁设计 */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-white mb-3">
                    {star.name}
                  </h1>
                  <div className="flex items-center gap-4 text-gray-400 text-sm">
                    <span className="flex items-center gap-1">
                      <Play className="w-4 h-4" />
                      {star.stats.totalMovies} 部作品
                    </span>
                    {star.stats.latestMovieDate && (
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        最新: {star.stats.latestMovieDate}
                      </span>
                    )}
                  </div>
                </div>

                <button
                  onClick={handleFollow}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors text-sm ${
                    isFollowing
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                >
                  <Heart className={`w-4 h-4 ${isFollowing ? 'fill-current' : ''}`} />
                  {isFollowing ? '已关注' : '关注'}
                </button>
              </div>

              {/* 详细信息 - 简洁列表 */}
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-6 text-sm">
                {star.birthday && (
                  <div>
                    <div className="text-gray-500 mb-1">生日</div>
                    <div className="text-white">{star.birthday}</div>
                  </div>
                )}
                {star.age && (
                  <div>
                    <div className="text-gray-500 mb-1">年龄</div>
                    <div className="text-white">{star.age}</div>
                  </div>
                )}
                {star.height && (
                  <div>
                    <div className="text-gray-500 mb-1">身高</div>
                    <div className="text-white">{star.height}</div>
                  </div>
                )}
                {star.cupSize && (
                  <div>
                    <div className="text-gray-500 mb-1">罩杯</div>
                    <div className="text-white">{star.cupSize}</div>
                  </div>
                )}
                {star.measurements && (
                  <div>
                    <div className="text-gray-500 mb-1">三围</div>
                    <div className="text-white">{star.measurements}</div>
                  </div>
                )}
                {star.birthplace && (
                  <div>
                    <div className="text-gray-500 mb-1">出生地</div>
                    <div className="text-white">{star.birthplace}</div>
                  </div>
                )}
              </div>

              {/* 爱好 */}
              {star.hobby && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-white mb-2">爱好</h3>
                  <p className="text-gray-400 text-sm leading-relaxed">{star.hobby}</p>
                </div>
              )}

              {/* 热门分类 */}
              {star.stats.topGenres.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-white mb-3">热门分类</h3>
                  <div className="flex flex-wrap gap-2">
                    {star.stats.topGenres.slice(0, 8).map((genre) => (
                      <Link
                        key={genre.id}
                        href={`/genres/${genre.id}`}
                        className="px-3 py-1 bg-[#333] text-gray-300 rounded text-sm hover:bg-[#444] transition-colors"
                      >
                        #{genre.name} ({genre.count})
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 作品列表 - 简洁设计 */}
        <div>
          {/* 工具栏 */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-medium text-white">
              作品集合 <span className="text-gray-500 font-normal">({star.stats.totalMovies})</span>
            </h2>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-[#2a2a2a] text-white px-3 py-2 rounded text-sm border-none focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="latest">最新发布</option>
              <option value="oldest">最早发布</option>
              <option value="title">标题排序</option>
              <option value="date">发行日期</option>
            </select>
          </div>

          {/* 影片网格 - 使用MovieCard组件保持与首页一致 */}
          {star.movies.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {star.movies.map((movie) => (
                <MovieCard
                  key={movie.id}
                  movie={{
                    id: movie.id,
                    title: movie.title,
                    localImg: movie.localImg,
                    img: movie.img,
                    date: movie.date,
                    videoLength: movie.videoLength,
                    description: movie.description,
                    createdAt: movie.createdAt,
                    updatedAt: movie.updatedAt,
                    director: movie.director,
                    producer: movie.producer,
                    publisher: movie.publisher,
                    series: movie.series,
                    genres: movie.genres,
                    samples: movie.samples,
                    magnets: movie.magnets,
                    stars: [{ id: star.id, name: star.name }] // 当前演员信息
                  }}
                  viewMode="grid"
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Play className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <div className="text-gray-400 mb-1">暂无作品</div>
              <div className="text-gray-600 text-sm">该演员还没有上传任何作品</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

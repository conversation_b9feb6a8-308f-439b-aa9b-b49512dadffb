'use client';

import { useState, useEffect, Suspense, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import SearchBar from '@/components/SearchBar';
import MovieCard from '@/components/MovieCard';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import { Button } from '@/components/ui/button';
import {
  Search as SearchIcon,
  Grid,
  List,
  Filter,
  SlidersHorizontal,
  Calendar,
  User,
  Tag,
  ArrowUpDown,
  ChevronDown
} from 'lucide-react';
import type { LocalMovie } from '@/types/javbus';

interface SearchFilters {
  category?: string;
  star?: string;
  dateFrom?: string;
  dateTo?: string;
  videoLength?: string;
}

interface SearchResult {
  movies: LocalMovie[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  query: string;
  category?: string;
  star?: string;
  suggestions?: Array<{
    text: string;
    type: 'star' | 'genre' | 'movie';
    count: number;
  }>;
}

function SearchContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<SearchResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'relevance' | 'date' | 'title'>('relevance');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);

  // 搜索筛选条件
  const [filters, setFilters] = useState<SearchFilters>({
    category: '',
    star: '',
    dateFrom: '',
    dateTo: '',
    videoLength: ''
  });

  // 从URL参数获取初始搜索条件
  const initialQuery = searchParams?.get('q') || '';
  const initialCategory = searchParams?.get('category') || '';
  const initialStar = searchParams?.get('star') || '';

  const handleSearch = useCallback(async (
    query: string,
    searchFilters: SearchFilters,
    page: number = 1,
    sort: string = sortBy,
    order: string = sortOrder
  ) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (query) params.append('q', query);
      if (searchFilters.category) params.append('category', searchFilters.category);
      if (searchFilters.star) params.append('star', searchFilters.star);
      if (searchFilters.dateFrom) params.append('dateFrom', searchFilters.dateFrom);
      if (searchFilters.dateTo) params.append('dateTo', searchFilters.dateTo);
      if (searchFilters.videoLength) params.append('videoLength', searchFilters.videoLength);
      params.append('page', page.toString());
      params.append('limit', '20');
      params.append('sortBy', sort);
      params.append('sortOrder', order);

      // 更新URL参数
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('q', query);
      if (searchFilters.category) newUrl.searchParams.set('category', searchFilters.category);
      if (searchFilters.star) newUrl.searchParams.set('star', searchFilters.star);
      router.replace(newUrl.pathname + newUrl.search);

      const response = await fetch(`/api/search?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setSearchResult(data.data);
        setCurrentPage(page);
      } else {
        // 处理错误对象或字符串
        const errorMessage = typeof data.error === 'object' && data.error?.message
          ? data.error.message
          : typeof data.error === 'string'
            ? data.error
            : data.message || '搜索失败';
        setError(errorMessage);
      }
    } catch (err) {
      setError('搜索请求失败');
      console.error('搜索错误:', err);
    } finally {
      setLoading(false);
    }
  }, [router, sortBy, sortOrder]);

  useEffect(() => {
    // 如果有URL参数，自动执行搜索
    if (initialQuery || initialCategory || initialStar) {
      const initialFilters = {
        category: initialCategory,
        star: initialStar,
        dateFrom: '',
        dateTo: '',
        videoLength: ''
      };
      setFilters(initialFilters);
      handleSearch(initialQuery, initialFilters);
    }
  }, [initialQuery, initialCategory, initialStar, handleSearch]);

  // 处理筛选条件变化
  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  // 应用筛选条件
  const applyFilters = () => {
    setCurrentPage(1);
    handleSearch(searchResult?.query || '', filters, 1, sortBy, sortOrder);
    setShowFilters(false);
  };

  // 清除筛选条件
  const clearFilters = () => {
    const emptyFilters = {
      category: '',
      star: '',
      dateFrom: '',
      dateTo: '',
      videoLength: ''
    };
    setFilters(emptyFilters);
    setCurrentPage(1);
    handleSearch(searchResult?.query || '', emptyFilters, 1, sortBy, sortOrder);
  };

  // 处理排序变化
  const handleSortChange = (newSort: 'relevance' | 'date' | 'title') => {
    setSortBy(newSort);
    setCurrentPage(1);
    handleSearch(searchResult?.query || '', filters, 1, newSort, sortOrder);
  };

  // 处理排序顺序变化
  const handleSortOrderChange = () => {
    const newOrder = sortOrder === 'desc' ? 'asc' : 'desc';
    setSortOrder(newOrder);
    setCurrentPage(1);
    handleSearch(searchResult?.query || '', filters, 1, sortBy, newOrder);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    handleSearch(searchResult?.query || '', filters, page, sortBy, sortOrder);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        rightContent={
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 hover:bg-[#272727] rounded-full transition-colors ${
                viewMode === 'grid' ? 'bg-[#272727] text-white' : 'text-gray-400'
              }`}
              title="网格视图"
            >
              <Grid className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 hover:bg-[#272727] rounded-full transition-colors ${
                viewMode === 'list' ? 'bg-[#272727] text-white' : 'text-gray-400'
              }`}
              title="列表视图"
            >
              <List className="w-5 h-5" />
            </button>
          </div>
        }
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      {/* 主内容区域 - 全宽度 */}
      <main className="w-full min-h-screen">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* 搜索栏 */}
            <div className="mb-8">
              <div className="text-center mb-6">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <SearchIcon className="w-6 h-6 text-red-500" />
                  <h2 className="text-2xl font-bold text-white">影片搜索</h2>
                </div>
                <p className="text-gray-400">搜索您感兴趣的影片、演员或分类</p>
              </div>

              <SearchBar
                onSearch={(query) => handleSearch(query, filters)}
                placeholder="搜索影片、演员、番号..."
              />

              {/* 搜索工具栏 - 响应式布局 */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 gap-4">
                <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                  {/* 高级筛选按钮 */}
                  <Button
                    onClick={() => setShowFilters(!showFilters)}
                    variant="outline"
                    className={`bg-[#272727] border-[#3f3f3f] text-white hover:bg-[#3f3f3f] ${
                      showFilters ? 'bg-[#3f3f3f]' : ''
                    }`}
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    高级筛选
                    <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${
                      showFilters ? 'rotate-180' : ''
                    }`} />
                  </Button>

                  {/* 排序选择 - 移动端优化 */}
                  <div className="flex items-center gap-2 flex-wrap">
                    <span className="text-sm text-gray-400 whitespace-nowrap">排序:</span>
                    <div className="flex items-center gap-1">
                      <select
                        value={sortBy}
                        onChange={(e) => handleSortChange(e.target.value as 'relevance' | 'date' | 'title')}
                        className="bg-[#272727] border border-[#3f3f3f] text-white rounded px-2 sm:px-3 py-1 text-sm focus:outline-none focus:border-red-500 min-w-0"
                      >
                        <option value="relevance">相关性</option>
                        <option value="date">发行日期</option>
                        <option value="title">标题</option>
                      </select>
                      <Button
                        onClick={handleSortOrderChange}
                        variant="outline"
                        size="sm"
                        className="bg-[#272727] border-[#3f3f3f] text-white hover:bg-[#3f3f3f] p-2 flex-shrink-0"
                        title={sortOrder === 'desc' ? '降序' : '升序'}
                      >
                        <ArrowUpDown className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 结果统计 */}
                {searchResult && (
                  <div className="text-sm text-gray-400 text-center sm:text-right">
                    共 {searchResult.total} 个结果
                  </div>
                )}
              </div>

              {/* 高级筛选面板 */}
              {showFilters && (
                <div className="mt-4 bg-[#1a1a1a] border border-[#3f3f3f] rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* 分类筛选 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        <Tag className="w-4 h-4 inline mr-1" />
                        分类
                      </label>
                      <input
                        type="text"
                        value={filters.category || ''}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                        placeholder="输入分类名称"
                        className="w-full bg-[#272727] border border-[#3f3f3f] text-white rounded px-3 py-2 text-sm focus:outline-none focus:border-red-500"
                      />
                    </div>

                    {/* 演员筛选 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        <User className="w-4 h-4 inline mr-1" />
                        演员
                      </label>
                      <input
                        type="text"
                        value={filters.star || ''}
                        onChange={(e) => handleFilterChange('star', e.target.value)}
                        placeholder="输入演员姓名"
                        className="w-full bg-[#272727] border border-[#3f3f3f] text-white rounded px-3 py-2 text-sm focus:outline-none focus:border-red-500"
                      />
                    </div>

                    {/* 发行日期筛选 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        <Calendar className="w-4 h-4 inline mr-1" />
                        发行日期
                      </label>
                      <div className="flex gap-2">
                        <input
                          type="date"
                          value={filters.dateFrom || ''}
                          onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                          className="flex-1 bg-[#272727] border border-[#3f3f3f] text-white rounded px-2 py-2 text-sm focus:outline-none focus:border-red-500"
                        />
                        <span className="text-gray-400 self-center">至</span>
                        <input
                          type="date"
                          value={filters.dateTo || ''}
                          onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                          className="flex-1 bg-[#272727] border border-[#3f3f3f] text-white rounded px-2 py-2 text-sm focus:outline-none focus:border-red-500"
                        />
                      </div>
                    </div>

                    {/* 时长筛选 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        <SlidersHorizontal className="w-4 h-4 inline mr-1" />
                        时长
                      </label>
                      <select
                        value={filters.videoLength || ''}
                        onChange={(e) => handleFilterChange('videoLength', e.target.value)}
                        className="w-full bg-[#272727] border border-[#3f3f3f] text-white rounded px-3 py-2 text-sm focus:outline-none focus:border-red-500"
                      >
                        <option value="">不限</option>
                        <option value="short">短片 (&lt; 60分钟)</option>
                        <option value="medium">中等 (60-120分钟)</option>
                        <option value="long">长片 (&gt; 120分钟)</option>
                      </select>
                    </div>
                  </div>

                  {/* 筛选操作按钮 */}
                  <div className="flex items-center justify-end gap-3 mt-6 pt-4 border-t border-[#3f3f3f]">
                    <Button
                      onClick={clearFilters}
                      variant="outline"
                      className="bg-transparent border-[#3f3f3f] text-gray-400 hover:bg-[#272727] hover:text-white"
                    >
                      清除筛选
                    </Button>
                    <Button
                      onClick={applyFilters}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      应用筛选
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* 搜索结果 */}
            {error && (
              <div className="text-center py-12">
                <div className="bg-red-900/20 border border-red-700/50 rounded-lg p-6 max-w-md mx-auto">
                  <p className="text-red-400">{error}</p>
                </div>
              </div>
            )}

            {searchResult && (
              <div className="space-y-6">
                {/* 搜索结果统计和建议 */}
                <div className="bg-[#272727] rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="text-white">
                      找到 <span className="text-red-500 font-semibold">{searchResult.total}</span> 个结果
                      {searchResult.query && (
                        <span className="text-gray-400 ml-2">
                          关键词: &ldquo;{searchResult.query}&rdquo;
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-400">
                      第 {currentPage} 页，共 {searchResult.totalPages} 页
                    </div>
                  </div>

                  {/* 搜索建议 */}
                  {searchResult.suggestions && searchResult.suggestions.length > 0 && (
                    <div className="border-t border-[#3f3f3f] pt-3">
                      <div className="text-sm text-gray-400 mb-2">相关建议:</div>
                      <div className="flex flex-wrap gap-2">
                        {searchResult.suggestions.slice(0, 5).map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSearch(suggestion.text, filters)}
                            className="px-3 py-1 bg-[#3f3f3f] hover:bg-[#4f4f4f] text-white text-sm rounded-full transition-colors"
                          >
                            {suggestion.text}
                            <span className="ml-1 text-gray-400">({suggestion.count})</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* 影片网格 */}
                {searchResult.movies.length > 0 ? (
                  <>
                    <div className={
                      viewMode === 'grid'
                        ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
                        : "space-y-4"
                    }>
                      {searchResult.movies.map((movie, index) => (
                        <div
                          key={movie.id}
                          className="fade-in"
                          style={{ animationDelay: `${index * 0.05}s` }}
                        >
                          <MovieCard movie={movie} viewMode={viewMode} />
                        </div>
                      ))}
                    </div>

                    {/* 分页组件 */}
                    {searchResult.totalPages > 1 && (
                      <div className="flex items-center justify-center gap-2 mt-8">
                        {/* 上一页 */}
                        <Button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage <= 1}
                          variant="outline"
                          className="bg-[#272727] border-[#3f3f3f] text-white hover:bg-[#3f3f3f] disabled:opacity-50"
                        >
                          上一页
                        </Button>

                        {/* 页码 */}
                        {Array.from({ length: Math.min(5, searchResult.totalPages) }, (_, i) => {
                          const pageNum = Math.max(1, Math.min(
                            searchResult.totalPages - 4,
                            currentPage - 2
                          )) + i;

                          if (pageNum > searchResult.totalPages) return null;

                          return (
                            <Button
                              key={pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              variant={currentPage === pageNum ? "default" : "outline"}
                              className={
                                currentPage === pageNum
                                  ? "bg-red-600 hover:bg-red-700 text-white"
                                  : "bg-[#272727] border-[#3f3f3f] text-white hover:bg-[#3f3f3f]"
                              }
                            >
                              {pageNum}
                            </Button>
                          );
                        })}

                        {/* 下一页 */}
                        <Button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage >= searchResult.totalPages}
                          variant="outline"
                          className="bg-[#272727] border-[#3f3f3f] text-white hover:bg-[#3f3f3f] disabled:opacity-50"
                        >
                          下一页
                        </Button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-12">
                    <div className="bg-[#272727] rounded-lg p-8">
                      <SearchIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-white mb-2">没有找到相关结果</h3>
                      <p className="text-gray-400 mb-4">
                        请尝试使用不同的关键词或调整筛选条件
                      </p>
                      {/* 搜索建议 */}
                      {searchResult.suggestions && searchResult.suggestions.length > 0 && (
                        <div className="mt-4">
                          <p className="text-sm text-gray-400 mb-2">您可能想搜索:</p>
                          <div className="flex flex-wrap justify-center gap-2">
                            {searchResult.suggestions.slice(0, 3).map((suggestion, index) => (
                              <button
                                key={index}
                                onClick={() => handleSearch(suggestion.text, filters)}
                                className="px-3 py-1 bg-[#3f3f3f] hover:bg-[#4f4f4f] text-white text-sm rounded-full transition-colors"
                              >
                                {suggestion.text}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 空状态 */}
            {!searchResult && !loading && !error && (
              <div className="text-center py-12">
                <div className="bg-[#272727] rounded-lg p-8">
                  <SearchIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">开始搜索</h3>
                  <p className="text-gray-400">
                    输入关键词或使用高级筛选来查找您想要的内容
                  </p>
                </div>
              </div>
            )}
          </div>
        </main>
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { MessageCircle, ArrowLeft, Calendar, ThumbsUp, Trash2, ExternalLink } from 'lucide-react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import { useSession } from '@/components/SessionProvider';

interface UserComment {
  id: string;
  content: string;
  createdAt: string;
  movieId?: string;
  magnetId?: string;
  likes: number;
  movie?: {
    id: string;
    title: string;
  };
  magnet?: {
    id: string;
    title: string;
    movie: {
      id: string;
      title: string;
    };
  };
}

export default function CommentsPage() {
  const { user, loading } = useSession();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [comments, setComments] = useState<UserComment[]>([]);
  const [commentsLoading, setCommentsLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    }
  }, [user, loading, router]);

  const fetchComments = useCallback(async () => {
    try {
      setCommentsLoading(true);

      const response = await fetch(`/api/users/comments?userId=${user?.id}`);
      const data = await response.json();

      if (response.ok) {
        setComments(data.comments);
      } else {
        console.error('获取评论历史失败:', data.error);
      }
    } catch (error) {
      console.error('获取评论历史失败:', error);
    } finally {
      setCommentsLoading(false);
    }
  }, [user?.id]);

  // 获取评论历史
  useEffect(() => {
    if (user) {
      fetchComments();
    }
  }, [user, fetchComments]);

  const deleteComment = async (commentId: string) => {
    try {
      setDeletingId(commentId);
      
      const response = await fetch(`/api/comments?id=${commentId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // 从列表中移除
        setComments(prev => prev.filter(comment => comment.id !== commentId));
      } else {
        console.error('删除评论失败');
      }
    } catch (error) {
      console.error('删除评论失败:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-400">正在加载...</span>
      </div>
    );
  }

  if (!user) {
    return null; // 重定向处理中
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      {/* 主内容区域 */}
      <main className="w-full min-h-screen pt-6">
        <div className="max-w-[1280px] mx-auto px-4">
          {/* 页面头部 */}
          <div className="flex items-center space-x-4 mb-6">
            <Link
              href="/profile"
              className="p-2 hover:bg-[#272727] rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-gray-400" />
            </Link>
            <div className="flex items-center space-x-3">
              <MessageCircle className="w-8 h-8 text-blue-500" />
              <div>
                <h1 className="text-2xl font-bold text-white">我的评论</h1>
                <p className="text-gray-400">
                  {commentsLoading ? '加载中...' : `共 ${comments.length} 条评论`}
                </p>
              </div>
            </div>
          </div>

          {/* 评论列表 */}
          {commentsLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-400">正在加载评论历史...</span>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-12">
              <MessageCircle className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">还没有发表任何评论</h3>
              <p className="text-gray-500 mb-6">去影片页面发表你的第一条评论吧！</p>
              <Link
                href="/"
                className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                浏览影片
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <div key={comment.id} className="bg-[#212121] rounded-lg p-6 hover:bg-[#2a2a2a] transition-colors">
                  {/* 评论头部 */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      {/* 评论目标 */}
                      <div className="flex items-center space-x-2 mb-2">
                        {comment.movie ? (
                          <Link
                            href={`/movies/${comment.movie.id}`}
                            className="flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors"
                          >
                            <span className="text-sm">评论影片：</span>
                            <span className="font-medium">{comment.movie.title}</span>
                            <ExternalLink className="w-3 h-3" />
                          </Link>
                        ) : comment.magnet ? (
                          <div className="space-y-1">
                            <Link
                              href={`/movies/${comment.magnet.movie.id}`}
                              className="flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors"
                            >
                              <span className="text-sm">评论磁力：</span>
                              <span className="font-medium">{comment.magnet.movie.title}</span>
                              <ExternalLink className="w-3 h-3" />
                            </Link>
                            <p className="text-xs text-gray-500 ml-16">
                              磁力链接：{comment.magnet.title}
                            </p>
                          </div>
                        ) : null}
                      </div>

                      {/* 评论时间 */}
                      <div className="flex items-center space-x-2 text-sm text-gray-400">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(comment.createdAt)}</span>
                      </div>
                    </div>

                    {/* 删除按钮 */}
                    <button
                      onClick={() => deleteComment(comment.id)}
                      disabled={deletingId === comment.id}
                      className="p-2 hover:bg-[#3f3f3f] rounded transition-colors disabled:opacity-50"
                      title="删除评论"
                    >
                      {deletingId === comment.id ? (
                        <div className="w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Trash2 className="w-4 h-4 text-gray-400 hover:text-red-400" />
                      )}
                    </button>
                  </div>

                  {/* 评论内容 */}
                  <div className="mb-4">
                    <p className="text-white leading-relaxed">{comment.content}</p>
                  </div>

                  {/* 评论统计 */}
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <ThumbsUp className="w-4 h-4" />
                      <span>{comment.likes} 个赞</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

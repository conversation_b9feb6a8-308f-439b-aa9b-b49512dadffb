'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Settings, ArrowLeft, User, Lock, Save, Eye, EyeOff } from 'lucide-react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import { useSession } from '@/components/SessionProvider';

interface UserSettings {
  username: string;
  email: string;
  name?: string;
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function SettingsPage() {
  const { user, loading } = useSession();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [settings, setSettings] = useState<UserSettings>({
    username: '',
    email: '',
    name: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    }
  }, [user, loading, router]);

  // 初始化用户设置
  useEffect(() => {
    if (user) {
      setSettings(prev => ({
        ...prev,
        username: user.username || '',
        email: user.email || '',
        name: user.name || '',
      }));
    }
  }, [user]);

  const handleInputChange = (field: keyof UserSettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [field]: value,
    }));
    // 清除消息
    if (message) {
      setMessage(null);
    }
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdating(true);
    setMessage(null);

    try {
      // 验证输入
      if (!settings.username.trim()) {
        setMessage({ type: 'error', text: '用户名不能为空' });
        return;
      }

      if (!settings.email.trim()) {
        setMessage({ type: 'error', text: '邮箱不能为空' });
        return;
      }

      // 如果要修改密码，验证密码
      if (settings.newPassword) {
        if (!settings.currentPassword) {
          setMessage({ type: 'error', text: '请输入当前密码' });
          return;
        }

        if (settings.newPassword.length < 6) {
          setMessage({ type: 'error', text: '新密码长度至少6位' });
          return;
        }

        if (settings.newPassword !== settings.confirmPassword) {
          setMessage({ type: 'error', text: '两次输入的新密码不一致' });
          return;
        }
      }

      // 这里需要创建更新用户信息的API
      // 暂时模拟成功
      setTimeout(() => {
        setMessage({ type: 'success', text: '设置已保存' });
        // 清空密码字段
        setSettings(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        }));
      }, 1000);

    } catch {
      setMessage({ type: 'error', text: '保存设置失败，请重试' });
    } finally {
      setIsUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-400">正在加载...</span>
      </div>
    );
  }

  if (!user) {
    return null; // 重定向处理中
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      {/* 主内容区域 */}
      <main className="w-full min-h-screen pt-6">
        <div className="max-w-[800px] mx-auto px-4">
          {/* 页面头部 */}
          <div className="flex items-center space-x-4 mb-6">
            <Link
              href="/profile"
              className="p-2 hover:bg-[#272727] rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-gray-400" />
            </Link>
            <div className="flex items-center space-x-3">
              <Settings className="w-8 h-8 text-gray-400" />
              <div>
                <h1 className="text-2xl font-bold text-white">账户设置</h1>
                <p className="text-gray-400">管理您的个人信息和安全设置</p>
              </div>
            </div>
          </div>

          {/* 设置表单 */}
          <form onSubmit={handleUpdateProfile} className="space-y-6">
            {/* 基本信息 */}
            <div className="bg-[#212121] rounded-lg p-6">
              <h2 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>基本信息</span>
              </h2>

              <div className="space-y-4">
                {/* 用户名 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    用户名
                  </label>
                  <input
                    type="text"
                    value={settings.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className="w-full px-3 py-2 bg-[#3f3f3f] border border-[#4f4f4f] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    placeholder="请输入用户名"
                  />
                </div>

                {/* 显示名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    显示名称（可选）
                  </label>
                  <input
                    type="text"
                    value={settings.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 bg-[#3f3f3f] border border-[#4f4f4f] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    placeholder="请输入显示名称"
                  />
                </div>

                {/* 邮箱 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    邮箱地址
                  </label>
                  <input
                    type="email"
                    value={settings.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 bg-[#3f3f3f] border border-[#4f4f4f] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    placeholder="请输入邮箱地址"
                  />
                </div>
              </div>
            </div>

            {/* 密码修改 */}
            <div className="bg-[#212121] rounded-lg p-6">
              <h2 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Lock className="w-5 h-5" />
                <span>修改密码</span>
              </h2>

              <div className="space-y-4">
                {/* 当前密码 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    当前密码
                  </label>
                  <div className="relative">
                    <input
                      type={showCurrentPassword ? 'text' : 'password'}
                      value={settings.currentPassword}
                      onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                      className="w-full px-3 py-2 pr-10 bg-[#3f3f3f] border border-[#4f4f4f] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      placeholder="请输入当前密码"
                    />
                    <button
                      type="button"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* 新密码 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    新密码
                  </label>
                  <div className="relative">
                    <input
                      type={showNewPassword ? 'text' : 'password'}
                      value={settings.newPassword}
                      onChange={(e) => handleInputChange('newPassword', e.target.value)}
                      className="w-full px-3 py-2 pr-10 bg-[#3f3f3f] border border-[#4f4f4f] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      placeholder="请输入新密码（至少6位）"
                    />
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* 确认新密码 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    确认新密码
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={settings.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      className="w-full px-3 py-2 pr-10 bg-[#3f3f3f] border border-[#4f4f4f] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      placeholder="请再次输入新密码"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* 消息提示 */}
            {message && (
              <div className={`p-4 rounded-lg ${
                message.type === 'success' 
                  ? 'bg-green-900/20 border border-green-900/50 text-green-400' 
                  : 'bg-red-900/20 border border-red-900/50 text-red-400'
              }`}>
                {message.text}
              </div>
            )}

            {/* 保存按钮 */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isUpdating}
                className="flex items-center space-x-2 px-6 py-3 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white rounded-lg transition-colors"
              >
                {isUpdating ? (
                  <div className="w-4 h-4 border border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isUpdating ? '保存中...' : '保存设置'}</span>
              </button>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}

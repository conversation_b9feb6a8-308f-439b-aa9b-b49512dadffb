'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Star, ArrowLeft, Calendar, Trash2, ExternalLink } from 'lucide-react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import OptimizedImage from '@/components/OptimizedImage';
import { useSession } from '@/components/SessionProvider';

interface UserRating {
  id: string;
  score: number;
  createdAt: string;
  movieId: string;
  movie: {
    id: string;
    title: string;
    img?: string;
    localImg?: string;
    date?: string;
  };
}

export default function RatingsPage() {
  const { user, loading } = useSession();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [ratings, setRatings] = useState<UserRating[]>([]);
  const [ratingsLoading, setRatingsLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    }
  }, [user, loading, router]);

  const fetchRatings = useCallback(async () => {
    try {
      setRatingsLoading(true);

      const response = await fetch(`/api/users/ratings?userId=${user?.id}`);
      const data = await response.json();

      if (response.ok) {
        setRatings(data.ratings);
      } else {
        console.error('获取评分历史失败:', data.error);
      }
    } catch (error) {
      console.error('获取评分历史失败:', error);
    } finally {
      setRatingsLoading(false);
    }
  }, [user?.id]);

  // 获取评分历史
  useEffect(() => {
    if (user) {
      fetchRatings();
    }
  }, [user, fetchRatings]);

  const deleteRating = async (ratingId: string, movieId: string) => {
    try {
      setDeletingId(ratingId);
      
      const response = await fetch(`/api/ratings?movieId=${movieId}&userId=${user?.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // 从列表中移除
        setRatings(prev => prev.filter(rating => rating.id !== ratingId));
      } else {
        console.error('删除评分失败');
      }
    } catch (error) {
      console.error('删除评分失败:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const renderStars = (score: number) => {
    const stars = [];
    for (let i = 1; i <= 10; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-4 h-4 ${i <= score ? 'text-yellow-400 fill-current' : 'text-gray-600'}`}
        />
      );
    }
    return stars;
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-400';
    if (score >= 6) return 'text-yellow-400';
    return 'text-red-400';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // 计算平均评分
  const averageScore = ratings.length > 0 
    ? (ratings.reduce((sum, rating) => sum + rating.score, 0) / ratings.length).toFixed(1)
    : '0.0';

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-400">正在加载...</span>
      </div>
    );
  }

  if (!user) {
    return null; // 重定向处理中
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      {/* 主内容区域 */}
      <main className="w-full min-h-screen pt-6">
        <div className="max-w-[1280px] mx-auto px-4">
          {/* 页面头部 */}
          <div className="flex items-center space-x-4 mb-6">
            <Link
              href="/profile"
              className="p-2 hover:bg-[#272727] rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-gray-400" />
            </Link>
            <div className="flex items-center space-x-3">
              <Star className="w-8 h-8 text-yellow-500" />
              <div>
                <h1 className="text-2xl font-bold text-white">我的评分</h1>
                <p className="text-gray-400">
                  {ratingsLoading ? '加载中...' : `共 ${ratings.length} 部影片 · 平均评分 ${averageScore}`}
                </p>
              </div>
            </div>
          </div>

          {/* 评分列表 */}
          {ratingsLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
              <span className="ml-3 text-gray-400">正在加载评分历史...</span>
            </div>
          ) : ratings.length === 0 ? (
            <div className="text-center py-12">
              <Star className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">还没有评分任何影片</h3>
              <p className="text-gray-500 mb-6">去影片页面给喜欢的影片评分吧！</p>
              <Link
                href="/"
                className="inline-flex items-center px-6 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
              >
                浏览影片
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {ratings.map((rating) => (
                <div key={rating.id} className="bg-[#212121] rounded-lg p-6 hover:bg-[#2a2a2a] transition-colors">
                  <div className="flex items-start space-x-4">
                    {/* 影片封面 */}
                    <Link href={`/movies/${rating.movie.id}`} className="flex-shrink-0">
                      <div className="w-20 h-28 bg-[#3f3f3f] rounded overflow-hidden">
                        {rating.movie.localImg && (
                          <OptimizedImage
                            src={rating.movie.localImg}
                            alt={rating.movie.title}
                            width={80}
                            height={112}
                            className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                          />
                        )}
                      </div>
                    </Link>

                    {/* 评分信息 */}
                    <div className="flex-1 min-w-0">
                      {/* 影片标题 */}
                      <Link
                        href={`/movies/${rating.movie.id}`}
                        className="flex items-center space-x-2 mb-2 group"
                      >
                        <h3 className="text-white font-medium line-clamp-1 group-hover:text-blue-400 transition-colors">
                          {rating.movie.title}
                        </h3>
                        <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-blue-400 transition-colors" />
                      </Link>

                      {/* 评分显示 */}
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="flex items-center space-x-1">
                          {renderStars(rating.score)}
                        </div>
                        <span className={`text-lg font-bold ${getScoreColor(rating.score)}`}>
                          {rating.score}/10
                        </span>
                      </div>

                      {/* 评分时间和影片信息 */}
                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>评分于 {formatDate(rating.createdAt)}</span>
                        </div>
                        {rating.movie.date && (
                          <span>影片发布：{rating.movie.date}</span>
                        )}
                      </div>
                    </div>

                    {/* 删除按钮 */}
                    <button
                      onClick={() => deleteRating(rating.id, rating.movie.id)}
                      disabled={deletingId === rating.id}
                      className="p-2 hover:bg-[#3f3f3f] rounded transition-colors disabled:opacity-50"
                      title="删除评分"
                    >
                      {deletingId === rating.id ? (
                        <div className="w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Trash2 className="w-4 h-4 text-gray-400 hover:text-red-400" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

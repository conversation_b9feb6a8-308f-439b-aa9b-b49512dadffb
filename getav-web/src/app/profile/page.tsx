'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { User, Heart, MessageCircle, Star, Settings, TrendingUp } from 'lucide-react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import { useSession } from '@/components/SessionProvider';

interface UserStats {
  totalFavorites: number;
  totalComments: number;
  totalRatings: number;
  averageRating: number;
}

export default function ProfilePage() {
  const { user, loading } = useSession();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userStats, setUserStats] = useState<UserStats>({
    totalFavorites: 0,
    totalComments: 0,
    totalRatings: 0,
    averageRating: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    }
  }, [user, loading, router]);

  const fetchUserStats = useCallback(async () => {
    try {
      setStatsLoading(true);

      const response = await fetch(`/api/users/stats?userId=${user?.id}`);
      const data = await response.json();

      if (response.ok) {
        setUserStats(data);
      } else {
        console.error('获取用户统计失败:', data.error);
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
    } finally {
      setStatsLoading(false);
    }
  }, [user?.id]);

  // 获取用户统计数据
  useEffect(() => {
    if (user) {
      fetchUserStats();
    }
  }, [user, fetchUserStats]);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-400">正在加载...</span>
      </div>
    );
  }

  if (!user) {
    return null; // 重定向处理中
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      {/* 主内容区域 */}
      <main className="w-full min-h-screen pt-6">
        <div className="max-w-[1280px] mx-auto px-4">
          {/* 用户信息头部 */}
          <div className="bg-[#212121] rounded-lg p-6 mb-6">
            <div className="flex items-center space-x-6">
              {/* 用户头像 */}
              <div className="w-24 h-24 bg-[#3f3f3f] rounded-full flex items-center justify-center">
                <User className="w-12 h-12 text-gray-400" />
              </div>

              {/* 用户信息 */}
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-white mb-2">
                  {user.name || user.username}
                </h1>
                <p className="text-gray-400 mb-2">@{user.username}</p>
                <p className="text-gray-400 text-sm">
                  加入时间：{new Date().toLocaleDateString()}
                </p>
              </div>

              {/* 设置按钮 */}
              <Link
                href="/profile/settings"
                className="flex items-center gap-2 px-4 py-2 bg-[#3f3f3f] hover:bg-[#4f4f4f] rounded-lg transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span>设置</span>
              </Link>
            </div>
          </div>

          {/* 统计数据卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* 收藏数量 */}
            <div className="bg-[#212121] rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">收藏影片</p>
                  <p className="text-2xl font-bold text-white">
                    {statsLoading ? '...' : userStats.totalFavorites}
                  </p>
                </div>
                <Heart className="w-8 h-8 text-red-500" />
              </div>
            </div>

            {/* 评论数量 */}
            <div className="bg-[#212121] rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">发表评论</p>
                  <p className="text-2xl font-bold text-white">
                    {statsLoading ? '...' : userStats.totalComments}
                  </p>
                </div>
                <MessageCircle className="w-8 h-8 text-blue-500" />
              </div>
            </div>

            {/* 评分数量 */}
            <div className="bg-[#212121] rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">评分影片</p>
                  <p className="text-2xl font-bold text-white">
                    {statsLoading ? '...' : userStats.totalRatings}
                  </p>
                </div>
                <Star className="w-8 h-8 text-yellow-500" />
              </div>
            </div>

            {/* 平均评分 */}
            <div className="bg-[#212121] rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">平均评分</p>
                  <p className="text-2xl font-bold text-white">
                    {statsLoading ? '...' : userStats.averageRating.toFixed(1)}
                  </p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-500" />
              </div>
            </div>
          </div>

          {/* 快捷操作 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* 我的收藏 */}
            <Link
              href="/profile/favorites"
              className="bg-[#212121] hover:bg-[#2a2a2a] rounded-lg p-6 transition-colors group"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <Heart className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">我的收藏</h3>
                  <p className="text-gray-400 text-sm">查看收藏的影片</p>
                </div>
              </div>
            </Link>

            {/* 我的评论 */}
            <Link
              href="/profile/comments"
              className="bg-[#212121] hover:bg-[#2a2a2a] rounded-lg p-6 transition-colors group"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">我的评论</h3>
                  <p className="text-gray-400 text-sm">查看评论历史</p>
                </div>
              </div>
            </Link>

            {/* 我的评分 */}
            <Link
              href="/profile/ratings"
              className="bg-[#212121] hover:bg-[#2a2a2a] rounded-lg p-6 transition-colors group"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <Star className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">我的评分</h3>
                  <p className="text-gray-400 text-sm">查看评分历史</p>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}

'use client';

import { useEffect, useState, useCallback } from 'react';
import MovieGrid from '@/components/MovieGrid';
import StudiosGrid from '@/components/StudiosGrid';
import ActressesGrid from '@/components/ActressesGrid';
import Header from '@/components/Header';
import RecommendedContent from '@/components/RecommendedContent';
import Sidebar from '@/components/Sidebar';

import { useSession } from '@/components/SessionProvider';
import { Grid, List } from 'lucide-react';
import type { LocalMovie, Studio, Star } from '@/types/javbus';

// 定义点赞API返回的影片数据类型
interface LikedMovieData {
  id: string;
  title: string;
  img?: string;
  localImg?: string;
  date?: string;
  videoLength?: string;
  description?: string;
  gid?: string;
  uc?: string;
  views?: number;
  likes?: number;
  favorites?: number;
  director?: { id: string; name: string } | null;
  producer?: { id: string; name: string } | null;
  publisher?: { id: string; name: string } | null;
  series?: { id: string; name: string } | null;
  stars?: Array<{ id: string; name: string; localAvatar?: string }>;
  genres?: Array<{ id: string; name: string }>;
  samples?: Array<{ id: string; url: string; localPath?: string }>;
  magnets?: Array<{ id: string; link: string; title?: string; size?: string }>;
  createdAt: string;
  updatedAt: string;
  likedAt: string;
}

interface InitialData {
  movies?: LocalMovie[];
  studios?: Studio[];
  stars?: Star[];
  total: number;
  page: number;
  needLogin?: boolean;
  message?: string;
  type?: 'movies' | 'studios' | 'actresses';
}

interface CategoryStats {
  all: number;
  trending: number;
  latest: number;
  subtitle: number;
  hd: number;
  censored: number;
  uncensored: number;
  studios: number;
  actresses: number;
}



export default function Home() {
  const { user } = useSession();
  const [initialData, setInitialData] = useState<InitialData | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeCategory, setActiveCategory] = useState<string>('all');

  const [sortBy, setSortBy] = useState<string>('latest');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [categoryStats, setCategoryStats] = useState<CategoryStats | null>(null);





  // 从localStorage加载视图偏好
  useEffect(() => {
    const savedViewMode = localStorage.getItem('viewMode') as 'grid' | 'list';
    if (savedViewMode) {
      setViewMode(savedViewMode);
    }
  }, []);

  // 保存视图偏好到localStorage
  const saveViewMode = (mode: 'grid' | 'list') => {
    setViewMode(mode);
    localStorage.setItem('viewMode', mode);
  };

  // 获取影片数据
  const fetchMovies = useCallback(async (category: string = 'all', sort: string = 'latest') => {
    setLoading(true);
    try {
      let response;
      let data;

      // 处理特殊分类：点赞、收藏和历史
      if (category === 'likes') {
        // 点赞功能需要用户登录
        if (!user) {
          setInitialData({
            movies: [],
            total: 0,
            page: 1,
            needLogin: true,
            message: '请登录后查看点赞内容'
          });
          setLoading(false);
          return;
        }

        // 调用点赞API
        const params = new URLSearchParams({
          userId: user.id,
          page: '1',
          limit: '20',
          sortBy: sort === 'latest' ? 'latest' : sort === 'popular' ? 'popular' : 'rating',
        });
        response = await fetch(`/api/likes/user?${params}`);
        data = await response.json();

        if (response.ok && data.success) {
          // 转换点赞数据格式为LocalMovie格式
          const movies = data.data.items.map((movie: LikedMovieData) => ({
            id: movie.id,
            title: movie.title,
            img: movie.img,
            localImg: movie.localImg,
            date: movie.date,
            videoLength: movie.videoLength,
            description: movie.description || `${movie.title} - ${movie.id}`,
            gid: movie.gid,
            uc: movie.uc,
            views: movie.views || 0,
            likes: movie.likes || 0,
            favorites: movie.favorites || 0,
            director: movie.director || null,
            producer: movie.producer || null,
            publisher: movie.publisher || null,
            series: movie.series || null,
            stars: movie.stars || [],
            genres: movie.genres || [],
            samples: movie.samples || [],
            magnets: movie.magnets || [],
            createdAt: new Date(movie.createdAt || movie.likedAt),
            updatedAt: new Date(movie.updatedAt || movie.likedAt)
          }));

          const likesData = {
            movies,
            total: data.data.total || data.data.items.length,
            page: data.data.page || 1,
          };
          setInitialData(likesData);
        } else {
          setInitialData({
            movies: [],
            total: 0,
            page: 1,
            message: data.error || '暂无点赞内容'
          });
        }
      } else if (category === 'favorites') {
        // 收藏功能需要用户登录
        if (!user) {
          setInitialData({
            movies: [],
            total: 0,
            page: 1,
            needLogin: true,
            message: '请登录后查看收藏内容'
          });
          setLoading(false);
          return;
        }

        // 调用收藏API - 使用与个人页面相同的API
        const params = new URLSearchParams({
          userId: user.id,
          page: '1',
          limit: '20',
        });
        response = await fetch(`/api/users/favorites?${params}`);
        data = await response.json();

        if (response.ok && data.favorites) {
          // 转换收藏数据格式为LocalMovie格式
          const movies = data.favorites.map((favorite: { movie: LocalMovie; createdAt: string }) => ({
            id: favorite.movie.id,
            title: favorite.movie.title,
            img: favorite.movie.img,
            localImg: favorite.movie.localImg,
            date: favorite.movie.date,
            videoLength: favorite.movie.videoLength,
            description: favorite.movie.description || `${favorite.movie.title} - ${favorite.movie.id}`,
            gid: favorite.movie.gid,
            uc: favorite.movie.uc,
            views: favorite.movie.views || 0,
            likes: favorite.movie.likes || 0,
            favorites: favorite.movie.favorites || 0,
            director: favorite.movie.director || null,
            producer: favorite.movie.producer || null,
            publisher: favorite.movie.publisher || null,
            series: favorite.movie.series || null,
            stars: favorite.movie.stars || [],
            genres: favorite.movie.genres || [],
            samples: favorite.movie.samples || [],
            magnets: favorite.movie.magnets || [],
            createdAt: new Date(favorite.movie.createdAt || favorite.createdAt),
            updatedAt: new Date(favorite.movie.updatedAt || favorite.createdAt)
          }));

          const favoritesData = {
            movies,
            total: data.pagination?.total || data.favorites.length,
            page: data.pagination?.page || 1,
          };
          setInitialData(favoritesData);
        } else {
          setInitialData({
            movies: [],
            total: 0,
            page: 1,
            message: data.error || '暂无收藏内容'
          });
        }
      } else if (category.startsWith('favorite-list-')) {
        // 收藏列表功能需要用户登录
        if (!user) {
          setInitialData({
            movies: [],
            total: 0,
            page: 1,
            needLogin: true,
            message: '请登录后查看收藏列表'
          });
          setLoading(false);
          return;
        }

        // 提取收藏列表ID
        const listId = category.replace('favorite-list-', '');

        // 调用收藏列表API
        const params = new URLSearchParams({
          userId: user.id,
          page: '1',
          limit: '20',
        });
        response = await fetch(`/api/favorite-lists/${listId}/movies?${params}`);
        data = await response.json();

        if (response.ok && data.success) {
          // 转换收藏列表数据格式为LocalMovie格式
          const movies = data.data.movies.map((movie: LocalMovie) => ({
            id: movie.id,
            title: movie.title,
            img: movie.img,
            localImg: movie.localImg,
            date: movie.date,
            videoLength: movie.videoLength,
            description: movie.description || `${movie.title} - ${movie.id}`,
            gid: movie.gid,
            uc: movie.uc,
            views: movie.views || 0,
            likes: movie.likes || 0,
            favorites: movie.favorites || 0,
            director: movie.director || null,
            producer: movie.producer || null,
            publisher: movie.publisher || null,
            series: movie.series || null,
            stars: movie.stars || [],
            genres: movie.genres || [],
            samples: movie.samples || [],
            magnets: movie.magnets || [],
            createdAt: new Date(movie.createdAt),
            updatedAt: new Date(movie.updatedAt)
          }));

          const favoriteListData = {
            movies,
            total: data.data.pagination?.total || data.data.movies.length,
            page: data.data.pagination?.page || 1,
          };
          setInitialData(favoriteListData);
        } else {
          setInitialData({
            movies: [],
            total: 0,
            page: 1,
            message: data.message || '暂无收藏内容'
          });
        }
      } else if (category === 'history') {
        // 历史记录功能对所有用户开放，从localStorage获取
        try {
          const localHistory = localStorage.getItem('watch_history');
          let historyData = localHistory ? JSON.parse(localHistory) : [];

          // 如果没有历史记录，创建一个提示信息
          if (historyData.length === 0) {
            data = {
              success: true,
              data: {
                items: [],
                total: 0,
                page: 1,
              },
              message: '暂无观看历史，观看影片后会自动记录到这里'
            };
          } else {
            // 确保历史记录数据格式符合LocalMovie接口
            historyData = historyData.map((item: LocalMovie & { movieId?: string; imageUrl?: string; watchedAt?: string; duration?: string }) => ({
              id: item.id || item.movieId || `history-${Date.now()}-${Math.random()}`,
              title: item.title || '未知标题',
              img: item.img || item.imageUrl,
              localImg: item.localImg,
              date: item.date || item.watchedAt,
              videoLength: item.videoLength || item.duration,
              description: item.description,
              gid: item.gid,
              uc: item.uc,
              views: item.views || 0,
              likes: item.likes || 0,
              favorites: item.favorites || 0,
              director: item.director || null,
              producer: item.producer || null,
              publisher: item.publisher || null,
              series: item.series || null,
              stars: item.stars || [],
              genres: item.genres || [],
              samples: item.samples || [],
              magnets: item.magnets || [],
              createdAt: new Date(item.createdAt || item.watchedAt || Date.now()),
              updatedAt: new Date(item.updatedAt || item.watchedAt || Date.now())
            }));

            data = {
              success: true,
              data: {
                items: historyData.slice(0, 20), // 限制显示20条
                total: historyData.length,
                page: 1,
              }
            };
          }
        } catch (storageError) {
          console.error('读取本地历史记录失败:', storageError);
          data = {
            success: true,
            data: {
              items: [],
              total: 0,
              page: 1,
            },
            message: '暂无观看历史'
          };
        }

        if (data.success && data.data) {
          // 转换历史数据格式为LocalMovie格式
          const historyData = {
            movies: data.data.items || data.data || [],
            total: data.data.total || 0,
            page: data.data.page || 1,
          };
          setInitialData(historyData);
        } else {
          setInitialData({
            movies: [],
            total: 0,
            page: 1,
            message: data.message || '暂无观看历史'
          });
        }
      } else if (category === 'studios') {
        // 片商分类处理
        const params = new URLSearchParams({
          page: '1',
          limit: '10',
          sort: sort === 'latest' ? 'latest' : 'popular',
        });

        response = await fetch(`/api/studios?${params}`);
        data = await response.json();
        if (data.success) {
          // 转换片商数据格式为适合显示的格式
          const studiosData = {
            studios: data.data.studios,
            total: data.data.total,
            page: data.data.page,
            type: 'studios' as const
          };
          setInitialData(studiosData);
        }
      } else if (category === 'actresses') {
        // 女优分类处理
        const params = new URLSearchParams({
          page: '1',
          limit: '40',
          sort: sort === 'latest' ? 'latest' : 'popular',
        });

        response = await fetch(`/api/stars?${params}`);
        data = await response.json();
        if (data.success) {
          // 转换女优数据格式为适合显示的格式
          const starsData = {
            stars: data.data.stars,
            total: data.data.pagination.total,
            page: data.data.pagination.page,
            type: 'actresses' as const
          };
          setInitialData(starsData);
        }
      } else {
        // 普通分类处理
        const params = new URLSearchParams({
          page: '1',
          limit: '20',
          ...(category !== 'all' && { category }),
          sort,
        });

        response = await fetch(`/api/movies?${params}`);
        data = await response.json();
        if (data.success) {
          setInitialData(data.data);
        }
      }
    } catch (error) {
      console.error('获取影片数据失败:', error);
      setInitialData({
        movies: [],
        total: 0,
        page: 1,
        message: '数据加载失败，请重试'
      });
    } finally {
      setLoading(false);
    }
  }, [user]);

  // 处理分类点击
  const handleCategoryClick = (categoryId: string) => {
    // 如果点击赞过的视频但未登录，显示提示
    if (categoryId === 'likes' && !user) {
      const shouldLogin = window.confirm('查看赞过的视频需要登录，是否前往登录页面？');
      if (shouldLogin) {
        window.location.href = '/auth/signin';
        return;
      }
      return;
    }

    // 如果点击收藏但未登录，显示提示
    if ((categoryId === 'favorites' || categoryId.startsWith('favorite-list-')) && !user) {
      // 可以在这里添加一个toast提示或者直接跳转到登录页
      const shouldLogin = window.confirm('查看收藏需要登录，是否前往登录页面？');
      if (shouldLogin) {
        window.location.href = '/auth/signin';
        return;
      }
      return;
    }

    // 滚动到页面顶部，提供平滑的滚动效果
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setActiveCategory(categoryId);
    fetchMovies(categoryId, sortBy);
  };

  // 处理排序变化
  const handleSortChange = (sort: string) => {
    setSortBy(sort);
    fetchMovies(activeCategory, sort);
  };

  // 获取分类统计数据
  const fetchCategoryStats = async () => {
    try {
      const response = await fetch('/api/categories/stats');
      const data = await response.json();
      if (data.success) {
        setCategoryStats(data.data);
      }
    } catch (error) {
      console.error('获取分类统计失败:', error);
    }
  };

  // 获取初始影片数据和统计数据
  useEffect(() => {
    fetchMovies();
    fetchCategoryStats();
  }, [fetchMovies]);

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 使用Sidebar组件 - 桌面端 */}
      <div className="fixed top-16 left-0 h-[calc(100vh-4rem)] z-40 hidden md:block">
        <Sidebar
          sidebarOpen={sidebarOpen}
          activeCategory={activeCategory}
          onCategoryClick={handleCategoryClick}
          categoryStats={categoryStats}
        />
      </div>



      {/* 移动端侧边栏覆盖层 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)] md:hidden">
            <div className="w-64">
              <Sidebar
                sidebarOpen={true}
                activeCategory={activeCategory}
                onCategoryClick={(categoryId) => {
                  handleCategoryClick(categoryId);
                  setSidebarOpen(false);
                }}
                categoryStats={categoryStats}
              />
            </div>
          </div>
        </>
      )}

      {/* 主内容区域 - 根据侧边栏宽度调整左边距 */}
      <main
        className={`min-h-screen transition-all duration-300 ease-in-out md:ml-0 ${
          sidebarOpen ? 'md:ml-64' : 'md:ml-20'
        }`}
      >


          {/* 内容区域 - 严格控制溢出 */}
          <div className="px-6 py-6 overflow-hidden">
            {/* 推荐内容区域 - 只在【全部】分类显示 */}
            {activeCategory === 'all' && initialData?.movies && initialData.movies.length > 0 && (
              <RecommendedContent
                movies={initialData.movies.slice(0, 10)}
                showControls={true}
              />
            )}

            {/* 主要内容标题 */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                {activeCategory === 'likes' ? '赞过的视频' :
                 activeCategory === 'favorites' ? '我的收藏' :
                 activeCategory.startsWith('favorite-list-') ? '收藏列表' :
                 activeCategory === 'history' ? '观看历史' :
                 activeCategory === 'studios' ? '片商列表' :
                 activeCategory === 'actresses' ? '女优列表' :
                 activeCategory === 'all' ? '全部影片' :
                 activeCategory === 'trending' ? '热门影片' :
                 activeCategory === 'latest' ? '最新影片' :
                 activeCategory === 'censored' ? '有码影片' :
                 activeCategory === 'uncensored' ? '无码影片' :
                 activeCategory === 'subtitle' ? '中文字幕' :
                 activeCategory === 'hd' ? '高清影片' :
                 '最新影片'} ({initialData?.total || 0})
              </h2>
              <div className="flex items-center space-x-4">
                {/* 视图切换按钮 - 移除背景，参考YouTube */}
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => saveViewMode('grid')}
                    className={`p-2 rounded-full transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-[#272727] text-white'
                        : 'text-gray-400 hover:bg-[#272727] hover:text-white'
                    }`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => saveViewMode('list')}
                    className={`p-2 rounded-full transition-colors ${
                      viewMode === 'list'
                        ? 'bg-[#272727] text-white'
                        : 'text-gray-400 hover:bg-[#272727] hover:text-white'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>

                <span className="text-sm text-gray-400">排序:</span>
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="bg-transparent border-none text-sm text-white focus:outline-none cursor-pointer"
                >
                  <option value="latest">最新发布</option>
                  <option value="popular">最受欢迎</option>
                  <option value="rating">评分最高</option>
                  <option value="duration">时长排序</option>
                </select>
              </div>
            </div>

            {/* 影片内容 */}
            <div>
              {loading ? (
                <div className="flex items-center justify-center py-16">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
                  <span className="ml-3 text-gray-400">正在加载...</span>
                </div>
              ) : initialData?.needLogin ? (
                <div className="flex flex-col items-center justify-center py-16">
                  <div className="text-6xl mb-4">🔒</div>
                  <h3 className="text-xl font-semibold text-white mb-2">需要登录</h3>
                  <p className="text-gray-400 mb-6">{initialData.message}</p>
                  <div className="flex space-x-4">
                    <button
                      onClick={() => window.location.href = '/auth/signin'}
                      className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors"
                    >
                      立即登录
                    </button>
                    <button
                      onClick={() => window.location.href = '/auth/signup'}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors"
                    >
                      注册账号
                    </button>
                  </div>
                </div>
              ) : activeCategory === 'studios' ? (
                <StudiosGrid
                  initialStudios={initialData?.studios || []}
                  initialPage={initialData?.page || 1}
                  initialTotal={initialData?.total || 0}
                  viewMode={viewMode}
                  sortBy={sortBy}
                />
              ) : activeCategory === 'actresses' ? (
                <ActressesGrid
                  initialActresses={initialData?.stars || []}
                  initialPage={initialData?.page || 1}
                  initialTotal={initialData?.total || 0}
                  viewMode={viewMode}
                  sortBy={sortBy}
                />
              ) : initialData?.movies?.length === 0 && initialData?.message ? (
                <div className="flex flex-col items-center justify-center py-16">
                  <div className="text-6xl mb-4">
                    {activeCategory === 'likes' ? '❤️' :
                     activeCategory === 'favorites' ? '💝' :
                     activeCategory === 'history' ? '📚' : '🎬'}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">暂无内容</h3>
                  <p className="text-gray-400">{initialData.message}</p>
                </div>
              ) : (
                <MovieGrid
                  initialMovies={initialData?.movies || []}
                  initialPage={initialData?.page || 1}
                  initialTotal={initialData?.total || 0}
                  viewMode={viewMode}
                  category={activeCategory}
                  sortBy={sortBy}
                />
              )}
            </div>
          </div>
      </main>
    </div>
  );
}

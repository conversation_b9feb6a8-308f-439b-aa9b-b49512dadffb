@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* 深色主题作为默认 - 成人视频网站行业标准 */
  --background: oklch(0.1 0 0);              /* #1a1a1a - 主背景 */
  --foreground: oklch(0.98 0 0);             /* #ffffff - 主文字 */
  --card: oklch(0.23 0 0);                   /* #3a3a3a - 卡片背景 */
  --card-foreground: oklch(0.98 0 0);        /* #ffffff - 卡片文字 */
  --popover: oklch(0.23 0 0);                /* #3a3a3a - 弹窗背景 */
  --popover-foreground: oklch(0.98 0 0);     /* #ffffff - 弹窗文字 */
  --primary: oklch(0.68 0.15 35);            /* #ff6b35 - 橙色强调色 */
  --primary-foreground: oklch(0.98 0 0);     /* #ffffff - 主按钮文字 */
  --secondary: oklch(0.18 0 0);              /* #2d2d2d - 次要背景 */
  --secondary-foreground: oklch(0.8 0 0);    /* #cccccc - 次要文字 */
  --muted: oklch(0.18 0 0);                  /* #2d2d2d - 静音背景 */
  --muted-foreground: oklch(0.6 0 0);        /* #999999 - 静音文字 */
  --accent: oklch(0.68 0.15 35);             /* #ff6b35 - 强调色 */
  --accent-foreground: oklch(0.98 0 0);      /* #ffffff - 强调文字 */
  --destructive: oklch(0.577 0.245 27.325);  /* 保持红色用于危险操作 */
  --border: oklch(0.25 0 0);                 /* #404040 - 边框色 */
  --input: oklch(0.2 0 0);                   /* #333333 - 输入框背景 */
  --ring: oklch(0.68 0.15 35);               /* #ff6b35 - 焦点环 */
  --chart-1: oklch(0.68 0.15 35);            /* 橙色图表 */
  --chart-2: oklch(0.6 0.118 184.704);       /* 保持蓝色 */
  --chart-3: oklch(0.398 0.07 227.392);      /* 保持深蓝 */
  --chart-4: oklch(0.828 0.189 84.429);      /* 保持黄绿 */
  --chart-5: oklch(0.769 0.188 70.08);       /* 保持橙黄 */
  --sidebar: oklch(0.18 0 0);                /* #2d2d2d - 侧边栏 */
  --sidebar-foreground: oklch(0.98 0 0);     /* #ffffff - 侧边栏文字 */
  --sidebar-primary: oklch(0.68 0.15 35);    /* #ff6b35 - 侧边栏主色 */
  --sidebar-primary-foreground: oklch(0.98 0 0); /* #ffffff */
  --sidebar-accent: oklch(0.23 0 0);         /* #3a3a3a - 侧边栏强调 */
  --sidebar-accent-foreground: oklch(0.98 0 0);  /* #ffffff */
  --sidebar-border: oklch(0.25 0 0);         /* #404040 - 侧边栏边框 */
  --sidebar-ring: oklch(0.68 0.15 35);       /* #ff6b35 - 侧边栏焦点 */
}

.light {
  /* 浅色主题 - 可选的替代主题 */
  --background: oklch(1 0 0);                /* #ffffff - 白色背景 */
  --foreground: oklch(0.145 0 0);            /* #222222 - 深色文字 */
  --card: oklch(1 0 0);                      /* #ffffff - 白色卡片 */
  --card-foreground: oklch(0.145 0 0);       /* #222222 - 深色文字 */
  --popover: oklch(1 0 0);                   /* #ffffff - 白色弹窗 */
  --popover-foreground: oklch(0.145 0 0);    /* #222222 - 深色文字 */
  --primary: oklch(0.68 0.15 35);            /* #ff6b35 - 保持橙色强调 */
  --primary-foreground: oklch(0.98 0 0);     /* #ffffff - 白色文字 */
  --secondary: oklch(0.97 0 0);              /* #f5f5f5 - 浅灰背景 */
  --secondary-foreground: oklch(0.205 0 0);  /* #333333 - 深色文字 */
  --muted: oklch(0.97 0 0);                  /* #f5f5f5 - 静音背景 */
  --muted-foreground: oklch(0.556 0 0);      /* #888888 - 静音文字 */
  --accent: oklch(0.68 0.15 35);             /* #ff6b35 - 橙色强调 */
  --accent-foreground: oklch(0.98 0 0);      /* #ffffff - 白色文字 */
  --destructive: oklch(0.577 0.245 27.325);  /* 红色危险操作 */
  --border: oklch(0.922 0 0);                /* #eeeeee - 浅色边框 */
  --input: oklch(0.922 0 0);                 /* #eeeeee - 输入框背景 */
  --ring: oklch(0.68 0.15 35);               /* #ff6b35 - 橙色焦点环 */
  --chart-1: oklch(0.68 0.15 35);            /* 橙色图表 */
  --chart-2: oklch(0.6 0.118 184.704);       /* 蓝色图表 */
  --chart-3: oklch(0.398 0.07 227.392);      /* 深蓝图表 */
  --chart-4: oklch(0.828 0.189 84.429);      /* 黄绿图表 */
  --chart-5: oklch(0.769 0.188 70.08);       /* 橙黄图表 */
  --sidebar: oklch(0.985 0 0);               /* #fafafa - 浅色侧边栏 */
  --sidebar-foreground: oklch(0.145 0 0);    /* #222222 - 深色文字 */
  --sidebar-primary: oklch(0.68 0.15 35);    /* #ff6b35 - 橙色主色 */
  --sidebar-primary-foreground: oklch(0.98 0 0); /* #ffffff */
  --sidebar-accent: oklch(0.97 0 0);         /* #f5f5f5 - 浅色强调 */
  --sidebar-accent-foreground: oklch(0.205 0 0); /* #333333 */
  --sidebar-border: oklch(0.922 0 0);        /* #eeeeee - 浅色边框 */
  --sidebar-ring: oklch(0.68 0.15 35);       /* #ff6b35 - 橙色焦点 */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* 自定义滚动条 - 参考现代网站设计 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-slate-100 dark:bg-slate-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-slate-300 dark:bg-slate-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-400 dark:bg-slate-500;
  }

  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 玻璃态效果 */
  .glass-effect {
    @apply bg-white/80 dark:bg-slate-800/80 backdrop-blur-md border border-white/20 dark:border-slate-700/50;
  }

  /* 渐变文字效果 - 更新为紫色系 */
  .gradient-text {
    @apply bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent;
  }

  /* 悬浮动画 - 优雅版本 */
  .hover-lift {
    @apply transition-all duration-500 ease-out hover:-translate-y-1 hover:shadow-xl hover:shadow-black/25;
  }

  /* 加载动画 */
  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  /* 淡入动画 */
  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 卡片悬浮效果 - 优雅的YouTube风格 */
  .card-hover {
    @apply transition-all duration-500 ease-out hover:shadow-xl hover:shadow-black/25 hover:-translate-y-1;
  }

  /* 按钮悬浮效果 - 更柔和 */
  .button-hover {
    @apply transition-all duration-300 ease-out hover:scale-102 active:scale-98 hover:shadow-md hover:shadow-black/20;
  }

  /* 横向卡片特殊效果 - 优雅版本 */
  .movie-card-horizontal {
    @apply transition-all duration-500 ease-out hover:shadow-lg hover:shadow-black/20 hover:-translate-y-0.5;
  }

  /* 搜索栏发光效果 */
  .search-glow:focus-within {
    box-shadow: 0 0 0 1px rgb(168 85 247 / 0.5), 0 0 20px rgb(168 85 247 / 0.3);
  }

  /* 导航链接悬浮效果 */
  .nav-link-hover {
    @apply relative transition-all duration-200;
  }

  .nav-link-hover::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-400 to-pink-400 transition-all duration-300;
  }

  .nav-link-hover:hover::after {
    @apply w-full;
  }

  /* 轮播图容器严格限制 */
  .carousel-container {
    contain: layout style size;
    overflow: hidden;
  }

  /* 主内容区域布局控制 */
  .main-content {
    max-width: 100%;
    overflow-x: hidden;
  }

  /* 侧边栏固定宽度 */
  .sidebar-fixed {
    flex-shrink: 0;
    min-width: 288px; /* w-72 */
    max-width: 288px;
  }

  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* YouTube风格滚动条 - 提高优先级 */
  .youtube-scrollbar {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent !important;
  }

  .youtube-scrollbar::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
  }

  .youtube-scrollbar::-webkit-scrollbar-track {
    background: transparent !important;
  }

  .youtube-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-radius: 3px !important;
    transition: background-color 0.2s ease !important;
  }

  .youtube-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.4) !important;
  }

  .youtube-scrollbar::-webkit-scrollbar-corner {
    background: transparent !important;
  }

  /* 无限滚动加载动画 */
  .infinite-scroll-loading {
    animation: infiniteScrollPulse 1.5s ease-in-out infinite;
  }

  @keyframes infiniteScrollPulse {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  /* 滚动加载骨架屏动画 */
  .skeleton-loading {
    animation: skeletonShimmer 1.5s ease-in-out infinite;
  }

  @keyframes skeletonShimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .skeleton-shimmer {
    background: linear-gradient(90deg, #272727 25%, #3f3f3f 50%, #272727 75%);
    background-size: 200px 100%;
    animation: skeletonShimmer 1.5s infinite;
  }

  /* 无限滚动新内容淡入动画 */
  .infinite-scroll-item {
    animation: infiniteScrollFadeIn 0.8s ease-out forwards;
    opacity: 0;
  }

  @keyframes infiniteScrollFadeIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}

'use client';

import { useState } from 'react';
import { Heart, Plus, Settings } from 'lucide-react';
import FavoriteListManager from '@/components/FavoriteListManager';
import { useFavoriteLists } from '@/hooks/useFavoriteLists';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ToastProvider } from '@/contexts/ToastContext';

// 模拟用户数据
const mockUser = {
  id: 'test-user-123',
  name: '测试用户'
};

// 模拟影片数据
const mockMovies = [
  {
    id: 'movie-1',
    title: '测试影片 1',
    img: '/placeholder-movie.jpg'
  },
  {
    id: 'movie-2', 
    title: '测试影片 2',
    img: '/placeholder-movie.jpg'
  },
  {
    id: 'movie-3',
    title: '测试影片 3',
    img: '/placeholder-movie.jpg'
  }
];

function TestFavoritesContent() {
  const [selectedMovie, setSelectedMovie] = useState(mockMovies[0]);
  const { favoriteLists, loading, error, refetch } = useFavoriteLists({
    userId: mockUser.id,
    autoFetch: true
  });

  const handleFavoriteChange = (listId: string | null, action: 'add' | 'remove' | 'move') => {
    console.log('收藏状态变化:', { listId, action });
    // 刷新列表以获取最新状态
    refetch();
  };

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">收藏列表功能测试</h1>

        {/* 用户信息 */}
        <div className="bg-[#1a1a1a] rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold mb-2">当前用户</h2>
          <p className="text-gray-300">ID: {mockUser.id}</p>
          <p className="text-gray-300">姓名: {mockUser.name}</p>
        </div>

        {/* 影片选择 */}
        <div className="bg-[#1a1a1a] rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold mb-4">选择测试影片</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {mockMovies.map((movie) => (
              <button
                key={movie.id}
                onClick={() => setSelectedMovie(movie)}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  selectedMovie.id === movie.id
                    ? 'border-blue-500 bg-blue-900/20'
                    : 'border-gray-600 hover:border-gray-500'
                }`}
              >
                <h3 className="font-medium">{movie.title}</h3>
                <p className="text-sm text-gray-400">ID: {movie.id}</p>
              </button>
            ))}
          </div>
        </div>

        {/* 收藏操作测试 */}
        <div className="bg-[#1a1a1a] rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold mb-4">收藏操作测试</h2>
          <div className="flex flex-wrap gap-4">
            {/* 收藏按钮 */}
            <FavoriteListManager
              userId={mockUser.id}
              movieId={selectedMovie.id}
              movieTitle={selectedMovie.title}
              onFavoriteChange={handleFavoriteChange}
            >
              {({ showFavoriteModal }) => (
                <button
                  onClick={showFavoriteModal}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <Heart className="w-4 h-4" />
                  收藏到列表
                </button>
              )}
            </FavoriteListManager>

            {/* 创建新列表按钮 */}
            <FavoriteListManager
              userId={mockUser.id}
              movieId={selectedMovie.id}
              movieTitle={selectedMovie.title}
              onFavoriteChange={handleFavoriteChange}
            >
              {({ showCreateModal }) => (
                <button
                  onClick={showCreateModal}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  创建新列表
                </button>
              )}
            </FavoriteListManager>
          </div>
        </div>

        {/* 收藏列表状态 */}
        <div className="bg-[#1a1a1a] rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">当前收藏列表</h2>
            <button
              onClick={refetch}
              className="flex items-center gap-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors"
            >
              刷新
            </button>
          </div>

          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-400">加载中...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <p className="text-red-400">错误: {error}</p>
            </div>
          )}

          {!loading && !error && (
            <div className="space-y-3">
              {favoriteLists.length === 0 ? (
                <p className="text-gray-400 text-center py-8">暂无收藏列表</p>
              ) : (
                favoriteLists.map((list) => (
                  <div
                    key={list.id}
                    className="flex items-center justify-between p-3 bg-[#2a2a2a] rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-gray-400">
                        {list.isDefault ? '⏰' : '📁'}
                      </div>
                      <div>
                        <h3 className="font-medium">
                          {list.name}
                          {list.isDefault && (
                            <span className="ml-2 text-xs text-blue-400 bg-blue-900/30 px-2 py-0.5 rounded">
                              默认
                            </span>
                          )}
                        </h3>
                        {list.description && (
                          <p className="text-sm text-gray-400">{list.description}</p>
                        )}
                        <p className="text-xs text-gray-500">
                          {list.count || 0} 个视频 • {list.isPublic ? '公开' : '私密'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <FavoriteListManager
                        userId={mockUser.id}
                        onFavoriteChange={handleFavoriteChange}
                      >
                        {({ showEditModal }) => (
                          <button
                            onClick={() => showEditModal(list)}
                            className="p-2 text-gray-400 hover:text-white hover:bg-[#3f3f3f] rounded transition-colors"
                          >
                            <Settings className="w-4 h-4" />
                          </button>
                        )}
                      </FavoriteListManager>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* 测试说明 */}
        <div className="bg-[#1a1a1a] rounded-lg p-4 mt-6">
          <h2 className="text-lg font-semibold mb-4">测试说明</h2>
          <div className="text-sm text-gray-300 space-y-2">
            <p>• 选择不同的测试影片来测试收藏功能</p>
            <p>• 点击&ldquo;收藏到列表&rdquo;按钮打开收藏列表选择界面</p>
            <p>• 点击&ldquo;创建新列表&rdquo;按钮创建新的收藏列表</p>
            <p>• 在收藏列表中点击设置按钮可以编辑或删除列表</p>
            <p>• 系统会自动为新用户创建默认的&ldquo;稍后观看&rdquo;列表</p>
            <p>• 所有操作都会实时更新列表状态</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function TestFavoritesPage() {
  return (
    <ToastProvider>
      <ErrorBoundary>
        <TestFavoritesContent />
      </ErrorBoundary>
    </ToastProvider>
  );
}

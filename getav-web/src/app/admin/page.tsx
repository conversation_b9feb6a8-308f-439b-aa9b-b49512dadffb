'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import ImportPanel from '@/components/ImportPanel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Shield, 
  Database, 
  Download, 
  Users, 
  Film, 
  Settings,
  LogOut,
  BarChart3
} from 'lucide-react';

interface AdminStats {
  totalMovies: number;
  totalStars: number;
  totalGenres: number;
  recentImports: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${getCookie('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const getCookie = (name: string) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift();
    return '';
  };

  const handleLogout = () => {
    document.cookie = 'admin-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    router.push('/admin/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800">
      {/* 管理员导航栏 */}
      <header className="bg-slate-800/90 backdrop-blur-md shadow-lg border-b border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-2xl font-bold text-white">
                  管理员后台
                </h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push('/')}
                variant="outline"
                className="text-slate-300 border-slate-600 hover:bg-slate-700"
              >
                返回前台
              </Button>
              <Button
                onClick={handleLogout}
                variant="outline"
                className="text-red-400 border-red-600 hover:bg-red-900/20"
              >
                <LogOut className="w-4 h-4 mr-2" />
                退出登录
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-slate-800/70 backdrop-blur-sm border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">总影片数</p>
                  <p className="text-2xl font-bold text-white">
                    {loading ? '...' : stats?.totalMovies || 0}
                  </p>
                </div>
                <Film className="w-8 h-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/70 backdrop-blur-sm border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">演员数量</p>
                  <p className="text-2xl font-bold text-white">
                    {loading ? '...' : stats?.totalStars || 0}
                  </p>
                </div>
                <Users className="w-8 h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/70 backdrop-blur-sm border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">分类数量</p>
                  <p className="text-2xl font-bold text-white">
                    {loading ? '...' : stats?.totalGenres || 0}
                  </p>
                </div>
                <BarChart3 className="w-8 h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/70 backdrop-blur-sm border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">近期导入</p>
                  <p className="text-2xl font-bold text-white">
                    {loading ? '...' : stats?.recentImports || 0}
                  </p>
                </div>
                <Download className="w-8 h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 管理功能区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 数据导入面板 */}
          <Card className="bg-slate-800/70 backdrop-blur-sm border-slate-700/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <Database className="w-5 h-5" />
                数据管理
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ImportPanel />
            </CardContent>
          </Card>

          {/* 系统设置 */}
          <Card className="bg-slate-800/70 backdrop-blur-sm border-slate-700/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <Settings className="w-5 h-5" />
                系统设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-slate-300">
                <h4 className="font-medium mb-2">快速操作</h4>
                <div className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start text-slate-300 border-slate-600 hover:bg-slate-700"
                  >
                    <Database className="w-4 h-4 mr-2" />
                    数据库管理
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start text-slate-300 border-slate-600 hover:bg-slate-700"
                  >
                    <Users className="w-4 h-4 mr-2" />
                    用户管理
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start text-slate-300 border-slate-600 hover:bg-slate-700"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    系统配置
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import MovieCard from '@/components/MovieCard';
import {
  Package,
  Calendar,
  Users,
  Film
} from 'lucide-react';

interface Publisher {
  id: string;
  name: string;
  stats: {
    totalMovies: number;
    latestMovieDate?: string | null;
    topGenres: Array<{ id: string; name: string; count: number }>;
    topStars: Array<{ id: string; name: string; count: number }>;
    topDirectors: Array<{ id: string; name: string; count: number }>;
  };
  movies: Array<{
    id: string;
    title: string;
    localImg?: string | null;
    img?: string | null;
    date?: string | null;
    videoLength?: number | null;
    description?: string | null;
    createdAt: Date;
    updatedAt: Date;
    director?: { id: string; name: string } | null;
    producer?: { id: string; name: string } | null;
    publisher?: { id: string; name: string } | null;
    series?: { id: string; name: string } | null;
    genres: Array<{ id: string; name: string }>;
    stars: Array<{ id: string; name: string }>;
    magnets: Array<{
      id: string;
      link: string;
      isHD?: boolean | null;
      title?: string | null;
      size?: string | null;
      hasSubtitle?: boolean | null;
    }>;
    views?: number;
    likes?: number;
    favorites?: number;
  }>;
}

export default function PublisherDetailPage() {
  const params = useParams();
  const publisherId = params.id as string;
  
  const [publisher, setPublisher] = useState<Publisher | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // 加载发行商详情
  useEffect(() => {
    const fetchPublisherDetail = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/publishers/${publisherId}`);
        const data = await response.json();
        
        if (data.success) {
          setPublisher(data.data);
        } else {
          setError(data.error || '获取发行商信息失败');
        }
      } catch (err) {
        setError('网络请求失败');
        console.error('获取发行商详情失败:', err);
      } finally {
        setLoading(false);
      }
    };

    if (publisherId) {
      fetchPublisherDetail();
    }
  }, [publisherId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-white">加载中...</div>
      </div>
    );
  }

  if (error || !publisher) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-red-400">{error || '发行商不存在'}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* 发行商信息 - 简洁设计 */}
        <div className="mb-8">
          <div className="flex items-start gap-6">
            {/* 图标 - 方形占位符 */}
            <div className="flex-shrink-0">
              <div className="w-24 h-24 rounded-lg overflow-hidden bg-[#2a2a2a]">
                <div className="w-full h-full flex items-center justify-center">
                  <Package className="w-8 h-8 text-gray-500" />
                </div>
              </div>
            </div>

            {/* 基本信息 - 简洁设计 */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-white mb-3">
                    {publisher.name}
                  </h1>
                  <div className="flex items-center gap-4 text-gray-400 text-sm">
                    <span className="flex items-center gap-1">
                      <Film className="w-4 h-4" />
                      {publisher.stats.totalMovies} 部作品
                    </span>
                    {publisher.stats.latestMovieDate && (
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        最新: {publisher.stats.latestMovieDate}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* 热门合作演员 */}
              {publisher.stats.topStars.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-white mb-3">热门合作演员</h3>
                  <div className="flex flex-wrap gap-2">
                    {publisher.stats.topStars.slice(0, 8).map((star) => (
                      <Link
                        key={star.id}
                        href={`/stars/${star.id}`}
                        className="px-3 py-1 bg-[#333] text-gray-300 rounded text-sm hover:bg-[#444] transition-colors"
                      >
                        {star.name} ({star.count})
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* 热门合作导演 */}
              {publisher.stats.topDirectors.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-white mb-3">热门合作导演</h3>
                  <div className="flex flex-wrap gap-2">
                    {publisher.stats.topDirectors.slice(0, 6).map((director) => (
                      <Link
                        key={director.id}
                        href={`/directors/${director.id}`}
                        className="px-3 py-1 bg-[#333] text-gray-300 rounded text-sm hover:bg-[#444] transition-colors"
                      >
                        {director.name} ({director.count})
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* 热门分类 */}
              {publisher.stats.topGenres.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-white mb-3">热门分类</h3>
                  <div className="flex flex-wrap gap-2">
                    {publisher.stats.topGenres.slice(0, 8).map((genre) => (
                      <Link
                        key={genre.id}
                        href={`/genres/${genre.id}`}
                        className="px-3 py-1 bg-[#333] text-gray-300 rounded text-sm hover:bg-[#444] transition-colors"
                      >
                        #{genre.name} ({genre.count})
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 作品列表 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <Users className="w-5 h-5" />
            发行作品
          </h2>

          {/* 影片网格 - 使用MovieCard组件保持与首页一致 */}
          {publisher.movies.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {publisher.movies.map((movie) => (
                <MovieCard
                  key={movie.id}
                  movie={{
                    id: movie.id,
                    title: movie.title,
                    localImg: movie.localImg,
                    img: movie.img,
                    date: movie.date,
                    videoLength: movie.videoLength,
                    description: movie.description,
                    createdAt: movie.createdAt,
                    updatedAt: movie.updatedAt,
                    director: movie.director,
                    producer: movie.producer,
                    publisher: movie.publisher,
                    series: movie.series,
                    genres: movie.genres,
                    samples: [],
                    magnets: movie.magnets,
                    stars: movie.stars,
                    views: movie.views,
                    likes: movie.likes,
                    favorites: movie.favorites
                  }}
                  viewMode="grid"
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400">该发行商暂无作品</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

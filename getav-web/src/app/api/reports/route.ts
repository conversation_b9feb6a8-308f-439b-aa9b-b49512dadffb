import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import jwt from 'jsonwebtoken';
import { ReportType, ReportStatus } from '@prisma/client';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 提交举报
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { movieId, type, description } = body;

    // 验证必填字段
    if (!movieId || !type || !description) {
      return NextResponse.json({
        success: false,
        error: '缺少必填字段'
      }, { status: 400 });
    }

    // 验证举报类型并转换为数据库枚举值
    const typeMapping: { [key: string]: ReportType } = {
      'copyright': ReportType.COPYRIGHT,
      'inappropriate': ReportType.INAPPROPRIATE,
      'spam': ReportType.SPAM,
      'misleading': ReportType.MISLEADING,
      'violence': ReportType.VIOLENCE,
      'other': ReportType.OTHER
    };

    const dbType = typeMapping[type];
    if (!dbType) {
      return NextResponse.json({
        success: false,
        error: '无效的举报类型'
      }, { status: 400 });
    }

    // 验证影片是否存在
    const movie = await prisma.movie.findUnique({
      where: { id: movieId }
    });

    if (!movie) {
      return NextResponse.json({
        success: false,
        error: '影片不存在'
      }, { status: 404 });
    }

    // 获取用户信息（如果已登录）
    let userId = null;
    let userEmail = null;
    
    const token = request.cookies.get('auth-token')?.value;
    if (token) {
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as {
          userId: string;
          email: string;
          username: string;
        };
        userId = decoded.userId;
        userEmail = decoded.email;
      } catch {
        // 忽略token验证错误，允许匿名举报
        console.log('Token验证失败，使用匿名举报');
      }
    }

    // 获取IP地址
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';

    // 检查是否重复举报（同一用户或IP对同一影片的相同类型举报）
    const existingReport = await prisma.report.findFirst({
      where: {
        movieId,
        type: dbType,
        OR: [
          userId ? { userId } : {},
          { ipAddress: ip }
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    if (existingReport) {
      return NextResponse.json({
        success: false,
        error: '您已经举报过此内容'
      }, { status: 409 });
    }

    // 创建举报记录
    const report = await prisma.report.create({
      data: {
        movieId,
        type: dbType,
        description: description.trim(),
        userId,
        userEmail,
        ipAddress: ip,
        userAgent: request.headers.get('user-agent') || '',
        status: ReportStatus.PENDING
      }
    });

    return NextResponse.json({
      success: true,
      message: '举报已提交，我们会尽快处理',
      data: {
        reportId: report.id
      }
    });

  } catch (error) {
    console.error('提交举报失败:', error);
    return NextResponse.json({
      success: false,
      error: '提交举报失败'
    }, { status: 500 });
  }
}

// 获取举报列表（管理员功能）
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({
        success: false,
        error: '未登录'
      }, { status: 401 });
    }

    const decoded = jwt.verify(token, JWT_SECRET) as {
      userId: string;
      email: string;
      username: string;
    };

    // 这里应该检查用户是否为管理员
    // 暂时简化处理，后续可以添加角色系统
    console.log('管理员用户:', decoded.userId);
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || 'all';
    const type = searchParams.get('type') || 'all';

    const skip = (page - 1) * limit;

    // 构建查询条件
    const whereConditions: Record<string, unknown> = {};
    
    if (status !== 'all') {
      whereConditions.status = status;
    }
    
    if (type !== 'all') {
      whereConditions.type = type;
    }

    // 获取举报列表
    const [reports, total] = await Promise.all([
      prisma.report.findMany({
        where: whereConditions,
        include: {
          movie: {
            select: {
              id: true,
              title: true,
              localImg: true
            }
          },
          user: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.report.count({
        where: whereConditions
      })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        reports,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取举报列表失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取举报列表失败'
    }, { status: 500 });
  }
}

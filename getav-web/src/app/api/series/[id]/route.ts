import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';
import { convertBigIntToString } from '@/lib/bigint-converter';

/**
 * 获取系列详情API
 * GET /api/series/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const seriesId = resolvedParams.id;

    // 验证系列ID
    if (!seriesId || typeof seriesId !== 'string' || seriesId.trim().length === 0) {
      const error = ErrorFactory.createValidationError(
        'id',
        seriesId,
        'required',
        '系列ID不能为空'
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 400 }
      );
    }

    try {
      // 获取系列基本信息和相关影片
      const series = await prisma.series.findUnique({
        where: { id: seriesId },
        include: {
          movies: {
            include: {
              director: true,
              producer: true,
              publisher: true,
              series: true,
              stars: {
                include: {
                  star: true
                }
              },
              genres: {
                include: {
                  genre: true
                }
              },
              magnets: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      });

      if (!series) {
        const error = ErrorFactory.createError(
          ErrorCode.RECORD_NOT_FOUND,
          '系列不存在',
          `未找到ID为 ${seriesId} 的系列`
        );
        return NextResponse.json(
          ResponseFactory.error(error),
          { status: 404 }
        );
      }

      // 计算统计信息
      const totalMovies = series.movies.length;
      const latestMovieDate = series.movies.length > 0 
        ? series.movies[0].date 
        : null;

      // 统计热门分类
      const genreCount = new Map<string, { id: string; name: string; count: number }>();
      series.movies.forEach(movie => {
        movie.genres.forEach(movieGenre => {
          const genre = movieGenre.genre;
          if (genreCount.has(genre.id)) {
            genreCount.get(genre.id)!.count++;
          } else {
            genreCount.set(genre.id, {
              id: genre.id,
              name: genre.name,
              count: 1
            });
          }
        });
      });

      const topGenres = Array.from(genreCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计合作演员
      const starCount = new Map<string, { id: string; name: string; count: number }>();
      series.movies.forEach(movie => {
        movie.stars.forEach(movieStar => {
          const star = movieStar.star;
          if (starCount.has(star.id)) {
            starCount.get(star.id)!.count++;
          } else {
            starCount.set(star.id, {
              id: star.id,
              name: star.name,
              count: 1
            });
          }
        });
      });

      const topStars = Array.from(starCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计合作导演
      const directorCount = new Map<string, { id: string; name: string; count: number }>();
      series.movies.forEach(movie => {
        if (movie.director) {
          const director = movie.director;
          if (directorCount.has(director.id)) {
            directorCount.get(director.id)!.count++;
          } else {
            directorCount.set(director.id, {
              id: director.id,
              name: director.name,
              count: 1
            });
          }
        }
      });

      const topDirectors = Array.from(directorCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计制作商和发行商
      const producerCount = new Map<string, { id: string; name: string; count: number }>();
      const publisherCount = new Map<string, { id: string; name: string; count: number }>();
      
      series.movies.forEach(movie => {
        if (movie.producer) {
          const producer = movie.producer;
          if (producerCount.has(producer.id)) {
            producerCount.get(producer.id)!.count++;
          } else {
            producerCount.set(producer.id, {
              id: producer.id,
              name: producer.name,
              count: 1
            });
          }
        }
        
        if (movie.publisher) {
          const publisher = movie.publisher;
          if (publisherCount.has(publisher.id)) {
            publisherCount.get(publisher.id)!.count++;
          } else {
            publisherCount.set(publisher.id, {
              id: publisher.id,
              name: publisher.name,
              count: 1
            });
          }
        }
      });

      const topProducers = Array.from(producerCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
        
      const topPublishers = Array.from(publisherCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // 格式化影片数据并获取统计信息
      const formattedMovies = await Promise.all(
        series.movies.map(async (movie) => {
          // 获取统计数据
          const [likesCount, favoritesCount, viewsCount] = await Promise.all([
            prisma.like.count({
              where: { movieId: movie.id }
            }),
            prisma.favorite.count({
              where: { movieId: movie.id }
            }),
            prisma.view.count({
              where: { movieId: movie.id }
            })
          ]);

          return {
            id: movie.id,
            title: movie.title,
            localImg: movie.localImg,
            img: movie.img,
            date: movie.date,
            videoLength: movie.videoLength,
            description: movie.description,
            createdAt: movie.createdAt.toISOString(),
            updatedAt: movie.updatedAt.toISOString(),
            director: movie.director,
            producer: movie.producer,
            publisher: movie.publisher,
            series: movie.series,
            genres: movie.genres.map(mg => ({
              id: mg.genre.id,
              name: mg.genre.name
            })),
            stars: movie.stars.map(ms => ({
              id: ms.star.id,
              name: ms.star.name
            })),
            magnets: movie.magnets,
            // 添加统计数据
            views: viewsCount,
            likes: likesCount,
            favorites: favoritesCount
          };
        })
      );

      const responseData = {
        id: series.id,
        name: series.name,
        stats: {
          totalMovies,
          latestMovieDate,
          topGenres,
          topStars,
          topDirectors,
          topProducers,
          topPublishers
        },
        movies: formattedMovies
      };

      // 转换所有BigInt字段
      const convertedData = convertBigIntToString(responseData);

      return NextResponse.json(
        ResponseFactory.success(convertedData, '成功获取系列详情')
      );
    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      const error = ErrorFactory.fromError(
        dbError instanceof Error ? dbError : new Error('数据库查询异常'),
        ErrorCode.DATABASE_QUERY_ERROR,
        { seriesId }
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('获取系列详情失败:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('未知错误'),
      ErrorCode.UNKNOWN_ERROR
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

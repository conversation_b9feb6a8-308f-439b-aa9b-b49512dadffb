import { NextRequest, NextResponse } from 'next/server';
import { getCachedMovie } from '@/lib/cached-services';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const movieId = resolvedParams.id;

    // 验证影片ID
    if (!movieId || typeof movieId !== 'string' || movieId.trim().length === 0) {
      const error = ErrorFactory.createValidationError(
        'id',
        movieId,
        'required',
        '影片ID不能为空'
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 400 }
      );
    }

    try {
      // 使用带缓存的影片获取服务
      const movie = await getCachedMovie(movieId.trim());

      if (!movie) {
        const error = ErrorFactory.createError(
          ErrorCode.RECORD_NOT_FOUND,
          '影片不存在',
          `未找到ID为 ${movieId} 的影片`
        );
        return NextResponse.json(
          ResponseFactory.error(error),
          { status: 404 }
        );
      }

      return NextResponse.json(
        ResponseFactory.success(movie, '成功获取影片详情')
      );
    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      const error = ErrorFactory.fromError(
        dbError instanceof Error ? dbError : new Error('数据库查询异常'),
        ErrorCode.DATABASE_QUERY_ERROR,
        { movieId }
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('获取影片详情失败:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('未知错误'),
      ErrorCode.UNKNOWN_ERROR
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

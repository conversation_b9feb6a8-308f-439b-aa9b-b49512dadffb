import { NextRequest } from 'next/server';
import { getCachedMovies } from '@/lib/cached-services';
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON>,
  RequestParser,
  Validators,
} from '@/lib/api-utils';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';

// 影片列表参数类型定义
interface MoviesParams extends Record<string, unknown> {
  page?: number;
  limit?: number;
  category?: string;
  star?: string;
  sortBy?: string;
}

// 影片列表参数验证器
const moviesParamsValidators = {
  page: Validators.number(),
  limit: Validators.number(),
  category: Validators.string(),
  star: Validators.string(),
  sortBy: Validators.string(),
};

// 影片列表处理器
async function moviesHandler(request: NextRequest) {
  // 解析和验证请求参数
  const params = RequestParser.parseQuery<MoviesParams>(
    request,
    moviesParamsValidators
  );

  // 设置默认值和验证
  const page = params.page || 1;
  const limit = Math.min(params.limit || 20, 100); // 限制最大返回数量

  // 验证分页参数
  if (page < 1) {
    throw ErrorFactory.createValidationError(
      'page',
      page,
      'min_value',
      '页码必须大于0'
    );
  }

  if (limit < 1 || limit > 100) {
    throw ErrorFactory.createValidationError(
      'limit',
      limit,
      'range',
      '每页数量必须在1-100之间'
    );
  }

  try {
    // 使用带缓存的影片获取服务
    const result = await getCachedMovies(page, limit, {
      category: params.category,
      star: params.star,
      sortBy: params.sortBy
    });

    return ResponseFactory.success(
      result,
      `成功获取第 ${page} 页影片列表`
    );
  } catch (error) {
    console.error('获取影片列表失败:', error);
    throw ErrorFactory.fromError(
      error instanceof Error ? error : new Error('获取影片列表异常'),
      ErrorCode.DATABASE_QUERY_ERROR,
      { page, limit, category: params.category, star: params.star }
    );
  }
}

// 导出API处理器
export const GET = withApiHandler(moviesHandler, {
  rateLimit: 200, // 每分钟200次请求限制
});

/**
 * 定时任务状态管理API
 * 提供定时任务的查询和管理功能
 */

import { NextRequest } from 'next/server';
import { cronScheduler } from '@/lib/cron-scheduler';
import { logger } from '@/lib/logger';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-error-wrapper';
import { ErrorCode } from '@/lib/error-types';

// 强制使用Node.js运行时
export const runtime = 'nodejs';

/**
 * GET /api/cron/status - 获取所有定时任务状态
 */
export async function GET() {
  try {
    const jobsStatus = cronScheduler.getJobsStatus();
    
    return createSuccessResponse({
      jobs: jobsStatus,
      totalJobs: jobsStatus.length,
      runningJobs: jobsStatus.filter(job => job.running).length,
      timestamp: new Date().toISOString()
    }, '定时任务状态获取成功');

  } catch (error) {
    logger.error('获取定时任务状态失败', error as Error);
    return createErrorResponse(
      '获取定时任务状态失败',
      ErrorCode.UNKNOWN_ERROR,
      500
    );
  }
}

/**
 * POST /api/cron/status - 管理定时任务
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, jobName } = body;

    if (!action || !jobName) {
      return createErrorResponse(
        '缺少必要参数: action 和 jobName',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    let result = false;
    let message = '';

    switch (action) {
      case 'start':
        result = cronScheduler.startJob(jobName);
        message = result ? `任务 ${jobName} 已启动` : `任务 ${jobName} 不存在或启动失败`;
        break;

      case 'stop':
        result = cronScheduler.stopJob(jobName);
        message = result ? `任务 ${jobName} 已停止` : `任务 ${jobName} 不存在或停止失败`;
        break;

      default:
        return createErrorResponse(
          `不支持的操作: ${action}`,
          ErrorCode.VALIDATION_ERROR,
          400
        );
    }

    if (result) {
      logger.info(`定时任务操作成功`, {
        action,
        jobName,
        requestId: request.headers.get('x-request-id')
      });

      return createSuccessResponse({
        action,
        jobName,
        success: true,
        timestamp: new Date().toISOString()
      }, message);
    } else {
      return createErrorResponse(
        message,
        ErrorCode.NOT_FOUND,
        404
      );
    }

  } catch (error) {
    logger.error('定时任务管理操作失败', error as Error);
    return createErrorResponse(
      '定时任务管理操作失败',
      ErrorCode.UNKNOWN_ERROR,
      500
    );
  }
}

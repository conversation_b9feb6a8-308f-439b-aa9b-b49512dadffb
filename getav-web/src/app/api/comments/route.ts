import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { z } from 'zod';

// 创建评论的请求schema
const createCommentSchema = z.object({
  content: z.string().min(1).max(500),
  movieId: z.string().optional(),
  magnetId: z.string().optional(),
  userId: z.string().optional(), // 可选，支持匿名评论
  username: z.string().optional(), // 匿名用户名
});

// 获取评论的查询schema
const getCommentsSchema = z.object({
  movieId: z.string().optional(),
  magnetId: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content, movieId, magnetId, userId } = createCommentSchema.parse(body);

    // 验证至少有一个目标（影片或磁力链接）
    if (!movieId && !magnetId) {
      return NextResponse.json(
        { error: '必须指定影片ID或磁力链接ID' },
        { status: 400 }
      );
    }

    // 评论功能需要用户登录
    if (!userId) {
      return NextResponse.json(
        { error: '评论功能需要登录，请先注册或登录' },
        { status: 401 }
      );
    }

    const finalUserId = userId;

    // 创建评论
    const comment = await prisma.comment.create({
      data: {
        content,
        movieId,
        magnetId,
        userId: finalUserId,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            image: true,
          },
        },
        likes: {
          select: {
            type: true,
            userId: true,
          },
        },
      },
    });

    // 计算点赞统计
    const likesCount = comment.likes.filter(like => like.type === 'LIKE').length;
    const dislikesCount = comment.likes.filter(like => like.type === 'DISLIKE').length;

    const responseComment = {
      id: comment.id,
      text: comment.content,
      author: comment.user?.username || '匿名用户',
      time: comment.createdAt.toLocaleString(),
      likes: likesCount,
      dislikes: dislikesCount,
      userVote: null, // 新评论没有当前用户投票
    };

    return NextResponse.json(responseComment);
  } catch (error) {
    console.error('创建评论失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: '创建评论失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const { movieId, magnetId, page = '1', limit = '20' } = getCommentsSchema.parse({
      movieId: searchParams.get('movieId') || undefined,
      magnetId: searchParams.get('magnetId') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '20',
    });

    // 验证至少有一个目标
    if (!movieId && !magnetId) {
      return NextResponse.json(
        { error: '必须指定影片ID或磁力链接ID' },
        { status: 400 }
      );
    }

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // 查询评论
    const comments = await prisma.comment.findMany({
      where: {
        ...(movieId && { movieId }),
        ...(magnetId && { magnetId }),
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            image: true,
          },
        },
        likes: {
          select: {
            type: true,
            userId: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limitNum,
    });

    // 转换为前端格式
    const responseComments = comments.map(comment => {
      const likesCount = comment.likes.filter(like => like.type === 'LIKE').length;
      const dislikesCount = comment.likes.filter(like => like.type === 'DISLIKE').length;

      return {
        id: comment.id,
        text: comment.content,
        author: comment.user?.username || '匿名用户',
        time: comment.createdAt.toLocaleString(),
        likes: likesCount,
        dislikes: dislikesCount,
        userVote: null, // TODO: 根据当前用户ID判断投票状态
      };
    });

    return NextResponse.json(responseComments);
  } catch (error) {
    console.error('获取评论失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: '获取评论失败' },
      { status: 500 }
    );
  }
}

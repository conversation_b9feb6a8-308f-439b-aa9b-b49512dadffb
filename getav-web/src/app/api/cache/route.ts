import { NextRequest, NextResponse } from 'next/server';
import { cacheService } from '@/lib/cache-service';
import { CacheInvalidation } from '@/lib/cached-services';
import { isRedisConnected, CacheKeys } from '@/lib/redis';
import { cacheScheduler } from '@/lib/cache-scheduler';
import { logger } from '@/lib/logger';

/**
 * 缓存管理API
 * 提供缓存状态查询、清理等功能
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'stats':
        // 获取缓存统计信息
        const stats = await cacheService.getStats();
        const connected = await isRedisConnected();
        
        return NextResponse.json({
          success: true,
          data: {
            ...stats,
            connected,
            timestamp: new Date().toISOString()
          }
        });

      case 'health':
        // 健康检查
        const isConnected = await isRedisConnected();
        return NextResponse.json({
          success: true,
          data: {
            status: isConnected ? 'healthy' : 'disconnected',
            connected: isConnected,
            timestamp: new Date().toISOString()
          }
        });

      case 'scheduler':
        // 获取调度器状态
        const schedulerStatus = cacheScheduler.getStatus();
        return NextResponse.json({
          success: true,
          data: {
            ...schedulerStatus,
            timestamp: new Date().toISOString()
          }
        });

      default:
        return NextResponse.json({
          success: false,
          error: '无效的操作'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('缓存API错误:', error);
    return NextResponse.json({
      success: false,
      error: '缓存操作失败'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, target } = body;

    switch (action) {
      case 'clear':
        // 清理缓存
        switch (target) {
          case 'all':
            await CacheInvalidation.invalidateAll();
            break;
          case 'homepage':
            // 清空首页相关缓存
            await Promise.all([
              cacheService.deletePattern('movies:*'),
              cacheService.delete(CacheKeys.popularMovies()),
              cacheService.delete(CacheKeys.stats()),
              cacheService.deletePattern('search:*')
            ]);
            break;
          case 'movies':
            await cacheService.deletePattern('movie*');
            break;
          case 'search':
            await CacheInvalidation.invalidateSearch();
            break;
          case 'stars':
            await cacheService.deletePattern('star*');
            break;
          default:
            return NextResponse.json({
              success: false,
              error: '无效的清理目标'
            }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          message: `${target} 缓存已清理`
        });

      case 'invalidate':
        // 失效特定缓存
        const { type, id } = body;
        
        switch (type) {
          case 'movie':
            await CacheInvalidation.invalidateMovie(id);
            break;
          case 'star':
            await CacheInvalidation.invalidateStar(id);
            break;
          default:
            return NextResponse.json({
              success: false,
              error: '无效的失效类型'
            }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          message: `${type} ${id} 缓存已失效`
        });

      case 'scheduler':
        // 调度器操作
        const { operation, config } = body;

        switch (operation) {
          case 'start':
            cacheScheduler.start();
            logger.info('缓存调度器已启动');
            break;
          case 'stop':
            cacheScheduler.stop();
            logger.info('缓存调度器已停止');
            break;
          case 'trigger':
            await cacheScheduler.triggerWarmup();
            logger.info('手动触发缓存预热完成');
            break;
          case 'config':
            if (config) {
              cacheScheduler.updateConfig(config);
              logger.info('缓存调度器配置已更新', { config });
            }
            break;
          default:
            return NextResponse.json({
              success: false,
              error: '无效的调度器操作'
            }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          message: `调度器操作 ${operation} 执行成功`,
          data: cacheScheduler.getStatus()
        });

      default:
        return NextResponse.json({
          success: false,
          error: '无效的操作'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('缓存操作错误:', error);
    return NextResponse.json({
      success: false,
      error: '缓存操作失败'
    }, { status: 500 });
  }
}

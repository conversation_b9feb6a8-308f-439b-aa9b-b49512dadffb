/**
 * 缓存预热API端点
 * 提供手动触发和自动缓存预热功能
 */

import { NextRequest } from 'next/server';
import { cacheWarmer } from '@/lib/cache-warmer';
import { logger } from '@/lib/logger';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-error-wrapper';
import { ErrorCode } from '@/lib/error-types';

// 强制使用Node.js运行时
export const runtime = 'nodejs';

/**
 * GET /api/cache/warmup - 获取预热任务信息
 */
export async function GET() {
  try {
    const taskInfo = cacheWarmer.getTaskInfo();
    const totalEstimatedTime = taskInfo.reduce((sum, task) => sum + task.estimatedTime, 0);

    return createSuccessResponse({
      tasks: taskInfo,
      totalTasks: taskInfo.length,
      totalEstimatedTime,
      estimatedDuration: `${Math.ceil(totalEstimatedTime / 1000)}秒`
    }, '预热任务信息获取成功');

  } catch (error) {
    logger.error('获取预热任务信息失败', error as Error);
    return createErrorResponse('获取预热任务信息失败', ErrorCode.UNKNOWN_ERROR, 500);
  }
}

/**
 * POST /api/cache/warmup - 执行缓存预热
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { 
      maxConcurrency = 3, 
      timeout = 30000, 
      retryAttempts = 2,
      skipOnError = true 
    } = body;

    logger.info('开始手动缓存预热', {
      maxConcurrency,
      timeout,
      retryAttempts,
      skipOnError,
      requestId: request.headers.get('x-request-id')
    });

    // 创建自定义配置的预热器
    const customWarmer = new (await import('@/lib/cache-warmer')).CacheWarmer({
      maxConcurrency,
      timeout,
      retryAttempts,
      skipOnError
    });

    const result = await customWarmer.warmup();

    if (result.success) {
      logger.info('手动缓存预热成功', {
        completedTasks: result.completedTasks,
        duration: result.duration,
        failedTasks: result.failedTasks
      });

      return createSuccessResponse({
        ...result,
        message: `预热完成：${result.completedTasks}/${result.totalTasks} 个任务成功`
      }, '缓存预热执行成功');
    } else {
      logger.warn('缓存预热部分失败', {
        completedTasks: result.completedTasks,
        failedTasks: result.failedTasks,
        errors: result.errors
      });

      return createSuccessResponse({
        ...result,
        message: `预热部分完成：${result.completedTasks}/${result.totalTasks} 个任务成功，${result.failedTasks} 个失败`
      }, '缓存预热部分完成', 206); // 206 Partial Content
    }

  } catch (error) {
    logger.error('缓存预热执行失败', error as Error);
    return createErrorResponse('缓存预热执行失败', ErrorCode.UNKNOWN_ERROR, 500);
  }
}

/**
 * DELETE /api/cache/warmup - 清空所有缓存
 */
export async function DELETE(request: NextRequest) {
  try {
    const { redis } = await import('@/lib/redis');
    
    logger.info('开始清空所有缓存', {
      requestId: request.headers.get('x-request-id')
    });

    await redis.flushdb();

    logger.info('所有缓存已清空');

    return createSuccessResponse({
      cleared: true,
      timestamp: new Date().toISOString()
    }, '所有缓存已清空');

  } catch (error) {
    logger.error('清空缓存失败', error as Error);
    return createErrorResponse('清空缓存失败', ErrorCode.UNKNOWN_ERROR, 500);
  }
}

/**
 * 队列状态监控API
 * 提供队列状态查询和管理功能
 */

import { NextRequest } from 'next/server';
import { queueManager } from '@/lib/queue-manager';
import { QueueNames } from '@/lib/queue-config';
import { logger } from '@/lib/logger';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-error-wrapper';
import { ErrorCode } from '@/lib/error-types';

// 强制使用Node.js运行时
export const runtime = 'nodejs';

/**
 * GET /api/queue/status - 获取所有队列状态
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const queueName = url.searchParams.get('queue');

    // 如果指定了队列名称，只返回该队列状态
    if (queueName) {
      if (!Object.values(QueueNames).includes(queueName as typeof QueueNames[keyof typeof QueueNames])) {
        return createErrorResponse(
          `无效的队列名称: ${queueName}`,
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      const status = await queueManager.getQueueStatus(queueName);
      return createSuccessResponse(status, `队列 ${queueName} 状态获取成功`);
    }

    // 获取所有队列状态
    const allStatuses = await Promise.all(
      Object.values(QueueNames).map(async (name) => {
        try {
          return await queueManager.getQueueStatus(name);
        } catch (error) {
          logger.error(`获取队列 ${name} 状态失败`, error as Error);
          return {
            name,
            error: (error as Error).message,
            counts: {
              waiting: 0,
              active: 0,
              completed: 0,
              failed: 0,
              delayed: 0,
            },
            jobs: {
              waiting: [],
              active: [],
              failed: [],
            }
          };
        }
      })
    );

    // 计算总体统计
    const totalCounts = allStatuses.reduce(
      (total, status) => {
        if (!('error' in status)) {
          total.waiting += status.counts.waiting;
          total.active += status.counts.active;
          total.completed += status.counts.completed;
          total.failed += status.counts.failed;
          total.delayed += status.counts.delayed;
        }
        return total;
      },
      { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 }
    );

    return createSuccessResponse({
      queues: allStatuses,
      summary: {
        totalQueues: Object.values(QueueNames).length,
        totalJobs: Object.values(totalCounts).reduce((sum, count) => sum + count, 0),
        counts: totalCounts,
        timestamp: new Date().toISOString(),
      }
    }, '所有队列状态获取成功');

  } catch (error) {
    logger.error('获取队列状态失败', error as Error);
    return createErrorResponse(
      '获取队列状态失败',
      ErrorCode.UNKNOWN_ERROR,
      500
    );
  }
}

/**
 * POST /api/queue/status - 队列管理操作
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, queueName, jobId } = body;

    if (!action) {
      return createErrorResponse(
        '缺少操作类型',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    switch (action) {
      case 'pause':
        // TODO: 实现暂停队列
        return createSuccessResponse(
          { action: 'pause', queueName },
          `队列 ${queueName} 已暂停`
        );

      case 'resume':
        // TODO: 实现恢复队列
        return createSuccessResponse(
          { action: 'resume', queueName },
          `队列 ${queueName} 已恢复`
        );

      case 'clear':
        // TODO: 实现清空队列
        return createSuccessResponse(
          { action: 'clear', queueName },
          `队列 ${queueName} 已清空`
        );

      case 'retry':
        // TODO: 实现重试失败任务
        return createSuccessResponse(
          { action: 'retry', queueName, jobId },
          `任务 ${jobId} 已重新加入队列`
        );

      default:
        return createErrorResponse(
          `不支持的操作: ${action}`,
          ErrorCode.VALIDATION_ERROR,
          400
        );
    }

  } catch (error) {
    logger.error('队列管理操作失败', error as Error);
    return createErrorResponse(
      '队列管理操作失败',
      ErrorCode.UNKNOWN_ERROR,
      500
    );
  }
}

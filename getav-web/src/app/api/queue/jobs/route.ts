/**
 * 队列任务管理API
 * 提供任务添加和管理功能
 */

import { NextRequest } from 'next/server';
import { queueManager } from '@/lib/queue-manager';
import { JobPriority } from '@/lib/queue-config';
import { logger } from '@/lib/logger';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-error-wrapper';
import { ErrorCode } from '@/lib/error-types';

// 强制使用Node.js运行时
export const runtime = 'nodejs';

/**
 * POST /api/queue/jobs - 添加队列任务
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, data, options = {} } = body;

    if (!type || !data) {
      return createErrorResponse(
        '缺少必要参数: type 和 data',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    let job;
    const requestId = request.headers.get('x-request-id');

    switch (type) {
      case 'data-import':
        // 数据导入任务
        job = await queueManager.addDataImportJob(data, {
          priority: options.priority || JobPriority.NORMAL,
          delay: options.delay || 0,
          attempts: options.attempts || 3,
        });

        logger.info('数据导入任务已添加到队列', {
          jobId: job.id,
          data,
          requestId
        });

        return createSuccessResponse({
          jobId: job.id,
          type: 'data-import',
          status: 'queued',
          data,
          options
        }, '数据导入任务已添加到队列');

      case 'image-processing':
        // 图片处理任务
        job = await queueManager.addImageProcessingJob(data, {
          priority: options.priority || JobPriority.NORMAL,
          delay: options.delay || 0,
        });

        logger.info('图片处理任务已添加到队列', {
          jobId: job.id,
          data,
          requestId
        });

        return createSuccessResponse({
          jobId: job.id,
          type: 'image-processing',
          status: 'queued',
          data,
          options
        }, '图片处理任务已添加到队列');

      case 'batch-import':
        // 批量导入任务
        const batchJobs = [];
        const { pages = [1], category, limit = 20 } = data;

        for (const page of pages) {
          const batchJob = await queueManager.addDataImportJob({
            page,
            limit,
            category,
            options: data.options
          }, {
            priority: options.priority || JobPriority.LOW,
            delay: (page - 1) * 1000, // 每页延迟1秒避免并发过高
          });

          batchJobs.push({
            jobId: batchJob.id,
            page,
            category
          });
        }

        logger.info('批量导入任务已添加到队列', {
          totalJobs: batchJobs.length,
          pages,
          category,
          requestId
        });

        return createSuccessResponse({
          type: 'batch-import',
          totalJobs: batchJobs.length,
          jobs: batchJobs,
          status: 'queued'
        }, `批量导入任务已添加到队列，共 ${batchJobs.length} 个任务`);

      default:
        return createErrorResponse(
          `不支持的任务类型: ${type}`,
          ErrorCode.VALIDATION_ERROR,
          400
        );
    }

  } catch (error) {
    logger.error('添加队列任务失败', error as Error);
    return createErrorResponse(
      '添加队列任务失败',
      ErrorCode.UNKNOWN_ERROR,
      500
    );
  }
}

/**
 * GET /api/queue/jobs - 获取任务详情
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const jobId = url.searchParams.get('jobId');
    const queueName = url.searchParams.get('queue');

    if (!jobId || !queueName) {
      return createErrorResponse(
        '缺少必要参数: jobId 和 queue',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    // TODO: 实现获取具体任务详情的逻辑
    // 这需要从BullMQ队列中获取任务信息

    return createSuccessResponse({
      jobId,
      queueName,
      status: 'pending',
      message: '任务详情获取功能待实现'
    }, '任务详情获取成功');

  } catch (error) {
    logger.error('获取任务详情失败', error as Error);
    return createErrorResponse(
      '获取任务详情失败',
      ErrorCode.UNKNOWN_ERROR,
      500
    );
  }
}

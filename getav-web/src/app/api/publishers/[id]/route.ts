import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';
import { convertBigIntToString } from '@/lib/bigint-converter';

/**
 * 获取发行商详情API
 * GET /api/publishers/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const publisherId = resolvedParams.id;

    // 验证发行商ID
    if (!publisherId || typeof publisherId !== 'string' || publisherId.trim().length === 0) {
      const error = ErrorFactory.createValidationError(
        'id',
        publisherId,
        'required',
        '发行商ID不能为空'
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 400 }
      );
    }

    try {
      // 获取发行商基本信息和相关影片
      const publisher = await prisma.publisher.findUnique({
        where: { id: publisherId },
        include: {
          movies: {
            include: {
              director: true,
              producer: true,
              publisher: true,
              series: true,
              stars: {
                include: {
                  star: true
                }
              },
              genres: {
                include: {
                  genre: true
                }
              },
              magnets: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      });

      if (!publisher) {
        const error = ErrorFactory.createError(
          ErrorCode.RECORD_NOT_FOUND,
          '发行商不存在',
          `未找到ID为 ${publisherId} 的发行商`
        );
        return NextResponse.json(
          ResponseFactory.error(error),
          { status: 404 }
        );
      }

      // 计算统计信息
      const totalMovies = publisher.movies.length;
      const latestMovieDate = publisher.movies.length > 0 
        ? publisher.movies[0].date 
        : null;

      // 统计热门分类
      const genreCount = new Map<string, { id: string; name: string; count: number }>();
      publisher.movies.forEach(movie => {
        movie.genres.forEach(movieGenre => {
          const genre = movieGenre.genre;
          if (genreCount.has(genre.id)) {
            genreCount.get(genre.id)!.count++;
          } else {
            genreCount.set(genre.id, {
              id: genre.id,
              name: genre.name,
              count: 1
            });
          }
        });
      });

      const topGenres = Array.from(genreCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计合作演员
      const starCount = new Map<string, { id: string; name: string; count: number }>();
      publisher.movies.forEach(movie => {
        movie.stars.forEach(movieStar => {
          const star = movieStar.star;
          if (starCount.has(star.id)) {
            starCount.get(star.id)!.count++;
          } else {
            starCount.set(star.id, {
              id: star.id,
              name: star.name,
              count: 1
            });
          }
        });
      });

      const topStars = Array.from(starCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计合作导演
      const directorCount = new Map<string, { id: string; name: string; count: number }>();
      publisher.movies.forEach(movie => {
        if (movie.director) {
          const director = movie.director;
          if (directorCount.has(director.id)) {
            directorCount.get(director.id)!.count++;
          } else {
            directorCount.set(director.id, {
              id: director.id,
              name: director.name,
              count: 1
            });
          }
        }
      });

      const topDirectors = Array.from(directorCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 格式化影片数据并获取统计信息
      const formattedMovies = await Promise.all(
        publisher.movies.map(async (movie) => {
          // 获取统计数据
          const [likesCount, favoritesCount, viewsCount] = await Promise.all([
            prisma.like.count({
              where: { movieId: movie.id }
            }),
            prisma.favorite.count({
              where: { movieId: movie.id }
            }),
            prisma.view.count({
              where: { movieId: movie.id }
            })
          ]);

          return {
            id: movie.id,
            title: movie.title,
            localImg: movie.localImg,
            img: movie.img,
            date: movie.date,
            videoLength: movie.videoLength,
            description: movie.description,
            createdAt: movie.createdAt.toISOString(),
            updatedAt: movie.updatedAt.toISOString(),
            director: movie.director,
            producer: movie.producer,
            publisher: movie.publisher,
            series: movie.series,
            genres: movie.genres.map(mg => ({
              id: mg.genre.id,
              name: mg.genre.name
            })),
            stars: movie.stars.map(ms => ({
              id: ms.star.id,
              name: ms.star.name
            })),
            magnets: movie.magnets,
            // 添加统计数据
            views: viewsCount,
            likes: likesCount,
            favorites: favoritesCount
          };
        })
      );

      const responseData = {
        id: publisher.id,
        name: publisher.name,
        stats: {
          totalMovies,
          latestMovieDate,
          topGenres,
          topStars,
          topDirectors
        },
        movies: formattedMovies
      };

      // 转换所有BigInt字段
      const convertedData = convertBigIntToString(responseData);

      return NextResponse.json(
        ResponseFactory.success(convertedData, '成功获取发行商详情')
      );
    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      const error = ErrorFactory.fromError(
        dbError instanceof Error ? dbError : new Error('数据库查询异常'),
        ErrorCode.DATABASE_QUERY_ERROR,
        { publisherId }
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('获取发行商详情失败:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('未知错误'),
      ErrorCode.UNKNOWN_ERROR
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

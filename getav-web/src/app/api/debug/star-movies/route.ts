import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

/**
 * 调试演员影片数据API
 * GET /api/debug/star-movies?starId=xhf
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const starId = searchParams.get('starId') || 'xhf';

    // 获取演员及其影片信息
    const star = await prisma.star.findUnique({
      where: { id: starId },
      include: {
        movies: {
          include: {
            movie: {
              select: {
                id: true,
                title: true,
                img: true,
                localImg: true,
                date: true,
                videoLength: true
              }
            }
          },
          take: 5 // 只取前5部影片用于调试
        }
      }
    });

    if (!star) {
      return NextResponse.json({
        success: false,
        error: '演员不存在'
      }, { status: 404 });
    }

    // 格式化影片数据
    const movies = star.movies.map(ms => ms.movie);

    return NextResponse.json({
      success: true,
      data: {
        starId: star.id,
        starName: star.name,
        starAvatar: star.localAvatar,
        totalMovies: star.movies.length,
        movies: movies
      }
    });
  } catch (error) {
    console.error('调试演员影片数据失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取演员影片数据失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

/**
 * 调试演员数据API
 * GET /api/debug/stars
 */
export async function GET() {
  try {
    // 获取前10个演员的数据，包括头像信息
    const stars = await prisma.star.findMany({
      take: 10,
      select: {
        id: true,
        name: true,
        avatar: true,
        localAvatar: true,
        _count: {
          select: {
            movies: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: stars,
      count: stars.length
    });
  } catch (error) {
    console.error('调试演员数据失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取演员数据失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

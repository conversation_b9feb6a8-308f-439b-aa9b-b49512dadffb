import { NextRequest } from 'next/server';
import { getCachedSearch } from '@/lib/cached-services';
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON>,
  RequestParser,
  Validators
} from '@/lib/api-utils';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';
import type { SearchResult } from '@/lib/search-service';

// 搜索参数类型定义
interface SearchParams extends Record<string, unknown> {
  q?: string; // 支持q参数
  query?: string;
  page?: number;
  limit?: number;
  category?: string;
  star?: string;
  sortBy?: 'relevance' | 'date' | 'title';
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
  videoLength?: string;
}

// 搜索参数验证器
const searchParamsValidators = {
  q: Validators.string(), // 支持q参数
  query: Validators.string(), // 也支持query参数
  page: Validators.number(),
  limit: Validators.number(),
  category: Validators.string(),
  star: Validators.string(),
  sortBy: Validators.enum(['relevance', 'date', 'title'] as const),
  sortOrder: Validators.enum(['asc', 'desc'] as const),
  dateFrom: Validators.string(),
  dateTo: Validators.string(),
  videoLength: Validators.string(),
};

// 搜索处理器
async function searchHandler(request: NextRequest) {
  // 解析和验证请求参数
  const params = RequestParser.parseQuery<SearchParams>(
    request,
    searchParamsValidators
  );

  // 设置默认值
  const searchOptions = {
    query: params.q || params.query || '', // 支持q和query参数
    page: params.page || 1,
    limit: Math.min(params.limit || 20, 100), // 限制最大返回数量
    category: params.category || '',
    star: params.star || '',
    sortBy: params.sortBy || 'relevance' as const,
    sortOrder: params.sortOrder || 'desc' as const,
    dateFrom: params.dateFrom,
    dateTo: params.dateTo,
    videoLength: params.videoLength,
  };

  // 验证搜索条件
  if (!searchOptions.query.trim() && !searchOptions.category && !searchOptions.star) {
    throw ErrorFactory.createError(
      ErrorCode.SEARCH_QUERY_INVALID,
      '请提供搜索关键词或筛选条件',
      '搜索需要至少一个查询条件：关键词、分类或演员'
    );
  }

  // 验证分页参数
  if (searchOptions.page < 1) {
    throw ErrorFactory.createValidationError(
      'page',
      searchOptions.page,
      'min_value',
      '页码必须大于0'
    );
  }

  if (searchOptions.limit < 1 || searchOptions.limit > 100) {
    throw ErrorFactory.createValidationError(
      'limit',
      searchOptions.limit,
      'range',
      '每页数量必须在1-100之间'
    );
  }

  try {
    // 执行搜索
    const searchResult = await getCachedSearch(searchOptions);

    return ResponseFactory.success<SearchResult>(
      searchResult,
      `找到 ${searchResult.total} 个结果`
    );
  } catch (error) {
    console.error('搜索服务错误:', error);
    throw ErrorFactory.fromError(
      error instanceof Error ? error : new Error('搜索服务异常'),
      ErrorCode.SEARCH_SERVICE_ERROR,
      { searchOptions }
    );
  }
}

// 导出API处理器
export const GET = withApiHandler(searchHandler, {
  rateLimit: 100, // 每分钟100次请求限制
});

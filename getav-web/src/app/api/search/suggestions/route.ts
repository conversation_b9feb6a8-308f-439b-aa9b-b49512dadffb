import { NextRequest, NextResponse } from 'next/server';
import { getSearchSuggestions } from '@/lib/search-service';

/**
 * 搜索建议API
 * GET /api/search/suggestions?q=keyword&limit=10
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!query.trim()) {
      return NextResponse.json({
        success: false,
        error: '请提供搜索关键词'
      }, { status: 400 });
    }

    if (limit < 1 || limit > 50) {
      return NextResponse.json({
        success: false,
        error: '建议数量必须在1-50之间'
      }, { status: 400 });
    }

    const suggestions = await getSearchSuggestions(query, limit);

    return NextResponse.json({
      success: true,
      data: {
        suggestions,
        query,
        count: suggestions.length
      }
    });

  } catch (error) {
    console.error('获取搜索建议失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取搜索建议失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

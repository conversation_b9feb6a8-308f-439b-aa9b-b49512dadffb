import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';
import { convertBigIntToString } from '@/lib/bigint-converter';

/**
 * 获取导演详情API
 * GET /api/directors/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const directorId = resolvedParams.id;

    // 验证导演ID
    if (!directorId || typeof directorId !== 'string' || directorId.trim().length === 0) {
      const error = ErrorFactory.createValidationError(
        'id',
        directorId,
        'required',
        '导演ID不能为空'
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 400 }
      );
    }

    try {
      // 获取导演基本信息和相关影片
      const director = await prisma.director.findUnique({
        where: { id: directorId },
        include: {
          movies: {
            include: {
              director: true,
              producer: true,
              publisher: true,
              series: true,
              stars: {
                include: {
                  star: true
                }
              },
              genres: {
                include: {
                  genre: true
                }
              },
              magnets: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      });

      if (!director) {
        const error = ErrorFactory.createError(
          ErrorCode.RECORD_NOT_FOUND,
          '导演不存在',
          `未找到ID为 ${directorId} 的导演`
        );
        return NextResponse.json(
          ResponseFactory.error(error),
          { status: 404 }
        );
      }

      // 计算统计信息
      const totalMovies = director.movies.length;
      const latestMovieDate = director.movies.length > 0 
        ? director.movies[0].date 
        : null;

      // 统计热门分类
      const genreCount = new Map<string, { id: string; name: string; count: number }>();
      director.movies.forEach(movie => {
        movie.genres.forEach(movieGenre => {
          const genre = movieGenre.genre;
          if (genreCount.has(genre.id)) {
            genreCount.get(genre.id)!.count++;
          } else {
            genreCount.set(genre.id, {
              id: genre.id,
              name: genre.name,
              count: 1
            });
          }
        });
      });

      const topGenres = Array.from(genreCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计合作演员
      const starCount = new Map<string, { id: string; name: string; count: number }>();
      director.movies.forEach(movie => {
        movie.stars.forEach(movieStar => {
          const star = movieStar.star;
          if (starCount.has(star.id)) {
            starCount.get(star.id)!.count++;
          } else {
            starCount.set(star.id, {
              id: star.id,
              name: star.name,
              count: 1
            });
          }
        });
      });

      const topStars = Array.from(starCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 格式化影片数据并获取统计信息
      const formattedMovies = await Promise.all(
        director.movies.map(async (movie) => {
          // 获取统计数据
          const [likesCount, favoritesCount, viewsCount] = await Promise.all([
            prisma.like.count({
              where: { movieId: movie.id }
            }),
            prisma.favorite.count({
              where: { movieId: movie.id }
            }),
            prisma.view.count({
              where: { movieId: movie.id }
            })
          ]);

          return {
            id: movie.id,
            title: movie.title,
            localImg: movie.localImg,
            img: movie.img,
            date: movie.date,
            videoLength: movie.videoLength,
            description: movie.description,
            createdAt: movie.createdAt.toISOString(),
            updatedAt: movie.updatedAt.toISOString(),
            director: movie.director,
            producer: movie.producer,
            publisher: movie.publisher,
            series: movie.series,
            genres: movie.genres.map(mg => ({
              id: mg.genre.id,
              name: mg.genre.name
            })),
            stars: movie.stars.map(ms => ({
              id: ms.star.id,
              name: ms.star.name
            })),
            magnets: movie.magnets,
            // 添加统计数据
            views: viewsCount,
            likes: likesCount,
            favorites: favoritesCount
          };
        })
      );

      const responseData = {
        id: director.id,
        name: director.name,
        stats: {
          totalMovies,
          latestMovieDate,
          topGenres,
          topStars
        },
        movies: formattedMovies
      };

      // 转换所有BigInt字段
      const convertedData = convertBigIntToString(responseData);

      return NextResponse.json(
        ResponseFactory.success(convertedData, '成功获取导演详情')
      );
    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      const error = ErrorFactory.fromError(
        dbError instanceof Error ? dbError : new Error('数据库查询异常'),
        ErrorCode.DATABASE_QUERY_ERROR,
        { directorId }
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('获取导演详情失败:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('未知错误'),
      ErrorCode.UNKNOWN_ERROR
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

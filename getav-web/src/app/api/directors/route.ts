import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';


interface DirectorsParams {
  page?: number;
  limit?: number;
  sort?: 'popular' | 'name' | 'latest' | 'movies';
  search?: string;
}

/**
 * 获取导演列表API
 * GET /api/directors?page=1&limit=20&sort=popular&search=keyword
 */
async function directors<PERSON><PERSON>ler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  // 解析和验证请求参数
  const page = parseInt(searchParams.get('page') || '1');
  const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
  const sort = (searchParams.get('sort') || 'popular') as DirectorsParams['sort'];
  const search = searchParams.get('search') || '';

  // 验证分页参数
  if (page < 1) {
    throw ErrorFactory.createValidationError(
      'page',
      page,
      'min_value',
      '页码必须大于0'
    );
  }

  if (limit < 1 || limit > 100) {
    throw ErrorFactory.createValidationError(
      'limit',
      limit,
      'range',
      '每页数量必须在1-100之间'
    );
  }

  try {
    const skip = (page - 1) * limit;

    // 构建搜索条件
    const whereConditions: {
      name?: {
        contains: string;
        mode: 'insensitive';
      };
    } = {};
    
    if (search.trim()) {
      whereConditions.name = {
        contains: search,
        mode: 'insensitive'
      };
    }

    // 构建排序条件
    let orderBy: { name?: 'asc'; id?: 'desc' } = {};
    let includeMovieCount = false;

    switch (sort) {
      case 'name':
        orderBy = { name: 'asc' };
        break;
      case 'latest':
        orderBy = { id: 'desc' };
        break;
      case 'movies':
      case 'popular':
      default:
        // 按影片数量排序（受欢迎程度）
        includeMovieCount = true;
        break;
    }

    let directors;
    let total;

    if (includeMovieCount) {
      // 需要按影片数量排序时，使用特殊查询
      const directorsWithCount = await prisma.director.findMany({
        where: whereConditions,
        include: {
          _count: {
            select: {
              movies: true
            }
          }
        }
      });

      // 按影片数量排序
      directorsWithCount.sort((a, b) => b._count.movies - a._count.movies);

      // 分页
      directors = directorsWithCount.slice(skip, skip + limit);
      total = directorsWithCount.length;

      // 添加影片数量
      directors = directors.map(director => ({
        ...director,
        movieCount: director._count.movies
      }));
    } else {
      // 普通查询
      [directors, total] = await Promise.all([
        prisma.director.findMany({
          where: whereConditions,
          orderBy,
          skip,
          take: limit,
          include: {
            _count: {
              select: {
                movies: true
              }
            }
          }
        }),
        prisma.director.count({
          where: whereConditions
        })
      ]);

      // 添加影片数量
      directors = directors.map(director => ({
        ...director,
        movieCount: director._count.movies
      }));
    }

    // 格式化响应数据
    const formattedDirectors = directors.map(director => ({
      id: director.id,
      name: director.name,
      movieCount: director.movieCount || 0
    }));

    return NextResponse.json(
      ResponseFactory.success({
        directors: formattedDirectors,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        sort,
        search
      }, `成功获取第 ${page} 页导演列表`)
    );

  } catch (error) {
    console.error('获取导演列表失败:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('获取导演列表异常'),
      ErrorCode.DATABASE_QUERY_ERROR,
      { page, limit, sort, search }
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

// 导出API处理器
export async function GET(request: NextRequest) {
  try {
    return await directorsHandler(request);
  } catch (error) {
    console.error('Directors API error:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('未知错误'),
      ErrorCode.UNKNOWN_ERROR
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

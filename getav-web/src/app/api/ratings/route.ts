import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { z } from 'zod';

// 评分请求验证schema
const ratingSchema = z.object({
  movieId: z.string().min(1, '影片ID不能为空'),
  score: z.number().int().min(1, '评分最低为1').max(10, '评分最高为10'),
  userId: z.string().optional(),
});

/**
 * 创建或更新评分
 * POST /api/ratings
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { movieId, score, userId } = ratingSchema.parse(body);

    // 评分功能需要用户登录
    if (!userId) {
      return NextResponse.json(
        { error: '评分功能需要登录，请先注册或登录' },
        { status: 401 }
      );
    }

    const finalUserId = userId;

    // 检查是否已评分
    const existingRating = await prisma.rating.findUnique({
      where: {
        userId_movieId: {
          userId: finalUserId,
          movieId,
        },
      },
    });

    if (existingRating) {
      // 更新评分
      const updatedRating = await prisma.rating.update({
        where: { id: existingRating.id },
        data: { score },
        include: {
          user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      
      return NextResponse.json({
        action: 'updated',
        rating: updatedRating,
        message: '评分已更新',
      });
    } else {
      // 创建新评分
      const newRating = await prisma.rating.create({
        data: {
          userId: finalUserId,
          movieId,
          score,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      
      return NextResponse.json({
        action: 'created',
        rating: newRating,
        message: '评分已提交',
      });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }
    
    console.error('评分操作失败:', error);
    return NextResponse.json(
      { error: '评分操作失败' },
      { status: 500 }
    );
  }
}

/**
 * 获取评分信息
 * GET /api/ratings?movieId=xxx&userId=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const movieId = searchParams.get('movieId');
    const userId = searchParams.get('userId');

    if (!movieId) {
      return NextResponse.json(
        { error: '必须提供影片ID' },
        { status: 400 }
      );
    }

    // 获取影片的平均评分和评分数量
    const ratingStats = await prisma.rating.aggregate({
      where: { movieId },
      _avg: { score: true },
      _count: { score: true },
    });

    // 获取用户的评分（如果已登录）
    let userRating = null;
    if (userId) {
      userRating = await prisma.rating.findUnique({
        where: {
          userId_movieId: {
            userId,
            movieId,
          },
        },
      });
    }

    return NextResponse.json({
      averageScore: ratingStats._avg.score ? Number(ratingStats._avg.score.toFixed(1)) : null,
      totalRatings: ratingStats._count.score,
      userScore: userRating?.score || null,
      hasRated: !!userRating,
    });
  } catch (error) {
    console.error('获取评分信息失败:', error);
    return NextResponse.json(
      { error: '获取评分信息失败' },
      { status: 500 }
    );
  }
}

/**
 * 删除评分
 * DELETE /api/ratings?movieId=xxx&userId=xxx
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const movieId = searchParams.get('movieId');
    const userId = searchParams.get('userId');

    if (!movieId || !userId) {
      return NextResponse.json(
        { error: '必须提供影片ID和用户ID' },
        { status: 400 }
      );
    }

    // 查找并删除评分
    const rating = await prisma.rating.findUnique({
      where: {
        userId_movieId: {
          userId,
          movieId,
        },
      },
    });

    if (!rating) {
      return NextResponse.json(
        { error: '评分不存在' },
        { status: 404 }
      );
    }

    await prisma.rating.delete({
      where: { id: rating.id },
    });

    return NextResponse.json({
      action: 'deleted',
      message: '评分已删除',
    });
  } catch (error) {
    console.error('删除评分失败:', error);
    return NextResponse.json(
      { error: '删除评分失败' },
      { status: 500 }
    );
  }
}

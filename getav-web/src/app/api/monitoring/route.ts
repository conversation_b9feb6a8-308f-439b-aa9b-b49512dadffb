/**
 * 系统监控API端点
 * 提供系统性能指标、错误统计和运行状态
 */

import { NextRequest } from 'next/server';

// 强制使用Node.js运行时
export const runtime = 'nodejs';
import { createSuccessResponse } from '@/lib/api-error-wrapper';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/database';
import { redis } from '@/lib/redis';

interface MonitoringData {
  system: {
    uptime: number;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: NodeJS.CpuUsage;
    loadAverage: number[];
  };
  database: {
    connectionStatus: 'connected' | 'disconnected';
    totalMovies: number;
    totalUsers: number;
    totalStars: number;
    recentActivity: {
      newMoviesToday: number;
      newUsersToday: number;
    };
  };
  cache: {
    connectionStatus: 'connected' | 'disconnected';
    memoryUsage: string;
    hitRate: number;
    totalKeys: number;
  };
  performance: {
    averageResponseTime: number;
    requestsPerMinute: number;
    errorRate: number;
  };
  errors: {
    last24Hours: number;
    byType: Record<string, number>;
    recent: Array<{
      timestamp: string;
      level: string;
      message: string;
      path?: string;
    }>;
  };
}

/**
 * 获取系统指标
 */
async function getSystemMetrics() {
  const memUsage = process.memoryUsage();
  
  return {
    uptime: process.uptime(),
    memory: {
      used: memUsage.rss,
      total: memUsage.rss + memUsage.heapTotal,
      percentage: (memUsage.rss / (memUsage.rss + memUsage.heapTotal)) * 100
    },
    cpu: process.cpuUsage(),
    loadAverage: process.platform === 'win32' ? [0, 0, 0] : (await import('os')).loadavg()
  };
}

/**
 * 获取数据库指标
 */
async function getDatabaseMetrics() {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const [
      totalMovies,
      totalUsers,
      totalStars,
      newMoviesToday,
      newUsersToday
    ] = await Promise.all([
      prisma.movie.count(),
      prisma.user.count(),
      prisma.star.count(),
      prisma.movie.count({
        where: {
          createdAt: {
            gte: today
          }
        }
      }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: today
          }
        }
      })
    ]);

    return {
      connectionStatus: 'connected' as const,
      totalMovies,
      totalUsers,
      totalStars,
      recentActivity: {
        newMoviesToday,
        newUsersToday
      }
    };
  } catch (error) {
    logger.error('获取数据库指标失败', error as Error);
    return {
      connectionStatus: 'disconnected' as const,
      totalMovies: 0,
      totalUsers: 0,
      totalStars: 0,
      recentActivity: {
        newMoviesToday: 0,
        newUsersToday: 0
      }
    };
  }
}

/**
 * 获取缓存指标
 */
async function getCacheMetrics() {
  try {
    const info = await redis.info('memory');
    const keyCount = await redis.dbsize();
    
    // 解析Redis内存信息
    const memoryLines = info.split('\r\n');
    const usedMemoryLine = memoryLines.find(line => line.startsWith('used_memory_human:'));
    const memoryUsage = usedMemoryLine ? usedMemoryLine.split(':')[1] : 'unknown';
    
    // 简单的命中率计算（实际应该基于统计数据）
    const hitRate = Math.random() * 20 + 80; // 模拟80-100%的命中率
    
    return {
      connectionStatus: 'connected' as const,
      memoryUsage,
      hitRate: Math.round(hitRate * 100) / 100,
      totalKeys: keyCount
    };
  } catch (error) {
    logger.error('获取缓存指标失败', error as Error);
    return {
      connectionStatus: 'disconnected' as const,
      memoryUsage: 'unknown',
      hitRate: 0,
      totalKeys: 0
    };
  }
}

/**
 * 获取性能指标
 */
async function getPerformanceMetrics() {
  // 这里应该从实际的性能监控数据中获取
  // 简化实现，返回模拟数据
  return {
    averageResponseTime: Math.random() * 100 + 50, // 50-150ms
    requestsPerMinute: Math.floor(Math.random() * 1000 + 100), // 100-1100 req/min
    errorRate: Math.random() * 5 // 0-5% 错误率
  };
}

/**
 * 获取错误统计
 */
async function getErrorMetrics() {
  // 这里应该从日志文件或错误跟踪系统中获取实际数据
  // 简化实现，返回模拟数据
  const errorTypes = ['ValidationError', 'DatabaseError', 'NetworkError', 'UnknownError'];
  const byType: Record<string, number> = {};
  
  errorTypes.forEach(type => {
    byType[type] = Math.floor(Math.random() * 10);
  });
  
  const recent = [
    {
      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
      level: 'ERROR',
      message: '数据库连接超时',
      path: '/api/movies'
    },
    {
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
      level: 'WARN',
      message: '缓存未命中',
      path: '/api/stars'
    },
    {
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      level: 'ERROR',
      message: '请求参数验证失败',
      path: '/api/search'
    }
  ];
  
  return {
    last24Hours: Object.values(byType).reduce((sum, count) => sum + count, 0),
    byType,
    recent
  };
}

/**
 * GET /api/monitoring - 获取系统监控数据
 */
export async function GET() {
  try {
    const startTime = Date.now();

    // 并行获取所有监控数据
    const [system, database, cache, performance, errors] = await Promise.all([
      getSystemMetrics(),
      getDatabaseMetrics(),
      getCacheMetrics(),
      getPerformanceMetrics(),
      getErrorMetrics()
    ]);

    const monitoringData: MonitoringData = {
      system,
      database,
      cache,
      performance,
      errors
    };

    const duration = Date.now() - startTime;

    logger.info('监控数据获取完成', {
      duration,
      databaseStatus: database.connectionStatus,
      cacheStatus: cache.connectionStatus,
      totalMovies: database.totalMovies,
      errorCount: errors.last24Hours
    });

    return createSuccessResponse(monitoringData, '监控数据获取成功');
  } catch (error) {
    logger.error('获取监控数据失败', error as Error);
    return createSuccessResponse(null, '获取监控数据失败', 500);
  }
}

/**
 * POST /api/monitoring/clear-cache - 清除缓存
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'clear-cache') {
      try {
        await redis.flushdb();

        logger.info('缓存已清除', {
          action: 'manual_cache_clear',
          admin: true
        });

        return createSuccessResponse(
          { cleared: true, timestamp: new Date().toISOString() },
          '缓存清除成功'
        );
      } catch (error) {
        logger.error('清除缓存失败', error as Error);
        throw error;
      }
    }

    return createSuccessResponse(null, '无效的操作');
  } catch (error) {
    logger.error('监控API POST请求失败', error as Error);
    return createSuccessResponse(null, '请求处理失败', 500);
  }
}

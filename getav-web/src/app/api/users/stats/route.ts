import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

/**
 * 获取用户统计数据
 * GET /api/users/stats?userId=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: '必须提供用户ID' },
        { status: 400 }
      );
    }

    // 并行获取用户各项统计数据
    const [
      favoritesCount,
      commentsCount,
      ratingsCount,
      averageRating,
    ] = await Promise.all([
      // 收藏数量
      prisma.favorite.count({
        where: { userId },
      }),

      // 评论数量
      prisma.comment.count({
        where: { userId },
      }),

      // 评分数量
      prisma.rating.count({
        where: { userId },
      }),

      // 平均评分
      prisma.rating.aggregate({
        where: { userId },
        _avg: { score: true },
      }),
    ]);

    return NextResponse.json({
      totalFavorites: favoritesCount,
      totalComments: commentsCount,
      totalRatings: ratingsCount,
      averageRating: averageRating._avg.score ? Number(averageRating._avg.score.toFixed(1)) : 0,
    });
  } catch (error) {
    console.error('获取用户统计数据失败:', error);
    return NextResponse.json(
      { error: '获取用户统计数据失败' },
      { status: 500 }
    );
  }
}

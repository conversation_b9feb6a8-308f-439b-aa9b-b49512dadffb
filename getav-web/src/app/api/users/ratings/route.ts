import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

/**
 * 获取用户评分历史
 * GET /api/users/ratings?userId=xxx&page=1&limit=20
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (!userId) {
      return NextResponse.json(
        { error: '必须提供用户ID' },
        { status: 400 }
      );
    }

    const skip = (page - 1) * limit;

    // 获取用户评分历史
    const [ratings, totalCount] = await Promise.all([
      prisma.rating.findMany({
        where: { userId },
        include: {
          movie: {
            select: {
              id: true,
              title: true,
              img: true,
              localImg: true,
              date: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),

      prisma.rating.count({
        where: { userId },
      }),
    ]);

    return NextResponse.json({
      ratings,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('获取用户评分历史失败:', error);
    return NextResponse.json(
      { error: '获取用户评分历史失败' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

/**
 * 获取用户评论历史
 * GET /api/users/comments?userId=xxx&page=1&limit=20
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (!userId) {
      return NextResponse.json(
        { error: '必须提供用户ID' },
        { status: 400 }
      );
    }

    const skip = (page - 1) * limit;

    // 获取用户评论历史
    const [comments, totalCount] = await Promise.all([
      prisma.comment.findMany({
        where: { userId },
        include: {
          movie: {
            select: {
              id: true,
              title: true,
            },
          },
          magnet: {
            select: {
              id: true,
              title: true,
              movie: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
          _count: {
            select: {
              likes: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),

      prisma.comment.count({
        where: { userId },
      }),
    ]);

    // 转换数据格式，添加点赞数
    const commentsWithLikes = comments.map(comment => ({
      ...comment,
      likes: comment._count.likes,
      _count: undefined, // 移除_count字段
    }));

    return NextResponse.json({
      comments: commentsWithLikes,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('获取用户评论历史失败:', error);
    return NextResponse.json(
      { error: '获取用户评论历史失败' },
      { status: 500 }
    );
  }
}

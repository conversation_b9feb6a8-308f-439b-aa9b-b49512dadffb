import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

/**
 * 获取用户收藏列表
 * GET /api/users/favorites?userId=xxx&page=1&limit=20
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (!userId) {
      return NextResponse.json(
        { error: '必须提供用户ID' },
        { status: 400 }
      );
    }

    const skip = (page - 1) * limit;

    // 获取用户收藏列表
    const [favorites, totalCount] = await Promise.all([
      prisma.favorite.findMany({
        where: { userId },
        include: {
          movie: {
            select: {
              id: true,
              title: true,
              img: true,
              localImg: true,
              date: true,
              videoLength: true,
              description: true,
              gid: true,
              uc: true,
              createdAt: true,
              updatedAt: true,
              _count: {
                select: {
                  views: true,
                  likes: {
                    where: { type: 'LIKE' }
                  },
                  favorites: true
                }
              }
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),

      prisma.favorite.count({
        where: { userId },
      }),
    ]);

    // 转换数据格式，添加统计数据
    const favoritesWithStats = favorites.map(favorite => ({
      ...favorite,
      movie: {
        ...favorite.movie,
        views: favorite.movie._count.views || 0,
        likes: favorite.movie._count.likes || 0,
        favorites: favorite.movie._count.favorites || 0,
      }
    }));

    return NextResponse.json({
      favorites: favoritesWithStats,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('获取用户收藏列表失败:', error);
    return NextResponse.json(
      { error: '获取用户收藏列表失败' },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

export async function GET() {
  try {
    // 获取统计数据
    const [
      totalMovies,
      totalStars,
      totalGenres,
      recentImports
    ] = await Promise.all([
      prisma.movie.count(),
      prisma.star.count(),
      prisma.genre.count(),
      prisma.movie.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
          }
        }
      })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        totalMovies,
        totalStars,
        totalGenres,
        recentImports
      }
    });

  } catch (error) {
    console.error('获取管理员统计数据失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取统计数据失败'
    }, { status: 500 });
  }
}

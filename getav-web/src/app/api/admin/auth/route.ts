import { NextRequest, NextResponse } from 'next/server';

const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'admin123';

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json();

    if (!password) {
      return NextResponse.json({
        success: false,
        error: '请输入密码'
      }, { status: 400 });
    }

    if (password === ADMIN_TOKEN) {
      return NextResponse.json({
        success: true,
        token: ADMIN_TOKEN,
        message: '登录成功'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: '密码错误'
      }, { status: 401 });
    }
  } catch (error) {
    console.error('管理员认证失败:', error);
    return NextResponse.json({
      success: false,
      error: '认证失败'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: false,
    error: '请使用POST方法'
  }, { status: 405 });
}

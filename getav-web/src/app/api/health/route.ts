/**
 * 健康检查API端点
 * 提供系统状态监控和诊断信息
 */

import { NextRequest, NextResponse } from 'next/server';

// 强制使用Node.js运行时
export const runtime = 'nodejs';
import { logger } from '@/lib/logger';
import { errorHandler } from '@/lib/error-handler';
import { prisma } from '@/lib/database';
import { redis } from '@/lib/redis';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: ServiceStatus;
    redis: ServiceStatus;
    filesystem: ServiceStatus;
  };
  metrics: {
    memory: MemoryMetrics;
    process: ProcessMetrics;
  };
  checks: HealthCheck[];
}

interface ServiceStatus {
  status: 'up' | 'down' | 'degraded';
  responseTime?: number;
  error?: string;
  lastCheck: string;
}

interface MemoryMetrics {
  used: number;
  total: number;
  percentage: number;
  heap: {
    used: number;
    total: number;
  };
}

interface ProcessMetrics {
  pid: number;
  uptime: number;
  cpuUsage: NodeJS.CpuUsage;
}

interface HealthCheck {
  name: string;
  status: 'pass' | 'fail' | 'warn';
  duration: number;
  message?: string;
  details?: Record<string, unknown>;
}

/**
 * 检查数据库连接
 */
async function checkDatabase(): Promise<ServiceStatus> {
  const startTime = Date.now();
  
  try {
    await prisma.$queryRaw`SELECT 1`;
    
    return {
      status: 'up',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString()
    };
  } catch (error) {
    logger.error('数据库健康检查失败', error as Error);
    
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
      error: (error as Error).message,
      lastCheck: new Date().toISOString()
    };
  }
}

/**
 * 检查Redis连接
 */
async function checkRedis(): Promise<ServiceStatus> {
  const startTime = Date.now();
  
  try {
    await redis.ping();
    
    return {
      status: 'up',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Redis健康检查失败', error as Error);
    
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
      error: (error as Error).message,
      lastCheck: new Date().toISOString()
    };
  }
}

/**
 * 检查文件系统
 */
async function checkFilesystem(): Promise<ServiceStatus> {
  const startTime = Date.now();
  
  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    // 检查关键目录
    const publicDir = path.join(process.cwd(), 'public');
    const imagesDir = path.join(publicDir, 'images');
    
    await fs.access(publicDir);
    await fs.access(imagesDir);
    
    return {
      status: 'up',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString()
    };
  } catch (error) {
    logger.error('文件系统健康检查失败', error as Error);
    
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
      error: (error as Error).message,
      lastCheck: new Date().toISOString()
    };
  }
}

/**
 * 获取内存指标
 */
function getMemoryMetrics(): MemoryMetrics {
  const memUsage = process.memoryUsage();
  
  return {
    used: memUsage.rss,
    total: memUsage.rss + memUsage.heapTotal,
    percentage: (memUsage.rss / (memUsage.rss + memUsage.heapTotal)) * 100,
    heap: {
      used: memUsage.heapUsed,
      total: memUsage.heapTotal
    }
  };
}

/**
 * 获取进程指标
 */
function getProcessMetrics(): ProcessMetrics {
  return {
    pid: process.pid,
    uptime: process.uptime(),
    cpuUsage: process.cpuUsage()
  };
}

/**
 * 执行详细健康检查
 */
async function performHealthChecks(): Promise<HealthCheck[]> {
  const checks: HealthCheck[] = [];
  
  // 数据库连接检查
  const dbStart = Date.now();
  try {
    const movieCount = await prisma.movie.count();
    checks.push({
      name: 'database_connectivity',
      status: 'pass',
      duration: Date.now() - dbStart,
      details: { movieCount }
    });
  } catch (error) {
    checks.push({
      name: 'database_connectivity',
      status: 'fail',
      duration: Date.now() - dbStart,
      message: (error as Error).message
    });
  }
  
  // Redis连接检查
  const redisStart = Date.now();
  try {
    const info = await redis.info('server');
    checks.push({
      name: 'redis_connectivity',
      status: 'pass',
      duration: Date.now() - redisStart,
      details: { serverInfo: info.split('\r\n')[1] }
    });
  } catch (error) {
    checks.push({
      name: 'redis_connectivity',
      status: 'fail',
      duration: Date.now() - redisStart,
      message: (error as Error).message
    });
  }
  
  // 磁盘空间检查
  const diskStart = Date.now();
  try {
    const fs = await import('fs/promises');
    const stats = await fs.stat(process.cwd());
    checks.push({
      name: 'disk_space',
      status: 'pass',
      duration: Date.now() - diskStart,
      details: { 
        accessible: true,
        lastModified: stats.mtime
      }
    });
  } catch (error) {
    checks.push({
      name: 'disk_space',
      status: 'fail',
      duration: Date.now() - diskStart,
      message: (error as Error).message
    });
  }
  
  return checks;
}

/**
 * 确定整体健康状态
 */
function determineOverallStatus(
  services: HealthStatus['services'],
  checks: HealthCheck[]
): 'healthy' | 'degraded' | 'unhealthy' {
  const serviceStatuses = Object.values(services);
  const failedChecks = checks.filter(check => check.status === 'fail');
  const warnChecks = checks.filter(check => check.status === 'warn');
  
  // 如果有关键服务宕机，状态为不健康
  if (serviceStatuses.some(service => service.status === 'down')) {
    return 'unhealthy';
  }
  
  // 如果有检查失败，状态为不健康
  if (failedChecks.length > 0) {
    return 'unhealthy';
  }
  
  // 如果有服务降级或警告，状态为降级
  if (serviceStatuses.some(service => service.status === 'degraded') || warnChecks.length > 0) {
    return 'degraded';
  }
  
  return 'healthy';
}

/**
 * GET /api/health - 基础健康检查
 */
export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();
    
    // 并行检查所有服务
    const [database, redisStatus, filesystem] = await Promise.all([
      checkDatabase(),
      checkRedis(),
      checkFilesystem()
    ]);
    
    const services = { database, redis: redisStatus, filesystem };
    const metrics = {
      memory: getMemoryMetrics(),
      process: getProcessMetrics()
    };
    
    // 执行详细检查（可选）
    const detailed = request.nextUrl.searchParams.get('detailed') === 'true';
    const checks = detailed ? await performHealthChecks() : [];
    
    const status = determineOverallStatus(services, checks);
    
    const healthStatus: HealthStatus = {
      status,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services,
      metrics,
      checks
    };
    
    // 记录健康检查日志
    const duration = Date.now() - startTime;
    logger.info(`健康检查完成: ${status}`, {
      duration,
      servicesUp: Object.values(services).filter(s => s.status === 'up').length,
      totalServices: Object.keys(services).length,
      checksRun: checks.length
    });
    
    // 根据状态返回适当的HTTP状态码
    const httpStatus = status === 'healthy' ? 200 : status === 'degraded' ? 200 : 503;
    
    return NextResponse.json(healthStatus, { status: httpStatus });
    
  } catch (error) {
    logger.error('健康检查失败', error as Error);
    
    return errorHandler.handleApiError(error as Error, {
      method: request.method,
      path: request.nextUrl.pathname,
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    });
  }
}

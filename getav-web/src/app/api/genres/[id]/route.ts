import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';
import { convertBigIntToString } from '@/lib/bigint-converter';

/**
 * 获取分类详情API
 * GET /api/genres/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const genreId = resolvedParams.id;

    // 验证分类ID
    if (!genreId || typeof genreId !== 'string' || genreId.trim().length === 0) {
      const error = ErrorFactory.createValidationError(
        'id',
        genreId,
        'required',
        '分类ID不能为空'
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 400 }
      );
    }

    try {
      // 获取分类基本信息和相关影片
      const genre = await prisma.genre.findUnique({
        where: { id: genreId },
        include: {
          movies: {
            include: {
              movie: {
                include: {
                  director: true,
                  producer: true,
                  publisher: true,
                  series: true,
                  stars: {
                    include: {
                      star: true
                    }
                  },
                  genres: {
                    include: {
                      genre: true
                    }
                  },
                  magnets: true
                }
              }
            },
            orderBy: {
              movie: {
                createdAt: 'desc'
              }
            }
          }
        }
      });

      if (!genre) {
        const error = ErrorFactory.createError(
          ErrorCode.RECORD_NOT_FOUND,
          '分类不存在',
          `未找到ID为 ${genreId} 的分类`
        );
        return NextResponse.json(
          ResponseFactory.error(error),
          { status: 404 }
        );
      }

      // 提取影片数据
      const movies = genre.movies.map(movieGenre => movieGenre.movie);

      // 计算统计信息
      const totalMovies = movies.length;
      const latestMovieDate = movies.length > 0 
        ? movies[0].date 
        : null;

      // 统计合作演员
      const starCount = new Map<string, { id: string; name: string; count: number }>();
      movies.forEach(movie => {
        movie.stars.forEach(movieStar => {
          const star = movieStar.star;
          if (starCount.has(star.id)) {
            starCount.get(star.id)!.count++;
          } else {
            starCount.set(star.id, {
              id: star.id,
              name: star.name,
              count: 1
            });
          }
        });
      });

      const topStars = Array.from(starCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计合作导演
      const directorCount = new Map<string, { id: string; name: string; count: number }>();
      movies.forEach(movie => {
        if (movie.director) {
          const director = movie.director;
          if (directorCount.has(director.id)) {
            directorCount.get(director.id)!.count++;
          } else {
            directorCount.set(director.id, {
              id: director.id,
              name: director.name,
              count: 1
            });
          }
        }
      });

      const topDirectors = Array.from(directorCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // 统计制作商和发行商
      const producerCount = new Map<string, { id: string; name: string; count: number }>();
      const publisherCount = new Map<string, { id: string; name: string; count: number }>();
      
      movies.forEach(movie => {
        if (movie.producer) {
          const producer = movie.producer;
          if (producerCount.has(producer.id)) {
            producerCount.get(producer.id)!.count++;
          } else {
            producerCount.set(producer.id, {
              id: producer.id,
              name: producer.name,
              count: 1
            });
          }
        }
        
        if (movie.publisher) {
          const publisher = movie.publisher;
          if (publisherCount.has(publisher.id)) {
            publisherCount.get(publisher.id)!.count++;
          } else {
            publisherCount.set(publisher.id, {
              id: publisher.id,
              name: publisher.name,
              count: 1
            });
          }
        }
      });

      const topProducers = Array.from(producerCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
        
      const topPublishers = Array.from(publisherCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // 统计系列
      const seriesCount = new Map<string, { id: string; name: string; count: number }>();
      movies.forEach(movie => {
        if (movie.series) {
          const series = movie.series;
          if (seriesCount.has(series.id)) {
            seriesCount.get(series.id)!.count++;
          } else {
            seriesCount.set(series.id, {
              id: series.id,
              name: series.name,
              count: 1
            });
          }
        }
      });

      const topSeries = Array.from(seriesCount.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // 格式化影片数据并获取统计信息
      const formattedMovies = await Promise.all(
        movies.map(async (movie) => {
          // 获取统计数据
          const [likesCount, favoritesCount, viewsCount] = await Promise.all([
            prisma.like.count({
              where: { movieId: movie.id }
            }),
            prisma.favorite.count({
              where: { movieId: movie.id }
            }),
            prisma.view.count({
              where: { movieId: movie.id }
            })
          ]);

          return {
            id: movie.id,
            title: movie.title,
            localImg: movie.localImg,
            img: movie.img,
            date: movie.date,
            videoLength: movie.videoLength,
            description: movie.description,
            createdAt: movie.createdAt.toISOString(),
            updatedAt: movie.updatedAt.toISOString(),
            director: movie.director,
            producer: movie.producer,
            publisher: movie.publisher,
            series: movie.series,
            genres: movie.genres.map(mg => ({
              id: mg.genre.id,
              name: mg.genre.name
            })),
            stars: movie.stars.map(ms => ({
              id: ms.star.id,
              name: ms.star.name
            })),
            magnets: movie.magnets,
            // 添加统计数据
            views: viewsCount,
            likes: likesCount,
            favorites: favoritesCount
          };
        })
      );

      const responseData = {
        id: genre.id,
        name: genre.name,
        stats: {
          totalMovies,
          latestMovieDate,
          topStars,
          topDirectors,
          topProducers,
          topPublishers,
          topSeries
        },
        movies: formattedMovies
      };

      // 转换所有BigInt字段
      const convertedData = convertBigIntToString(responseData);

      return NextResponse.json(
        ResponseFactory.success(convertedData, '成功获取分类详情')
      );
    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      const error = ErrorFactory.fromError(
        dbError instanceof Error ? dbError : new Error('数据库查询异常'),
        ErrorCode.DATABASE_QUERY_ERROR,
        { genreId }
      );
      return NextResponse.json(
        ResponseFactory.error(error),
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('获取分类详情失败:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('未知错误'),
      ErrorCode.UNKNOWN_ERROR
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

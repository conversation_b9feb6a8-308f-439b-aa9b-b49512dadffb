import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

interface WhereConditions {
  OR?: Array<{
    name?: { contains: string; mode: 'insensitive' };
    id?: { contains: string; mode: 'insensitive' };
  }>;
}



interface MovieData {
  id: string;
  title: string;
  localImg: string | null;
  date: string | null;
}

/**
 * 获取片商列表API
 * GET /api/studios?page=1&limit=20&sort=popular&search=keyword
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 解析参数
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const sort = (searchParams.get('sort') || 'popular') as 'popular' | 'name' | 'latest';
    const search = searchParams.get('search') || '';

    // 验证参数
    if (page < 1) {
      return NextResponse.json({
        success: false,
        error: '页码必须大于0'
      }, { status: 400 });
    }

    if (limit < 1 || limit > 100) {
      return NextResponse.json({
        success: false,
        error: '每页数量必须在1-100之间'
      }, { status: 400 });
    }

    const skip = (page - 1) * limit;

    // 构建查询条件
    const whereConditions: WhereConditions = {};
    if (search.trim()) {
      whereConditions.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { id: { contains: search, mode: 'insensitive' } }
      ];
    }

    // 注意：排序逻辑在后续的合并数据后进行

    // 获取所有制作商和发行商数据（不分页，先合并去重再分页）
    const [allProducers, allPublishers] = await Promise.all([
      prisma.producer.findMany({
        where: whereConditions,
        include: {
          _count: {
            select: {
              movies: true
            }
          },
          movies: {
            take: 3,
            select: {
              id: true,
              title: true,
              localImg: true,
              date: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      }),
      prisma.publisher.findMany({
        where: whereConditions,
        include: {
          _count: {
            select: {
              movies: true
            }
          },
          movies: {
            take: 3,
            select: {
              id: true,
              title: true,
              localImg: true,
              date: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      })
    ]);

    // 智能合并相同名称的制作商和发行商
    const studioMap = new Map<string, {
      id: string;
      name: string;
      types: ('producer' | 'publisher')[];
      movieCount: number;
      recentMovies: MovieData[];
      allMovies: Set<string>; // 用于去重
    }>();

    // 处理制作商
    allProducers.forEach(producer => {
      const key = producer.name.toLowerCase().trim();
      if (studioMap.has(key)) {
        const existing = studioMap.get(key)!;
        existing.types.push('producer');
        existing.movieCount += producer._count.movies;
        // 合并影片，去重
        producer.movies.forEach(movie => {
          if (!existing.allMovies.has(movie.id)) {
            existing.allMovies.add(movie.id);
            existing.recentMovies.push(movie);
          }
        });
      } else {
        studioMap.set(key, {
          id: producer.id,
          name: producer.name,
          types: ['producer'],
          movieCount: producer._count.movies,
          recentMovies: [...producer.movies],
          allMovies: new Set(producer.movies.map(m => m.id))
        });
      }
    });

    // 处理发行商
    allPublishers.forEach(publisher => {
      const key = publisher.name.toLowerCase().trim();
      if (studioMap.has(key)) {
        const existing = studioMap.get(key)!;
        if (!existing.types.includes('publisher')) {
          existing.types.push('publisher');
        }
        // 合并影片数量（去重后重新计算）
        publisher.movies.forEach(movie => {
          if (!existing.allMovies.has(movie.id)) {
            existing.allMovies.add(movie.id);
            existing.recentMovies.push(movie);
          }
        });
        // 重新计算总数
        existing.movieCount = existing.allMovies.size;
      } else {
        studioMap.set(key, {
          id: publisher.id,
          name: publisher.name,
          types: ['publisher'],
          movieCount: publisher._count.movies,
          recentMovies: [...publisher.movies],
          allMovies: new Set(publisher.movies.map(m => m.id))
        });
      }
    });

    // 转换为最终格式
    const studios = Array.from(studioMap.values()).map(studio => ({
      id: studio.id,
      name: studio.name,
      types: studio.types,
      movieCount: studio.movieCount,
      recentMovies: studio.recentMovies
        .sort((a, b) => new Date(b.date || '').getTime() - new Date(a.date || '').getTime())
        .slice(0, 6) // 取最新的6部影片用于封面展示
    }));

    // 重新排序合并后的数据
    if (sort === 'popular') {
      studios.sort((a, b) => b.movieCount - a.movieCount);
    } else if (sort === 'name' || sort === 'latest') {
      studios.sort((a, b) => a.name.localeCompare(b.name));
    }

    // 计算合并去重后的实际总数
    const total = studios.length;
    const totalPages = Math.ceil(total / limit);

    // 应用分页
    const paginatedStudios = studios.slice(skip, skip + limit);

    return NextResponse.json({
      success: true,
      data: {
        studios: paginatedStudios,
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('获取片商列表失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取片商列表失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

interface StudioDetail {
  id: string;
  name: string;
  types: ('producer' | 'publisher')[];
  movieCount: number;
  movies: Array<{
    id: string;
    title: string;
    localImg: string | null;
    img: string | null;
    date: string | null;
    videoLength: number | null;
    description: string | null;
    createdAt: Date;
    updatedAt: Date;
    director: { id: string; name: string } | null;
    producer: { id: string; name: string } | null;
    publisher: { id: string; name: string } | null;
    series: { id: string; name: string } | null;
    stars: Array<{ id: string; name: string; localAvatar: string | null }>;
    genres: Array<{ id: string; name: string }>;
    magnets: Array<{ id: string; link: string; title: string | null; size: string | null }>;
  }>;
}

/**
 * 获取片商详情API
 * GET /api/studios/[id]
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const studioId = params.id;

    if (!studioId) {
      return NextResponse.json({
        success: false,
        error: '片商ID不能为空'
      }, { status: 400 });
    }

    // 首先尝试作为制作商查找
    const producer = await prisma.producer.findUnique({
      where: { id: studioId },
      include: {
        _count: {
          select: {
            movies: true
          }
        },
        movies: {
          include: {
            director: {
              select: {
                id: true,
                name: true
              }
            },
            producer: {
              select: {
                id: true,
                name: true
              }
            },
            publisher: {
              select: {
                id: true,
                name: true
              }
            },
            series: {
              select: {
                id: true,
                name: true
              }
            },
            stars: {
              select: {
                star: {
                  select: {
                    id: true,
                    name: true,
                    localAvatar: true
                  }
                }
              }
            },
            genres: {
              select: {
                genre: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            magnets: {
              select: {
                id: true,
                link: true,
                title: true,
                size: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    // 然后尝试作为发行商查找
    const publisher = await prisma.publisher.findUnique({
      where: { id: studioId },
      include: {
        _count: {
          select: {
            movies: true
          }
        },
        movies: {
          include: {
            director: {
              select: {
                id: true,
                name: true
              }
            },
            producer: {
              select: {
                id: true,
                name: true
              }
            },
            publisher: {
              select: {
                id: true,
                name: true
              }
            },
            series: {
              select: {
                id: true,
                name: true
              }
            },
            stars: {
              select: {
                star: {
                  select: {
                    id: true,
                    name: true,
                    localAvatar: true
                  }
                }
              }
            },
            genres: {
              select: {
                genre: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            magnets: {
              select: {
                id: true,
                link: true,
                title: true,
                size: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    // 如果都没找到，返回404
    if (!producer && !publisher) {
      return NextResponse.json({
        success: false,
        error: '片商不存在'
      }, { status: 404 });
    }

    // 数据转换函数
    const transformMovie = (movie: {
      id: string;
      title: string;
      localImg: string | null;
      img: string | null;
      date: string | null;
      videoLength: number | null;
      description: string | null;
      createdAt: Date;
      updatedAt: Date;
      director: { id: string; name: string } | null;
      producer: { id: string; name: string } | null;
      publisher: { id: string; name: string } | null;
      series: { id: string; name: string } | null;
      stars: Array<{ star: { id: string; name: string; localAvatar: string | null } }>;
      genres: Array<{ genre: { id: string; name: string } }>;
      magnets: Array<{ id: string; link: string; title: string | null; size: string | null }>;
    }) => ({
      id: movie.id,
      title: movie.title,
      localImg: movie.localImg,
      img: movie.img,
      date: movie.date,
      videoLength: movie.videoLength,
      description: movie.description,
      createdAt: movie.createdAt,
      updatedAt: movie.updatedAt,
      director: movie.director,
      producer: movie.producer,
      publisher: movie.publisher,
      series: movie.series,
      stars: movie.stars.map((s) => ({
        id: s.star.id,
        name: s.star.name,
        localAvatar: s.star.localAvatar
      })),
      genres: movie.genres.map((g) => ({
        id: g.genre.id,
        name: g.genre.name
      })),
      magnets: movie.magnets
    });

    // 构建片商详情数据
    const studioDetail: StudioDetail = {
      id: studioId,
      name: '',
      types: [],
      movieCount: 0,
      movies: []
    };
    let studioName: string;
    let types: ('producer' | 'publisher')[] = [];
    let allMovies: Array<{
      id: string;
      title: string;
      localImg: string | null;
      img: string | null;
      date: string | null;
      videoLength: number | null;
      description: string | null;
      createdAt: Date;
      updatedAt: Date;
      director: { id: string; name: string } | null;
      producer: { id: string; name: string } | null;
      publisher: { id: string; name: string } | null;
      series: { id: string; name: string } | null;
      stars: Array<{ id: string; name: string; localAvatar: string | null }>;
      genres: Array<{ id: string; name: string }>;
      magnets: Array<{ id: string; link: string; title: string | null; size: string | null }>;
    }> = [];

    if (producer && publisher) {
      // 同时是制作商和发行商
      studioName = producer.name;
      types = ['producer', 'publisher'];

      // 合并影片，去重
      const movieMap = new Map();

      producer.movies.forEach(movie => {
        movieMap.set(movie.id, transformMovie(movie));
      });

      publisher.movies.forEach(movie => {
        if (!movieMap.has(movie.id)) {
          movieMap.set(movie.id, transformMovie(movie));
        }
      });

      allMovies = Array.from(movieMap.values());
    } else if (producer) {
      // 只是制作商
      studioName = producer.name;
      types = ['producer'];
      allMovies = producer.movies.map(transformMovie);
    } else if (publisher) {
      // 只是发行商
      studioName = publisher.name;
      types = ['publisher'];
      allMovies = publisher.movies.map(transformMovie);
    }

    // 更新 studioDetail 对象
    studioDetail.name = studioName!;
    studioDetail.types = types;
    studioDetail.movieCount = allMovies.length;
    studioDetail.movies = allMovies;

    return NextResponse.json({
      success: true,
      data: studioDetail
    });

  } catch (error) {
    console.error('获取片商详情失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取片商详情失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

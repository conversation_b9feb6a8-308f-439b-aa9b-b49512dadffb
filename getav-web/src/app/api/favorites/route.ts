import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { CacheInvalidation } from '@/lib/cached-services';
import { withApiHandler } from '@/lib/api-utils';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';
import type { ApiResponse } from '@/lib/error-types';
import { z } from 'zod';

// 收藏请求schema - 支持收藏列表
const favoriteSchema = z.object({
  movieId: z.string(),
  userId: z.string(),
  favoriteListId: z.string().optional(), // 收藏列表ID，可选
  action: z.enum(['add', 'remove', 'toggle']).default('toggle'), // 操作类型
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { movieId, userId, favoriteListId, action } = favoriteSchema.parse(body);

    // 收藏功能需要用户登录
    if (!userId) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '收藏功能需要登录，请先注册或登录'
        ),
        { status: 401 }
      );
    }

    // 如果没有指定收藏列表，获取或创建用户的默认列表
    let targetListId = favoriteListId;
    if (!targetListId) {
      let defaultList = await prisma.favoriteList.findFirst({
        where: {
          userId,
          isDefault: true
        }
      });

      // 如果没有默认列表，创建一个
      if (!defaultList) {
        defaultList = await prisma.favoriteList.create({
          data: {
            name: '稍后观看',
            description: '保存想要稍后观看的影片',
            userId,
            isDefault: true,
            isPublic: false
          }
        });
      }

      targetListId = defaultList.id;
    }

    // 检查是否已在指定列表中收藏
    const existingFavorite = await prisma.favorite.findFirst({
      where: {
        userId,
        movieId,
        favoriteListId: targetListId
      }
    });

    let resultAction: string;
    let message: string;

    if (action === 'add' || (action === 'toggle' && !existingFavorite)) {
      if (existingFavorite) {
        return NextResponse.json(
          ResponseFactory.simpleError(
            ErrorCode.VALIDATION_ERROR,
            '该影片已在此收藏列表中'
          ),
          { status: 400 }
        );
      }

      // 添加到收藏列表
      await prisma.favorite.create({
        data: {
          userId,
          movieId,
          favoriteListId: targetListId
        }
      });
      resultAction = 'favorited';
      message = '已添加到收藏列表';

    } else if (action === 'remove' || (action === 'toggle' && existingFavorite)) {
      if (!existingFavorite) {
        return NextResponse.json(
          ResponseFactory.simpleError(
            ErrorCode.VALIDATION_ERROR,
            '该影片不在此收藏列表中'
          ),
          { status: 400 }
        );
      }

      // 从收藏列表中移除
      await prisma.favorite.delete({
        where: { id: existingFavorite.id }
      });
      resultAction = 'unfavorited';
      message = '已从收藏列表中移除';
    } else {
      // 不应该到达这里，但为了类型安全添加默认情况
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '无效的操作类型'
        ),
        { status: 400 }
      );
    }

    // 获取更新后的收藏次数
    const totalFavorites = await prisma.favorite.count({
      where: { movieId }
    });

    // 清除电影缓存
    await CacheInvalidation.invalidateMovie(movieId);

    return NextResponse.json(
      ResponseFactory.success({
        action: resultAction,
        favoriteListId: targetListId,
        totalFavorites
      }, message)
    );

  } catch (error) {
    console.error('收藏操作失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '收藏操作失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 获取收藏状态API处理器 - 支持收藏列表
 */
async function getFavoriteStatusHandler(request: NextRequest): Promise<ApiResponse<{
  isFavorited: boolean;
  favoriteListId?: string;
  favoriteLists?: Array<{ id: string; name: string; isFavorited: boolean }>;
}>> {
  try {
    const { searchParams } = new URL(request.url);
    const movieId = searchParams.get('movieId');
    const userId = searchParams.get('userId');
    const includeAllLists = searchParams.get('includeAllLists') === 'true';

    if (!movieId) {
      return ResponseFactory.simpleError(
        ErrorCode.VALIDATION_ERROR,
        '必须提供影片ID'
      ) as ApiResponse<{ isFavorited: boolean; favoriteListId?: string; favoriteLists?: Array<{ id: string; name: string; isFavorited: boolean }> }>;
    }

    if (!userId) {
      return ResponseFactory.success({
        isFavorited: false,
      });
    }

    if (includeAllLists) {
      // 获取用户所有收藏列表及该影片在各列表中的状态
      const [userLists, userFavorites] = await Promise.all([
        prisma.favoriteList.findMany({
          where: { userId },
          orderBy: [
            { isDefault: 'desc' },
            { createdAt: 'asc' }
          ]
        }),
        prisma.favorite.findMany({
          where: {
            userId,
            movieId
          },
          include: {
            favoriteList: true
          }
        })
      ]);

      const favoriteListIds = new Set(userFavorites.map(f => f.favoriteListId).filter(Boolean));

      const favoriteLists = userLists.map(list => ({
        id: list.id,
        name: list.name,
        isFavorited: favoriteListIds.has(list.id)
      }));

      const isFavorited = userFavorites.length > 0;
      const primaryFavorite = userFavorites.find(f => f.favoriteList?.isDefault) || userFavorites[0];

      return ResponseFactory.success({
        isFavorited,
        favoriteListId: primaryFavorite?.favoriteListId || undefined,
        favoriteLists
      });

    } else {
      // 简单查询：检查是否收藏及主要收藏列表
      const favorite = await prisma.favorite.findFirst({
        where: {
          userId,
          movieId
        },
        include: {
          favoriteList: true
        },
        orderBy: {
          favoriteList: {
            isDefault: 'desc'
          }
        }
      });

      return ResponseFactory.success({
        isFavorited: !!favorite,
        favoriteListId: favorite?.favoriteListId || undefined
      });
    }

  } catch (error) {
    console.error('获取收藏状态失败:', error);
    return ResponseFactory.simpleError(
      ErrorCode.DATABASE_QUERY_ERROR,
      '获取收藏状态失败',
      error instanceof Error ? error.message : '未知错误'
    ) as ApiResponse<{ isFavorited: boolean; favoriteListId?: string; favoriteLists?: Array<{ id: string; name: string; isFavorited: boolean }> }>;
  }
}

// 导出API处理器
export const GET = withApiHandler(getFavoriteStatusHandler, {
  rateLimit: 200, // 每分钟200次请求限制
});

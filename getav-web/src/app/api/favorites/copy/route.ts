import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { CacheInvalidation } from '@/lib/cached-services';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';
import { z } from 'zod';

// 复制收藏请求schema
const copyFavoriteSchema = z.object({
  movieId: z.string(),
  userId: z.string(),
  fromListId: z.string().optional(), // 源列表ID，可为空
  toListId: z.string(), // 目标列表ID
});

/**
 * 复制收藏到其他列表
 * POST /api/favorites/copy
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { movieId, userId, fromListId, toListId } = copyFavoriteSchema.parse(body);

    // 验证目标收藏列表是否存在且属于用户
    const targetList = await prisma.favoriteList.findFirst({
      where: {
        id: toListId,
        userId
      }
    });

    if (!targetList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '目标收藏列表不存在或无权访问'
        ),
        { status: 404 }
      );
    }

    // 验证源收藏是否存在
    const sourceFavorite = await prisma.favorite.findFirst({
      where: {
        userId,
        movieId,
        favoriteListId: fromListId || null
      }
    });

    if (!sourceFavorite) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '源收藏不存在'
        ),
        { status: 404 }
      );
    }

    // 检查目标列表中是否已存在该收藏
    const targetFavorite = await prisma.favorite.findFirst({
      where: {
        userId,
        movieId,
        favoriteListId: toListId
      }
    });

    if (targetFavorite) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '该影片已在目标收藏列表中'
        ),
        { status: 400 }
      );
    }

    // 创建新的收藏记录
    await prisma.favorite.create({
      data: {
        userId,
        movieId,
        favoriteListId: toListId
      }
    });

    // 清除电影缓存
    await CacheInvalidation.invalidateMovie(movieId);

    return NextResponse.json(
      ResponseFactory.success({
        movieId,
        fromListId: fromListId || null,
        toListId,
        toListName: targetList.name
      }, `已将影片复制到"${targetList.name}"`)
    );

  } catch (error) {
    console.error('复制收藏失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '复制收藏失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 批量复制收藏到其他列表
 * PUT /api/favorites/copy
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const batchCopySchema = z.object({
      movieIds: z.array(z.string()).min(1, '至少需要一个影片ID'),
      userId: z.string(),
      fromListId: z.string().optional(),
      toListId: z.string(),
    });

    const { movieIds, userId, fromListId, toListId } = batchCopySchema.parse(body);

    // 验证目标收藏列表
    const targetList = await prisma.favoriteList.findFirst({
      where: {
        id: toListId,
        userId
      }
    });

    if (!targetList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '目标收藏列表不存在或无权访问'
        ),
        { status: 404 }
      );
    }

    // 查找源收藏记录
    const sourceFavorites = await prisma.favorite.findMany({
      where: {
        userId,
        movieId: { in: movieIds },
        favoriteListId: fromListId || null
      }
    });

    if (sourceFavorites.length === 0) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '在指定的源列表中未找到任何收藏'
        ),
        { status: 404 }
      );
    }

    // 检查目标列表中已存在的收藏
    const existingTargetFavorites = await prisma.favorite.findMany({
      where: {
        userId,
        movieId: { in: movieIds },
        favoriteListId: toListId
      }
    });

    const existingMovieIds = new Set(existingTargetFavorites.map(f => f.movieId));
    const validSourceFavorites = sourceFavorites.filter(f => !existingMovieIds.has(f.movieId));

    if (validSourceFavorites.length === 0) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '所有影片都已在目标收藏列表中'
        ),
        { status: 400 }
      );
    }

    // 批量创建新的收藏记录
    const newFavorites = validSourceFavorites.map(favorite => ({
      userId,
      movieId: favorite.movieId,
      favoriteListId: toListId
    }));

    await prisma.favorite.createMany({
      data: newFavorites
    });

    // 清除相关电影缓存
    for (const movieId of movieIds) {
      await CacheInvalidation.invalidateMovie(movieId);
    }

    return NextResponse.json(
      ResponseFactory.success({
        copiedCount: validSourceFavorites.length,
        skippedCount: existingMovieIds.size,
        totalRequested: movieIds.length,
        toListName: targetList.name
      }, `成功复制 ${validSourceFavorites.length} 个收藏到"${targetList.name}"${existingMovieIds.size > 0 ? `，${existingMovieIds.size} 个已存在` : ''}`)
    );

  } catch (error) {
    console.error('批量复制收藏失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '批量复制收藏失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

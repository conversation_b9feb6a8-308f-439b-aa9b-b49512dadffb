import { NextRequest } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/database';
import { withApiHandler } from '@/lib/api-utils';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';
import type { ApiResponse } from '@/lib/error-types';

// 批量查询收藏状态的请求schema
const batchFavoriteSchema = z.object({
  movieIds: z.array(z.string()).min(1).max(50), // 限制最多50个影片ID
  userId: z.string().optional()
});

/**
 * 批量获取收藏状态API处理器
 * 一次请求获取多个影片的收藏状态，减少数据库连接数
 */
async function batchFavoritesHandler(request: NextRequest): Promise<ApiResponse<Record<string, boolean>>> {
  const body = await request.json();
  const { movieIds, userId } = batchFavoriteSchema.parse(body);

  // 如果没有用户ID，返回所有影片都未收藏
  if (!userId) {
    const result = movieIds.reduce((acc, movieId) => {
      acc[movieId] = false;
      return acc;
    }, {} as Record<string, boolean>);

    return ResponseFactory.success(result);
  }

  try {
    // 批量查询收藏状态
    const favorites = await prisma.favorite.findMany({
      where: {
        userId,
        movieId: { in: movieIds }
      },
      select: {
        movieId: true
      }
    });

    // 创建收藏状态映射
    const favoriteMovieIds = new Set(favorites.map(fav => fav.movieId));
    const result = movieIds.reduce((acc, movieId) => {
      acc[movieId] = favoriteMovieIds.has(movieId);
      return acc;
    }, {} as Record<string, boolean>);

    return ResponseFactory.success(
      result,
      `成功获取 ${movieIds.length} 个影片的收藏状态`
    );

  } catch (error) {
    console.error('批量获取收藏状态失败:', error);
    return ResponseFactory.simpleError(
      ErrorCode.DATABASE_QUERY_ERROR,
      '获取收藏状态失败',
      error instanceof Error ? error.message : '未知错误'
    ) as ApiResponse<Record<string, boolean>>;
  }
}

// 导出API处理器
export const POST = withApiHandler(batchFavoritesHandler, {
  rateLimit: 100, // 每分钟100次请求限制
});

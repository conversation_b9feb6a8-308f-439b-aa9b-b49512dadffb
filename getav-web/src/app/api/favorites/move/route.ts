import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { CacheInvalidation } from '@/lib/cached-services';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';
import { z } from 'zod';

// 移动收藏请求schema
const moveFavoriteSchema = z.object({
  movieId: z.string(),
  userId: z.string(),
  fromListId: z.string().optional(), // 源列表ID，可为空（表示从无列表状态移动）
  toListId: z.string(), // 目标列表ID
});

/**
 * 移动收藏到其他列表
 * POST /api/favorites/move
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { movieId, userId, fromListId, toListId } = moveFavoriteSchema.parse(body);

    // 验证目标收藏列表是否存在且属于用户
    const targetList = await prisma.favoriteList.findFirst({
      where: {
        id: toListId,
        userId
      }
    });

    if (!targetList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '目标收藏列表不存在或无权访问'
        ),
        { status: 404 }
      );
    }

    // 如果指定了源列表，验证源列表
    if (fromListId) {
      const sourceList = await prisma.favoriteList.findFirst({
        where: {
          id: fromListId,
          userId
        }
      });

      if (!sourceList) {
        return NextResponse.json(
          ResponseFactory.simpleError(
            ErrorCode.NOT_FOUND,
            '源收藏列表不存在或无权访问'
          ),
          { status: 404 }
        );
      }
    }

    // 查找现有的收藏记录
    const existingFavorite = await prisma.favorite.findFirst({
      where: {
        userId,
        movieId,
        favoriteListId: fromListId || null
      }
    });

    if (!existingFavorite) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '在指定的源列表中未找到该收藏'
        ),
        { status: 404 }
      );
    }

    // 检查目标列表中是否已存在该收藏
    const targetFavorite = await prisma.favorite.findFirst({
      where: {
        userId,
        movieId,
        favoriteListId: toListId
      }
    });

    if (targetFavorite) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '该影片已在目标收藏列表中'
        ),
        { status: 400 }
      );
    }

    // 执行移动操作：更新收藏记录的列表ID
    await prisma.favorite.update({
      where: { id: existingFavorite.id },
      data: { favoriteListId: toListId }
    });

    // 清除电影缓存
    await CacheInvalidation.invalidateMovie(movieId);

    return NextResponse.json(
      ResponseFactory.success({
        movieId,
        fromListId: fromListId || null,
        toListId,
        fromListName: fromListId ? '原列表' : '未分类',
        toListName: targetList.name
      }, `已将影片移动到"${targetList.name}"`)
    );

  } catch (error) {
    console.error('移动收藏失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '移动收藏失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 批量移动收藏到其他列表
 * PUT /api/favorites/move
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const batchMoveSchema = z.object({
      movieIds: z.array(z.string()).min(1, '至少需要一个影片ID'),
      userId: z.string(),
      fromListId: z.string().optional(),
      toListId: z.string(),
    });

    const { movieIds, userId, fromListId, toListId } = batchMoveSchema.parse(body);

    // 验证目标收藏列表
    const targetList = await prisma.favoriteList.findFirst({
      where: {
        id: toListId,
        userId
      }
    });

    if (!targetList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '目标收藏列表不存在或无权访问'
        ),
        { status: 404 }
      );
    }

    // 查找需要移动的收藏记录
    const existingFavorites = await prisma.favorite.findMany({
      where: {
        userId,
        movieId: { in: movieIds },
        favoriteListId: fromListId || null
      }
    });

    if (existingFavorites.length === 0) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '在指定的源列表中未找到任何收藏'
        ),
        { status: 404 }
      );
    }

    // 检查目标列表中是否已存在这些收藏
    const targetFavorites = await prisma.favorite.findMany({
      where: {
        userId,
        movieId: { in: movieIds },
        favoriteListId: toListId
      }
    });

    const conflictMovieIds = targetFavorites.map(f => f.movieId);
    const validFavorites = existingFavorites.filter(f => !conflictMovieIds.includes(f.movieId));

    if (validFavorites.length === 0) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '所有影片都已在目标收藏列表中'
        ),
        { status: 400 }
      );
    }

    // 批量更新收藏记录
    const updateResult = await prisma.favorite.updateMany({
      where: {
        id: { in: validFavorites.map(f => f.id) }
      },
      data: { favoriteListId: toListId }
    });

    // 清除相关电影缓存
    for (const movieId of movieIds) {
      await CacheInvalidation.invalidateMovie(movieId);
    }

    return NextResponse.json(
      ResponseFactory.success({
        movedCount: updateResult.count,
        conflictCount: conflictMovieIds.length,
        totalRequested: movieIds.length,
        toListName: targetList.name
      }, `成功移动 ${updateResult.count} 个收藏到"${targetList.name}"${conflictMovieIds.length > 0 ? `，${conflictMovieIds.length} 个已存在` : ''}`)
    );

  } catch (error) {
    console.error('批量移动收藏失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '批量移动收藏失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';


interface ProducersParams {
  page?: number;
  limit?: number;
  sort?: 'popular' | 'name' | 'latest' | 'movies';
  search?: string;
}

/**
 * 获取制作商列表API
 * GET /api/producers?page=1&limit=20&sort=popular&search=keyword
 */
async function producersHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  // 解析和验证请求参数
  const page = parseInt(searchParams.get('page') || '1');
  const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
  const sort = (searchParams.get('sort') || 'popular') as ProducersParams['sort'];
  const search = searchParams.get('search') || '';

  // 验证分页参数
  if (page < 1) {
    throw ErrorFactory.createValidationError(
      'page',
      page,
      'min_value',
      '页码必须大于0'
    );
  }

  if (limit < 1 || limit > 100) {
    throw ErrorFactory.createValidationError(
      'limit',
      limit,
      'range',
      '每页数量必须在1-100之间'
    );
  }

  try {
    const skip = (page - 1) * limit;

    // 构建搜索条件
    const whereConditions: {
      name?: {
        contains: string;
        mode: 'insensitive';
      };
    } = {};
    
    if (search.trim()) {
      whereConditions.name = {
        contains: search,
        mode: 'insensitive'
      };
    }

    // 注意：排序逻辑在后续的合并数据后进行

    // 获取制作商和发行商数据，然后合并去重（与 /api/categories/stats 保持一致）
    const [allProducers, allPublishers] = await Promise.all([
      prisma.producer.findMany({
        where: search.trim() ? {
          name: {
            contains: search,
            mode: 'insensitive'
          }
        } : {},
        include: {
          _count: {
            select: {
              movies: true
            }
          }
        }
      }),
      prisma.publisher.findMany({
        where: search.trim() ? {
          name: {
            contains: search,
            mode: 'insensitive'
          }
        } : {},
        include: {
          _count: {
            select: {
              movies: true
            }
          }
        }
      })
    ]);

    // 合并并去重（按名称）
    const studioMap = new Map();

    // 添加制作商
    allProducers.forEach(producer => {
      if (producer.name && producer.name.trim()) {
        const existing = studioMap.get(producer.name);
        if (existing) {
          existing.movieCount += producer._count.movies;
          existing.types.push('producer');
        } else {
          studioMap.set(producer.name, {
            id: producer.id,
            name: producer.name,
            movieCount: producer._count.movies,
            types: ['producer']
          });
        }
      }
    });

    // 添加发行商
    allPublishers.forEach(publisher => {
      if (publisher.name && publisher.name.trim()) {
        const existing = studioMap.get(publisher.name);
        if (existing) {
          existing.movieCount += publisher._count.movies;
          if (!existing.types.includes('publisher')) {
            existing.types.push('publisher');
          }
        } else {
          studioMap.set(publisher.name, {
            id: publisher.id,
            name: publisher.name,
            movieCount: publisher._count.movies,
            types: ['publisher']
          });
        }
      }
    });

    // 转换为数组
    const allStudios = Array.from(studioMap.values());

    // 排序
    if (sort === 'name') {
      allStudios.sort((a, b) => a.name.localeCompare(b.name));
    } else if (sort === 'latest') {
      allStudios.sort((a, b) => b.id.localeCompare(a.id));
    } else {
      // 按影片数量排序（默认）
      allStudios.sort((a, b) => b.movieCount - a.movieCount);
    }

    // 分页
    const total = allStudios.length;
    const producers = allStudios.slice(skip, skip + limit);

    // 格式化响应数据
    const formattedProducers = producers.map(producer => ({
      id: producer.id,
      name: producer.name,
      movieCount: producer.movieCount || 0,
      types: producer.types || ['producer']
    }));

    return NextResponse.json(
      ResponseFactory.success({
        producers: formattedProducers,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        sort,
        search
      }, `成功获取第 ${page} 页制作商列表`)
    );

  } catch (error) {
    console.error('获取制作商列表失败:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('获取制作商列表异常'),
      ErrorCode.DATABASE_QUERY_ERROR,
      { page, limit, sort, search }
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

// 导出API处理器
export async function GET(request: NextRequest) {
  try {
    return await producersHandler(request);
  } catch (error) {
    console.error('Producers API error:', error);
    const apiError = ErrorFactory.fromError(
      error instanceof Error ? error : new Error('未知错误'),
      ErrorCode.UNKNOWN_ERROR
    );
    return NextResponse.json(
      ResponseFactory.error(apiError),
      { status: 500 }
    );
  }
}

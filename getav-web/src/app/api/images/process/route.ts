import { NextRequest, NextResponse } from 'next/server';
import { processImage, batchProcessImages, cleanupOldImages, type ImageType } from '@/lib/image-processor';
import path from 'path';
import fs from 'fs';

/**
 * 图片处理API路由
 * POST /api/images/process - 处理单个或批量图片
 * DELETE /api/images/process - 清理过期图片
 */

// 图片基础目录
const IMAGE_BASE_DIR = path.join(process.cwd(), 'public/images/getav');

/**
 * 处理图片请求
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...params } = body;

    switch (action) {
      case 'single':
        return await handleSingleImageProcess(params);
      case 'batch':
        return await handleBatchImageProcess(params);
      default:
        return NextResponse.json(
          { error: '无效的操作类型' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('图片处理API错误:', error);
    return NextResponse.json(
      { error: '图片处理失败', details: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    );
  }
}

/**
 * 清理过期图片
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const maxAge = parseInt(searchParams.get('maxAge') || '30') * 24 * 60 * 60 * 1000; // 默认30天
    const imageType = searchParams.get('type') as ImageType || 'cover';

    const imageDir = path.join(IMAGE_BASE_DIR, imageType);
    const deletedCount = await cleanupOldImages(imageDir, maxAge);

    return NextResponse.json({
      success: true,
      message: `清理完成，删除了 ${deletedCount} 个过期图片`,
      deletedCount
    });
  } catch (error) {
    console.error('清理图片失败:', error);
    return NextResponse.json(
      { error: '清理图片失败', details: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    );
  }
}

/**
 * 处理单个图片
 */
async function handleSingleImageProcess(params: {
  inputPath: string;
  imageType: ImageType;
  originalUrl: string;
  generateThumbnails?: boolean;
  outputFormat?: 'webp' | 'avif' | 'jpeg' | 'png';
  preserveOriginal?: boolean;
}) {
  const {
    inputPath,
    imageType,
    originalUrl,
    generateThumbnails = true,
    outputFormat = 'webp',
    preserveOriginal = true
  } = params;

  // 验证输入路径
  if (!inputPath || !fs.existsSync(inputPath)) {
    return NextResponse.json(
      { error: '输入文件不存在' },
      { status: 400 }
    );
  }

  // 验证图片类型
  if (!['cover', 'actress', 'samples'].includes(imageType)) {
    return NextResponse.json(
      { error: '无效的图片类型' },
      { status: 400 }
    );
  }

  try {
    const outputDir = path.join(IMAGE_BASE_DIR, imageType);
    const result = await processImage(
      inputPath,
      outputDir,
      imageType,
      originalUrl,
      {
        generateThumbnails,
        outputFormat,
        preserveOriginal
      }
    );

    // 转换为公共URL路径
    const publicPaths: { [key: string]: string } = {};
    for (const [size, localPath] of Object.entries(result.processedPaths)) {
      if (localPath) {
        const relativePath = path.relative(path.join(process.cwd(), 'public'), localPath);
        publicPaths[size] = '/' + relativePath.replace(/\\/g, '/');
      }
    }

    return NextResponse.json({
      success: true,
      result: {
        ...result,
        publicPaths
      }
    });
  } catch (error) {
    console.error('单个图片处理失败:', error);
    return NextResponse.json(
      { error: '图片处理失败', details: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    );
  }
}

/**
 * 批量处理图片
 */
async function handleBatchImageProcess(params: {
  imageList: Array<{
    inputPath: string;
    imageType: ImageType;
    originalUrl: string;
  }>;
  concurrency?: number;
  generateThumbnails?: boolean;
  outputFormat?: 'webp' | 'avif' | 'jpeg' | 'png';
}) {
  const {
    imageList,
    concurrency = 3,
    generateThumbnails = true,
    outputFormat = 'webp'
  } = params;

  // 验证输入
  if (!Array.isArray(imageList) || imageList.length === 0) {
    return NextResponse.json(
      { error: '图片列表不能为空' },
      { status: 400 }
    );
  }

  // 验证每个图片项
  for (const item of imageList) {
    if (!item.inputPath || !fs.existsSync(item.inputPath)) {
      return NextResponse.json(
        { error: `输入文件不存在: ${item.inputPath}` },
        { status: 400 }
      );
    }
    if (!['cover', 'actress', 'samples'].includes(item.imageType)) {
      return NextResponse.json(
        { error: `无效的图片类型: ${item.imageType}` },
        { status: 400 }
      );
    }
  }

  try {
    // 准备批量处理数据
    const batchData = imageList.map(item => ({
      ...item,
      outputDir: path.join(IMAGE_BASE_DIR, item.imageType)
    }));

    const results = await batchProcessImages(batchData, {
      concurrency,
      generateThumbnails,
      outputFormat
    });

    // 转换结果为公共URL路径
    const processedResults = results.map(result => {
      const publicPaths: { [key: string]: string } = {};
      for (const [size, localPath] of Object.entries(result.processedPaths)) {
        if (localPath) {
          const relativePath = path.relative(path.join(process.cwd(), 'public'), localPath);
          publicPaths[size] = '/' + relativePath.replace(/\\/g, '/');
        }
      }
      return {
        ...result,
        publicPaths
      };
    });

    return NextResponse.json({
      success: true,
      results: processedResults,
      summary: {
        total: imageList.length,
        processed: results.length,
        failed: imageList.length - results.length
      }
    });
  } catch (error) {
    console.error('批量图片处理失败:', error);
    return NextResponse.json(
      { error: '批量图片处理失败', details: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    );
  }
}

/**
 * 获取图片处理状态
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'stats') {
      // 获取各类型图片统计
      const stats = {
        cover: await getImageTypeStats('cover'),
        actress: await getImageTypeStats('actress'),
        samples: await getImageTypeStats('samples')
      };

      return NextResponse.json({
        success: true,
        stats
      });
    }

    return NextResponse.json(
      { error: '无效的查询参数' },
      { status: 400 }
    );
  } catch (error) {
    console.error('获取图片统计失败:', error);
    return NextResponse.json(
      { error: '获取图片统计失败', details: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    );
  }
}

/**
 * 获取指定类型图片的统计信息
 */
async function getImageTypeStats(imageType: ImageType) {
  const imageDir = path.join(IMAGE_BASE_DIR, imageType);
  
  if (!fs.existsSync(imageDir)) {
    return {
      count: 0,
      totalSize: 0,
      formats: {}
    };
  }

  const files = fs.readdirSync(imageDir);
  let totalSize = 0;
  const formats: { [key: string]: number } = {};

  for (const file of files) {
    const filePath = path.join(imageDir, file);
    const stats = fs.statSync(filePath);
    totalSize += stats.size;

    const ext = path.extname(file).toLowerCase().slice(1);
    formats[ext] = (formats[ext] || 0) + 1;
  }

  return {
    count: files.length,
    totalSize,
    formats
  };
}

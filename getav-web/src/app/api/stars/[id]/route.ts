import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

/**
 * 获取演员详情API
 * GET /api/stars/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const starId = resolvedParams.id;

    // 获取演员基本信息
    const star = await prisma.star.findUnique({
      where: { id: starId },
      include: {
        movies: {
          include: {
            movie: {
              include: {
                director: true,
                producer: true,
                publisher: true,
                series: true,
                genres: {
                  include: {
                    genre: true
                  }
                },
                samples: true,
                magnets: true
              }
            }
          },
          orderBy: {
            movie: {
              createdAt: 'desc'
            }
          }
        }
      }
    });

    if (!star) {
      return NextResponse.json({
        success: false,
        error: '演员不存在'
      }, { status: 404 });
    }

    // 计算统计信息
    const totalMovies = star.movies.length;
    const latestMovie = star.movies[0]?.movie;
    const genres = new Map<string, { id: string; name: string; count: number }>();
    
    // 统计分类信息
    star.movies.forEach(movieStar => {
      movieStar.movie.genres.forEach(movieGenre => {
        const genre = movieGenre.genre;
        if (genres.has(genre.id)) {
          genres.get(genre.id)!.count++;
        } else {
          genres.set(genre.id, {
            id: genre.id,
            name: genre.name,
            count: 1
          });
        }
      });
    });

    // 按出现次数排序分类
    const topGenres = Array.from(genres.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // 格式化影片数据
    const movies = star.movies.map(movieStar => {
      const movie = movieStar.movie;
      return {
        id: movie.id,
        title: movie.title,
        localImg: movie.localImg,
        img: movie.img,
        date: movie.date,
        videoLength: movie.videoLength,
        description: movie.description,
        createdAt: movie.createdAt,
        updatedAt: movie.updatedAt,
        director: movie.director,
        producer: movie.producer,
        publisher: movie.publisher,
        series: movie.series,
        genres: movie.genres.map(mg => ({
          id: mg.genre.id,
          name: mg.genre.name
        })),
        samples: movie.samples.map(sample => ({
          id: sample.id,
          alt: sample.alt,
          localSrc: sample.localSrc
        })),
        magnets: movie.magnets.map(magnet => ({
          id: magnet.id,
          link: magnet.link,
          isHD: magnet.isHD,
          title: magnet.title,
          size: magnet.size,
          hasSubtitle: magnet.hasSubtitle
        }))
      };
    });

    // 构建响应数据
    const responseData = {
      // 演员基本信息
      id: star.id,
      name: star.name,
      avatar: star.avatar,
      localAvatar: star.localAvatar,
      birthday: star.birthday,
      age: star.age,
      height: star.height,
      bust: star.bust,
      waist: star.waist,
      hip: star.hip,
      birthplace: star.birthplace,
      hobby: star.hobby,
      cupSize: star.cupSize,
      measurements: star.measurements,
      description: star.description,
      createdAt: star.createdAt,
      updatedAt: star.updatedAt,
      
      // 统计信息
      stats: {
        totalMovies,
        latestMovieDate: latestMovie?.date,
        topGenres
      },
      
      // 影片列表
      movies
    };

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('获取演员详情失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取演员详情失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}



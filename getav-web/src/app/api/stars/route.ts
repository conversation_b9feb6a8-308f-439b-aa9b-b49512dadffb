import { NextRequest } from 'next/server';
import { prisma } from '@/lib/database';
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON>,
  RequestParser,
  Validators,
} from '@/lib/api-utils';
import { ErrorCode, ErrorFactory, ResponseFactory } from '@/lib/error-types';

// 演员列表参数类型定义
interface StarsParams extends Record<string, unknown> {
  page?: number;
  limit?: number;
  sort?: 'popular' | 'name' | 'latest' | 'movies';
  search?: string;
  featured?: boolean;
}

// 演员列表参数验证器
const starsParamsValidators = {
  page: Validators.number(),
  limit: Validators.number(),
  sort: Validators.enum(['popular', 'name', 'latest', 'movies'] as const),
  search: Validators.string(),
  featured: Validators.boolean(),
};

/**
 * 获取演员列表API
 * GET /api/stars?page=1&limit=20&sort=popular&search=keyword
 */
async function starsHandler(request: NextRequest) {
  // 解析和验证请求参数
  const params = RequestParser.parseQuery<StarsParams>(
    request,
    starsParamsValidators
  );

  // 设置默认值和验证
  const page = params.page || 1;
  const limit = Math.min(params.limit || 20, 100); // 限制最大返回数量
  const sort = params.sort || 'popular';
  const search = params.search || '';
  const featured = params.featured || false;

  // 验证分页参数
  if (page < 1) {
    throw ErrorFactory.createValidationError(
      'page',
      page,
      'min_value',
      '页码必须大于0'
    );
  }

  if (limit < 1 || limit > 100) {
    throw ErrorFactory.createValidationError(
      'limit',
      limit,
      'range',
      '每页数量必须在1-100之间'
    );
  }

  try {
    // 如果是获取热门演员，使用专门的处理逻辑
    if (featured) {
      return await getFeaturedStars(limit);
    }

    const skip = (page - 1) * limit;

    // 构建搜索条件
    const whereConditions: {
      name?: {
        contains: string;
        mode: 'insensitive';
      };
    } = {};
    
    if (search.trim()) {
      whereConditions.name = {
        contains: search,
        mode: 'insensitive'
      };
    }

    // 构建排序条件
    let orderBy: { name?: 'asc'; createdAt?: 'desc' } = {};
    let includeMovieCount = false;

    switch (sort) {
      case 'name':
        orderBy = { name: 'asc' };
        break;
      case 'latest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'movies':
        // 按影片数量排序需要特殊处理
        includeMovieCount = true;
        break;
      case 'popular':
      default:
        // 按影片数量排序（受欢迎程度）
        includeMovieCount = true;
        break;
    }

    let stars;
    let total;

    if (includeMovieCount) {
      // 使用原生SQL查询以支持按影片数量排序
      let starsWithCount;
      let totalResult;

      if (search.trim()) {
        starsWithCount = await prisma.$queryRaw<Array<{
          id: string;
          name: string;
          avatar?: string | null;
          localAvatar?: string | null;
          birthday?: string | null;
          age?: string | null;
          height?: string | null;
          bust?: string | null;
          waist?: string | null;
          hip?: string | null;
          birthplace?: string | null;
          hobby?: string | null;
          cupSize?: string | null;
          measurements?: string | null;
          description?: string | null;
          createdAt: Date;
          updatedAt: Date;
          movie_count: bigint;
        }>>`
          SELECT
            s.*,
            COUNT(ms."movieId") as movie_count
          FROM "stars" s
          LEFT JOIN "movie_stars" ms ON s.id = ms."starId"
          WHERE s.name ILIKE ${'%' + search + '%'}
          GROUP BY s.id, s.name, s.avatar, s."localAvatar", s.birthday, s.age, s.height, s.bust, s.waist, s.hip, s.birthplace, s.hobby, s."cupSize", s.measurements, s.description, s."createdAt", s."updatedAt"
          ORDER BY movie_count DESC, s.name ASC
          LIMIT ${limit}
          OFFSET ${skip}
        `;

        totalResult = await prisma.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(DISTINCT s.id) as count
          FROM "stars" s
          WHERE s.name ILIKE ${'%' + search + '%'}
        `;
      } else {
        starsWithCount = await prisma.$queryRaw<Array<{
          id: string;
          name: string;
          avatar?: string | null;
          localAvatar?: string | null;
          birthday?: string | null;
          age?: string | null;
          height?: string | null;
          bust?: string | null;
          waist?: string | null;
          hip?: string | null;
          birthplace?: string | null;
          hobby?: string | null;
          cupSize?: string | null;
          measurements?: string | null;
          description?: string | null;
          createdAt: Date;
          updatedAt: Date;
          movie_count: bigint;
        }>>`
          SELECT
            s.*,
            COUNT(ms."movieId") as movie_count
          FROM "stars" s
          LEFT JOIN "movie_stars" ms ON s.id = ms."starId"
          GROUP BY s.id, s.name, s.avatar, s."localAvatar", s.birthday, s.age, s.height, s.bust, s.waist, s.hip, s.birthplace, s.hobby, s."cupSize", s.measurements, s.description, s."createdAt", s."updatedAt"
          ORDER BY movie_count DESC, s.name ASC
          LIMIT ${limit}
          OFFSET ${skip}
        `;

        totalResult = await prisma.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(DISTINCT s.id) as count
          FROM "stars" s
        `;
      }

      stars = starsWithCount.map(star => ({
        ...star,
        movieCount: Number(star.movie_count)
      }));

      total = Number(totalResult[0]?.count || 0);
    } else {
      // 普通查询
      [stars, total] = await Promise.all([
        prisma.star.findMany({
          where: whereConditions,
          orderBy,
          skip,
          take: limit,
          include: {
            _count: {
              select: {
                movies: true
              }
            }
          }
        }),
        prisma.star.count({
          where: whereConditions
        })
      ]);

      // 添加影片数量
      stars = stars.map(star => ({
        ...star,
        movieCount: star._count.movies
      }));
    }

    // 格式化响应数据
    const formattedStars = stars.map(star => ({
      id: star.id,
      name: star.name,
      avatar: star.avatar,
      localAvatar: star.localAvatar,
      birthday: star.birthday,
      age: star.age,
      height: star.height,
      bust: star.bust,
      waist: star.waist,
      hip: star.hip,
      birthplace: star.birthplace,
      hobby: star.hobby,
      cupSize: star.cupSize,
      measurements: star.measurements,
      description: star.description,
      createdAt: star.createdAt,
      updatedAt: star.updatedAt,
      movieCount: star.movieCount || 0
    }));

    return ResponseFactory.success({
      stars: formattedStars,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      sort,
      search
    }, `成功获取第 ${page} 页演员列表`);

  } catch (error) {
    console.error('获取演员列表失败:', error);
    throw ErrorFactory.fromError(
      error instanceof Error ? error : new Error('获取演员列表异常'),
      ErrorCode.DATABASE_QUERY_ERROR,
      { page, limit, sort, search }
    );
  }
}

/**
 * 获取热门演员（首页推荐）
 */
async function getFeaturedStars(limit: number = 12) {
  try {

    // 获取最受欢迎的演员（按影片数量和最近活跃度）
    const featuredStars = await prisma.$queryRaw<Array<{
      id: string;
      name: string;
      avatar?: string | null;
      localAvatar?: string | null;
      birthday?: string | null;
      age?: string | null;
      height?: string | null;
      bust?: string | null;
      waist?: string | null;
      hip?: string | null;
      birthplace?: string | null;
      hobby?: string | null;
      cupSize?: string | null;
      measurements?: string | null;
      description?: string | null;
      createdAt: Date;
      updatedAt: Date;
      movie_count: bigint;
      latest_movie_date?: Date | null;
    }>>`
      SELECT 
        s.*,
        COUNT(ms."movieId") as movie_count,
        MAX(m."createdAt") as latest_movie_date
      FROM "stars" s
      LEFT JOIN "movie_stars" ms ON s.id = ms."starId"
      LEFT JOIN "movies" m ON ms."movieId" = m.id
      WHERE s."localAvatar" IS NOT NULL
      GROUP BY s.id
      HAVING COUNT(ms."movieId") > 0
      ORDER BY 
        COUNT(ms."movieId") DESC,
        MAX(m."createdAt") DESC,
        s.name ASC
      LIMIT ${limit}
    `;

    const formattedStars = featuredStars.map(star => ({
      id: star.id,
      name: star.name,
      avatar: star.avatar,
      localAvatar: star.localAvatar,
      birthday: star.birthday,
      age: star.age,
      height: star.height,
      bust: star.bust,
      waist: star.waist,
      hip: star.hip,
      birthplace: star.birthplace,
      hobby: star.hobby,
      cupSize: star.cupSize,
      measurements: star.measurements,
      description: star.description,
      movieCount: Number(star.movie_count),
      latestMovieDate: star.latest_movie_date
    }));

    return ResponseFactory.success({
      stars: formattedStars,
      count: formattedStars.length
    }, '成功获取热门演员');

  } catch (error) {
    console.error('获取热门演员失败:', error);
    throw ErrorFactory.fromError(
      error instanceof Error ? error : new Error('获取热门演员异常'),
      ErrorCode.DATABASE_QUERY_ERROR,
      { limit }
    );
  }
}

// 导出API处理器
export const GET = withApiHandler(starsHandler, {
  rateLimit: 150, // 每分钟150次请求限制
});

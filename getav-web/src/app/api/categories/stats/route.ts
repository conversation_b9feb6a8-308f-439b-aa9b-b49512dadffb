import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 分类统计处理器
export async function GET() {
  try {
    // 并行查询各个分类的数量
    const [
      totalMovies,
      subtitleMovies,
      hdMovies,
      censoredMovies,
      uncensoredMovies,
      totalStudios,
      totalActresses
    ] = await Promise.all([
      // 总数
      prisma.movie.count(),
      
      // 中文字幕影片数量
      prisma.movie.count({
        where: {
          OR: [
            {
              magnets: {
                some: {
                  hasSubtitle: true
                }
              }
            },
            {
              title: {
                contains: '中文',
                mode: 'insensitive'
              }
            },
            {
              title: {
                contains: '字幕',
                mode: 'insensitive'
              }
            }
          ]
        }
      }),
      
      // 高清影片数量
      prisma.movie.count({
        where: {
          OR: [
            {
              magnets: {
                some: {
                  isHD: true
                }
              }
            },
            {
              title: {
                contains: 'HD',
                mode: 'insensitive'
              }
            },
            {
              title: {
                contains: '高清',
                mode: 'insensitive'
              }
            }
          ]
        }
      }),
      
      // 有码影片数量（排除无码模式）
      prisma.movie.count({
        where: {
          AND: [
            { NOT: { id: { startsWith: 'n' } } },
            { NOT: { id: { startsWith: 'heyzo' } } },
            { NOT: { id: { startsWith: 'xxx-av' } } },
            { NOT: { id: { startsWith: 'k' } } },
            { NOT: { id: { contains: 'carib' } } },
            { NOT: { id: { contains: 'pondo' } } },
            { NOT: { id: { contains: 'gachi' } } },
            { NOT: { id: { contains: '1pon' } } },
            { NOT: { id: { contains: 'mura' } } },
            { NOT: { id: { contains: 'siro' } } },
            { NOT: { id: { contains: 'fc2' } } }
          ]
        }
      }),
      
      // 无码影片数量
      prisma.movie.count({
        where: {
          OR: [
            { id: { startsWith: 'n' } },
            { id: { startsWith: 'heyzo' } },
            { id: { startsWith: 'xxx-av' } },
            { id: { startsWith: 'k' } },
            { id: { contains: 'carib' } },
            { id: { contains: 'pondo' } },
            { id: { contains: 'gachi' } },
            { id: { contains: '1pon' } },
            { id: { contains: 'mura' } },
            { id: { contains: 'siro' } },
            { id: { contains: 'fc2' } }
          ]
        }
      }),

      // 片商总数（制作商 + 发行商，去重）
      prisma.$queryRaw<[{count: bigint}]>`
        SELECT COUNT(DISTINCT name) as count
        FROM (
          SELECT name FROM "producers" WHERE name IS NOT NULL AND name != ''
          UNION
          SELECT name FROM "publishers" WHERE name IS NOT NULL AND name != ''
        ) AS combined_studios
      `.then(result => Number(result[0].count)),

      // 女优总数
      prisma.star.count()
    ]);

    const stats = {
      all: totalMovies,
      trending: totalMovies, // 暂时使用总数，后续可以添加热门算法
      latest: totalMovies,   // 暂时使用总数，后续可以添加最新筛选
      subtitle: subtitleMovies,
      hd: hdMovies,
      censored: censoredMovies,
      uncensored: uncensoredMovies,
      studios: totalStudios,
      actresses: totalActresses
    };

    return NextResponse.json({
      success: true,
      data: stats,
      message: '成功获取分类统计数据'
    });
  } catch (error) {
    console.error('获取分类统计失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取分类统计失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

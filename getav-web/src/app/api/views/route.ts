import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { CacheInvalidation } from '@/lib/cached-services';
import { z } from 'zod';

// 观看记录请求schema
const viewSchema = z.object({
  movieId: z.string(),
  userId: z.string().optional(), // 可选，支持匿名观看
});

/**
 * 记录观看次数
 * POST /api/views
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { movieId, userId } = viewSchema.parse(body);

    // 获取IP地址和User Agent用于匿名用户去重
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 检查电影是否存在
    const movie = await prisma.movie.findUnique({
      where: { id: movieId }
    });

    if (!movie) {
      return NextResponse.json(
        { error: '电影不存在' },
        { status: 404 }
      );
    }

    // 防重复计数机制
    if (userId) {
      // 已登录用户：检查是否已经观看过
      const existingView = await prisma.view.findUnique({
        where: {
          movieId_userId: {
            movieId,
            userId
          }
        }
      });

      if (existingView) {
        // 获取当前观看次数并返回
        const totalViews = await prisma.view.count({
          where: { movieId }
        });

        return NextResponse.json({
          success: true,
          message: '观看记录已存在',
          alreadyViewed: true,
          data: {
            totalViews
          }
        });
      }
    } else {
      // 匿名用户：基于IP和时间窗口防重复
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const recentView = await prisma.view.findFirst({
        where: {
          movieId,
          ipAddress,
          userId: null,
          createdAt: {
            gte: fiveMinutesAgo
          }
        }
      });

      if (recentView) {
        // 获取当前观看次数并返回
        const totalViews = await prisma.view.count({
          where: { movieId }
        });

        return NextResponse.json({
          success: true,
          message: '5分钟内已观看过此影片',
          alreadyViewed: true,
          data: {
            totalViews
          }
        });
      }
    }

    // 创建观看记录
    const view = await prisma.view.create({
      data: {
        movieId,
        userId,
        ipAddress,
        userAgent
      }
    });

    // 获取更新后的观看次数
    const totalViews = await prisma.view.count({
      where: { movieId }
    });

    // 清除该电影的缓存，确保下次获取时是最新数据
    await CacheInvalidation.invalidateMovie(movieId);

    return NextResponse.json({
      success: true,
      message: '观看记录已添加',
      data: {
        viewId: view.id,
        totalViews
      }
    });

  } catch (error) {
    console.error('记录观看次数失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 获取电影观看次数
 * GET /api/views?movieId=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const movieId = searchParams.get('movieId');

    if (!movieId) {
      return NextResponse.json(
        { error: '必须提供电影ID' },
        { status: 400 }
      );
    }

    // 获取观看次数
    const totalViews = await prisma.view.count({
      where: { movieId }
    });

    return NextResponse.json({
      success: true,
      data: {
        movieId,
        totalViews
      }
    });

  } catch (error) {
    console.error('获取观看次数失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

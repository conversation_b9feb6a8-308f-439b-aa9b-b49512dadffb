import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';
import { z } from 'zod';

// 初始化用户收藏列表请求schema
const initFavoriteListsSchema = z.object({
  userId: z.string(),
});

/**
 * 初始化用户的默认收藏列表
 * POST /api/favorite-lists/init
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = initFavoriteListsSchema.parse(body);

    // 检查用户是否已有默认收藏列表
    const existingDefaultList = await prisma.favoriteList.findFirst({
      where: {
        userId,
        isDefault: true
      }
    });

    if (existingDefaultList) {
      return NextResponse.json(
        ResponseFactory.success(
          {
            id: existingDefaultList.id,
            name: existingDefaultList.name,
            description: existingDefaultList.description,
            isDefault: existingDefaultList.isDefault,
            isPublic: existingDefaultList.isPublic,
            createdAt: existingDefaultList.createdAt,
            updatedAt: existingDefaultList.updatedAt
          },
          '默认收藏列表已存在'
        )
      );
    }

    // 创建默认收藏列表
    const defaultList = await prisma.favoriteList.create({
      data: {
        name: '稍后观看',
        description: '保存想要稍后观看的影片',
        userId,
        isDefault: true,
        isPublic: false
      }
    });

    // 将用户现有的没有分配到列表的收藏迁移到默认列表
    await prisma.favorite.updateMany({
      where: {
        userId,
        favoriteListId: null
      },
      data: {
        favoriteListId: defaultList.id
      }
    });

    return NextResponse.json(
      ResponseFactory.success(
        {
          id: defaultList.id,
          name: defaultList.name,
          description: defaultList.description,
          isDefault: defaultList.isDefault,
          isPublic: defaultList.isPublic,
          createdAt: defaultList.createdAt,
          updatedAt: defaultList.updatedAt
        },
        '默认收藏列表创建成功'
      ),
      { status: 201 }
    );

  } catch (error) {
    console.error('初始化默认收藏列表失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '初始化默认收藏列表失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 批量初始化所有用户的默认收藏列表（管理员功能）
 * PUT /api/favorite-lists/init
 */
export async function PUT() {
  try {
    // 获取所有没有默认收藏列表的用户
    const usersWithoutDefaultList = await prisma.user.findMany({
      where: {
        favoriteLists: {
          none: {
            isDefault: true
          }
        }
      },
      select: {
        id: true
      }
    });

    const results = [];

    // 为每个用户创建默认收藏列表
    for (const user of usersWithoutDefaultList) {
      try {
        const defaultList = await prisma.favoriteList.create({
          data: {
            name: '稍后观看',
            description: '保存想要稍后观看的影片',
            userId: user.id,
            isDefault: true,
            isPublic: false
          }
        });

        // 将用户现有的没有分配到列表的收藏迁移到默认列表
        const migratedCount = await prisma.favorite.updateMany({
          where: {
            userId: user.id,
            favoriteListId: null
          },
          data: {
            favoriteListId: defaultList.id
          }
        });

        results.push({
          userId: user.id,
          listId: defaultList.id,
          migratedFavorites: migratedCount.count,
          success: true
        });

      } catch (error) {
        console.error(`为用户 ${user.id} 创建默认列表失败:`, error);
        results.push({
          userId: user.id,
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return NextResponse.json(
      ResponseFactory.success(
        {
          total: usersWithoutDefaultList.length,
          success: successCount,
          failure: failureCount,
          results
        },
        `批量初始化完成：成功 ${successCount} 个，失败 ${failureCount} 个`
      )
    );

  } catch (error) {
    console.error('批量初始化默认收藏列表失败:', error);
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '批量初始化默认收藏列表失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

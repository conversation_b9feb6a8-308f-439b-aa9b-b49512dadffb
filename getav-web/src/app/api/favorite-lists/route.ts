import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';
import { z } from 'zod';

// 创建收藏列表请求schema
const createFavoriteListSchema = z.object({
  name: z.string().min(1, '列表名称不能为空').max(50, '列表名称不能超过50个字符'),
  description: z.string().max(200, '描述不能超过200个字符').optional(),
  isPublic: z.boolean().default(false),
  userId: z.string(),
});

// 这些schema在其他文件中使用，这里暂时注释掉避免ESLint警告
// const updateFavoriteListSchema = z.object({
//   name: z.string().min(1, '列表名称不能为空').max(50, '列表名称不能超过50个字符').optional(),
//   description: z.string().max(200, '描述不能超过200个字符').optional(),
//   isPublic: z.boolean().optional(),
// });

// const getFavoriteListsSchema = z.object({
//   userId: z.string(),
//   includeCount: z.boolean().default(true),
// });

/**
 * 获取用户收藏列表
 * GET /api/favorite-lists?userId=xxx&includeCount=true
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const includeCount = searchParams.get('includeCount') !== 'false';

    if (!userId) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '必须提供用户ID'
        ),
        { status: 400 }
      );
    }

    // 获取用户的收藏列表
    const favoriteLists = await prisma.favoriteList.findMany({
      where: { userId },
      include: includeCount ? {
        _count: {
          select: {
            favorites: true
          }
        }
      } : undefined,
      orderBy: [
        { isDefault: 'desc' }, // 默认列表排在前面
        { createdAt: 'asc' }   // 然后按创建时间排序
      ]
    });

    // 转换数据格式
    const formattedLists = favoriteLists.map(list => {
      const baseList = {
        id: list.id,
        name: list.name,
        description: list.description,
        isDefault: list.isDefault,
        isPublic: list.isPublic,
        createdAt: list.createdAt,
        updatedAt: list.updatedAt,
      };

      if (includeCount && '_count' in list) {
        const listWithCount = list as typeof list & { _count: { favorites: number } };
        return {
          ...baseList,
          count: listWithCount._count?.favorites || 0
        };
      }

      return baseList;
    });

    return NextResponse.json(
      ResponseFactory.success(
        formattedLists,
        `成功获取 ${formattedLists.length} 个收藏列表`
      )
    );

  } catch (error) {
    console.error('获取收藏列表失败:', error);
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '获取收藏列表失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 创建新收藏列表
 * POST /api/favorite-lists
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, isPublic, userId } = createFavoriteListSchema.parse(body);

    // 检查用户是否已有同名列表
    const existingList = await prisma.favoriteList.findUnique({
      where: {
        userId_name: {
          userId,
          name
        }
      }
    });

    if (existingList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '已存在同名的收藏列表'
        ),
        { status: 400 }
      );
    }

    // 创建收藏列表
    const favoriteList = await prisma.favoriteList.create({
      data: {
        name,
        description,
        isPublic,
        userId,
        isDefault: false // 新创建的列表不是默认列表
      },
      include: {
        _count: {
          select: {
            favorites: true
          }
        }
      }
    });

    return NextResponse.json(
      ResponseFactory.success(
        {
          id: favoriteList.id,
          name: favoriteList.name,
          description: favoriteList.description,
          isDefault: favoriteList.isDefault,
          isPublic: favoriteList.isPublic,
          createdAt: favoriteList.createdAt,
          updatedAt: favoriteList.updatedAt,
          count: favoriteList._count.favorites
        },
        '收藏列表创建成功'
      ),
      { status: 201 }
    );

  } catch (error) {
    console.error('创建收藏列表失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '创建收藏列表失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

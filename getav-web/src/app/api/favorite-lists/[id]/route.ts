import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';
import { z } from 'zod';

// 更新收藏列表请求schema
const updateFavoriteListSchema = z.object({
  name: z.string().min(1, '列表名称不能为空').max(50, '列表名称不能超过50个字符').optional(),
  description: z.string().max(200, '描述不能超过200个字符').optional(),
  isPublic: z.boolean().optional(),
  userId: z.string(), // 用于权限验证
});

/**
 * 获取单个收藏列表详情
 * GET /api/favorite-lists/[id]?userId=xxx
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const params = await context.params;
    const listId = params.id;

    if (!userId) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '必须提供用户ID'
        ),
        { status: 400 }
      );
    }

    // 获取收藏列表详情
    const favoriteList = await prisma.favoriteList.findFirst({
      where: {
        id: listId,
        OR: [
          { userId }, // 用户自己的列表
          { isPublic: true } // 或者公开的列表
        ]
      },
      include: {
        _count: {
          select: {
            favorites: true
          }
        },
        favorites: {
          include: {
            movie: {
              select: {
                id: true,
                title: true,
                img: true,
                localImg: true,
                date: true,
                videoLength: true,
                description: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 20 // 只返回前20个，完整列表需要分页
        }
      }
    });

    if (!favoriteList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '收藏列表不存在或无权访问'
        ),
        { status: 404 }
      );
    }

    return NextResponse.json(
      ResponseFactory.success({
        id: favoriteList.id,
        name: favoriteList.name,
        description: favoriteList.description,
        isDefault: favoriteList.isDefault,
        isPublic: favoriteList.isPublic,
        createdAt: favoriteList.createdAt,
        updatedAt: favoriteList.updatedAt,
        count: favoriteList._count.favorites,
        movies: favoriteList.favorites.map(fav => fav.movie)
      })
    );

  } catch (error) {
    console.error('获取收藏列表详情失败:', error);
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '获取收藏列表详情失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 更新收藏列表
 * PUT /api/favorite-lists/[id]
 */
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json();
    const { name, description, isPublic, userId } = updateFavoriteListSchema.parse(body);
    const params = await context.params;
    const listId = params.id;

    // 检查收藏列表是否存在且属于当前用户
    const existingList = await prisma.favoriteList.findFirst({
      where: {
        id: listId,
        userId
      }
    });

    if (!existingList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '收藏列表不存在或无权修改'
        ),
        { status: 404 }
      );
    }

    // 如果要修改名称，检查是否与其他列表重名
    if (name && name !== existingList.name) {
      const duplicateList = await prisma.favoriteList.findUnique({
        where: {
          userId_name: {
            userId,
            name
          }
        }
      });

      if (duplicateList) {
        return NextResponse.json(
          ResponseFactory.simpleError(
            ErrorCode.VALIDATION_ERROR,
            '已存在同名的收藏列表'
          ),
          { status: 400 }
        );
      }
    }

    // 更新收藏列表
    const updatedList = await prisma.favoriteList.update({
      where: { id: listId },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(isPublic !== undefined && { isPublic })
      },
      include: {
        _count: {
          select: {
            favorites: true
          }
        }
      }
    });

    return NextResponse.json(
      ResponseFactory.success({
        id: updatedList.id,
        name: updatedList.name,
        description: updatedList.description,
        isDefault: updatedList.isDefault,
        isPublic: updatedList.isPublic,
        createdAt: updatedList.createdAt,
        updatedAt: updatedList.updatedAt,
        count: updatedList._count.favorites
      }, '收藏列表更新成功')
    );

  } catch (error) {
    console.error('更新收藏列表失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '请求参数无效',
          error.errors.map(e => e.message).join(', ')
        ),
        { status: 400 }
      );
    }
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '更新收藏列表失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 删除收藏列表
 * DELETE /api/favorite-lists/[id]?userId=xxx
 */
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const params = await context.params;
    const listId = params.id;

    if (!userId) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '必须提供用户ID'
        ),
        { status: 400 }
      );
    }

    // 检查收藏列表是否存在且属于当前用户
    const existingList = await prisma.favoriteList.findFirst({
      where: {
        id: listId,
        userId
      }
    });

    if (!existingList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '收藏列表不存在或无权删除'
        ),
        { status: 404 }
      );
    }

    // 不允许删除默认列表
    if (existingList.isDefault) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '不能删除默认收藏列表'
        ),
        { status: 400 }
      );
    }

    // 删除收藏列表（关联的收藏记录会因为外键约束自动设置为NULL）
    await prisma.favoriteList.delete({
      where: { id: listId }
    });

    return NextResponse.json(
      ResponseFactory.success(
        { id: listId },
        '收藏列表删除成功'
      )
    );

  } catch (error) {
    console.error('删除收藏列表失败:', error);
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '删除收藏列表失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { ResponseFactory, ErrorCode } from '@/lib/error-types';

/**
 * 获取收藏列表中的影片（分页）
 * GET /api/favorite-lists/[id]/movies?userId=xxx&page=1&limit=20
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const params = await context.params;
    const listId = params.id;

    if (!userId) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '必须提供用户ID'
        ),
        { status: 400 }
      );
    }

    // 检查收藏列表是否存在且用户有权访问
    const favoriteList = await prisma.favoriteList.findFirst({
      where: {
        id: listId,
        OR: [
          { userId }, // 用户自己的列表
          { isPublic: true } // 或者公开的列表
        ]
      }
    });

    if (!favoriteList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '收藏列表不存在或无权访问'
        ),
        { status: 404 }
      );
    }

    const skip = (page - 1) * limit;

    // 获取收藏列表中的影片
    const [favorites, totalCount] = await Promise.all([
      prisma.favorite.findMany({
        where: { favoriteListId: listId },
        include: {
          movie: {
            include: {
              director: true,
              producer: true,
              publisher: true,
              series: true,
              stars: {
                include: {
                  star: true
                }
              },
              genres: {
                include: {
                  genre: true
                }
              },
              _count: {
                select: {
                  views: true,
                  likes: {
                    where: { type: 'LIKE' }
                  },
                  favorites: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),

      prisma.favorite.count({
        where: { favoriteListId: listId }
      })
    ]);

    // 转换数据格式
    const movies = favorites.map(favorite => ({
      id: favorite.movie.id,
      title: favorite.movie.title,
      img: favorite.movie.img,
      localImg: favorite.movie.localImg,
      date: favorite.movie.date,
      videoLength: favorite.movie.videoLength,
      description: favorite.movie.description,
      gid: favorite.movie.gid,
      uc: favorite.movie.uc,
      director: favorite.movie.director,
      producer: favorite.movie.producer,
      publisher: favorite.movie.publisher,
      series: favorite.movie.series,
      stars: favorite.movie.stars,
      genres: favorite.movie.genres,
      views: favorite.movie._count.views || 0,
      likes: favorite.movie._count.likes || 0,
      favorites: favorite.movie._count.favorites || 0,
      createdAt: favorite.movie.createdAt,
      updatedAt: favorite.movie.updatedAt,
      favoriteCreatedAt: favorite.createdAt // 收藏时间
    }));

    return NextResponse.json(
      ResponseFactory.success({
        list: {
          id: favoriteList.id,
          name: favoriteList.name,
          description: favoriteList.description,
          isDefault: favoriteList.isDefault,
          isPublic: favoriteList.isPublic
        },
        movies,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      })
    );

  } catch (error) {
    console.error('获取收藏列表影片失败:', error);
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '获取收藏列表影片失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

/**
 * 从收藏列表中移除影片
 * DELETE /api/favorite-lists/[id]/movies?userId=xxx&movieId=xxx
 */
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const movieId = searchParams.get('movieId');
    const params = await context.params;
    const listId = params.id;

    if (!userId || !movieId) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.VALIDATION_ERROR,
          '必须提供用户ID和影片ID'
        ),
        { status: 400 }
      );
    }

    // 检查收藏列表是否属于当前用户
    const favoriteList = await prisma.favoriteList.findFirst({
      where: {
        id: listId,
        userId
      }
    });

    if (!favoriteList) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '收藏列表不存在或无权操作'
        ),
        { status: 404 }
      );
    }

    // 从收藏列表中移除影片
    const deletedFavorite = await prisma.favorite.deleteMany({
      where: {
        userId,
        movieId,
        favoriteListId: listId
      }
    });

    if (deletedFavorite.count === 0) {
      return NextResponse.json(
        ResponseFactory.simpleError(
          ErrorCode.NOT_FOUND,
          '该影片不在此收藏列表中'
        ),
        { status: 404 }
      );
    }

    return NextResponse.json(
      ResponseFactory.success(
        { movieId, listId },
        '已从收藏列表中移除影片'
      )
    );

  } catch (error) {
    console.error('从收藏列表移除影片失败:', error);
    return NextResponse.json(
      ResponseFactory.simpleError(
        ErrorCode.DATABASE_QUERY_ERROR,
        '从收藏列表移除影片失败',
        error instanceof Error ? error.message : '未知错误'
      ),
      { status: 500 }
    );
  }
}

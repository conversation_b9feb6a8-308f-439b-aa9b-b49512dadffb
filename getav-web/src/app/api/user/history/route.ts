import { NextRequest, NextResponse } from 'next/server';

// 获取用户观看历史（从前端localStorage获取）
export async function GET() {
  try {
    // 历史记录功能暂时使用前端localStorage实现
    // 这里返回一个空的响应，实际数据由前端处理
    return NextResponse.json({
      success: true,
      data: {
        items: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0
      },
      message: '历史记录由前端localStorage管理'
    });

  } catch (error) {
    console.error('获取观看历史失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取观看历史失败'
    }, { status: 500 });
  }
}

// 添加观看历史记录
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { movieId, title, imageUrl, watchedAt } = body;

    // 这里可以实现将历史记录保存到数据库的逻辑
    // 目前返回成功响应，实际由前端localStorage处理
    
    return NextResponse.json({
      success: true,
      message: '观看历史已记录',
      data: {
        movieId,
        title,
        imageUrl,
        watchedAt: watchedAt || new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('添加观看历史失败:', error);
    return NextResponse.json({
      success: false,
      error: '添加观看历史失败'
    }, { status: 500 });
  }
}

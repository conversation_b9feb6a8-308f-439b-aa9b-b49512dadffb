import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 获取用户收藏列表
export async function GET(request: NextRequest) {
  try {
    // 从cookie获取token
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json({
        success: false,
        error: '未登录'
      }, { status: 401 });
    }

    // 验证token
    const decoded = jwt.verify(token, JWT_SECRET) as {
      userId: string;
      email: string;
      username: string;
    };

    // 获取分页参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 获取用户收藏的影片
    const favorites = await prisma.favorite.findMany({
      where: { userId: decoded.userId },
      include: {
        movie: {
          include: {
            director: true,
            producer: true,
            publisher: true,
            series: true,
            stars: {
              include: {
                star: true
              }
            },
            genres: {
              include: {
                genre: true
              }
            },
            samples: true,
            magnets: true,
            _count: {
              select: {
                views: true,
                likes: {
                  where: { type: 'LIKE' }
                },
                favorites: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit
    });

    // 获取总数
    const total = await prisma.favorite.count({
      where: { userId: decoded.userId }
    });

    // 转换数据格式为LocalMovie格式
    const movies = favorites.map(favorite => ({
      id: favorite.movie.id,
      title: favorite.movie.title,
      img: favorite.movie.img,
      localImg: favorite.movie.localImg,
      date: favorite.movie.date,
      videoLength: favorite.movie.videoLength,
      description: favorite.movie.description,
      gid: favorite.movie.gid,
      uc: favorite.movie.uc,
      views: favorite.movie._count.views || 0,
      likes: favorite.movie._count.likes || 0,
      favorites: favorite.movie._count.favorites || 0,
      director: favorite.movie.director ? {
        id: favorite.movie.director.id,
        name: favorite.movie.director.name
      } : null,
      producer: favorite.movie.producer ? {
        id: favorite.movie.producer.id,
        name: favorite.movie.producer.name
      } : null,
      publisher: favorite.movie.publisher ? {
        id: favorite.movie.publisher.id,
        name: favorite.movie.publisher.name
      } : null,
      series: favorite.movie.series ? {
        id: favorite.movie.series.id,
        name: favorite.movie.series.name
      } : null,
      stars: favorite.movie.stars.map(ms => ({
        id: ms.star.id,
        name: ms.star.name,
        localAvatar: ms.star.localAvatar
      })),
      genres: favorite.movie.genres.map(mg => ({
        id: mg.genre.id,
        name: mg.genre.name
      })),
      samples: favorite.movie.samples?.map(sample => ({
        id: sample.id,
        alt: sample.alt,
        localSrc: sample.localSrc
      })) || [],
      magnets: favorite.movie.magnets.map(magnet => ({
        id: magnet.id,
        link: magnet.link,
        isHD: magnet.isHD,
        title: magnet.title,
        size: magnet.size,
        numberSize: magnet.numberSize,
        shareDate: magnet.shareDate,
        hasSubtitle: magnet.hasSubtitle,
        createdAt: magnet.createdAt
      })),
      createdAt: favorite.movie.createdAt,
      updatedAt: favorite.movie.updatedAt
    }));

    return NextResponse.json({
      success: true,
      data: {
        items: movies,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取收藏列表失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取收藏列表失败'
    }, { status: 500 });
  }
}

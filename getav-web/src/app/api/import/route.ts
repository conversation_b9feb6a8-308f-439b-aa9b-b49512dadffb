import { NextRequest, NextResponse } from 'next/server';
import { queueManager } from '@/lib/queue-manager';
import { initializeQueues } from '@/lib/queue-init';
import { JobPriority } from '@/lib/queue-config';
import { getImportStatus } from '@/lib/javbus-import';
import { logger } from '@/lib/logger';

/**
 * 获取导入状态
 */
export async function GET() {
  try {
    const status = getImportStatus();
    return NextResponse.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('获取导入状态失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取导入状态失败'
    }, { status: 500 });
  }
}

/**
 * 开始导入影片数据 - 需要管理员权限（使用队列系统）
 */
export async function POST(request: NextRequest) {
  const requestId = request.headers.get('x-request-id') || `import_${Date.now()}`;

  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    const adminToken = process.env.ADMIN_TOKEN || 'admin123';
    const token = authHeader?.replace('Bearer ', '');

    if (!token || token !== adminToken) {
      return NextResponse.json({
        success: false,
        error: '需要管理员权限'
      }, { status: 401 });
    }

    const {
      count = 10,
      downloadImages = true,
      movieId,
      starId,
      useQueue = true
    } = await request.json();

    // 确保队列系统已初始化
    await initializeQueues();

    // 如果指定了单个影片ID，直接导入
    if (movieId) {
      logger.info('添加单个影片导入任务', { movieId, downloadImages, requestId });

      const job = await queueManager.addDataImportJob({
        movieId,
        options: {
          downloadImages,
          updateExisting: true,
          skipErrors: false
        }
      }, {
        priority: JobPriority.HIGH
      });

      return NextResponse.json({
        success: true,
        data: {
          type: 'single-import',
          jobId: job.id,
          movieId,
          message: '单个影片导入任务已添加到队列',
          queueStatus: await queueManager.getQueueStatus('data-import')
        }
      });
    }

    // 如果指定了演员ID，导入演员
    if (starId) {
      logger.info('添加演员导入任务', { starId, downloadImages, requestId });

      const job = await queueManager.addDataImportJob({
        starId,
        options: {
          downloadImages,
          updateExisting: true
        }
      }, {
        priority: JobPriority.NORMAL
      });

      return NextResponse.json({
        success: true,
        data: {
          type: 'star-import',
          jobId: job.id,
          starId,
          message: '演员导入任务已添加到队列'
        }
      });
    }

    // 批量导入：计算需要多少页
    const moviesPerPage = 20;
    const totalPages = Math.ceil(count / moviesPerPage);
    const jobs = [];

    logger.info('开始批量导入', {
      count,
      totalPages,
      downloadImages,
      useQueue,
      requestId
    });

    // 为每页创建一个队列任务
    for (let page = 1; page <= totalPages; page++) {
      const pageLimit = page === totalPages ? count - (page - 1) * moviesPerPage : moviesPerPage;

      const job = await queueManager.addDataImportJob({
        page,
        limit: pageLimit,
        options: {
          downloadImages,
          updateExisting: false,
          skipErrors: true
        }
      }, {
        priority: JobPriority.LOW,
        delay: (page - 1) * 2000 // 每页延迟2秒，避免并发过高
      });

      jobs.push({
        jobId: job.id,
        page,
        limit: pageLimit
      });
    }

    logger.info('批量导入任务已添加到队列', {
      totalJobs: jobs.length,
      totalPages,
      requestId
    });

    return NextResponse.json({
      success: true,
      data: {
        type: 'batch-import',
        totalJobs: jobs.length,
        totalPages,
        expectedMovies: count,
        jobs: jobs.slice(0, 5), // 只返回前5个任务信息
        message: `批量导入任务已添加到队列，共 ${jobs.length} 个任务`,
        queueStatus: await queueManager.getQueueStatus('data-import')
      }
    });

  } catch (error) {
    logger.error('导入任务创建失败', error as Error, { requestId });
    return NextResponse.json({
      success: false,
      error: '导入任务创建失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}


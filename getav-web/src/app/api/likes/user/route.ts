import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { z } from 'zod';

// 获取用户点赞影片列表的请求schema
const getUserLikesSchema = z.object({
  userId: z.string(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['latest', 'popular', 'rating']).default('latest'),
});

// 获取用户点赞的影片列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'latest';

    // 验证用户ID
    if (!userId) {
      return NextResponse.json(
        { error: '用户ID是必需的' },
        { status: 400 }
      );
    }

    const validatedParams = getUserLikesSchema.parse({
      userId,
      page,
      limit,
      sortBy,
    });

    const skip = (validatedParams.page - 1) * validatedParams.limit;

    // 构建排序条件
    let orderBy: { createdAt?: 'desc' | 'asc'; movie?: { views?: { _count: 'desc' | 'asc' }; likes?: { _count: 'desc' | 'asc' } } } = { createdAt: 'desc' }; // 默认按点赞时间排序
    if (validatedParams.sortBy === 'popular') {
      orderBy = {
        movie: {
          views: {
            _count: 'desc'
          }
        }
      };
    } else if (validatedParams.sortBy === 'rating') {
      orderBy = {
        movie: {
          likes: {
            _count: 'desc'
          }
        }
      };
    }

    // 获取用户点赞的影片列表（只获取LIKE类型，不包括DISLIKE）
    const [likes, totalCount] = await Promise.all([
      prisma.like.findMany({
        where: {
          userId: validatedParams.userId,
          type: 'LIKE',
          movieId: {
            not: null // 确保是影片点赞，不是评论或磁力链接点赞
          }
        },
        include: {
          movie: {
            select: {
              id: true,
              title: true,
              img: true,
              localImg: true,
              date: true,
              videoLength: true,
              description: true,
              gid: true,
              uc: true,
              createdAt: true,
              updatedAt: true,
              _count: {
                select: {
                  views: true,
                  likes: {
                    where: { type: 'LIKE' }
                  },
                  favorites: true
                }
              },
              // 包含相关信息用于显示
              director: {
                select: {
                  id: true,
                  name: true
                }
              },
              producer: {
                select: {
                  id: true,
                  name: true
                }
              },
              publisher: {
                select: {
                  id: true,
                  name: true
                }
              },
              series: {
                select: {
                  id: true,
                  name: true
                }
              },
              stars: {
                include: {
                  star: {
                    select: {
                      id: true,
                      name: true,
                      localAvatar: true
                    }
                  }
                }
              },
              genres: {
                include: {
                  genre: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy,
        skip,
        take: validatedParams.limit,
      }),

      prisma.like.count({
        where: {
          userId: validatedParams.userId,
          type: 'LIKE',
          movieId: {
            not: null
          }
        },
      }),
    ]);

    // 转换数据格式，过滤掉movie为null的记录
    const movies = likes
      .filter(like => like.movie !== null)
      .map(like => ({
        id: like.movie!.id,
        title: like.movie!.title,
        img: like.movie!.img,
        localImg: like.movie!.localImg,
        date: like.movie!.date,
        videoLength: like.movie!.videoLength,
        description: like.movie!.description,
        gid: like.movie!.gid,
        uc: like.movie!.uc,
        views: like.movie!._count.views || 0,
        likes: like.movie!._count.likes || 0,
        favorites: like.movie!._count.favorites || 0,
        director: like.movie!.director,
        producer: like.movie!.producer,
        publisher: like.movie!.publisher,
        series: like.movie!.series,
        stars: like.movie!.stars.map(ms => ({
          id: ms.star.id,
          name: ms.star.name,
          localAvatar: ms.star.localAvatar
        })),
        genres: like.movie!.genres.map(mg => ({
          id: mg.genre.id,
          name: mg.genre.name
        })),
        createdAt: like.movie!.createdAt,
        updatedAt: like.movie!.updatedAt,
        likedAt: like.createdAt, // 添加点赞时间
      }));

    return NextResponse.json({
      success: true,
      data: {
        items: movies,
        total: totalCount,
        page: validatedParams.page,
        limit: validatedParams.limit,
        totalPages: Math.ceil(totalCount / validatedParams.limit),
        sortBy: validatedParams.sortBy,
      }
    });

  } catch (error) {
    console.error('获取用户点赞影片列表失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: '获取点赞影片列表失败' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { CacheInvalidation } from '@/lib/cached-services';
import { z } from 'zod';

// 点赞请求schema
const likeSchema = z.object({
  type: z.enum(['LIKE', 'DISLIKE']),
  movieId: z.string().optional(),
  commentId: z.string().optional(),
  magnetId: z.string().optional(),
  userId: z.string().optional(), // 可选，支持匿名点赞
  username: z.string().optional(), // 匿名用户名
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, movieId, commentId, magnetId, userId, username } = likeSchema.parse(body);

    // 验证至少有一个目标
    if (!movieId && !commentId && !magnetId) {
      return NextResponse.json(
        { error: '必须指定影片ID、评论ID或磁力链接ID' },
        { status: 400 }
      );
    }

    // 点赞功能支持匿名用户
    let finalUserId = userId;
    if (!userId) {
      // 为匿名用户创建临时用户记录
      const usernameToUse = username || `匿名用户${Date.now()}`;

      // 尝试查找现有的匿名用户
      let anonymousUser = await prisma.user.findUnique({
        where: { username: usernameToUse },
      });

      if (!anonymousUser) {
        // 创建新的匿名用户
        anonymousUser = await prisma.user.create({
          data: {
            username: usernameToUse,
            email: null,
          },
        });
      }
      finalUserId = anonymousUser.id;
    }

    // 构建查询条件
    const whereCondition = {
      userId: finalUserId,
      ...(movieId && { movieId }),
      ...(commentId && { commentId }),
      ...(magnetId && { magnetId }),
    };

    // 查找现有的点赞记录
    const existingLike = await prisma.like.findFirst({
      where: whereCondition,
    });

    let action: string;

    if (existingLike) {
      if (existingLike.type === type) {
        // 如果是相同类型的点赞，则取消点赞
        await prisma.like.delete({
          where: { id: existingLike.id },
        });
        action = 'removed';
      } else {
        // 如果是不同类型，则更新点赞类型
        await prisma.like.update({
          where: { id: existingLike.id },
          data: { type },
        });
        action = 'updated';
      }
    } else {
      // 创建新的点赞记录
      await prisma.like.create({
        data: {
          type,
          userId: finalUserId,
          movieId,
          commentId,
          magnetId,
        },
      });
      action = 'created';
    }

    // 获取更新后的统计数据
    const statsWhereCondition = {
      ...(movieId && { movieId }),
      ...(commentId && { commentId }),
      ...(magnetId && { magnetId }),
    };

    const likes = await prisma.like.findMany({
      where: statsWhereCondition,
      select: { type: true },
    });

    const likesCount = likes.filter(like => like.type === 'LIKE').length;
    const dislikesCount = likes.filter(like => like.type === 'DISLIKE').length;

    // 如果是电影点赞，清除电影缓存
    if (movieId) {
      await CacheInvalidation.invalidateMovie(movieId);
    }

    return NextResponse.json({
      success: true,
      action,
      type: action === 'removed' ? null : type,
      data: {
        totalLikes: likesCount,
        totalDislikes: dislikesCount
      }
    });
  } catch (error) {
    console.error('点赞操作失败:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: '点赞操作失败' },
      { status: 500 }
    );
  }
}

// 获取点赞统计
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const movieId = searchParams.get('movieId');
    const commentId = searchParams.get('commentId');
    const magnetId = searchParams.get('magnetId');
    const userId = searchParams.get('userId');

    // 验证至少有一个目标
    if (!movieId && !commentId && !magnetId) {
      return NextResponse.json(
        { error: '必须指定影片ID、评论ID或磁力链接ID' },
        { status: 400 }
      );
    }

    // 构建查询条件
    const whereCondition = {
      ...(movieId && { movieId }),
      ...(commentId && { commentId }),
      ...(magnetId && { magnetId }),
    };

    // 获取点赞统计
    const likes = await prisma.like.findMany({
      where: whereCondition,
      select: {
        type: true,
        userId: true,
      },
    });

    const likesCount = likes.filter(like => like.type === 'LIKE').length;
    const dislikesCount = likes.filter(like => like.type === 'DISLIKE').length;

    // 获取当前用户的投票状态
    let userVote = null;
    if (userId) {
      const userLike = likes.find(like => like.userId === userId);
      userVote = userLike ? userLike.type.toLowerCase() : null;
    }

    return NextResponse.json({
      success: true,
      data: {
        totalLikes: likesCount,
        totalDislikes: dislikesCount,
        userVote,
      }
    });
  } catch (error) {
    console.error('获取点赞统计失败:', error);
    return NextResponse.json(
      { error: '获取点赞统计失败' },
      { status: 500 }
    );
  }
}

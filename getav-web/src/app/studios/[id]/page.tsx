'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Header from '@/components/Header';
import MovieCard from '@/components/MovieCard';
import { Building2, Film, Calendar, TrendingUp } from 'lucide-react';

interface StudioDetail {
  id: string;
  name: string;
  types: ('producer' | 'publisher')[];
  movieCount: number;
  movies: Array<{
    id: string;
    title: string;
    localImg: string | null;
    img: string | null;
    date: string | null;
    videoLength: number | null;
    description: string | null;
    createdAt: Date;
    updatedAt: Date;
    director: { id: string; name: string } | null;
    producer: { id: string; name: string } | null;
    publisher: { id: string; name: string } | null;
    series: { id: string; name: string } | null;
    stars: Array<{ id: string; name: string; localAvatar: string | null }>;
    genres: Array<{ id: string; name: string }>;
    magnets: Array<{ id: string; link: string; title: string; size: string | null }>;
  }>;
}

export default function StudioDetailPage() {
  const params = useParams();
  const studioId = params.id as string;
  
  const [studio, setStudio] = useState<StudioDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // 加载片商详情
  useEffect(() => {
    const fetchStudioDetail = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/studios/${studioId}`);
        const data = await response.json();
        
        if (data.success) {
          setStudio(data.data);
        } else {
          setError(data.error || '获取片商信息失败');
        }
      } catch (err) {
        setError('网络请求失败');
        console.error('获取片商详情失败:', err);
      } finally {
        setLoading(false);
      }
    };

    if (studioId) {
      fetchStudioDetail();
    }
  }, [studioId]);

  // 获取类型标签
  const getTypeLabels = () => {
    if (!studio) return [];
    
    const labels = [];
    if (studio.types.includes('producer')) {
      labels.push({ text: '制作商', color: 'bg-blue-600' });
    }
    if (studio.types.includes('publisher')) {
      labels.push({ text: '发行商', color: 'bg-green-600' });
    }
    return labels;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-white">加载中...</div>
      </div>
    );
  }

  if (error || !studio) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold text-white mb-2">片商不存在</h1>
          <p className="text-gray-400 mb-6">{error || '找不到指定的片商信息'}</p>
          <button
            onClick={() => window.history.back()}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            返回上页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            {/* 这里可以添加侧边栏组件 */}
          </div>
        </>
      )}

      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* 片商信息头部 */}
        <div className="mb-8">
          <div className="flex items-start space-x-6">
            {/* 片商图标 */}
            <div className="w-32 h-32 bg-[#272727] rounded-lg flex items-center justify-center flex-shrink-0">
              <Building2 className="w-16 h-16 text-gray-400" />
            </div>

            {/* 片商信息 */}
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <h1 className="text-3xl font-bold text-white">{studio.name}</h1>
                <div className="flex space-x-2">
                  {getTypeLabels().map((label, index) => (
                    <span key={index} className={`px-3 py-1 text-sm rounded-full text-white ${label.color}`}>
                      {label.text}
                    </span>
                  ))}
                </div>
              </div>

              {/* 统计信息 */}
              <div className="flex items-center space-x-6 text-gray-300">
                <div className="flex items-center space-x-2">
                  <Film className="w-5 h-5" />
                  <span>{studio.movieCount} 部影片</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>最新更新: {studio.movies.length > 0 ? new Date(studio.movies[0].createdAt).toLocaleDateString() : '暂无'}</span>
                </div>
              </div>

              {/* 描述信息 */}
              <div className="mt-4 text-gray-400">
                <p>
                  {studio.types.includes('producer') && studio.types.includes('publisher') 
                    ? `${studio.name} 是一家综合性影视公司，同时从事影片制作和发行业务。`
                    : studio.types.includes('producer')
                    ? `${studio.name} 是一家专业的影片制作公司。`
                    : `${studio.name} 是一家专业的影片发行公司。`
                  }
                  目前已发布 {studio.movieCount} 部影片作品。
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 影片列表 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <TrendingUp className="w-6 h-6 mr-2" />
              全部作品
            </h2>
            <div className="text-sm text-gray-400">
              共 {studio.movieCount} 部影片
            </div>
          </div>

          {/* 影片网格 - 使用MovieCard组件保持与首页一致 */}
          {studio.movies.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {studio.movies.map((movie) => (
                <MovieCard
                  key={movie.id}
                  movie={{
                    id: movie.id,
                    title: movie.title,
                    localImg: movie.localImg,
                    img: movie.img,
                    date: movie.date,
                    videoLength: movie.videoLength,
                    description: movie.description,
                    createdAt: movie.createdAt,
                    updatedAt: movie.updatedAt,
                    director: movie.director,
                    producer: movie.producer,
                    publisher: movie.publisher,
                    series: movie.series,
                    genres: movie.genres,
                    samples: [],
                    magnets: movie.magnets,
                    stars: movie.stars
                  }}
                  viewMode="grid"
                />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="text-6xl mb-4">🎬</div>
              <h3 className="text-xl font-semibold text-white mb-2">暂无作品</h3>
              <p className="text-gray-400">该片商暂时没有发布任何影片作品</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

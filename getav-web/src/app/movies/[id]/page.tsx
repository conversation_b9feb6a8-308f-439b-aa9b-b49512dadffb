'use client';

import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Users, Play, Download, Heart, Share2, ThumbsUp, MoreHorizontal, MessageCircle, Eye, Flag, Copy, EyeOff, UserX, Plus, FileDown } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import CommentModal from '@/components/CommentModal';
import MagnetModal from '@/components/MagnetModal';
import ReportModal from '@/components/ReportModal';
import RelatedMovies from '@/components/RelatedMovies';
import FavoriteListManager from '@/components/FavoriteListManager';
import M3U8Player from '@/components/M3U8Player';
import { useSession } from '@/components/SessionProvider';
import { useEffect, useState, useRef } from 'react';
import { formatRelativeTime, formatViewCount } from '@/lib/time-utils';
import { recordView } from '@/lib/stats-service';
import type { LocalMovie } from '@/types/javbus';

interface MovieDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function MovieDetailPage({ params }: MovieDetailPageProps) {
  const { user } = useSession();
  const [movie, setMovie] = useState<LocalMovie | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  const [showMagnetModal, setShowMagnetModal] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [comments, setComments] = useState<Array<{
    id: string;
    text: string;
    author: string;
    time: string;
    likes: number;
    dislikes: number;
    userVote: 'like' | 'dislike' | null;
  }>>([]);
  const [magnetComments, setMagnetComments] = useState<{[key: string]: Array<{
    id: string;
    text: string;
    author: string;
    time: string;
    likes: number;
    dislikes: number;
    userVote: 'like' | 'dislike' | null;
  }>}>({});
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [currentFavoriteListId, setCurrentFavoriteListId] = useState<string | null>(null);

  // 获取用户交互状态
  const fetchUserInteractionStatus = async (movieId: string) => {
    if (!user) {
      setIsLiked(false);
      setIsFavorited(false);
      setLikesCount(0);
      return;
    }

    try {
      // 并行获取点赞状态和收藏状态
      const [likesResponse, favoritesResponse] = await Promise.all([
        fetch(`/api/likes?movieId=${movieId}&userId=${user.id}`),
        fetch(`/api/favorites?movieId=${movieId}&userId=${user.id}`)
      ]);

      if (likesResponse.ok) {
        const likesData = await likesResponse.json();
        if (likesData.success) {
          setIsLiked(likesData.data.userVote === 'like');
          setLikesCount(likesData.data.totalLikes || 0);
        } else {
          // 兼容旧格式
          setIsLiked(likesData.userVote === 'like');
          setLikesCount(likesData.likes || 0);
        }
      }

      if (favoritesResponse.ok) {
        const favoritesData = await favoritesResponse.json();
        if (favoritesData.success) {
          setIsFavorited(favoritesData.data.isFavorited);
          setCurrentFavoriteListId(favoritesData.data.favoriteListId || null);
        } else {
          // 兼容旧格式
          setIsFavorited(favoritesData.isFavorited);
        }
      }
    } catch (error) {
      console.error('获取用户交互状态失败:', error);
    }
  };

  // 获取评论列表
  const fetchComments = async (movieId: string) => {
    try {
      const response = await fetch(`/api/comments?movieId=${movieId}`);
      if (response.ok) {
        const commentsData = await response.json();
        setComments(commentsData);
      }
    } catch (error) {
      console.error('获取评论列表失败:', error);
    }
  };

  // 分享影片
  const shareMovie = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      alert('影片链接已复制到剪贴板');
    } catch (err) {
      console.error('分享失败:', err);
    }
  };

  // 添加评论 - 需要登录
  const addComment = async (content: string) => {
    if (!user) {
      alert('评论功能需要登录，请先注册或登录');
      return;
    }

    if (!movie) return;

    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          movieId: movie.id,
          userId: user.id,
        }),
      });

      if (response.ok) {
        const newComment = await response.json();
        setComments(prev => [newComment, ...prev]);
      } else {
        const error = await response.json();
        alert(error.error || '评论失败');
      }
    } catch (error) {
      console.error('添加评论失败:', error);
      alert('评论失败，请重试');
    }
  };

  // 添加磁力评论 - 需要登录
  const addMagnetComment = async (magnetId: string, content: string) => {
    if (!user) {
      alert('评论功能需要登录，请先注册或登录');
      return;
    }

    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          magnetId,
          userId: user.id,
        }),
      });

      if (response.ok) {
        const newComment = await response.json();
        setMagnetComments(prev => ({
          ...prev,
          [magnetId]: [newComment, ...(prev[magnetId] || [])]
        }));
      } else {
        const error = await response.json();
        alert(error.error || '评论失败');
      }
    } catch (error) {
      console.error('添加磁力评论失败:', error);
      alert('评论失败，请重试');
    }
  };

  // 点赞/反对评论 - 需要登录
  const voteComment = async (commentId: string, type: 'like' | 'dislike') => {
    if (!user) {
      alert('评论点赞功能需要登录，请先注册或登录');
      return;
    }

    try {
      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: type.toUpperCase(),
          commentId,
          userId: user.id,
        }),
      });

      if (response.ok) {
        // 重新获取评论列表以更新点赞状态
        if (movie) {
          await fetchComments(movie.id);
        }
      }
    } catch (error) {
      console.error('评论点赞失败:', error);
    }
  };

  // 点赞/反对磁力评论 - 需要登录
  const voteMagnetComment = async (magnetId: string, commentId: string, vote: 'like' | 'dislike') => {
    if (!user) {
      alert('评论点赞功能需要登录，请先注册或登录');
      return;
    }

    try {
      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: vote.toUpperCase(),
          commentId,
          userId: user.id,
        }),
      });

      if (response.ok) {
        // 重新获取磁力评论列表以更新点赞状态
        // 这里可以根据需要实现磁力评论的刷新逻辑
        console.log(`磁力链接 ${magnetId} 的评论 ${commentId} 点赞状态已更新`);
      }
    } catch (error) {
      console.error('磁力评论点赞失败:', error);
    }
  };



  // 防止重复记录观看次数
  const viewRecorded = useRef(false);
  const currentMovieId = useRef<string | null>(null);

  // 保存到观看历史
  const saveToWatchHistory = (movieData: LocalMovie) => {
    try {
      const historyKey = 'watch_history';
      const existingHistory = localStorage.getItem(historyKey);
      let history: LocalMovie[] = existingHistory ? JSON.parse(existingHistory) : [];

      // 移除已存在的相同影片记录
      history = history.filter(item => item.id !== movieData.id);

      // 添加新的观看记录到开头
      const watchRecord = {
        id: movieData.id,
        title: movieData.title,
        img: movieData.img,
        localImg: movieData.localImg,
        date: movieData.date,
        videoLength: movieData.videoLength,
        description: movieData.description,
        gid: movieData.gid,
        uc: movieData.uc,
        director: movieData.director,
        producer: movieData.producer,
        publisher: movieData.publisher,
        series: movieData.series,
        stars: movieData.stars,
        genres: movieData.genres,
        samples: movieData.samples,
        magnets: movieData.magnets,
        createdAt: movieData.createdAt,
        updatedAt: movieData.updatedAt,
        views: movieData.views || 0,
        likes: movieData.likes || 0,
        favorites: movieData.favorites || 0,
        watchedAt: new Date().toISOString()
      };

      history.unshift(watchRecord);

      // 限制历史记录数量（保留最近100条）
      if (history.length > 100) {
        history = history.slice(0, 100);
      }

      localStorage.setItem(historyKey, JSON.stringify(history));
      console.log('观看历史已保存:', movieData.title);
    } catch (error) {
      console.error('保存观看历史失败:', error);
    }
  };

  useEffect(() => {
    async function loadMovie() {
      try {
        const resolvedParams = await params;
        const movieId = resolvedParams.id;

        // 如果是新的电影，重置观看记录状态
        if (currentMovieId.current !== movieId) {
          viewRecorded.current = false;
          currentMovieId.current = movieId;
        }

        const response = await fetch(`/api/movies/${movieId}`);
        if (!response.ok) {
          notFound();
          return;
        }
        const result = await response.json();
        if (result.success) {
          setMovie(result.data);
          // 加载用户交互状态和评论
          await fetchUserInteractionStatus(result.data.id);
          await fetchComments(result.data.id);

          // 记录观看次数并更新显示 - 添加防重复机制
          if (!viewRecorded.current) {
            // 检查localStorage防止短时间内重复计数
            const lastViewKey = `lastView_${result.data.id}`;
            const lastViewTime = localStorage.getItem(lastViewKey);
            const now = Date.now();
            const fiveMinutes = 5 * 60 * 1000;

            if (!lastViewTime || (now - parseInt(lastViewTime)) > fiveMinutes) {
              viewRecorded.current = true;
              const viewResult = await recordView(result.data.id, user?.id);
              if (viewResult.success && viewResult.totalViews !== undefined) {
                // 更新电影数据中的观看次数
                setMovie(prev => prev ? { ...prev, views: viewResult.totalViews } : null);
                // 记录观看时间到localStorage
                localStorage.setItem(lastViewKey, now.toString());

                // 保存到观看历史 - 使用更新后的观看次数
                const updatedMovieData = { ...result.data, views: viewResult.totalViews };
                saveToWatchHistory(updatedMovieData);
              }
            } else {
              console.log('5分钟内已观看过此影片，跳过观看次数记录');
              // 即使跳过观看次数记录，也要保存到历史记录
              saveToWatchHistory(result.data);
            }
          }
        } else {
          notFound();
        }
      } catch (error) {
        console.error('Error loading movie:', error);
        notFound();
      } finally {
        setLoading(false);
      }
    }
    loadMovie();
  }, [params]); // eslint-disable-line react-hooks/exhaustive-deps

  // 当用户登录状态改变时，重新获取交互状态
  useEffect(() => {
    if (movie) {
      fetchUserInteractionStatus(movie.id);
    }
  }, [user, movie]); // eslint-disable-line react-hooks/exhaustive-deps



  // 影片点赞功能 - 匿名用户可用
  const handleMovieLike = async () => {
    if (!movie) return;

    try {
      const requestBody: {
        type: string;
        movieId: string;
        userId?: string;
        username?: string;
      } = {
        type: 'LIKE',
        movieId: movie.id,
      };

      // 如果用户已登录，使用用户ID；否则使用匿名用户名
      if (user) {
        requestBody.userId = user.id;
      } else {
        requestBody.username = `匿名用户${Date.now()}`;
      }

      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 使用新的API格式
          setIsLiked(result.action === 'created' || result.action === 'updated');
          if (result.data) {
            setLikesCount(result.data.totalLikes || 0);
          }
        } else {
          // 兼容旧格式
          setIsLiked(result.type === 'LIKE');
        }
        // 重新获取点赞统计以确保数据一致
        await fetchUserInteractionStatus(movie.id);
      } else {
        console.error('点赞操作失败');
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
    }
  };

  // 收藏功能 - 需要登录，现在打开收藏列表选择模态框
  const handleFavorite = (showFavoriteModal: () => void) => {
    if (!user) {
      alert('收藏功能需要登录，请先注册或登录');
      return;
    }

    if (!movie) return;

    // 打开收藏列表选择模态框
    showFavoriteModal();
  };

  // 处理收藏状态变化
  const handleFavoriteChange = (listId: string | null, action: 'add' | 'remove' | 'move') => {
    if (action === 'add') {
      setIsFavorited(true);
      setCurrentFavoriteListId(listId);
    } else if (action === 'remove') {
      setIsFavorited(false);
      setCurrentFavoriteListId(null);
    } else if (action === 'move') {
      setCurrentFavoriteListId(listId);
    }
  };

  // 更多选项功能
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      alert('视频链接已复制到剪贴板');
      setShowMoreMenu(false);
    } catch (err) {
      console.error('复制链接失败:', err);
      alert('复制链接失败');
    }
  };

  const handleNotInterested = () => {
    // 这里可以添加不感兴趣的逻辑，比如保存到用户偏好
    alert('已标记为不感兴趣');
    setShowMoreMenu(false);
  };

  const handleBlockProducer = () => {
    if (!movie?.producer?.name && !movie?.publisher?.name) {
      alert('无法获取制作商信息');
      return;
    }
    const producerName = movie.producer?.name || movie.publisher?.name;
    alert(`已屏蔽制作商: ${producerName}`);
    setShowMoreMenu(false);
  };

  const handleAddToPlaylist = (showFavoriteModal: () => void) => {
    if (!user) {
      alert('添加到播放列表需要登录，请先注册或登录');
      return;
    }
    setShowMoreMenu(false);
    // 打开收藏列表选择模态框
    showFavoriteModal();
  };

  const handleDownloadInfo = () => {
    if (!movie) return;

    const movieInfo = {
      title: movie.title,
      id: movie.id,
      date: movie.date,
      director: movie.director?.name,
      producer: movie.producer?.name,
      publisher: movie.publisher?.name,
      stars: movie.stars?.map(s => s.name).join(', '),
      genres: movie.genres?.map(g => g.name).join(', '),
      magnets: movie.magnets?.map(m => m.link)
    };

    const dataStr = JSON.stringify(movieInfo, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${movie.id}_info.json`;
    link.click();
    URL.revokeObjectURL(url);
    setShowMoreMenu(false);
  };

  const handleReport = () => {
    setShowMoreMenu(false);
    setShowReportModal(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-400">正在加载...</span>
      </div>
    );
  }

  if (!movie) {
    notFound();
    return null;
  }

  // 使用本地图片路径，如果没有则使用原始图片
  const coverImage = movie.localImg || movie.img || '/placeholder-movie.jpg';

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      {/* 主内容区域 - 自适应侧边栏布局 */}
      <main className={`min-h-screen transition-all duration-300 ease-in-out md:ml-0 ${
        sidebarOpen ? 'md:ml-64' : 'md:ml-20'
      }`}>
        <div className="max-w-[1400px] mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* 左侧主内容区域 */}
            <div className="flex-1 min-w-0 max-w-[1080px]">
              {/* 视频播放器区域 */}
              <div className="relative aspect-video bg-black rounded-lg overflow-hidden mb-4">
                <M3U8Player
                  src={movie.localM3u8Path ? `/m3u8/playlists/${movie.localM3u8Path.split('/').pop()}` : ''}
                  poster={coverImage}
                  title={movie.title}
                  duration={movie.durationSeconds}
                  durationFormatted={movie.durationFormatted}
                />
              </div>

              {/* 影片标题 */}
              <h1 className="text-xl font-semibold text-white mb-3 leading-tight">
                {movie.title}
              </h1>

              {/* 操作按钮区域 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-300">
                  <span>{formatRelativeTime(movie.date)}</span>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    <span>{formatViewCount(movie.views || 0)}</span>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {/* 点赞按钮 */}
                  <button
                    onClick={handleMovieLike}
                    className={`flex items-center gap-2 px-4 py-2 rounded-full transition-colors ${
                      isLiked
                        ? 'bg-white text-black'
                        : 'bg-[#272727] text-white hover:bg-[#3f3f3f]'
                    }`}
                  >
                    <ThumbsUp className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      {isLiked ? '已赞' : '点赞'} {likesCount > 0 && `(${likesCount})`}
                    </span>
                  </button>

                  {/* 收藏按钮 - 使用FavoriteListManager */}
                  {user && movie && (
                    <FavoriteListManager
                      userId={user.id}
                      movieId={movie.id}
                      movieTitle={movie.title}
                      currentFavoriteListId={currentFavoriteListId || undefined}
                      onFavoriteChange={handleFavoriteChange}
                    >
                      {({ showFavoriteModal }) => (
                        <button
                          onClick={() => handleFavorite(showFavoriteModal)}
                          className={`flex items-center gap-2 px-4 py-2 rounded-full transition-colors ${
                            isFavorited
                              ? 'bg-red-600 text-white'
                              : 'bg-[#272727] text-white hover:bg-[#3f3f3f]'
                          }`}
                        >
                          <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
                          <span className="text-sm font-medium">
                            {isFavorited ? '已收藏' : '收藏'}
                          </span>
                        </button>
                      )}
                    </FavoriteListManager>
                  )}

                  {/* 未登录用户的收藏按钮 */}
                  {!user && (
                    <button
                      onClick={() => alert('收藏功能需要登录，请先注册或登录')}
                      className="flex items-center gap-2 px-4 py-2 bg-[#272727] text-white rounded-full hover:bg-[#3f3f3f] transition-colors"
                    >
                      <Heart className="w-4 h-4" />
                      <span className="text-sm font-medium">收藏</span>
                    </button>
                  )}

                  {/* 磁力下载按钮 */}
                  <button
                    onClick={() => setShowMagnetModal(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    <span className="text-sm font-medium">下载</span>
                  </button>

                  {/* 评论按钮 */}
                  <button
                    onClick={() => setShowComments(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-[#272727] text-white rounded-full hover:bg-[#3f3f3f] transition-colors"
                  >
                    <MessageCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">评论 ({comments.length})</span>
                  </button>

                  {/* 分享按钮 */}
                  <button
                    onClick={shareMovie}
                    className="flex items-center gap-2 px-4 py-2 bg-[#272727] text-white rounded-full hover:bg-[#3f3f3f] transition-colors"
                  >
                    <Share2 className="w-4 h-4" />
                    <span className="text-sm font-medium">分享</span>
                  </button>

                  {/* 更多选项 */}
                  <div className="relative">
                    <button
                      onClick={() => setShowMoreMenu(!showMoreMenu)}
                      className="p-2 bg-[#272727] text-white rounded-full hover:bg-[#3f3f3f] transition-colors"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </button>

                    {/* 下拉菜单 */}
                    {showMoreMenu && (
                      <>
                        {/* 背景遮罩 */}
                        <div
                          className="fixed inset-0 z-10"
                          onClick={() => setShowMoreMenu(false)}
                        />
                        {/* 菜单内容 */}
                        <div className="absolute right-0 top-full mt-2 w-56 bg-[#282828] rounded-lg shadow-lg border border-[#3f3f3f] z-20">
                          <div className="py-2">
                            <button
                              onClick={handleCopyLink}
                              className="w-full px-4 py-2 text-left text-white hover:bg-[#3f3f3f] transition-colors flex items-center gap-3"
                            >
                              <Copy className="w-4 h-4" />
                              <span>复制视频链接</span>
                            </button>

                            {/* 添加到播放列表按钮 - 使用FavoriteListManager */}
                            {user && movie && (
                              <FavoriteListManager
                                userId={user.id}
                                movieId={movie.id}
                                movieTitle={movie.title}
                                currentFavoriteListId={currentFavoriteListId || undefined}
                                onFavoriteChange={handleFavoriteChange}
                              >
                                {({ showFavoriteModal }) => (
                                  <button
                                    onClick={() => handleAddToPlaylist(showFavoriteModal)}
                                    className="w-full px-4 py-2 text-left text-white hover:bg-[#3f3f3f] transition-colors flex items-center gap-3"
                                  >
                                    <Plus className="w-4 h-4" />
                                    <span>添加到播放列表</span>
                                  </button>
                                )}
                              </FavoriteListManager>
                            )}

                            {/* 未登录用户的添加到播放列表按钮 */}
                            {!user && (
                              <button
                                onClick={() => {
                                  alert('添加到播放列表需要登录，请先注册或登录');
                                  setShowMoreMenu(false);
                                }}
                                className="w-full px-4 py-2 text-left text-white hover:bg-[#3f3f3f] transition-colors flex items-center gap-3"
                              >
                                <Plus className="w-4 h-4" />
                                <span>添加到播放列表</span>
                              </button>
                            )}

                            <button
                              onClick={handleDownloadInfo}
                              className="w-full px-4 py-2 text-left text-white hover:bg-[#3f3f3f] transition-colors flex items-center gap-3"
                            >
                              <FileDown className="w-4 h-4" />
                              <span>下载影片信息</span>
                            </button>

                            <div className="border-t border-[#3f3f3f] my-1"></div>

                            <button
                              onClick={handleNotInterested}
                              className="w-full px-4 py-2 text-left text-white hover:bg-[#3f3f3f] transition-colors flex items-center gap-3"
                            >
                              <EyeOff className="w-4 h-4" />
                              <span>不感兴趣</span>
                            </button>

                            <button
                              onClick={handleBlockProducer}
                              className="w-full px-4 py-2 text-left text-white hover:bg-[#3f3f3f] transition-colors flex items-center gap-3"
                            >
                              <UserX className="w-4 h-4" />
                              <span>屏蔽此制作商</span>
                            </button>

                            <div className="border-t border-[#3f3f3f] my-1"></div>

                            <button
                              onClick={handleReport}
                              className="w-full px-4 py-2 text-left text-red-400 hover:bg-[#3f3f3f] transition-colors flex items-center gap-3"
                            >
                              <Flag className="w-4 h-4" />
                              <span>举报</span>
                            </button>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* 制作商信息区域 */}
              <div className="flex items-center gap-4 p-4 bg-[#272727] rounded-lg mb-4">
                <div className="w-10 h-10 bg-[#3f3f3f] rounded-full flex items-center justify-center">
                  <Users className="w-5 h-5 text-gray-300" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-white">
                      {movie.producer?.name || movie.publisher?.name || '制作商'}
                    </span>
                    <span className="text-sm text-gray-400">•</span>
                    <span className="text-sm text-gray-400">{movie.id}</span>
                  </div>
                  <div className="text-sm text-gray-400 space-y-1">
                    {movie.director && (
                      <div>
                        导演: <Link
                          href={`/directors/${movie.director.id}`}
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          {movie.director.name}
                        </Link>
                      </div>
                    )}
                    {movie.producer && movie.publisher && movie.producer.name !== movie.publisher.name && (
                      <div>
                        制作商: <Link
                          href={`/producers/${movie.producer.id}`}
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          {movie.producer.name}
                        </Link> | 发行商: <Link
                          href={`/publishers/${movie.publisher.id}`}
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          {movie.publisher.name}
                        </Link>
                      </div>
                    )}
                    {movie.producer && !movie.publisher && (
                      <div>
                        制作商: <Link
                          href={`/producers/${movie.producer.id}`}
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          {movie.producer.name}
                        </Link>
                      </div>
                    )}
                    {!movie.producer && movie.publisher && (
                      <div>
                        发行商: <Link
                          href={`/publishers/${movie.publisher.id}`}
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          {movie.publisher.name}
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
                <button className="px-4 py-2 bg-white text-black rounded-full text-sm font-medium hover:bg-gray-200 transition-colors">
                  订阅
                </button>
              </div>

              {/* 影片详情信息 */}
              <div className="bg-[#272727] rounded-lg p-4 mb-4">
                {/* 演员信息 */}
                {movie.stars && movie.stars.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-white mb-2">演员</h3>
                    <div className="flex flex-wrap gap-2">
                      {movie.stars.slice(0, 6).map((movieStar: { star?: { id: string; name: string; localAvatar?: string | null }; id?: string; name?: string; localAvatar?: string | null }) => {
                        const star = movieStar.star || movieStar;
                        return (
                          <Link
                            key={star.id}
                            href={`/stars/${star.id}`}
                            className="flex items-center gap-2 bg-[#3f3f3f] hover:bg-[#4f4f4f] rounded-full px-3 py-1 transition-colors"
                          >
                            <div className="w-6 h-6 bg-[#555] rounded-full overflow-hidden">
                              {star.localAvatar && (
                                <OptimizedImage
                                  src={star.localAvatar}
                                  alt={star.name || ''}
                                  width={24}
                                  height={24}
                                  className="object-cover"
                                />
                              )}
                            </div>
                            <span className="text-sm text-white hover:text-blue-300 transition-colors">{star.name}</span>
                          </Link>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* 系列信息 */}
                {movie.series && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-white mb-2">系列</h3>
                    <div className="flex flex-wrap gap-2">
                      <Link
                        href={`/series/${movie.series.id}`}
                        className="bg-[#4f4f4f] hover:bg-[#5f5f5f] text-white px-3 py-1 rounded-full text-sm border border-gray-600 transition-colors"
                      >
                        📺 {movie.series.name}
                      </Link>
                    </div>
                  </div>
                )}

                {/* 分类标签 */}
                {movie.genres && movie.genres.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-white mb-2">分类</h3>
                    <div className="flex flex-wrap gap-2">
                      {movie.genres.map((movieGenre: { genre?: { id: string; name: string }; id?: string; name?: string }) => {
                        const genre = movieGenre.genre || movieGenre;
                        return (
                          <Link
                            key={genre.id}
                            href={`/genres/${genre.id}`}
                            className="bg-[#3f3f3f] hover:bg-[#4f4f4f] text-white px-3 py-1 rounded-full text-sm transition-colors"
                          >
                            #{genre.name}
                          </Link>
                        );
                      })}
                    </div>
                  </div>
                )}




              </div>




            </div>

            {/* 右侧推荐区域 */}
            <RelatedMovies
              movies={movie.relatedMovies || []}
              loading={loading}
            />
            </div>
          </div>
        </main>

      {/* 评论模态框 */}
      <CommentModal
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        title={movie.title}
        comments={comments}
        onAddComment={addComment}
        onVoteComment={voteComment}
      />

      {/* 磁力链接模态框 */}
      <MagnetModal
        isOpen={showMagnetModal}
        onClose={() => setShowMagnetModal(false)}
        title={movie.title}
        magnets={movie.magnets || []}
        magnetComments={magnetComments}
        onAddMagnetComment={addMagnetComment}
        onVoteMagnetComment={voteMagnetComment}
      />

      {/* 举报模态框 */}
      <ReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        movieTitle={movie.title}
        movieId={movie.id}
      />

    </div>
  );
}

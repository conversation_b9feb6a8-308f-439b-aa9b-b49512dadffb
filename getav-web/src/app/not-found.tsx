'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Film, Home, Search } from 'lucide-react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';

export default function NotFound() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      {/* 404内容区域 */}
      <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] px-6">
        <div className="text-center max-w-md">
          {/* 错误图标 */}
          <div className="w-24 h-24 mx-auto mb-8 bg-[#272727] rounded-lg flex items-center justify-center">
            <Film className="w-12 h-12 text-orange-400" />
          </div>

          {/* 错误信息 */}
          <h1 className="text-6xl font-bold text-white mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-white mb-4">页面未找到</h2>
          <p className="text-gray-400 mb-8 leading-relaxed">
            抱歉，您访问的页面不存在。可能是链接错误或页面已被移动。
          </p>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link
              href="/"
              className="inline-flex items-center justify-center gap-2 bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors duration-200 font-medium"
            >
              <Home className="w-5 h-5" />
              返回首页
            </Link>

            <Link
              href="/search"
              className="inline-flex items-center justify-center gap-2 bg-[#272727] hover:bg-[#3a3a3a] text-white px-6 py-3 rounded-lg transition-colors duration-200 font-medium"
            >
              <Search className="w-5 h-5" />
              搜索内容
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

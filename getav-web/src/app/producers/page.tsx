'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import {
  Search,
  Building,
  Play,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface Producer {
  id: string;
  name: string;
  movieCount: number;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export default function ProducersPage() {
  const [producers, setProducers] = useState<Producer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 24,
    total: 0,
    totalPages: 0
  });

  // 加载制作商列表
  const fetchProducers = async (page: number = 1, search: string = '', sort: string = 'popular') => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '24',
        sort,
        ...(search && { search })
      });

      const response = await fetch(`/api/producers?${params.toString()}`);
      const data = await response.json();
      
      if (data.success) {
        setProducers(data.data.producers);
        setPagination(data.data.pagination);
      } else {
        setError(data.error || '获取制作商列表失败');
      }
    } catch (err) {
      setError('网络请求失败');
      console.error('获取制作商列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchProducers(1, '', 'popular');
  }, []);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchProducers(1, searchQuery, sortBy);
  };

  // 处理排序变化
  const handleSortChange = (newSort: string) => {
    setSortBy(newSort);
    fetchProducers(1, searchQuery, newSort);
  };

  // 处理分页
  const handlePageChange = (newPage: number) => {
    fetchProducers(newPage, searchQuery, sortBy);
  };

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* YouTube风格的顶部导航 */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* 侧边栏覆盖层 - 仅在打开时显示 */}
      {sidebarOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          {/* 侧边栏 */}
          <div className="fixed left-0 top-16 z-50 h-[calc(100vh-4rem)]">
            <Sidebar
              sidebarOpen={true}
              activeCategory="all"
            />
          </div>
        </>
      )}

      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* 页面标题和搜索 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-4">制作商</h1>
          
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* 搜索框 */}
            <form onSubmit={handleSearch} className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索制作商..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-[#272727] text-white pl-10 pr-4 py-2 rounded-lg border-none focus:outline-none focus:ring-2 focus:ring-red-500"
                />
              </div>
            </form>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
              className="bg-[#272727] text-white px-3 py-2 rounded-lg border-none focus:outline-none text-sm"
            >
              <option value="popular">最受欢迎</option>
              <option value="name">名称排序</option>
              <option value="latest">最新加入</option>
              <option value="movies">作品数量</option>
            </select>
          </div>

          {/* 统计信息 */}
          <div className="text-gray-400 text-sm mt-4">
            {pagination.total} 个制作商
            {searchQuery && (
              <span className="ml-2">
                · 搜索 &ldquo;{searchQuery}&rdquo;
              </span>
            )}
          </div>
        </div>

        {/* 制作商列表 */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-white">加载中...</div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-red-400">{error}</div>
          </div>
        ) : producers.length > 0 ? (
          <>
            <div className="grid gap-4 grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
              {producers.map((producer) => (
                <Link
                  key={producer.id}
                  href={`/producers/${producer.id}`}
                  className="group transition-all duration-500 ease-out block text-center hover:-translate-y-1 hover:scale-105"
                >
                  {/* 制作商图标占位符 */}
                  <div className="w-20 h-20 mx-auto mb-2 relative">
                    <div className="w-full h-full rounded-lg bg-[#2a2a2a] flex items-center justify-center group-hover:bg-[#3a3a3a] transition-colors duration-500">
                      <Building className="w-8 h-8 text-gray-500" />
                    </div>
                  </div>

                  {/* 制作商信息 */}
                  <div className="px-1">
                    <h3 className="text-gray-300 group-hover:text-gray-100 font-medium mb-1 text-xs leading-tight transition-colors duration-500 line-clamp-2">{producer.name}</h3>
                    <div className="text-gray-400 group-hover:text-gray-300 text-xs flex items-center justify-center gap-1 transition-colors duration-500">
                      <Play className="w-3 h-3" />
                      {producer.movieCount}
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* 分页控件 */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-8">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="flex items-center gap-1 px-3 py-2 bg-[#272727] text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#3a3a3a] transition-colors"
                >
                  <ChevronLeft className="w-4 h-4" />
                  上一页
                </button>
                
                <span className="text-gray-400 text-sm">
                  第 {pagination.page} 页，共 {pagination.totalPages} 页
                </span>
                
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                  className="flex items-center gap-1 px-3 py-2 bg-[#272727] text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#3a3a3a] transition-colors"
                >
                  下一页
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center py-12">
            <div className="text-gray-400">
              {searchQuery ? `未找到包含 "${searchQuery}" 的制作商` : '暂无制作商数据'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

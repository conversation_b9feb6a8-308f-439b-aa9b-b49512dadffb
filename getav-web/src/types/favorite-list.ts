// 收藏列表相关类型定义

export interface FavoriteList {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  count?: number; // 收藏数量
}

export interface FavoriteListWithMovies extends FavoriteList {
  movies: FavoriteMovie[];
}

export interface FavoriteMovie {
  id: string;
  title: string;
  img?: string;
  localImg?: string;
  date?: string;
  videoLength?: string;
  description?: string;
  gid?: string;
  uc?: string;
  director?: {
    id: string;
    name: string;
  };
  producer?: {
    id: string;
    name: string;
  };
  publisher?: {
    id: string;
    name: string;
  };
  series?: {
    id: string;
    name: string;
  };
  stars?: Array<{
    star: {
      id: string;
      name: string;
      localAvatar?: string;
    };
  }>;
  genres?: Array<{
    genre: {
      id: string;
      name: string;
    };
  }>;
  views: number;
  likes: number;
  favorites: number;
  createdAt: Date;
  updatedAt: Date;
  favoriteCreatedAt?: Date; // 收藏时间
}

// API请求类型
export interface CreateFavoriteListRequest {
  name: string;
  description?: string;
  isPublic?: boolean;
  userId: string;
}

export interface UpdateFavoriteListRequest {
  name?: string;
  description?: string;
  isPublic?: boolean;
  userId: string;
}

export interface InitFavoriteListsRequest {
  userId: string;
}

// API响应类型
export interface FavoriteListResponse {
  success: boolean;
  data: FavoriteList;
  message: string;
}

export interface FavoriteListsResponse {
  success: boolean;
  data: FavoriteList[];
  message: string;
}

export interface FavoriteListMoviesResponse {
  success: boolean;
  data: {
    list: {
      id: string;
      name: string;
      description?: string;
      isDefault: boolean;
      isPublic: boolean;
    };
    movies: FavoriteMovie[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  message: string;
}

// 收藏操作相关类型
export interface AddToFavoriteListRequest {
  movieId: string;
  userId: string;
  favoriteListId?: string; // 如果不提供，则添加到默认列表
  action?: 'add' | 'remove' | 'toggle'; // 操作类型，默认为toggle
}

export interface MoveFavoriteRequest {
  movieId: string;
  userId: string;
  fromListId?: string; // 源列表ID，可为空（表示从无列表状态移动）
  toListId: string; // 目标列表ID
}

export interface BatchMoveFavoriteRequest {
  movieIds: string[];
  userId: string;
  fromListId?: string;
  toListId: string;
}

export interface CopyFavoriteRequest {
  movieId: string;
  userId: string;
  fromListId?: string; // 源列表ID，可为空
  toListId: string; // 目标列表ID
}

export interface BatchCopyFavoriteRequest {
  movieIds: string[];
  userId: string;
  fromListId?: string;
  toListId: string;
}

export interface RemoveFromFavoriteListRequest {
  movieId: string;
  userId: string;
  favoriteListId: string;
}

// 收藏状态查询响应
export interface FavoriteStatusResponse {
  success: boolean;
  data: {
    isFavorited: boolean;
    favoriteListId?: string;
    favoriteLists?: Array<{
      id: string;
      name: string;
      isFavorited: boolean;
    }>;
  };
  message: string;
}

// 前端组件使用的类型
export interface FavoriteListOption {
  id: string;
  name: string;
  count: number;
  isDefault: boolean;
}

export interface FavoriteListModalProps {
  isOpen: boolean;
  onClose: () => void;
  movieId: string;
  movieTitle: string;
  currentFavoriteListId?: string; // 当前所在的收藏列表ID
  onFavoriteChange?: (listId: string | null, action: 'add' | 'remove' | 'move') => void;
}

export interface CreateListModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onListCreated: (list: FavoriteList) => void;
  movieId?: string;
  movieTitle?: string;
}

export interface EditListModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  list: FavoriteList | null;
  onListUpdated: (list: FavoriteList) => void;
  onListDeleted: (listId: string) => void;
}

export interface QuickCreateListFormProps {
  userId: string;
  onListCreated: (list: FavoriteList) => void;
  onCancel: () => void;
  movieId?: string;
  className?: string;
}

export interface FavoriteListManagerProps {
  userId: string;
  movieId?: string;
  movieTitle?: string;
  currentFavoriteListId?: string;
  onFavoriteChange?: (listId: string | null, action: 'add' | 'remove' | 'move') => void;
  children: (props: {
    showFavoriteModal: () => void;
    showCreateModal: () => void;
    showEditModal: (list: FavoriteList) => void;
  }) => React.ReactNode;
}

// 错误类型
export interface FavoriteListError {
  code: string;
  message: string;
  details?: string;
}

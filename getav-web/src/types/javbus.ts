// JAVBUS API 返回的数据类型定义

export interface JavbusMovie {
  id: string;
  title: string;
  img: string;
  imageSize?: {
    width: number;
    height: number;
  };
  date: string;
  videoLength?: number;
  director?: {
    id: string;
    name: string;
  };
  producer?: {
    id: string;
    name: string;
  };
  publisher?: {
    id: string;
    name: string;
  };
  series?: {
    id: string;
    name: string;
  };
  genres: Array<{
    id: string;
    name: string;
  }>;
  stars: Array<{
    id: string;
    name: string;
  }>;
  samples: Array<{
    alt: string;
    id: string;
    src: string;
    thumbnail: string;
  }>;
  similarMovies: Array<{
    id: string;
    title: string;
    img: string;
  }>;
  gid: string;
  uc: string;
}

export interface JavbusStar {
  id: string;
  name: string;
  avatar: string;
  birthday?: string;
  age?: string;
  height?: string;
  bust?: string;
  waistline?: string;
  hipline?: string;
  birthplace?: string;
  hobby?: string;
  cupSize?: string;
  measurements?: string;
  description?: string;
}

export interface JavbusMagnet {
  id: string;
  link: string;
  isHD: boolean;
  title: string;
  size: string;
  numberSize: number;
  shareDate: string;
  hasSubtitle: boolean;
}

export interface JavbusMovieList {
  movies: Array<{
    date: string;
    id: string;
    img: string;
    title: string;
    tags: string[];
  }>;
  pagination: {
    currentPage: number;
    hasNextPage: boolean;
    nextPage?: number;
    pages: number[];
  };
  filter?: {
    name: string;
    type: string;
    value: string;
  };
}

// 本地数据库类型定义
export interface LocalMovie {
  id: string;
  title: string;
  img?: string | null;
  localImg?: string | null;
  date?: string | null;
  videoLength?: number | null;
  durationSeconds?: number | null;
  durationFormatted?: string | null;
  description?: string | null;
  gid?: string | null;
  uc?: string | null;
  streamtapeUrl?: string | null;
  streamhgUrl?: string | null;
  dataSource?: string;
  qualityScore?: number;
  createdAt: Date;
  updatedAt: Date;
  // 统计数据
  views?: number;
  likes?: number;
  favorites?: number;
  director?: {
    id: string;
    name: string;
  } | null;
  producer?: {
    id: string;
    name: string;
  } | null;
  publisher?: {
    id: string;
    name: string;
  } | null;
  series?: {
    id: string;
    name: string;
  } | null;
  stars: Array<{
    id: string;
    name: string;
    localAvatar?: string | null;
  }>;
  genres: Array<{
    id: string;
    name: string;
  }>;
  samples: Array<{
    id: string;
    alt?: string | null;
    localSrc?: string | null;
  }>;
  magnets: Array<{
    id: string;
    link: string;
    isHD?: boolean | null;
    title?: string | null;
    size?: string | null;
    numberSize?: string | null;
    shareDate?: string | null;
    hasSubtitle?: boolean | null;
    createdAt?: Date;
  }>;
  // 相关影片信息（只包含数据库中真实存在的影片）
  relatedMovies?: Array<{
    id: string;
    title: string;
    img?: string | null;
    date?: string | null;
    stars: Array<{
      id: string;
      name: string;
    }>;
  }>;
}

// 简化的Movie类型，用于Studio和Star的recentMovies
export interface SimpleMovie {
  id: string;
  title: string;
  localImg: string | null;
  date: string | null;
}

// 统一的Studio类型定义
export interface Studio {
  id: string;
  name: string;
  types: ('producer' | 'publisher')[];
  movieCount: number;
  recentMovies: SimpleMovie[];
}

// 统一的Star类型定义
export interface Star {
  id: string;
  name: string;
  movieCount: number;
  recentMovies: SimpleMovie[];
}

export interface LocalStar {
  id: string;
  name: string;
  avatar?: string;
  localAvatar?: string;
  birthday?: string;
  age?: string;
  height?: string;
  bust?: string;
  waist?: string;
  hip?: string;
  birthplace?: string;
  hobby?: string;
  cupSize?: string;
  measurements?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 数据采集状态
export interface ImportStatus {
  isImporting: boolean;
  totalMovies: number;
  processedMovies: number;
  successfulMovies: number;
  failedMovies: number;
  startTime?: Date;
  endTime?: Date;
  lastError?: string;
  currentMovieId?: string;
}

// 新API数据类型定义
export interface NewApiMovie {
  id: number;
  code: string;
  title: string;
  title_en?: string;
  studio?: string;
  release_date?: string;
  duration?: number;
  rating?: number;
  plot?: string;
  plot_en?: string;
  cover_url?: string;
  poster_url?: string;
  streamtape_url?: string;
  streamhg_url?: string;
  scraping_status?: string;
  scraping_source?: string;
  actor_count?: number;
  genre_count?: number;
  magnet_count?: number;
  created_at?: string;
  updated_at?: string;
}

// 增强的Movie类型（包含新字段）
export interface EnhancedMovie extends LocalMovie {
  streamtapeUrl?: string | null;
  streamhgUrl?: string | null;
  dataSource: 'new_api' | 'javbus' | 'mixed';
  qualityScore: number;
}

// 数据源类型
export type DataSourceType = 'new_api' | 'javbus' | 'mixed';

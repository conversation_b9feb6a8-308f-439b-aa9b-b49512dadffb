# JAV API 快速开始指南

## 🚀 快速开始

### 基础信息
- **服务器**: `http://*************:8081`
- **访问限制**: 仅限IP `************`
- **数据格式**: JSON

### 📋 可用的公开接口

| 接口 | 地址 | 说明 |
|------|------|------|
| 最新影片 | `GET /api/v1/jav/movies/latest` | 获取最新影片列表 |
| 热门影片 | `GET /api/v1/jav/movies/popular` | 获取热门影片列表 |
| 分类列表 | `GET /api/v1/jav/genres` | 获取所有分类 |
| 热门分类 | `GET /api/v1/jav/genres/popular` | 获取热门分类 |
| 热门演员 | `GET /api/v1/jav/actors/popular` | 获取热门演员 |
| 统计信息 | `GET /api/v1/jav/stats` | 获取数据库统计 |
| 健康检查 | `GET /health` | 服务器状态检查 |
| 系统版本 | `GET /api/v1/system/version` | 获取系统版本 |

### 🔥 常用示例

#### 1. 获取最新20部影片
```bash
curl "http://your-server-ip:8081/api/v1/jav/movies/latest?limit=20"
```

#### 2. 获取数据库统计
```bash
curl "http://your-server-ip:8081/api/v1/jav/stats"
```

#### 3. 获取所有分类
```bash
curl "http://your-server-ip:8081/api/v1/jav/genres"
```

#### 4. 检查服务器状态
```bash
curl "http://your-server-ip:8081/health"
```

### 📊 当前数据状态

根据最新统计：
- **总影片数**: 778部
- **已完成采集**: 745部
- **待处理**: 33部

### ⚠️ 重要提醒

1. **IP限制**: 只有IP `************` 可以访问JAV相关接口
2. **请求频率**: 建议每秒不超过10次请求
3. **数据完整性**: 优先使用`scraping_status`为"completed"的数据
4. **错误处理**: 403错误表示IP被拒绝，500错误请检查服务器状态

### 🐍 Python快速示例

```python
import requests

# API基础配置
BASE_URL = "http://your-server-ip:8081"

def get_latest_movies(limit=20):
    """获取最新影片"""
    url = f"{BASE_URL}/api/v1/jav/movies/latest"
    response = requests.get(url, params={"limit": limit})
    return response.json()

def get_stats():
    """获取统计信息"""
    url = f"{BASE_URL}/api/v1/jav/stats"
    response = requests.get(url)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 获取最新影片
    movies = get_latest_movies(10)
    print(f"获取到 {len(movies['data'])} 部影片")
    
    # 获取统计信息
    stats = get_stats()
    print(f"数据库共有 {stats['data']['total_movies']} 部影片")
```

### 📝 响应格式

所有接口都返回统一的JSON格式：

```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "data": { ... },
  "timestamp": 1751458604,
  "request_id": "1751458604095729718"
}
```

### 🔧 故障排除

| 错误码 | 原因 | 解决方案 |
|--------|------|----------|
| 403 | IP不在白名单 | 确认请求来源IP为************ |
| 404 | 接口不存在 | 检查URL路径是否正确 |
| 500 | 服务器错误 | 检查服务器状态或联系管理员 |

### 📚 完整文档

详细的API文档请参考：[API_USAGE_GUIDE.md](./API_USAGE_GUIDE.md)

---

**快速开始指南** | **最后更新**: 2025-07-02



# JAV API 使用说明文档

## 概述

本文档详细介绍了JAV API系统的使用方法，包括接口说明、访问限制、数据格式和使用示例。

## 基础信息

- **服务器地址**: `http://*************:8081`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **访问限制**: 仅限IP `************` 访问公开接口

## 访问控制

### IP白名单限制

所有JAV公开API接口仅允许以下IP地址访问：
- `************` - 指定的外部采集IP
- `127.0.0.1` - 本地访问
- `::1` - IPv6本地访问

**未授权IP访问将返回403错误：**
```json
{
  "success": false,
  "code": 403,
  "message": "Access denied: IP not in whitelist",
  "error": "forbidden",
  "client_ip": "your.ip.address"
}
```

## 公开API接口

### 1. 获取最新影片

**接口地址**: `GET /api/v1/jav/movies/latest`

**请求参数**:
- `limit` (可选): 返回数量，默认20，最大100

**请求示例**:
```bash
curl "http://your-server-ip:8081/api/v1/jav/movies/latest?limit=10"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 476,
      "code": "FCH-105",
      "title": "FCH-105 「あなたのち●ぽは私の物！」いじられまくって超悶絶！",
      "title_en": "[Javinizer] Movie Title - FCH-105",
      "studio": "DOC",
      "release_date": "2025-06-20T08:00:00+08:00",
      "duration": 156,
      "rating": 0,
      "plot": "影片详细描述...",
      "plot_en": "English description...",
      "cover_url": "https://www.javbus.com/pics/cover/bfo5_b.jpg",
      "poster_url": "",
      "streamtape_url": "",
      "streamhg_url": "",
      "scraping_status": "completed",
      "scraping_source": "javbus",
      "actor_count": 0,
      "genre_count": 0,
      "magnet_count": 0,
      "created_at": "2025-07-02T20:16:41.024814+08:00",
      "updated_at": "2025-07-02T20:16:41.02502+08:00"
    }
  ],
  "timestamp": 1751458604,
  "request_id": "1751458604095729718"
}
```

### 2. 获取热门影片

**接口地址**: `GET /api/v1/jav/movies/popular`

**请求参数**:
- `limit` (可选): 返回数量，默认20，最大100

**请求示例**:
```bash
curl "http://your-server-ip:8081/api/v1/jav/movies/popular?limit=10"
```

**注意**: 此接口目前可能返回500错误，因为数据库缺少`view_count`字段。

### 3. 获取所有分类

**接口地址**: `GET /api/v1/jav/genres`

**请求示例**:
```bash
curl "http://your-server-ip:8081/api/v1/jav/genres"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 71,
      "name": "4K",
      "name_en": "",
      "name_jp": "",
      "description": "",
      "movie_count": 0,
      "created_at": "2025-07-01T15:17:32.814335+08:00",
      "updated_at": "0001-01-01T00:00:00Z"
    }
  ],
  "timestamp": 1751458628,
  "request_id": "1751458628982368816"
}
```

### 4. 获取热门分类

**接口地址**: `GET /api/v1/jav/genres/popular`

**请求示例**:
```bash
curl "http://your-server-ip:8081/api/v1/jav/genres/popular"
```

### 5. 获取热门演员

**接口地址**: `GET /api/v1/jav/actors/popular`

**请求参数**:
- `limit` (可选): 返回数量，默认20，最大100

**请求示例**:
```bash
curl "http://your-server-ip:8081/api/v1/jav/actors/popular?limit=10"
```

### 6. 获取统计信息

**接口地址**: `GET /api/v1/jav/stats`

**请求示例**:
```bash
curl "http://your-server-ip:8081/api/v1/jav/stats"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "data": {
    "total_movies": 778,
    "total_actors": 0,
    "total_genres": 0,
    "total_magnets": 0,
    "completed_movies": 745,
    "pending_movies": 33,
    "failed_movies": 0,
    "avg_rating": 0,
    "avg_magnets_per_movie": 0,
    "top_studios": null,
    "top_actors": null,
    "top_genres": null,
    "quality_distribution": null,
    "subtitle_distribution": null
  },
  "timestamp": **********,
  "request_id": "**********733639543"
}
```

## 系统接口（无IP限制）

### 1. 健康检查

**接口地址**: `GET /health`

**请求示例**:
```bash
curl "http://your-server-ip:8081/health"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "data": {
    "aria2": true,
    "queue": true
  },
  "timestamp": **********,
  "request_id": "**********895918015"
}
```

### 2. 系统版本

**接口地址**: `GET /api/v1/system/version`

**请求示例**:
```bash
curl "http://your-server-ip:8081/api/v1/system/version"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "data": {
    "version": "1.0.0",
    "build_time": "2025-06-23",
    "go_version": "go1.24.3",
    "git_commit": "unknown"
  },
  "timestamp": **********,
  "request_id": "**********143656690"
}
```

## 需要认证的接口

以下接口需要JWT认证，不受IP白名单限制，但需要有效的访问令牌：

### 1. 搜索影片
- `GET /api/v1/jav/movies/search`

### 2. 获取影片详情
- `GET /api/v1/jav/movies/{code}`

### 3. 获取影片磁力链接
- `GET /api/v1/jav/movies/{code}/magnets`

### 4. 获取最佳磁力链接
- `GET /api/v1/jav/movies/{code}/best-magnet`

**认证方式**:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://your-server-ip:8081/api/v1/jav/movies/search?keyword=关键词"
```

## 数据字段说明

### 影片数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 影片ID |
| code | string | 影片番号 |
| title | string | 影片标题（日文） |
| title_en | string | 影片标题（英文） |
| studio | string | 制作公司 |
| release_date | string | 发布日期（ISO 8601格式） |
| duration | int | 时长（分钟） |
| rating | float | 评分 |
| plot | string | 剧情描述（中文） |
| plot_en | string | 剧情描述（英文） |
| cover_url | string | 封面图片URL |
| poster_url | string | 海报图片URL |
| streamtape_url | string | StreamTape播放链接 |
| streamhg_url | string | StreamHG播放链接 |
| scraping_status | string | 采集状态（completed/pending/failed） |
| scraping_source | string | 数据来源（javbus/javinizer等） |
| actor_count | int | 演员数量 |
| genre_count | int | 分类数量 |
| magnet_count | int | 磁力链接数量 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

### 分类数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 分类ID |
| name | string | 分类名称 |
| name_en | string | 英文名称 |
| name_jp | string | 日文名称 |
| description | string | 分类描述 |
| movie_count | int | 影片数量 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

## 错误处理

### 常见错误码

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 200 | 成功 | 请求成功 |
| 400 | 请求错误 | 参数错误或格式不正确 |
| 403 | 访问被拒绝 | IP不在白名单中 |
| 404 | 未找到 | 资源不存在 |
| 500 | 服务器错误 | 内部服务器错误 |

### 错误响应格式

```json
{
  "success": false,
  "code": 403,
  "message": "Access denied: IP not in whitelist",
  "error": "forbidden",
  "client_ip": "*************",
  "timestamp": 1751458000,
  "request_id": "1751458000123456789"
}
```

## 使用建议

### 1. 请求频率控制
- 建议控制请求频率，避免对服务器造成过大压力
- 推荐间隔时间：每秒不超过10次请求

### 2. 数据缓存
- 建议在客户端实现数据缓存机制
- 分类数据变化较少，可以缓存较长时间
- 影片数据建议每小时更新一次

### 3. 错误重试
- 遇到5xx错误时，建议实现指数退避重试机制
- 遇到403错误时，检查IP配置，不要重试

### 4. 数据完整性
- 部分字段可能为空，请在使用前进行空值检查
- `scraping_status`为"completed"的影片数据相对完整

## 高级用法

### 1. 批量数据采集示例

```bash
#!/bin/bash

# 设置服务器地址
SERVER="http://your-server-ip:8081"

# 获取最新100部影片
echo "获取最新影片..."
curl -s "${SERVER}/api/v1/jav/movies/latest?limit=100" > latest_movies.json

# 获取所有分类
echo "获取分类信息..."
curl -s "${SERVER}/api/v1/jav/genres" > genres.json

# 获取统计信息
echo "获取统计信息..."
curl -s "${SERVER}/api/v1/jav/stats" > stats.json

echo "数据采集完成！"
```

### 2. Python采集示例

```python
import requests
import json
import time

class JAVAPIClient:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()

    def get_latest_movies(self, limit=20):
        """获取最新影片"""
        url = f"{self.base_url}/api/v1/jav/movies/latest"
        params = {"limit": limit}
        response = self.session.get(url, params=params)
        return response.json()

    def get_genres(self):
        """获取所有分类"""
        url = f"{self.base_url}/api/v1/jav/genres"
        response = self.session.get(url)
        return response.json()

    def get_stats(self):
        """获取统计信息"""
        url = f"{self.base_url}/api/v1/jav/stats"
        response = self.session.get(url)
        return response.json()

    def batch_collect(self):
        """批量采集数据"""
        print("开始数据采集...")

        # 获取统计信息
        stats = self.get_stats()
        print(f"数据库共有 {stats['data']['total_movies']} 部影片")

        # 获取最新影片
        movies = self.get_latest_movies(100)
        print(f"获取到 {len(movies['data'])} 部最新影片")

        # 获取分类
        genres = self.get_genres()
        print(f"获取到 {len(genres['data'])} 个分类")

        return {
            "stats": stats,
            "movies": movies,
            "genres": genres
        }

# 使用示例
if __name__ == "__main__":
    client = JAVAPIClient("http://your-server-ip:8081")
    data = client.batch_collect()

    # 保存数据
    with open("jav_data.json", "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print("数据采集完成，已保存到 jav_data.json")
```

### 3. 数据同步策略

```python
import requests
import sqlite3
from datetime import datetime

class JAVDataSync:
    def __init__(self, api_base_url, local_db_path):
        self.api_base_url = api_base_url
        self.local_db_path = local_db_path
        self.init_local_db()

    def init_local_db(self):
        """初始化本地数据库"""
        conn = sqlite3.connect(self.local_db_path)
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS movies (
                id INTEGER PRIMARY KEY,
                code TEXT UNIQUE,
                title TEXT,
                studio TEXT,
                release_date TEXT,
                duration INTEGER,
                cover_url TEXT,
                streamtape_url TEXT,
                streamhg_url TEXT,
                created_at TEXT,
                updated_at TEXT,
                synced_at TEXT
            )
        ''')

        conn.commit()
        conn.close()

    def sync_latest_movies(self, limit=100):
        """同步最新影片数据"""
        # 从API获取数据
        response = requests.get(f"{self.api_base_url}/api/v1/jav/movies/latest?limit={limit}")
        data = response.json()

        if not data['success']:
            print(f"API请求失败: {data['message']}")
            return

        # 保存到本地数据库
        conn = sqlite3.connect(self.local_db_path)
        cursor = conn.cursor()

        synced_count = 0
        for movie in data['data']:
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO movies
                    (id, code, title, studio, release_date, duration,
                     cover_url, streamtape_url, streamhg_url, created_at, updated_at, synced_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    movie['id'], movie['code'], movie['title'], movie['studio'],
                    movie['release_date'], movie['duration'], movie['cover_url'],
                    movie['streamtape_url'], movie['streamhg_url'],
                    movie['created_at'], movie['updated_at'], datetime.now().isoformat()
                ))
                synced_count += 1
            except Exception as e:
                print(f"同步影片 {movie['code']} 失败: {e}")

        conn.commit()
        conn.close()

        print(f"成功同步 {synced_count} 部影片")
        return synced_count

# 使用示例
if __name__ == "__main__":
    sync = JAVDataSync("http://your-server-ip:8081", "local_jav.db")
    sync.sync_latest_movies(200)
```

## 数据库当前状态

根据最新统计信息，当前数据库包含：
- **总影片数**: 778部
- **已完成采集**: 745部
- **待处理**: 33部
- **失败**: 0部

## 性能优化建议

### 1. 分页获取大量数据
```bash
# 分批获取所有影片数据
for i in {0..7}; do
  offset=$((i * 100))
  curl -s "${SERVER}/api/v1/jav/movies/latest?limit=100&offset=${offset}" > "movies_batch_${i}.json"
  sleep 1  # 避免请求过于频繁
done
```

### 2. 增量更新策略
- 定期获取最新影片，与本地数据对比
- 只同步新增或更新的影片
- 使用`updated_at`字段判断数据是否有变化

### 3. 错误处理和重试
```python
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_session_with_retries():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session
```

## 监控和日志

### 1. API响应时间监控
```bash
# 监控API响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://your-server-ip:8081/api/v1/jav/stats"

# curl-format.txt 内容:
#      time_namelookup:  %{time_namelookup}\n
#         time_connect:  %{time_connect}\n
#      time_appconnect:  %{time_appconnect}\n
#     time_pretransfer:  %{time_pretransfer}\n
#        time_redirect:  %{time_redirect}\n
#   time_starttransfer:  %{time_starttransfer}\n
#                      ----------\n
#           time_total:  %{time_total}\n
```

### 2. 数据质量检查
```python
def check_data_quality(movies_data):
    """检查数据质量"""
    total = len(movies_data)
    has_cover = sum(1 for m in movies_data if m.get('cover_url'))
    has_streamtape = sum(1 for m in movies_data if m.get('streamtape_url'))
    has_plot = sum(1 for m in movies_data if m.get('plot'))

    print(f"数据质量报告:")
    print(f"总影片数: {total}")
    print(f"有封面图: {has_cover} ({has_cover/total*100:.1f}%)")
    print(f"有播放链接: {has_streamtape} ({has_streamtape/total*100:.1f}%)")
    print(f"有剧情描述: {has_plot} ({has_plot/total*100:.1f}%)")
```

## 故障排除

### 常见问题及解决方案

1. **403 Forbidden错误**
   - 检查请求IP是否为************
   - 确认代理设置正确

2. **连接超时**
   - 检查网络连接
   - 增加请求超时时间

3. **数据不完整**
   - 检查`scraping_status`字段
   - 优先使用状态为"completed"的数据

4. **500服务器错误**
   - 检查服务器日志
   - 可能是数据库字段缺失导致

## 联系信息

如有问题或需要技术支持，请联系系统管理员。

**服务器状态监控**: `GET /health`
**API文档**: 本文档
**数据更新频率**: 实时更新

---

**最后更新**: 2025-07-02
**API版本**: v1.0.0
**文档版本**: 1.0

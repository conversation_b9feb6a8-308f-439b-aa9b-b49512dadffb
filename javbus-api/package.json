{"name": "javbus-api", "version": "2.1.4", "description": "JavBus API", "main": "index.js", "homepage": "https://github.com/ovnrain/javbus-api#readme", "scripts": {"start": "node -r dotenv/config dist/server.js", "dev": "tsx watch --clear-screen=false -r dotenv/config api/server.ts", "build": "tsc", "vercel-build": "tsc", "format": "prettier --write \"api/**/*.ts\"", "lint": "eslint --config .eslintrc.commit.json \"api/**/*.ts\"", "prepare": "husky"}, "keywords": ["javbus", "api"], "author": {"name": "ovnrain", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/ovnrain/javbus-api.git"}, "license": "MIT", "devDependencies": {"@types/bytes": "^3.1.5", "@types/express": "^5.0.2", "@types/express-session": "^1.18.1", "@types/http-errors": "^2.0.4", "@types/node": "^22.15.29", "@types/probe-image-size": "^7.2.5", "@types/qs": "^6.14.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "dependencies": {"bytes": "^3.1.2", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "got": "^14.4.7", "http-errors": "^2.0.0", "https-proxy-agent": "^7.0.6", "memorystore": "^1.6.7", "node-html-parser": "^7.0.1", "probe-image-size": "^7.2.3", "qs": "^6.14.0", "socks-proxy-agent": "^8.0.5", "znv": "^0.5.0", "zod": "^3.25.42"}, "type": "module", "lint-staged": {"*.ts": ["eslint --config .eslintrc.commit.json"], "*.html": ["prettier --write"]}}
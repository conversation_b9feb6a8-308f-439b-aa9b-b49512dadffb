export const languageOptions = [
  { label: 'plaintext', value: 'plaintext' },
  { label: 'abap', value: 'abap' },
  { label: 'apex', value: 'apex' },
  { label: 'azcli', value: 'azcli' },
  { label: 'bat', value: 'bat' },
  { label: 'bicep', value: 'bicep' },
  { label: 'cameligo', value: 'cameligo' },
  { label: 'clojure', value: 'clojure' },
  { label: 'coffeescript', value: 'coffeescript' },
  { label: 'c', value: 'c' },
  { label: 'cpp', value: 'cpp' },
  { label: 'csharp', value: 'csharp' },
  { label: 'csp', value: 'csp' },
  { label: 'css', value: 'css' },
  { label: 'cypher', value: 'cypher' },
  { label: 'dart', value: 'dart' },
  { label: 'dockerfile', value: 'dockerfile' },
  { label: 'ecl', value: 'ecl' },
  { label: 'elixir', value: 'elixir' },
  { label: 'flow9', value: 'flow9' },
  { label: 'fsharp', value: 'fsharp' },
  { label: 'freemarker2', value: 'freemarker2' },
  {
    label: 'freemarker2.tag-angle.interpolation-dollar',
    value: 'freemarker2.tag-angle.interpolation-dollar'
  },
  {
    label: 'freemarker2.tag-bracket.interpolation-dollar',
    value: 'freemarker2.tag-bracket.interpolation-dollar'
  },
  {
    label: 'freemarker2.tag-angle.interpolation-bracket',
    value: 'freemarker2.tag-angle.interpolation-bracket'
  },
  {
    label: 'freemarker2.tag-bracket.interpolation-bracket',
    value: 'freemarker2.tag-bracket.interpolation-bracket'
  },
  {
    label: 'freemarker2.tag-auto.interpolation-dollar',
    value: 'freemarker2.tag-auto.interpolation-dollar'
  },
  {
    label: 'freemarker2.tag-auto.interpolation-bracket',
    value: 'freemarker2.tag-auto.interpolation-bracket'
  },
  { label: 'go', value: 'go' },
  { label: 'graphql', value: 'graphql' },
  { label: 'handlebars', value: 'handlebars' },
  { label: 'hcl', value: 'hcl' },
  { label: 'html', value: 'html' },
  { label: 'ini', value: 'ini' },
  { label: 'java', value: 'java' },
  { label: 'javascript', value: 'javascript' },
  { label: 'julia', value: 'julia' },
  { label: 'kotlin', value: 'kotlin' },
  { label: 'less', value: 'less' },
  { label: 'lexon', value: 'lexon' },
  { label: 'lua', value: 'lua' },
  { label: 'liquid', value: 'liquid' },
  { label: 'm3', value: 'm3' },
  { label: 'markdown', value: 'markdown' },
  { label: 'mdx', value: 'mdx' },
  { label: 'mips', value: 'mips' },
  { label: 'msdax', value: 'msdax' },
  { label: 'mysql', value: 'mysql' },
  { label: 'objective-c', value: 'objective-c' },
  { label: 'pascal', value: 'pascal' },
  { label: 'pascaligo', value: 'pascaligo' },
  { label: 'perl', value: 'perl' },
  { label: 'pgsql', value: 'pgsql' },
  { label: 'php', value: 'php' },
  { label: 'pla', value: 'pla' },
  { label: 'postiats', value: 'postiats' },
  { label: 'powerquery', value: 'powerquery' },
  { label: 'powershell', value: 'powershell' },
  { label: 'proto', value: 'proto' },
  { label: 'pug', value: 'pug' },
  { label: 'python', value: 'python' },
  { label: 'qsharp', value: 'qsharp' },
  { label: 'r', value: 'r' },
  { label: 'razor', value: 'razor' },
  { label: 'redis', value: 'redis' },
  { label: 'redshift', value: 'redshift' },
  { label: 'restructuredtext', value: 'restructuredtext' },
  { label: 'ruby', value: 'ruby' },
  { label: 'rust', value: 'rust' },
  { label: 'sb', value: 'sb' },
  { label: 'scala', value: 'scala' },
  { label: 'scheme', value: 'scheme' },
  { label: 'scss', value: 'scss' },
  { label: 'shell', value: 'shell' },
  { label: 'sol', value: 'sol' },
  { label: 'aes', value: 'aes' },
  { label: 'sparql', value: 'sparql' },
  { label: 'sql', value: 'sql' },
  { label: 'st', value: 'st' },
  { label: 'swift', value: 'swift' },
  { label: 'systemverilog', value: 'systemverilog' },
  { label: 'verilog', value: 'verilog' },
  { label: 'tcl', value: 'tcl' },
  { label: 'twig', value: 'twig' },
  { label: 'typescript', value: 'typescript' },
  { label: 'vb', value: 'vb' },
  { label: 'wgsl', value: 'wgsl' },
  { label: 'xml', value: 'xml' },
  { label: 'yaml', value: 'yaml' },
  { label: 'json', value: 'json' }
]

export const themeOptions = [
  {
    label: 'vs',
    value: 'vs'
  },
  {
    label: 'vs-dark',
    value: 'vs-dark'
  },
  {
    label: 'hc-black',
    value: 'hc-black'
  },
  {
    label: 'hc-light',
    value: 'hc-light'
  }
]

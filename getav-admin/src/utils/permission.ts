import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { PermissionItem } from '@/api/login/types'

/**
 * 权限工具函数
 * 提供便捷的权限检查方法
 */

/**
 * 检查用户是否拥有指定角色
 * @param roleName 角色名称
 * @returns boolean
 */
export function hasRole(roleName: string): boolean {
  const userStore = useUserStore()
  return userStore.hasRole(roleName)
}

/**
 * 检查用户是否拥有任一指定角色
 * @param roleNames 角色名称数组
 * @returns boolean
 */
export function hasAnyRole(roleNames: string[]): boolean {
  const userStore = useUserStore()
  return userStore.hasAnyRole(roleNames)
}

/**
 * 检查用户是否拥有指定权限
 * @param permissionName 权限名称
 * @returns boolean
 */
export function hasPermission(permissionName: string): boolean {
  const userStore = useUserStore()
  return userStore.hasPermission(permissionName)
}

/**
 * 检查用户是否拥有任一指定权限
 * @param permissionNames 权限名称数组
 * @returns boolean
 */
export function hasAnyPermission(permissionNames: string[]): boolean {
  const userStore = useUserStore()
  return userStore.hasAnyPermission(permissionNames)
}

/**
 * 检查用户是否拥有所有指定权限
 * @param permissionNames 权限名称数组
 * @returns boolean
 */
export function hasAllPermissions(permissionNames: string[]): boolean {
  const userStore = useUserStore()
  return userStore.hasAllPermissions(permissionNames)
}

/**
 * 检查用户是否拥有菜单权限
 * @param menuName 菜单名称
 * @returns boolean
 */
export function hasMenuPermission(menuName: string): boolean {
  const permissionStore = usePermissionStore()
  return permissionStore.hasMenuPermission(menuName)
}

/**
 * 检查用户是否拥有操作权限
 * @param actionName 操作名称
 * @returns boolean
 */
export function hasActionPermission(actionName: string): boolean {
  const permissionStore = usePermissionStore()
  return permissionStore.hasActionPermission(actionName)
}

/**
 * 检查用户是否拥有数据权限
 * @param dataName 数据权限名称
 * @returns boolean
 */
export function hasDataPermission(dataName: string): boolean {
  const permissionStore = usePermissionStore()
  return permissionStore.hasDataPermission(dataName)
}

/**
 * 检查用户是否拥有API权限
 * @param apiName API权限名称
 * @returns boolean
 */
export function hasApiPermission(apiName: string): boolean {
  const permissionStore = usePermissionStore()
  return permissionStore.hasApiPermission(apiName)
}

/**
 * 获取用户所有角色
 * @returns string[]
 */
export function getUserRoles(): string[] {
  const userStore = useUserStore()
  return userStore.getRoles()
}

/**
 * 获取用户所有权限
 * @returns PermissionItem[]
 */
export function getUserPermissions(): PermissionItem[] {
  const userStore = useUserStore()
  return userStore.getAllPermissions()
}

/**
 * 获取用户菜单权限
 * @returns PermissionItem[]
 */
export function getMenuPermissions(): PermissionItem[] {
  const permissionStore = usePermissionStore()
  return permissionStore.getMenuPermissions()
}

/**
 * 获取用户操作权限
 * @returns PermissionItem[]
 */
export function getActionPermissions(): PermissionItem[] {
  const permissionStore = usePermissionStore()
  return permissionStore.getActionPermissions()
}

/**
 * 检查是否为管理员
 * @returns boolean
 */
export function isAdmin(): boolean {
  return hasAnyRole(['admin', 'super_admin'])
}

/**
 * 检查是否为超级管理员
 * @returns boolean
 */
export function isSuperAdmin(): boolean {
  return hasRole('super_admin')
}

/**
 * 权限检查装饰器工厂
 * @param permissionName 权限名称
 * @returns 装饰器函数
 */
export function requirePermission(permissionName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function (...args: any[]) {
      if (!hasPermission(permissionName)) {
        console.warn(`缺少权限: ${permissionName}`)
        return
      }
      return originalMethod.apply(this, args)
    }

    return descriptor
  }
}

/**
 * 角色检查装饰器工厂
 * @param roleName 角色名称
 * @returns 装饰器函数
 */
export function requireRole(roleName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function (...args: any[]) {
      if (!hasRole(roleName)) {
        console.warn(`缺少角色: ${roleName}`)
        return
      }
      return originalMethod.apply(this, args)
    }

    return descriptor
  }
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 仪表板权限
  DASHBOARD_VIEW: 'dashboard:view',

  // 用户管理权限
  USER_READ: 'user:read',
  USER_CREATE: 'user:create',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_MANAGE: 'user:manage',

  // 角色管理权限
  ROLE_READ: 'role:read',
  ROLE_CREATE: 'role:create',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',
  ROLE_MANAGE: 'role:manage',

  // 权限管理权限
  PERMISSION_READ: 'permission:read',
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_UPDATE: 'permission:update',
  PERMISSION_DELETE: 'permission:delete',
  PERMISSION_MANAGE: 'permission:manage',

  // 影片管理权限
  MOVIE_READ: 'movie:read',
  MOVIE_CREATE: 'movie:create',
  MOVIE_UPDATE: 'movie:update',
  MOVIE_DELETE: 'movie:delete',
  MOVIE_MANAGE: 'movie:manage',

  // 演员管理权限
  STAR_READ: 'star:read',
  STAR_CREATE: 'star:create',
  STAR_UPDATE: 'star:update',
  STAR_DELETE: 'star:delete',
  STAR_MANAGE: 'star:manage',

  // 统计数据权限
  STATS_READ: 'stats:read',
  STATS_MANAGE: 'stats:manage',

  // 系统管理权限
  SYSTEM_READ: 'system:read',
  SYSTEM_UPDATE: 'system:update',
  SYSTEM_MANAGE: 'system:manage',

  // 数据导入权限
  IMPORT_EXECUTE: 'import:execute',
  IMPORT_MANAGE: 'import:manage'
} as const

/**
 * 角色常量定义
 */
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  EDITOR: 'editor',
  VIEWER: 'viewer',
  USER: 'user'
} as const

<template>
  <div class="activity-log-detail">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="日志ID">
        <el-tag type="info">{{ log?.id }}</el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="创建时间">
        {{ formatToDateTime(log?.createdAt) }}
      </el-descriptions-item>

      <el-descriptions-item label="用户信息">
        <div v-if="log?.user">
          <div class="font-medium">{{ log.user.username || log.user.name || '未知用户' }}</div>
          <div class="text-sm text-gray-500">{{ log.user.email }}</div>
          <div class="text-xs text-gray-400">ID: {{ log.user.id }}</div>
        </div>
        <div v-else class="text-gray-400">匿名用户</div>
      </el-descriptions-item>

      <el-descriptions-item label="行为类型">
        <el-tag :type="getActionTagType(log?.action)">
          {{ getActionLabel(log?.action) }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="目标类型">
        <el-tag type="info">
          {{ getTargetTypeLabel(log?.targetType) }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="目标ID">
        <span v-if="log?.targetId">{{ log.targetId }}</span>
        <span v-else class="text-gray-400">无</span>
      </el-descriptions-item>

      <el-descriptions-item label="IP地址">
        <span v-if="log?.ipAddress">{{ log.ipAddress }}</span>
        <span v-else class="text-gray-400">未记录</span>
      </el-descriptions-item>

      <el-descriptions-item label="用户代理" :span="2">
        <div v-if="log?.userAgent" class="user-agent">
          {{ log.userAgent }}
        </div>
        <span v-else class="text-gray-400">未记录</span>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 元数据信息 -->
    <div v-if="log?.metadata" class="mt-4">
      <h4 class="mb-2">元数据信息</h4>
      <el-card shadow="never" class="metadata-card">
        <pre class="metadata-content">{{ formatMetadata(log.metadata) }}</pre>
      </el-card>
    </div>

    <!-- 相关操作 -->
    <div class="mt-4 flex justify-end space-x-2">
      <el-button @click="handleCopyId">
        <Icon icon="ep:copy-document" class="mr-1" />
        复制ID
      </el-button>

      <el-button v-if="log?.targetId" type="primary" @click="handleViewTarget">
        <Icon icon="ep:view" class="mr-1" />
        查看目标
      </el-button>

      <el-button v-if="log?.user" type="success" @click="handleViewUser">
        <Icon icon="ep:user" class="mr-1" />
        查看用户
      </el-button>

      <el-button @click="$emit('close')"> 关闭 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@/components/Icon'
import type { ActivityLog } from '@/types/activity-logs'
import { ACTIVITY_ACTION_OPTIONS, ACTIVITY_TARGET_TYPE_OPTIONS } from '@/types/activity-logs'
import { formatToDateTime } from '@/utils/dateUtil'

// Props
interface Props {
  log: ActivityLog | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 计算属性
const actionOptions = ACTIVITY_ACTION_OPTIONS
const targetTypeOptions = ACTIVITY_TARGET_TYPE_OPTIONS

// 方法
const getActionLabel = (action?: string) => {
  if (!action) return '未知'
  const option = actionOptions.find((opt) => opt.value === action)
  return option?.label || action
}

const getTargetTypeLabel = (targetType?: string) => {
  if (!targetType) return '未知'
  const option = targetTypeOptions.find((opt) => opt.value === targetType)
  return option?.label || targetType
}

const getActionTagType = (action?: string) => {
  if (!action) return 'info'
  const typeMap: Record<string, string> = {
    view: 'info',
    like: 'success',
    dislike: 'warning',
    favorite: 'success',
    comment: 'primary',
    login: 'success',
    logout: 'info',
    error: 'danger'
  }
  return typeMap[action] || 'info'
}

const formatMetadata = (metadata: Record<string, any>) => {
  return JSON.stringify(metadata, null, 2)
}

const handleCopyId = async () => {
  if (!props.log?.id) return

  try {
    await navigator.clipboard.writeText(props.log.id)
    ElMessage.success('ID已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleViewTarget = () => {
  if (!props.log?.targetId || !props.log?.targetType) return

  // 根据目标类型跳转到相应页面
  const targetType = props.log.targetType
  const targetId = props.log.targetId

  ElMessage.info(`查看${getTargetTypeLabel(targetType)}: ${targetId}`)
  // TODO: 实现跳转逻辑
}

const handleViewUser = () => {
  if (!props.log?.user?.id) return

  ElMessage.info(`查看用户: ${props.log.user.username || props.log.user.name}`)
  // TODO: 实现跳转到用户详情页面
}
</script>

<style scoped>
.activity-log-detail {
  padding: 16px;
}

.font-medium {
  font-weight: 500;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.user-agent {
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}

.metadata-card {
  background-color: #f8f9fa;
}

.metadata-content {
  margin: 0;
  padding: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.space-x-2 > * + * {
  margin-left: 8px;
}
</style>

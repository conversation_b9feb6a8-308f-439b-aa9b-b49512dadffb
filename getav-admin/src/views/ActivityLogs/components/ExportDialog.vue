<template>
  <div class="export-dialog">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="导出格式" prop="format">
        <el-radio-group v-model="form.format">
          <el-radio label="csv">CSV格式</el-radio>
          <el-radio label="json">JSON格式</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="form.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="行为类型">
        <el-select
          v-model="form.action"
          placeholder="选择行为类型（可选）"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="option in actionOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="目标类型">
        <el-select
          v-model="form.targetType"
          placeholder="选择目标类型（可选）"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="option in targetTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="用户ID">
        <el-input v-model="form.userId" placeholder="输入用户ID（可选）" clearable />
      </el-form-item>

      <el-form-item label="导出选项">
        <el-checkbox-group v-model="form.options">
          <el-checkbox label="includeMetadata">包含元数据</el-checkbox>
          <el-checkbox label="includeUserInfo">包含用户信息</el-checkbox>
          <el-checkbox label="compressFile">压缩文件</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="文件名">
        <el-input v-model="form.filename" placeholder="自定义文件名（可选）" clearable>
          <template #suffix>
            <span class="text-gray-400">.{{ form.format }}</span>
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <div class="mt-4 flex justify-end space-x-2">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleExport">
        <Icon icon="ep:download" class="mr-1" />
        开始导出
      </el-button>
    </div>

    <!-- 导出进度 -->
    <div v-if="exporting" class="mt-4">
      <el-alert title="正在导出数据..." type="info" :closable="false" show-icon>
        <template #default>
          <div class="mt-2">
            <el-progress
              :percentage="exportProgress"
              :status="exportProgress === 100 ? 'success' : undefined"
            />
            <div class="text-sm text-gray-600 mt-1">
              {{ exportStatus }}
            </div>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Icon } from '@/components/Icon'
import { exportActivityLogsApi } from '@/api/activity-logs'
import type { ExportOptions } from '@/types/activity-logs'
import { ACTIVITY_ACTION_OPTIONS, ACTIVITY_TARGET_TYPE_OPTIONS } from '@/types/activity-logs'

// Emits
const emit = defineEmits<{
  export: [options: ExportOptions]
  close: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const exporting = ref(false)
const exportProgress = ref(0)
const exportStatus = ref('')

// 表单数据
const form = reactive({
  format: 'csv' as 'csv' | 'json',
  dateRange: null as [string, string] | null,
  action: '',
  targetType: '',
  userId: '',
  options: [] as string[],
  filename: ''
})

// 选项数据
const actionOptions = ACTIVITY_ACTION_OPTIONS
const targetTypeOptions = ACTIVITY_TARGET_TYPE_OPTIONS

// 表单验证规则
const rules: FormRules = {
  format: [{ required: true, message: '请选择导出格式', trigger: 'change' }]
}

// 计算属性
const exportOptions = computed((): ExportOptions => {
  const options: ExportOptions = {
    format: form.format
  }

  if (form.dateRange) {
    options.dateFrom = form.dateRange[0]
    options.dateTo = form.dateRange[1]
  }

  if (form.action) {
    options.action = form.action as any
  }

  if (form.targetType) {
    options.targetType = form.targetType as any
  }

  if (form.userId) {
    options.userId = form.userId
  }

  return options
})

// 方法
const handleCancel = () => {
  emit('close')
}

const handleExport = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 确认导出
    const result = await ElMessageBox.confirm('确定要导出用户行为日志数据吗？', '确认导出', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (result !== 'confirm') return

    loading.value = true
    exporting.value = true
    exportProgress.value = 0
    exportStatus.value = '准备导出...'

    // 模拟导出进度
    const progressInterval = setInterval(() => {
      if (exportProgress.value < 90) {
        exportProgress.value += Math.random() * 20
        exportStatus.value = `正在导出数据... ${Math.round(exportProgress.value)}%`
      }
    }, 500)

    try {
      const blob = await exportActivityLogsApi(exportOptions.value)

      // 完成进度
      clearInterval(progressInterval)
      exportProgress.value = 100
      exportStatus.value = '导出完成，正在下载...'

      // 下载文件
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      const filename = form.filename || `activity_logs_${Date.now()}`
      link.download = `${filename}.${form.format}`

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
      emit('export', exportOptions.value)
    } catch (error) {
      clearInterval(progressInterval)
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
    setTimeout(() => {
      exporting.value = false
      exportProgress.value = 0
      exportStatus.value = ''
    }, 2000)
  }
}
</script>

<style scoped>
.export-dialog {
  padding: 16px;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-600 {
  color: #4b5563;
}

.space-x-2 > * + * {
  margin-left: 8px;
}
</style>

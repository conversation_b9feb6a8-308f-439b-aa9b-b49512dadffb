<template>
  <div class="cleanup-dialog">
    <el-alert title="注意" type="warning" :closable="false" show-icon class="mb-4">
      <template #default>
        <p>清理操作将永久删除指定时间之前的用户行为日志，此操作不可恢复！</p>
        <p>建议在清理前先导出重要数据作为备份。</p>
      </template>
    </el-alert>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="保留天数" prop="daysToKeep">
        <el-input-number
          v-model="form.daysToKeep"
          :min="1"
          :max="3650"
          :step="1"
          style="width: 200px"
        />
        <span class="ml-2 text-gray-600">天</span>
      </el-form-item>

      <el-form-item label="清理范围">
        <el-radio-group v-model="form.cleanupType">
          <el-radio label="all">清理所有过期日志</el-radio>
          <el-radio label="selective">选择性清理</el-radio>
        </el-radio-group>
      </el-form-item>

      <div v-if="form.cleanupType === 'selective'">
        <el-form-item label="行为类型">
          <el-select
            v-model="form.actions"
            placeholder="选择要清理的行为类型"
            multiple
            style="width: 100%"
          >
            <el-option
              v-for="option in actionOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="目标类型">
          <el-select
            v-model="form.targetTypes"
            placeholder="选择要清理的目标类型"
            multiple
            style="width: 100%"
          >
            <el-option
              v-for="option in targetTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </div>

      <el-form-item label="清理选项">
        <el-checkbox-group v-model="form.options">
          <el-checkbox label="excludeErrors">排除错误日志</el-checkbox>
          <el-checkbox label="excludeAdminActions">排除管理员操作</el-checkbox>
          <el-checkbox label="createBackup">创建备份文件</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <!-- 预估信息 -->
    <div v-if="estimateInfo" class="mb-4">
      <el-card shadow="never" class="estimate-card">
        <template #header>
          <span class="font-medium">清理预估</span>
        </template>
        <div class="estimate-content">
          <div class="estimate-item">
            <span class="label">预计删除记录数：</span>
            <span class="value text-red-600 font-bold">{{ estimateInfo.estimatedCount }}</span>
          </div>
          <div class="estimate-item">
            <span class="label">预计释放空间：</span>
            <span class="value">{{ estimateInfo.estimatedSize }}</span>
          </div>
          <div class="estimate-item">
            <span class="label">清理时间范围：</span>
            <span class="value">{{ estimateInfo.dateRange }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 确认输入 -->
    <el-form-item label="确认清理" prop="confirmation">
      <el-input
        v-model="form.confirmation"
        placeholder="请输入 'CONFIRM' 确认清理操作"
        style="width: 300px"
      />
    </el-form-item>

    <div class="mt-4 flex justify-end space-x-2">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleEstimate">
        <Icon icon="ep:data-analysis" class="mr-1" />
        预估清理
      </el-button>
      <el-button type="danger" :loading="loading" :disabled="!canCleanup" @click="handleCleanup">
        <Icon icon="ep:delete" class="mr-1" />
        确认清理
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Icon } from '@/components/Icon'
import type { CleanupOptions } from '@/types/activity-logs'
import { ACTIVITY_ACTION_OPTIONS, ACTIVITY_TARGET_TYPE_OPTIONS } from '@/types/activity-logs'

// Emits
const emit = defineEmits<{
  cleanup: [options: CleanupOptions]
  close: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const estimateInfo = ref<{
  estimatedCount: number
  estimatedSize: string
  dateRange: string
} | null>(null)

// 表单数据
const form = reactive({
  daysToKeep: 90,
  cleanupType: 'all' as 'all' | 'selective',
  actions: [] as string[],
  targetTypes: [] as string[],
  options: [] as string[],
  confirmation: ''
})

// 选项数据
const actionOptions = ACTIVITY_ACTION_OPTIONS
const targetTypeOptions = ACTIVITY_TARGET_TYPE_OPTIONS

// 表单验证规则
const rules: FormRules = {
  daysToKeep: [
    { required: true, message: '请输入保留天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: '保留天数必须在1-3650之间', trigger: 'blur' }
  ],
  confirmation: [
    { required: true, message: '请输入确认文本', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== 'CONFIRM') {
          callback(new Error('请输入 CONFIRM 确认清理操作'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const canCleanup = computed(() => {
  return form.confirmation === 'CONFIRM' && estimateInfo.value
})

// 方法
const handleCancel = () => {
  emit('close')
}

const handleEstimate = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validateField('daysToKeep')

    // 模拟预估数据
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - form.daysToKeep)

    // 这里应该调用API获取真实的预估数据
    estimateInfo.value = {
      estimatedCount: Math.floor(Math.random() * 10000) + 1000,
      estimatedSize: `${(Math.random() * 100 + 10).toFixed(1)} MB`,
      dateRange: `${cutoffDate.toLocaleDateString()} 之前的所有记录`
    }

    ElMessage.success('预估完成')
  } catch (error) {
    console.error('预估失败:', error)
  }
}

const handleCleanup = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (!estimateInfo.value) {
      ElMessage.warning('请先进行清理预估')
      return
    }

    // 二次确认
    const result = await ElMessageBox.confirm(
      `确定要删除 ${estimateInfo.value.estimatedCount} 条记录吗？此操作不可恢复！`,
      '最终确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    )

    if (result !== 'confirm') return

    loading.value = true

    const options: CleanupOptions = {
      daysToKeep: form.daysToKeep
    }

    emit('cleanup', options)
  } catch (error) {
    console.error('清理失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.cleanup-dialog {
  padding: 16px;
}

.estimate-card {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
}

.estimate-content {
  space-y: 8px;
}

.estimate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.label {
  color: #6b7280;
  font-size: 14px;
}

.value {
  font-weight: 500;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-red-600 {
  color: #dc2626;
}

.text-gray-600 {
  color: #4b5563;
}

.space-x-2 > * + * {
  margin-left: 8px;
}
</style>

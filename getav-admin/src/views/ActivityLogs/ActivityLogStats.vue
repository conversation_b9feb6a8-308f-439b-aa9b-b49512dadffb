<template>
  <ContentWrap>
    <!-- 时间范围选择 -->
    <div class="mb-4">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-select
            v-model="selectedDays"
            placeholder="选择时间范围"
            @change="handleTimeRangeChange"
          >
            <el-option
              v-for="option in timeRangeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="customDateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleCustomDateChange"
          />
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="refreshData">
            <Icon icon="ep:refresh" class="mr-1" />
            刷新数据
          </el-button>
          <el-button @click="handleExportReport">
            <Icon icon="ep:download" class="mr-1" />
            导出报告
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <Icon icon="ep:data-line" size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(stats?.totalLogs || 0) }}</div>
              <div class="stat-label">核心业务操作数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon users">
              <Icon icon="ep:user" size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(stats?.uniqueUsers || 0) }}</div>
              <div class="stat-label">核心业务活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon actions">
              <Icon icon="ep:operation" size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats?.topActions?.[0]?.count || 0 }}</div>
              <div class="stat-label">热门操作</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon avg">
              <Icon icon="ep:trend-charts" size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ calculateAvgLogsPerUser() }}</div>
              <div class="stat-label">人均操作</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-4">
      <!-- 日活跃趋势图 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <span class="card-title">日活跃趋势</span>
          </template>
          <div ref="dailyTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 小时分布图 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <span class="card-title">小时分布</span>
          </template>
          <div ref="hourlyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb-4">
      <!-- 行为类型分布 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <span class="card-title">行为类型分布</span>
          </template>
          <div ref="actionPieChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 目标类型分布 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <span class="card-title">目标类型分布</span>
          </template>
          <div ref="targetPieChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜 -->
    <el-row :gutter="20">
      <!-- 活跃用户排行 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <span class="card-title">活跃用户排行</span>
          </template>
          <el-table :data="stats?.topUsers || []" stripe>
            <el-table-column prop="user.username" label="用户名" />
            <el-table-column prop="user.email" label="邮箱" show-overflow-tooltip />
            <el-table-column prop="count" label="操作次数" width="100" />
          </el-table>
        </el-card>
      </el-col>

      <!-- IP地址排行 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <span class="card-title">IP地址排行</span>
          </template>
          <el-table :data="stats?.topIpAddresses || []" stripe>
            <el-table-column prop="ipAddress" label="IP地址" />
            <el-table-column prop="count" label="访问次数" width="100" />
            <el-table-column prop="uniqueUsers" label="用户数" width="80" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { getActivityStatsApi, getActivityLogsApi } from '@/api/activity-logs'
import type { ActivityStats } from '@/types/activity-logs'
import { DATE_RANGE_OPTIONS } from '@/types/activity-logs'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const stats = ref<ActivityStats | null>(null)
const selectedDays = ref(7)
const customDateRange = ref<[string, string] | null>(null)

// 图表引用
const dailyTrendChart = ref<HTMLElement>()
const hourlyChart = ref<HTMLElement>()
const actionPieChart = ref<HTMLElement>()
const targetPieChart = ref<HTMLElement>()

// 图表实例
let dailyTrendChartInstance: echarts.ECharts | null = null
let hourlyChartInstance: echarts.ECharts | null = null
let actionPieChartInstance: echarts.ECharts | null = null
let targetPieChartInstance: echarts.ECharts | null = null

// 选项数据
const timeRangeOptions = DATE_RANGE_OPTIONS

// 获取统计数据
const fetchStats = async () => {
  loading.value = true
  try {
    const response = await getActivityStatsApi(selectedDays.value)
    if (response && response.success && response.data) {
      stats.value = response.data
      await nextTick()
      initCharts()
    } else {
      // 如果统计API不可用，使用基础日志数据进行简单统计
      const logsResponse = await getActivityLogsApi({
        page: 1,
        limit: 100,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })

      if (logsResponse && logsResponse.success && logsResponse.data) {
        const logs = logsResponse.data.items || []

        // 构建基础统计对象
        stats.value = {
          totalLogs: logs.length,
          uniqueUsers: new Set(logs.filter((log) => log.userId).map((log) => log.userId)).size,
          topActions: calculateTopActions(logs),
          dailyTrends: calculateDailyTrends(logs),
          hourlyDistribution: calculateHourlyDistribution(logs)
        }

        await nextTick()
        initCharts()
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
    // 设置默认值
    stats.value = {
      totalLogs: 0,
      uniqueUsers: 0,
      topActions: [],
      dailyTrends: [],
      hourlyDistribution: []
    }
  } finally {
    loading.value = false
  }
}

// 计算热门操作
const calculateTopActions = (logs: any[]) => {
  const actionCounts = logs.reduce((acc, log) => {
    acc[log.action] = (acc[log.action] || 0) + 1
    return acc
  }, {})

  return Object.entries(actionCounts)
    .map(([action, count]) => ({ action, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
}

// 计算每日趋势
const calculateDailyTrends = (logs: any[]) => {
  const dailyCounts = logs.reduce((acc, log) => {
    const date = new Date(log.createdAt).toISOString().split('T')[0]
    acc[date] = (acc[date] || 0) + 1
    return acc
  }, {})

  return Object.entries(dailyCounts)
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => a.date.localeCompare(b.date))
}

// 计算小时分布
const calculateHourlyDistribution = (logs: any[]) => {
  const hourlyCounts = logs.reduce((acc, log) => {
    const hour = new Date(log.createdAt).getHours()
    acc[hour] = (acc[hour] || 0) + 1
    return acc
  }, {})

  return Array.from({ length: 24 }, (_, hour) => ({
    hour,
    count: hourlyCounts[hour] || 0
  }))
}

// 初始化图表
const initCharts = () => {
  initDailyTrendChart()
  initHourlyChart()
  initActionPieChart()
  initTargetPieChart()
}

// 日活跃趋势图
const initDailyTrendChart = () => {
  if (!dailyTrendChart.value || !stats.value) return

  if (dailyTrendChartInstance) {
    dailyTrendChartInstance.dispose()
  }

  dailyTrendChartInstance = echarts.init(dailyTrendChart.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['日志数量', '活跃用户']
    },
    xAxis: {
      type: 'category',
      data: stats.value.dailyStats.map((item) => item.date)
    },
    yAxis: [
      {
        type: 'value',
        name: '日志数量'
      },
      {
        type: 'value',
        name: '活跃用户'
      }
    ],
    series: [
      {
        name: '日志数量',
        type: 'line',
        data: stats.value.dailyStats.map((item) => item.count)
      },
      {
        name: '活跃用户',
        type: 'line',
        yAxisIndex: 1,
        data: stats.value.dailyStats.map((item) => item.uniqueUsers)
      }
    ]
  }

  dailyTrendChartInstance.setOption(option)
}

// 小时分布图
const initHourlyChart = () => {
  if (!hourlyChart.value || !stats.value) return

  if (hourlyChartInstance) {
    hourlyChartInstance.dispose()
  }

  hourlyChartInstance = echarts.init(hourlyChart.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '活动数量',
        type: 'bar',
        data: stats.value.hourlyStats.map((item) => item.count)
      }
    ]
  }

  hourlyChartInstance.setOption(option)
}

// 行为类型饼图
const initActionPieChart = () => {
  if (!actionPieChart.value || !stats.value) return

  if (actionPieChartInstance) {
    actionPieChartInstance.dispose()
  }

  actionPieChartInstance = echarts.init(actionPieChart.value)

  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '行为类型',
        type: 'pie',
        radius: '50%',
        data: stats.value.topActions.map((item) => ({
          value: item.count,
          name: item.action
        }))
      }
    ]
  }

  actionPieChartInstance.setOption(option)
}

// 目标类型饼图
const initTargetPieChart = () => {
  if (!targetPieChart.value || !stats.value) return

  if (targetPieChartInstance) {
    targetPieChartInstance.dispose()
  }

  targetPieChartInstance = echarts.init(targetPieChart.value)

  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '目标类型',
        type: 'pie',
        radius: '50%',
        data: stats.value.topTargetTypes.map((item) => ({
          value: item.count,
          name: item.targetType
        }))
      }
    ]
  }

  targetPieChartInstance.setOption(option)
}

// 事件处理
const handleTimeRangeChange = (days: number) => {
  selectedDays.value = days
  customDateRange.value = null
  fetchStats()
}

const handleCustomDateChange = (dateRange: [string, string] | null) => {
  if (dateRange) {
    customDateRange.value = dateRange
    // 这里应该根据自定义时间范围获取数据
    ElMessage.info('自定义时间范围功能待实现')
  }
}

const refreshData = () => {
  fetchStats()
}

const handleExportReport = () => {
  ElMessage.info('导出报告功能待实现')
}

// 工具函数
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const calculateAvgLogsPerUser = () => {
  if (!stats.value || stats.value.uniqueUsers === 0) return '0'
  return (stats.value.totalLogs / stats.value.uniqueUsers).toFixed(1)
}

// 生命周期
onMounted(() => {
  fetchStats()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    dailyTrendChartInstance?.resize()
    hourlyChartInstance?.resize()
    actionPieChartInstance?.resize()
    targetPieChartInstance?.resize()
  })
})
</script>

<style scoped>
.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.users {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.actions {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.avg {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.card-title {
  font-weight: 500;
  color: #303133;
}
</style>

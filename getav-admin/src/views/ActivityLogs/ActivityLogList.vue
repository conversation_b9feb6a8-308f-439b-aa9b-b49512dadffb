<template>
  <ContentWrap>
    <!-- 搜索区域 -->
    <div class="mb-4">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchParams.search"
            placeholder="搜索用户ID、IP地址等"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <Icon icon="ep:search" />
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchParams.action"
            placeholder="行为类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="option in actionOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchParams.targetType"
            placeholder="目标类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="option in targetTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateRangeChange"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <Icon icon="ep:search" class="mr-1" />
            搜索
          </el-button>
          <el-button @click="handleReset">
            <Icon icon="ep:refresh" class="mr-1" />
            重置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="mb-4">
      <el-button type="success" @click="handleExport">
        <Icon icon="ep:download" class="mr-1" />
        导出数据
      </el-button>
      <el-button type="warning" @click="handleCleanup">
        <Icon icon="ep:delete" class="mr-1" />
        清理日志
      </el-button>
      <el-button type="info" @click="handleRefresh">
        <Icon icon="ep:refresh" class="mr-1" />
        刷新
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column prop="id" label="ID" width="120" show-overflow-tooltip />

      <el-table-column prop="user" label="用户" width="150">
        <template #default="{ row }">
          <div v-if="row.user">
            <div class="font-medium">{{ row.user.username || row.user.name || '未知用户' }}</div>
            <div class="text-xs text-gray-500">{{ row.user.email }}</div>
          </div>
          <div v-else class="text-gray-400">匿名用户</div>
        </template>
      </el-table-column>

      <el-table-column prop="action" label="行为类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getActionTagType(row.action)">
            {{ getActionLabel(row.action) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="targetType" label="目标类型" width="120">
        <template #default="{ row }">
          <el-tag type="info">
            {{ getTargetTypeLabel(row.targetType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="targetId" label="目标ID" width="150" show-overflow-tooltip />

      <el-table-column prop="ipAddress" label="IP地址" width="140" show-overflow-tooltip />

      <el-table-column prop="createdAt" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatToDateTime(row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleViewDetail(row)"> 详情 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-center">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="日志详情" width="800px" destroy-on-close>
      <ActivityLogDetail
        v-if="detailDialogVisible"
        :log="selectedLog"
        @close="detailDialogVisible = false"
      />
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog v-model="exportDialogVisible" title="导出数据" width="500px" destroy-on-close>
      <ExportDialog
        v-if="exportDialogVisible"
        @export="handleConfirmExport"
        @close="exportDialogVisible = false"
      />
    </el-dialog>

    <!-- 清理对话框 -->
    <el-dialog v-model="cleanupDialogVisible" title="清理日志" width="500px" destroy-on-close>
      <CleanupDialog
        v-if="cleanupDialogVisible"
        @cleanup="handleConfirmCleanup"
        @close="cleanupDialogVisible = false"
      />
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import ActivityLogDetail from './components/ActivityLogDetail.vue'
import ExportDialog from './components/ExportDialog.vue'
import CleanupDialog from './components/CleanupDialog.vue'
import { getActivityLogsApi, cleanupOldLogsApi } from '@/api/activity-logs'
import type { ActivityLog, ActivityLogQueryParams } from '@/types/activity-logs'
import { ACTIVITY_ACTION_OPTIONS, ACTIVITY_TARGET_TYPE_OPTIONS } from '@/types/activity-logs'
import { formatToDateTime } from '@/utils/dateUtil'

// 响应式数据
const loading = ref(false)
const tableData = ref<ActivityLog[]>([])
const selectedRows = ref<ActivityLog[]>([])
const selectedLog = ref<ActivityLog | null>(null)
const dateRange = ref<[string, string] | null>(null)

// 对话框状态
const detailDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const cleanupDialogVisible = ref(false)

// 搜索参数
const searchParams = reactive<ActivityLogQueryParams>({
  search: '',
  action: undefined,
  targetType: undefined,
  userId: '',
  ipAddress: '',
  dateFrom: '',
  dateTo: '',
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// 分页参数
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 选项数据
const actionOptions = ACTIVITY_ACTION_OPTIONS
const targetTypeOptions = ACTIVITY_TARGET_TYPE_OPTIONS

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      page: pagination.page,
      limit: pagination.limit
    }

    const response = await getActivityLogsApi(params)
    if (response && response.success && response.data) {
      tableData.value = response.data.items || []
      pagination.total = response.data.total || 0
    } else {
      tableData.value = []
      pagination.total = 0
      ElMessage.warning('未获取到数据')
    }
  } catch (error) {
    console.error('获取用户行为日志失败:', error)
    ElMessage.error('获取数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchParams, {
    search: '',
    action: undefined,
    targetType: undefined,
    userId: '',
    ipAddress: '',
    dateFrom: '',
    dateTo: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
  dateRange.value = null
  pagination.page = 1
  fetchData()
}

const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchParams.dateFrom = value[0]
    searchParams.dateTo = value[1]
  } else {
    searchParams.dateFrom = ''
    searchParams.dateTo = ''
  }
  handleSearch()
}

const handleRefresh = () => {
  fetchData()
}

const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

const handleSelectionChange = (selection: ActivityLog[]) => {
  selectedRows.value = selection
}

const handleViewDetail = (row: ActivityLog) => {
  selectedLog.value = row
  detailDialogVisible.value = true
}

const handleExport = () => {
  exportDialogVisible.value = true
}

const handleCleanup = () => {
  cleanupDialogVisible.value = true
}

const handleConfirmExport = async (options: any) => {
  // 导出逻辑
  exportDialogVisible.value = false
  ElMessage.success('导出成功')
}

const handleConfirmCleanup = async (options: { daysToKeep: number }) => {
  try {
    const response = await cleanupOldLogsApi(options)
    if (response.success) {
      ElMessage.success(`清理完成，删除了 ${response.data?.deletedCount} 条记录`)
      fetchData()
    }
  } catch (error) {
    ElMessage.error('清理失败')
  }
  cleanupDialogVisible.value = false
}

// 工具函数
const getActionLabel = (action: string) => {
  const option = actionOptions.find((opt) => opt.value === action)
  return option?.label || action
}

const getTargetTypeLabel = (targetType: string) => {
  const option = targetTypeOptions.find((opt) => opt.value === targetType)
  return option?.label || targetType
}

const getActionTagType = (action: string) => {
  const typeMap: Record<string, string> = {
    view: 'info',
    like: 'success',
    dislike: 'warning',
    favorite: 'success',
    comment: 'primary',
    login: 'success',
    logout: 'info',
    error: 'danger'
  }
  return typeMap[action] || 'info'
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.font-medium {
  font-weight: 500;
}

.text-xs {
  font-size: 0.75rem;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>

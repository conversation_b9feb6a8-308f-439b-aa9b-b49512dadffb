<template>
  <ContentWrap>
    <!-- 实时状态指示器 -->
    <div class="mb-4">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-alert
            :title="realtimeStatus.title"
            :type="realtimeStatus.type"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="flex items-center">
                <span>{{ realtimeStatus.message }}</span>
                <el-tag :type="isConnected ? 'success' : 'danger'" size="small" class="ml-2">
                  {{ isConnected ? '已连接' : '已断开' }}
                </el-tag>
              </div>
            </template>
          </el-alert>
        </el-col>
        <el-col :span="6">
          <div class="flex justify-end space-x-2">
            <el-button :type="isConnected ? 'warning' : 'success'" @click="toggleConnection">
              <Icon :icon="isConnected ? 'ep:video-pause' : 'ep:video-play'" class="mr-1" />
              {{ isConnected ? '暂停监控' : '开始监控' }}
            </el-button>
            <el-button @click="clearData">
              <Icon icon="ep:delete" class="mr-1" />
              清空数据
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 实时统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card shadow="hover" class="realtime-stat-card">
          <div class="stat-content">
            <div class="stat-icon online">
              <Icon icon="ep:user" size="20" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ realtimeData.activeUsers }}</div>
              <div class="stat-label">核心业务活跃用户</div>
            </div>
            <div class="stat-trend">
              <Icon :icon="userTrend.icon" :class="userTrend.class" size="16" />
              <span :class="userTrend.class">{{ userTrend.value }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="realtime-stat-card">
          <div class="stat-content">
            <div class="stat-icon actions">
              <Icon icon="ep:operation" size="20" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ realtimeData.currentMinuteActions }}</div>
              <div class="stat-label">核心业务操作</div>
            </div>
            <div class="stat-trend">
              <Icon :icon="actionTrend.icon" :class="actionTrend.class" size="16" />
              <span :class="actionTrend.class">{{ actionTrend.value }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="realtime-stat-card">
          <div class="stat-content">
            <div class="stat-icon errors">
              <Icon icon="ep:warning" size="20" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ realtimeData.errorCount }}</div>
              <div class="stat-label">错误数量</div>
            </div>
            <div class="stat-trend">
              <Icon :icon="errorTrend.icon" :class="errorTrend.class" size="16" />
              <span :class="errorTrend.class">{{ errorTrend.value }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="realtime-stat-card">
          <div class="stat-content">
            <div class="stat-icon response">
              <Icon icon="ep:timer" size="20" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ realtimeData.avgResponseTime }}ms</div>
              <div class="stat-label">平均响应时间</div>
            </div>
            <div class="stat-trend">
              <Icon :icon="responseTrend.icon" :class="responseTrend.class" size="16" />
              <span :class="responseTrend.class">{{ responseTrend.value }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时图表 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="16">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="card-title">实时活动流</span>
              <el-tag type="info" size="small"> 最近 {{ realtimeChartData.length }} 分钟 </el-tag>
            </div>
          </template>
          <div ref="realtimeChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <span class="card-title">操作类型分布</span>
          </template>
          <div ref="actionDistChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新活动日志 -->
    <el-card shadow="hover">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="card-title">最新核心业务日志</span>
          <div class="flex items-center space-x-2">
            <el-switch v-model="autoScroll" active-text="自动滚动" inactive-text="" size="small" />
            <el-button size="small" @click="refreshRecentLogs">
              <Icon icon="ep:refresh" />
            </el-button>
          </div>
        </div>
      </template>

      <div class="recent-logs-container" :class="{ 'auto-scroll': autoScroll }">
        <div v-for="log in recentLogs" :key="log.id" class="log-item" :class="getLogItemClass(log)">
          <div class="log-time">
            {{ formatTime(log.createdAt) }}
          </div>
          <div class="log-content">
            <div class="log-main">
              <el-tag :type="getActionTagType(log.action)" size="small" class="mr-2">
                {{ getActionLabel(log.action) }}
              </el-tag>
              <span class="log-user">
                {{ log.user?.username || log.user?.name || '匿名用户' }}
              </span>
              <span class="log-target">
                {{ getTargetTypeLabel(log.targetType) }}
                <span v-if="log.targetId" class="text-gray-500"> ({{ log.targetId }}) </span>
              </span>
            </div>
            <div class="log-meta">
              <span class="log-ip">{{ log.ipAddress || '未知IP' }}</span>
              <span v-if="log.metadata" class="log-metadata">
                {{ formatMetadata(log.metadata) }}
              </span>
            </div>
          </div>
        </div>

        <div v-if="recentLogs.length === 0" class="empty-logs">
          <el-empty description="暂无实时日志数据" />
        </div>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { getRealTimeActivityApi, getActivityLogsApi } from '@/api/activity-logs'
import type { ActivityLog } from '@/types/activity-logs'
import { ACTIVITY_ACTION_OPTIONS, ACTIVITY_TARGET_TYPE_OPTIONS } from '@/types/activity-logs'
import * as echarts from 'echarts'

// 响应式数据
const isConnected = ref(false)
const autoScroll = ref(true)
const recentLogs = ref<ActivityLog[]>([])
const realtimeChartData = ref<Array<{ time: string; count: number; users: number }>>([])

// 实时数据
const realtimeData = reactive({
  activeUsers: 0,
  currentMinuteActions: 0,
  errorCount: 0,
  avgResponseTime: 0
})

// 历史数据用于计算趋势
const previousData = reactive({
  activeUsers: 0,
  currentMinuteActions: 0,
  errorCount: 0,
  avgResponseTime: 0
})

// 图表引用
const realtimeChart = ref<HTMLElement>()
const actionDistChart = ref<HTMLElement>()

// 图表实例
let realtimeChartInstance: echarts.ECharts | null = null
let actionDistChartInstance: echarts.ECharts | null = null

// 定时器
let dataUpdateTimer: NodeJS.Timeout | null = null
let chartUpdateTimer: NodeJS.Timeout | null = null

// 计算属性
const realtimeStatus = computed(() => {
  if (isConnected.value) {
    return {
      title: '核心业务监控已启动',
      type: 'success' as const,
      message:
        '正在监控核心业务行为（下载、收藏、评分、评论等），数据每5秒更新一次。页面访问统计请查看Google Analytics。'
    }
  } else {
    return {
      title: '核心业务监控已暂停',
      type: 'warning' as const,
      message: '点击"开始监控"按钮启动核心业务数据监控'
    }
  }
})

const userTrend = computed(() => {
  const change = realtimeData.activeUsers - previousData.activeUsers
  const percentage =
    previousData.activeUsers === 0 ? 0 : Math.abs((change / previousData.activeUsers) * 100)

  if (change > 0) {
    return {
      icon: 'ep:arrow-up',
      class: 'text-green-500',
      value: percentage.toFixed(1)
    }
  } else if (change < 0) {
    return {
      icon: 'ep:arrow-down',
      class: 'text-red-500',
      value: percentage.toFixed(1)
    }
  } else {
    return {
      icon: 'ep:minus',
      class: 'text-gray-500',
      value: '0.0'
    }
  }
})

const actionTrend = computed(() => {
  const change = realtimeData.currentMinuteActions - previousData.currentMinuteActions
  const percentage =
    previousData.currentMinuteActions === 0
      ? 0
      : Math.abs((change / previousData.currentMinuteActions) * 100)

  if (change > 0) {
    return {
      icon: 'ep:arrow-up',
      class: 'text-green-500',
      value: percentage.toFixed(1)
    }
  } else if (change < 0) {
    return {
      icon: 'ep:arrow-down',
      class: 'text-red-500',
      value: percentage.toFixed(1)
    }
  } else {
    return {
      icon: 'ep:minus',
      class: 'text-gray-500',
      value: '0.0'
    }
  }
})

const errorTrend = computed(() => {
  const change = realtimeData.errorCount - previousData.errorCount
  const percentage =
    previousData.errorCount === 0 ? 0 : Math.abs((change / previousData.errorCount) * 100)

  if (change > 0) {
    return {
      icon: 'ep:arrow-up',
      class: 'text-red-500',
      value: percentage.toFixed(1)
    }
  } else if (change < 0) {
    return {
      icon: 'ep:arrow-down',
      class: 'text-green-500',
      value: percentage.toFixed(1)
    }
  } else {
    return {
      icon: 'ep:minus',
      class: 'text-gray-500',
      value: '0.0'
    }
  }
})

const responseTrend = computed(() => {
  const change = realtimeData.avgResponseTime - previousData.avgResponseTime
  const percentage =
    previousData.avgResponseTime === 0 ? 0 : Math.abs((change / previousData.avgResponseTime) * 100)

  if (change > 0) {
    return {
      icon: 'ep:arrow-up',
      class: 'text-red-500',
      value: percentage.toFixed(1)
    }
  } else if (change < 0) {
    return {
      icon: 'ep:arrow-down',
      class: 'text-green-500',
      value: percentage.toFixed(1)
    }
  } else {
    return {
      icon: 'ep:minus',
      class: 'text-gray-500',
      value: '0.0'
    }
  }
})

// 方法
const toggleConnection = () => {
  if (isConnected.value) {
    stopMonitoring()
  } else {
    startMonitoring()
  }
}

const startMonitoring = () => {
  isConnected.value = true

  // 立即获取一次数据
  fetchRealtimeData()

  // 设置定时器
  dataUpdateTimer = setInterval(fetchRealtimeData, 5000) // 每5秒更新数据
  chartUpdateTimer = setInterval(updateCharts, 10000) // 每10秒更新图表

  ElMessage.success('实时监控已启动')
}

const stopMonitoring = () => {
  isConnected.value = false

  // 清除定时器
  if (dataUpdateTimer) {
    clearInterval(dataUpdateTimer)
    dataUpdateTimer = null
  }

  if (chartUpdateTimer) {
    clearInterval(chartUpdateTimer)
    chartUpdateTimer = null
  }

  ElMessage.info('实时监控已暂停')
}

const fetchRealtimeData = async () => {
  try {
    // 保存之前的数据用于计算趋势
    Object.assign(previousData, realtimeData)

    // 尝试获取实时监控API数据
    try {
      const realtimeResponse = await getRealTimeActivityApi()
      if (realtimeResponse && realtimeResponse.success && realtimeResponse.data) {
        // 使用实时API数据
        const data = realtimeResponse.data
        realtimeData.activeUsers = data.activeUsers || 0
        realtimeData.currentMinuteActions = Array.isArray(data.currentActions)
          ? data.currentActions.reduce((sum, item) => sum + item.count, 0)
          : data.currentActions || 0
        realtimeData.errorCount = 0 // 暂时固定为0
        realtimeData.avgResponseTime = 120 // 暂时固定值

        if (autoScroll.value && data.recentLogs) {
          recentLogs.value = [...data.recentLogs, ...recentLogs.value].slice(0, 50)
        }
      }
    } catch (realtimeError) {
      // 如果实时API不可用，降级到基础日志数据
      const response = await getActivityLogsApi({
        page: 1,
        limit: 20,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })

      if (response && response.success && response.data) {
        const logs = response.data.items || []

        // 过滤核心业务行为
        const coreActions = [
          'DOWNLOAD_MAGNET',
          'FAVORITE',
          'UNFAVORITE',
          'RATE',
          'UPDATE_RATING',
          'COMMENT',
          'DELETE_COMMENT',
          'LIKE',
          'UNLIKE',
          'REPORT'
        ]
        const coreBusinessLogs = logs.filter((log) => coreActions.includes(log.action))

        // 计算核心业务指标
        const last5Minutes = new Date(Date.now() - 5 * 60 * 1000)
        const recentCoreActions = coreBusinessLogs.filter(
          (log) => new Date(log.createdAt) > last5Minutes
        )

        // 更新实时数据（专注于核心业务指标）
        realtimeData.activeUsers = Math.min(recentCoreActions.length * 2, 20) // 估算活跃用户
        realtimeData.currentMinuteActions = recentCoreActions.length
        realtimeData.errorCount = 0
        realtimeData.avgResponseTime = 120

        // 更新最新核心业务日志
        if (autoScroll.value) {
          recentLogs.value = [...coreBusinessLogs, ...recentLogs.value].slice(0, 50)
        }
      }
    }

    // 更新图表数据
    const now = new Date()
    realtimeChartData.value.push({
      time: now.toLocaleTimeString(),
      count: realtimeData.currentMinuteActions,
      users: realtimeData.activeUsers
    })

    // 保持最近30个数据点
    if (realtimeChartData.value.length > 30) {
      realtimeChartData.value.shift()
    }
  } catch (error) {
    console.error('获取实时数据失败:', error)
    // 降级到基础显示
    realtimeData.activeUsers = 0
    realtimeData.currentMinuteActions = 0
    realtimeData.errorCount = 0
    realtimeData.avgResponseTime = 0
  }
}

// 模拟数据生成函数已移除，现在使用真实数据

const updateCharts = () => {
  updateRealtimeChart()
  updateActionDistChart()
}

const updateRealtimeChart = () => {
  if (!realtimeChartInstance || realtimeChartData.value.length === 0) return

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['操作数量', '活跃用户']
    },
    xAxis: {
      type: 'category',
      data: realtimeChartData.value.map((item) => item.time)
    },
    yAxis: [
      {
        type: 'value',
        name: '操作数量'
      },
      {
        type: 'value',
        name: '活跃用户'
      }
    ],
    series: [
      {
        name: '操作数量',
        type: 'line',
        data: realtimeChartData.value.map((item) => item.count),
        smooth: true
      },
      {
        name: '活跃用户',
        type: 'line',
        yAxisIndex: 1,
        data: realtimeChartData.value.map((item) => item.users),
        smooth: true
      }
    ]
  }

  realtimeChartInstance.setOption(option)
}

const updateActionDistChart = () => {
  if (!actionDistChartInstance) return

  // 模拟操作分布数据
  const actionData = [
    { name: '查看', value: Math.floor(Math.random() * 100) + 50 },
    { name: '点赞', value: Math.floor(Math.random() * 50) + 20 },
    { name: '评论', value: Math.floor(Math.random() * 30) + 10 },
    { name: '收藏', value: Math.floor(Math.random() * 40) + 15 },
    { name: '搜索', value: Math.floor(Math.random() * 60) + 25 }
  ]

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '操作类型',
        type: 'pie',
        radius: ['40%', '70%'],
        data: actionData
      }
    ]
  }

  actionDistChartInstance.setOption(option)
}

const initCharts = () => {
  if (realtimeChart.value) {
    realtimeChartInstance = echarts.init(realtimeChart.value)
  }

  if (actionDistChart.value) {
    actionDistChartInstance = echarts.init(actionDistChart.value)
  }
}

const clearData = () => {
  recentLogs.value = []
  realtimeChartData.value = []
  ElMessage.success('数据已清空')
}

const refreshRecentLogs = () => {
  if (isConnected.value) {
    fetchRealtimeData()
  } else {
    ElMessage.warning('请先启动实时监控')
  }
}

// 工具函数
const getActionLabel = (action: string) => {
  const option = ACTIVITY_ACTION_OPTIONS.find((opt) => opt.value === action)
  return option?.label || action
}

const getTargetTypeLabel = (targetType: string) => {
  const option = ACTIVITY_TARGET_TYPE_OPTIONS.find((opt) => opt.value === targetType)
  return option?.label || targetType
}

const getActionTagType = (action: string) => {
  const typeMap: Record<string, string> = {
    view: 'info',
    like: 'success',
    dislike: 'warning',
    favorite: 'success',
    comment: 'primary',
    login: 'success',
    logout: 'info',
    error: 'danger'
  }
  return typeMap[action] || 'info'
}

const getLogItemClass = (log: ActivityLog) => {
  if (log.action === 'error') return 'log-error'
  if (log.action === 'login') return 'log-success'
  return 'log-normal'
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString()
}

const formatMetadata = (metadata: Record<string, any>) => {
  return Object.entries(metadata)
    .map(([key, value]) => `${key}: ${value}`)
    .join(', ')
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})

onUnmounted(() => {
  stopMonitoring()

  // 销毁图表实例
  realtimeChartInstance?.dispose()
  actionDistChartInstance?.dispose()
})
</script>

<style scoped>
.realtime-stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
}

.stat-icon.online {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.actions {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.errors {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-icon.response {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.stat-trend {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.chart-card {
  height: 350px;
}

.chart-container {
  height: 280px;
}

.recent-logs-container {
  max-height: 400px;
  overflow-y: auto;
}

.recent-logs-container.auto-scroll {
  scroll-behavior: smooth;
}

.log-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.log-item:hover {
  background-color: #f8f9fa;
}

.log-item.log-error {
  background-color: #fef2f2;
  border-left: 3px solid #ef4444;
}

.log-item.log-success {
  background-color: #f0fdf4;
  border-left: 3px solid #22c55e;
}

.log-time {
  width: 80px;
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.log-content {
  flex: 1;
  margin-left: 12px;
}

.log-main {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.log-user {
  font-weight: 500;
  margin-right: 8px;
}

.log-target {
  color: #6b7280;
  font-size: 14px;
}

.log-meta {
  font-size: 12px;
  color: #9ca3af;
}

.log-ip {
  margin-right: 12px;
}

.empty-logs {
  padding: 40px 0;
}

.text-green-500 {
  color: #10b981;
}

.text-red-500 {
  color: #ef4444;
}

.text-gray-500 {
  color: #6b7280;
}

.space-x-2 > * + * {
  margin-left: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}
</style>

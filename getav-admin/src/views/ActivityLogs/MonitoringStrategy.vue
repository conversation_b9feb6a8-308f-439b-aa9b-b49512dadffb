<template>
  <ContentWrap>
    <el-card shadow="hover">
      <template #header>
        <div class="flex items-center">
          <Icon icon="ep:info-filled" class="mr-2" />
          <span class="text-lg font-semibold">用户行为监控策略说明</span>
        </div>
      </template>

      <div class="monitoring-strategy">
        <!-- 概述 -->
        <el-alert title="双轨制监控策略" type="info" :closable="false" show-icon class="mb-6">
          <template #default>
            <p>
              为了提供更专业和高效的数据分析，GETAV.NET采用双轨制用户行为监控策略：
              <strong>Google Analytics</strong> 处理通用网站分析，
              <strong>自建监控系统</strong> 专注于核心业务行为追踪。
            </p>
          </template>
        </el-alert>

        <!-- 分工说明 -->
        <el-row :gutter="20" class="mb-6">
          <el-col :span="12">
            <el-card shadow="hover" class="strategy-card ga-card">
              <template #header>
                <div class="flex items-center">
                  <Icon icon="ep:data-analysis" class="mr-2 text-blue-500" />
                  <span class="font-semibold">Google Analytics 负责</span>
                </div>
              </template>
              <ul class="strategy-list">
                <li><Icon icon="ep:view" class="mr-2" />页面浏览统计</li>
                <li><Icon icon="ep:location" class="mr-2" />用户访问来源分析</li>
                <li><Icon icon="ep:monitor" class="mr-2" />设备和浏览器统计</li>
                <li><Icon icon="ep:map-location" class="mr-2" />地理位置分析</li>
                <li><Icon icon="ep:search" class="mr-2" />搜索关键词统计</li>
                <li><Icon icon="ep:timer" class="mr-2" />页面性能监控</li>
                <li><Icon icon="ep:trend-charts" class="mr-2" />转化漏斗分析</li>
              </ul>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card shadow="hover" class="strategy-card custom-card">
              <template #header>
                <div class="flex items-center">
                  <Icon icon="ep:cpu" class="mr-2 text-green-500" />
                  <span class="font-semibold">自建监控系统负责</span>
                </div>
              </template>
              <ul class="strategy-list">
                <li><Icon icon="ep:download" class="mr-2" />磁力链接下载追踪</li>
                <li><Icon icon="ep:star" class="mr-2" />用户收藏/取消收藏行为</li>
                <li><Icon icon="ep:chat-dot-round" class="mr-2" />用户评分和评论行为</li>
                <li><Icon icon="ep:user" class="mr-2" />用户登录/注册行为</li>
                <li><Icon icon="ep:warning" class="mr-2" />违规行为报告</li>
                <li><Icon icon="ep:shield" class="mr-2" />反爬虫监控</li>
                <li><Icon icon="ep:data-board" class="mr-2" />内容推荐算法数据</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>

        <!-- 优势说明 -->
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <div class="flex items-center">
              <Icon icon="ep:trophy" class="mr-2 text-yellow-500" />
              <span class="font-semibold">双轨制策略的优势</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="advantage-item">
                <Icon icon="ep:money" class="advantage-icon" />
                <h4>成本效益</h4>
                <p>利用Google Analytics的免费专业分析能力，降低开发和维护成本</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="advantage-item">
                <Icon icon="ep:lock" class="advantage-icon" />
                <h4>数据控制</h4>
                <p>核心业务数据完全掌控，保护商业机密和用户隐私</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="advantage-item">
                <Icon icon="ep:cpu" class="advantage-icon" />
                <h4>性能优化</h4>
                <p>专注于核心业务逻辑，提高系统性能和响应速度</p>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 使用指南 -->
        <el-card shadow="hover">
          <template #header>
            <div class="flex items-center">
              <Icon icon="ep:guide" class="mr-2 text-purple-500" />
              <span class="font-semibold">使用指南</span>
            </div>
          </template>
          <el-steps :active="3" align-center class="mb-4">
            <el-step title="查看通用分析" description="访问Google Analytics控制台" />
            <el-step title="监控核心业务" description="使用本系统的实时监控功能" />
            <el-step title="数据整合分析" description="结合两者数据进行深度分析" />
          </el-steps>

          <div class="guide-actions">
            <el-button type="primary" @click="openGA">
              <Icon icon="ep:link" class="mr-1" />
              访问 Google Analytics
            </el-button>
            <el-button type="success" @click="goToRealtime">
              <Icon icon="ep:monitor" class="mr-1" />
              查看实时监控
            </el-button>
            <el-button type="info" @click="downloadGuide">
              <Icon icon="ep:download" class="mr-1" />
              下载集成指南
            </el-button>
          </div>
        </el-card>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

const router = useRouter()

const openGA = () => {
  window.open('https://analytics.google.com/', '_blank')
}

const goToRealtime = () => {
  router.push('/activity-logs/realtime')
}

const downloadGuide = () => {
  ElMessage.info('集成指南下载功能待实现')
}
</script>

<style scoped>
.monitoring-strategy {
  max-width: 1200px;
  margin: 0 auto;
}

.strategy-card {
  height: 100%;
}

.strategy-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.strategy-list li {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.strategy-list li:last-child {
  border-bottom: none;
}

.advantage-item {
  text-align: center;
  padding: 20px;
}

.advantage-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 12px;
}

.advantage-item h4 {
  margin: 12px 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.advantage-item p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.guide-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
}

.ga-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.custom-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}
</style>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import {
  ElTable,
  ElTableColumn,
  ElPagination,
  ElButton,
  ElInput,
  ElCard,
  ElTag,
  ElSpace,
  ElMessage,
  ElAvatar
} from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { getUsersApi, deleteUserApi, type User, type UserQueryParams } from '@/api/users'

const { t } = useI18n()

const loading = ref(false)
const tableData = ref<User[]>([])
const total = ref(0)

const queryParams = reactive<UserQueryParams>({
  page: 1,
  limit: 20,
  search: '',
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const res = await getUsersApi(queryParams)
    if (res && res.data) {
      tableData.value = res.data.items
      total.value = res.data.total
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.page = 1
  getUserList()
}

// 重置搜索
const handleReset = () => {
  queryParams.search = ''
  queryParams.page = 1
  getUserList()
}

// 分页变化
const handlePageChange = (page: number) => {
  queryParams.page = page
  getUserList()
}

const handleSizeChange = (size: number) => {
  queryParams.limit = size
  queryParams.page = 1
  getUserList()
}

// 删除用户
const handleDelete = async (id: string) => {
  try {
    await deleteUserApi(id)
    ElMessage.success('删除成功')
    getUserList()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取用户显示名称
const getUserDisplayName = (user: User) => {
  return user.name || user.username || user.email || '未知用户'
}

onMounted(() => {
  getUserList()
})
</script>

<template>
  <ContentWrap>
    <!-- 搜索区域 -->
    <ElCard class="mb-20px">
      <div class="flex items-center space-x-4">
        <ElInput
          v-model="queryParams.search"
          placeholder="搜索用户名、邮箱..."
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        />
        <ElButton type="primary" @click="handleSearch">搜索</ElButton>
        <ElButton @click="handleReset">重置</ElButton>
      </div>
    </ElCard>

    <!-- 表格区域 -->
    <ElCard>
      <ElTable v-loading="loading" :data="tableData" style="width: 100%" row-key="id">
        <ElTableColumn prop="image" label="头像" width="100">
          <template #default="{ row }">
            <ElAvatar v-if="row.image" :src="row.image" :size="50" shape="circle" />
            <ElAvatar v-else :size="50" shape="circle">
              {{ getUserDisplayName(row).charAt(0).toUpperCase() }}
            </ElAvatar>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="username" label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="font-medium">{{ getUserDisplayName(row) }}</div>
            <div v-if="row.username" class="text-sm text-gray-500 mt-1">
              用户名: {{ row.username }}
            </div>
            <div class="text-xs text-gray-400 mt-1">ID: {{ row.id }}</div>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="email" label="邮箱" min-width="200">
          <template #default="{ row }">
            <div v-if="row.email">
              <div class="text-sm">{{ row.email }}</div>
              <ElTag v-if="row.emailVerified" size="small" type="success" class="mt-1">
                已验证
              </ElTag>
              <ElTag v-else size="small" type="warning" class="mt-1"> 未验证 </ElTag>
            </div>
            <span v-else class="text-gray-400">未设置</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="name" label="真实姓名" width="120">
          <template #default="{ row }">
            {{ row.name || '-' }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="createdAt" label="注册时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="updatedAt" label="最后更新" width="120">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </ElTableColumn>

        <ElTableColumn label="状态" width="100">
          <template #default="{ row }">
            <div class="space-y-1">
              <ElTag size="small" :type="row.emailVerified ? 'success' : 'info'">
                {{ row.emailVerified ? '活跃' : '待激活' }}
              </ElTag>
            </div>
          </template>
        </ElTableColumn>

        <ElTableColumn label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <ElSpace>
              <ElButton size="small" type="primary" link>编辑</ElButton>
              <ElButton size="small" type="danger" link @click="handleDelete(row.id)">
                删除
              </ElButton>
            </ElSpace>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="flex justify-center mt-20px">
        <ElPagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </ElCard>
  </ContentWrap>
</template>

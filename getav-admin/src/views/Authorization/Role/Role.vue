<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import { getRoleListApi, deleteRole<PERSON>pi, type RoleItem, type RoleQueryParams } from '@/api/role'
import { useTable } from '@/hooks/web/useTable'
import { useI18n } from '@/hooks/web/useI18n'
import { Table, TableColumn } from '@/components/Table'
import { ElTag, ElMessageBox, ElMessage } from 'element-plus'
import { Search } from '@/components/Search'
import { FormSchema } from '@/components/Form'
import { ContentWrap } from '@/components/ContentWrap'
import Write from './components/Write.vue'
import Detail from './components/Detail.vue'
import { Dialog } from '@/components/Dialog'
import { BaseButton } from '@/components/Button'
import { hasPermission } from '@/utils/permission'
import { PERMISSIONS } from '@/utils/permission'

const { t } = useI18n()

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const params: RoleQueryParams = {
      page: unref(currentPage),
      limit: unref(pageSize),
      ...unref(searchParams)
    }
    const res = await getRoleListApi(params)
    return {
      list: res.data.data || [],
      total: res.data.total || 0
    }
  }
})

const currentPage = ref(1)
const pageSize = ref(20)

const { dataList, loading, total } = tableState
const { getList } = tableMethods

const tableColumns = reactive<TableColumn[]>([
  {
    field: 'index',
    label: t('userDemo.index'),
    type: 'index'
  },
  {
    field: 'displayName',
    label: '角色名称'
  },
  {
    field: 'name',
    label: '角色标识'
  },
  {
    field: 'description',
    label: '描述'
  },
  {
    field: 'isSystem',
    label: '系统角色',
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={data.row.isSystem ? 'warning' : 'info'}>
              {data.row.isSystem ? '系统角色' : '自定义角色'}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'isActive',
    label: t('menu.status'),
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={data.row.isActive ? 'success' : 'danger'}>
              {data.row.isActive ? t('userDemo.enable') : t('userDemo.disable')}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'stats',
    label: '统计信息',
    slots: {
      default: (data: any) => {
        const stats = data.row.stats
        return (
          <>
            <div>用户: {stats?.userCount || 0}</div>
            <div>权限: {stats?.permissionCount || 0}</div>
          </>
        )
      }
    }
  },
  {
    field: 'createdAt',
    label: t('tableDemo.displayTime'),
    slots: {
      default: (data: any) => {
        return new Date(data.row.createdAt).toLocaleString()
      }
    }
  },
  {
    field: 'action',
    label: t('userDemo.action'),
    width: 280,
    slots: {
      default: (data: any) => {
        const row = data.row as RoleItem
        return (
          <>
            {hasPermission(PERMISSIONS.ROLE_READ) && (
              <BaseButton type="success" onClick={() => action(row, 'detail')}>
                {t('exampleDemo.detail')}
              </BaseButton>
            )}
            {hasPermission(PERMISSIONS.ROLE_UPDATE) && (
              <BaseButton type="primary" onClick={() => action(row, 'edit')}>
                {t('exampleDemo.edit')}
              </BaseButton>
            )}
            {hasPermission(PERMISSIONS.ROLE_DELETE) && !row.isSystem && (
              <BaseButton type="danger" onClick={() => deleteRole(row)}>
                {t('exampleDemo.del')}
              </BaseButton>
            )}
          </>
        )
      }
    }
  }
])

const searchSchema = reactive<FormSchema[]>([
  {
    field: 'search',
    label: '搜索',
    component: 'Input',
    componentProps: {
      placeholder: '请输入角色名称或描述'
    }
  },
  {
    field: 'isActive',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      clearable: true,
      options: [
        {
          label: '启用',
          value: true
        },
        {
          label: '禁用',
          value: false
        }
      ]
    }
  },
  {
    field: 'isSystem',
    label: '角色类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择角色类型',
      clearable: true,
      options: [
        {
          label: '系统角色',
          value: true
        },
        {
          label: '自定义角色',
          value: false
        }
      ]
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const dialogVisible = ref(false)
const dialogTitle = ref('')

const currentRow = ref()
const actionType = ref('')

const writeRef = ref<ComponentRef<typeof Write>>()

const saveLoading = ref(false)

const action = (row: any, type: string) => {
  dialogTitle.value = t(type === 'edit' ? 'exampleDemo.edit' : 'exampleDemo.detail')
  actionType.value = type
  currentRow.value = row
  dialogVisible.value = true
}

const AddAction = () => {
  dialogTitle.value = t('exampleDemo.add')
  currentRow.value = undefined
  dialogVisible.value = true
  actionType.value = ''
}

const save = async () => {
  const write = unref(writeRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true
    try {
      // 这里会在Write组件中处理实际的保存逻辑
      saveLoading.value = false
      dialogVisible.value = false
      ElMessage.success('保存成功')
      getList() // 刷新列表
    } catch (error) {
      saveLoading.value = false
      ElMessage.error('保存失败')
    }
  }
}

// 删除角色
const deleteRole = async (row: RoleItem) => {
  if (row.isSystem) {
    ElMessage.warning('系统角色不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.displayName}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteRoleApi(row.id)
    ElMessage.success('删除成功')
    getList() // 刷新列表
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchema" @reset="setSearchParams" @search="setSearchParams" />
    <div class="mb-10px">
      <BaseButton v-if="hasPermission(PERMISSIONS.ROLE_CREATE)" type="primary" @click="AddAction">
        {{ t('exampleDemo.add') }}
      </BaseButton>
    </div>
    <Table
      :columns="tableColumns"
      :data="dataList"
      :loading="loading"
      :pagination="{
        total,
        currentPage,
        pageSize
      }"
      @register="tableRegister"
    />
  </ContentWrap>

  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <Write v-if="actionType !== 'detail'" ref="writeRef" :current-row="currentRow" />
    <Detail v-else :current-row="currentRow" />

    <template #footer>
      <BaseButton
        v-if="actionType !== 'detail'"
        type="primary"
        :loading="saveLoading"
        @click="save"
      >
        {{ t('exampleDemo.save') }}
      </BaseButton>
      <BaseButton @click="dialogVisible = false">{{ t('dialogDemo.close') }}</BaseButton>
    </template>
  </Dialog>
</template>

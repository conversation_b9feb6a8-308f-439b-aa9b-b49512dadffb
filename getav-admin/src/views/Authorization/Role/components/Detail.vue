<script setup lang="tsx">
import { PropType, ref, unref, nextTick, watch } from 'vue'
import { Descriptions, DescriptionsSchema } from '@/components/Descriptions'
import { ElTag, ElTree } from 'element-plus'
import { getRolePermissionsApi, type RoleItem } from '@/api/role'
import { getPermissionTreeApi, type PermissionItem } from '@/api/permission'

const props = defineProps({
  currentRow: {
    type: Object as PropType<RoleItem>,
    default: () => undefined
  }
})

const renderTag = (enable?: boolean) => {
  return <ElTag type={!enable ? 'danger' : 'success'}>{enable ? '启用' : '禁用'}</ElTag>
}

const renderSystemTag = (isSystem?: boolean) => {
  return <ElTag type={isSystem ? 'warning' : 'info'}>{isSystem ? '系统角色' : '自定义角色'}</ElTag>
}

const treeRef = ref<typeof ElTree>()
const permissionTree = ref<PermissionItem[]>([])
const rolePermissions = ref<PermissionItem[]>([])

// 获取权限树
const getPermissionTree = async () => {
  try {
    const res = await getPermissionTreeApi()
    if (res.data) {
      permissionTree.value = res.data
    }
  } catch (error) {
    console.error('获取权限树失败:', error)
  }
}

// 获取角色权限
const getRolePermissions = async (roleId: string) => {
  try {
    const res = await getRolePermissionsApi(roleId)
    if (res.data) {
      rolePermissions.value = res.data
      await nextTick()
      // 设置树的选中状态（仅用于显示）
      const permissionIds = res.data.map((p: PermissionItem) => p.id)
      unref(treeRef)?.setCheckedKeys(permissionIds, false)
    }
  } catch (error) {
    console.error('获取角色权限失败:', error)
  }
}

// 初始化
getPermissionTree()

const detailSchema = ref<DescriptionsSchema[]>([
  {
    field: 'displayName',
    label: '角色名称'
  },
  {
    field: 'name',
    label: '角色标识'
  },
  {
    field: 'description',
    label: '角色描述'
  },
  {
    field: 'priority',
    label: '优先级'
  },
  {
    field: 'isSystem',
    label: '角色类型',
    slots: {
      default: (data: any) => {
        return renderSystemTag(data.isSystem)
      }
    }
  },
  {
    field: 'isActive',
    label: '状态',
    slots: {
      default: (data: any) => {
        return renderTag(data.isActive)
      }
    }
  },
  {
    field: 'stats',
    label: '统计信息',
    slots: {
      default: (data: any) => {
        const stats = data.stats
        return (
          <>
            <div>用户数量: {stats?.userCount || 0}</div>
            <div>权限数量: {stats?.permissionCount || 0}</div>
          </>
        )
      }
    }
  },
  {
    field: 'createdAt',
    label: '创建时间',
    slots: {
      default: (data: any) => {
        return new Date(data.createdAt).toLocaleString()
      }
    }
  },
  {
    field: 'updatedAt',
    label: '更新时间',
    slots: {
      default: (data: any) => {
        return new Date(data.updatedAt).toLocaleString()
      }
    }
  },
  {
    field: 'permissions',
    label: '权限分配',
    span: 24,
    slots: {
      default: () => {
        return (
          <>
            <div class="w-full">
              <div class="mb-4">
                <h4>已分配权限:</h4>
                <div class="flex flex-wrap gap-2 mt-2">
                  {rolePermissions.value.map((permission) => (
                    <ElTag key={permission.id} type="success">
                      {permission.displayName}
                    </ElTag>
                  ))}
                </div>
              </div>
              <div>
                <h4>权限树结构:</h4>
                <ElTree
                  ref={treeRef}
                  show-checkbox
                  node-key="id"
                  check-strictly={false}
                  expand-on-click-node={false}
                  data={permissionTree.value}
                  props={{
                    children: 'children',
                    label: 'displayName'
                  }}
                  disabled
                >
                  {{
                    default: ({ data }: { data: PermissionItem }) => {
                      return (
                        <span>
                          {data.displayName}
                          <span class="text-gray-400 ml-2 text-sm">({data.name})</span>
                        </span>
                      )
                    }
                  }}
                </ElTree>
              </div>
            </div>
          </>
        )
      }
    }
  }
])

// 监听当前行变化
watch(
  () => props.currentRow,
  async (currentRow) => {
    if (currentRow) {
      await getRolePermissions(currentRow.id)
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <Descriptions :schema="detailSchema" :data="props.currentRow || {}" />
</template>

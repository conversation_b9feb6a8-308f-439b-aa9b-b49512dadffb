<script setup lang="tsx">
import { Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { PropType, reactive, watch, ref, unref, nextTick } from 'vue'
import { useValidator } from '@/hooks/web/useValidator'
import { useI18n } from '@/hooks/web/useI18n'
import { ElTree, ElMessage } from 'element-plus'
import {
  createRoleApi,
  updateRoleApi,
  getRolePermissionsApi,
  setRolePermissionsApi,
  type RoleItem,
  type RoleFormData
} from '@/api/role'
import { getPermissionTreeApi, type PermissionItem } from '@/api/permission'

const { t } = useI18n()

const { required } = useValidator()

const props = defineProps({
  currentRow: {
    type: Object as PropType<RoleItem | null>,
    default: () => null
  }
})

const treeRef = ref<typeof ElTree>()

const formSchema = ref<FormSchema[]>([
  {
    field: 'name',
    label: '角色标识',
    component: 'Input',
    componentProps: {
      placeholder: '请输入角色标识，如：editor',
      disabled: !!props.currentRow // 编辑时不允许修改角色标识
    }
  },
  {
    field: 'displayName',
    label: '角色名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入角色显示名称，如：编辑员'
    }
  },
  {
    field: 'description',
    label: '角色描述',
    component: 'Input',
    componentProps: {
      type: 'textarea',
      placeholder: '请输入角色描述'
    }
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'InputNumber',
    componentProps: {
      placeholder: '数字越大优先级越高',
      min: 0,
      max: 100
    }
  },
  {
    field: 'isActive',
    label: '状态',
    component: 'Switch',
    componentProps: {
      activeText: '启用',
      inactiveText: '禁用'
    }
  },
  {
    field: 'permissions',
    label: '权限分配',
    colProps: {
      span: 24
    },
    formItemProps: {
      slots: {
        default: () => {
          return (
            <>
              <div class="w-full">
                <ElTree
                  ref={treeRef}
                  show-checkbox
                  node-key="id"
                  check-strictly={false}
                  expand-on-click-node={false}
                  data={permissionTree.value}
                  props={{
                    children: 'children',
                    label: 'displayName'
                  }}
                >
                  {{
                    default: ({ data }: { data: PermissionItem }) => {
                      return (
                        <span>
                          {data.displayName}
                          <span class="text-gray-400 ml-2 text-sm">({data.name})</span>
                        </span>
                      )
                    }
                  }}
                </ElTree>
              </div>
            </>
          )
        }
      }
    }
  }
])

const rules = reactive({
  name: [required()],
  displayName: [required()],
  isActive: [required()]
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose } = formMethods

const permissionTree = ref<PermissionItem[]>([])
const currentRolePermissions = ref<string[]>([])

// 获取权限树
const getPermissionTree = async () => {
  try {
    const res = await getPermissionTreeApi()
    if (res.data) {
      permissionTree.value = res.data
    }
  } catch (error) {
    console.error('获取权限树失败:', error)
  }
}

// 获取角色权限
const getRolePermissions = async (roleId: string) => {
  try {
    const res = await getRolePermissionsApi(roleId)
    if (res.data) {
      currentRolePermissions.value = res.data.map((p: PermissionItem) => p.id)
      await nextTick()
      // 设置树的选中状态
      unref(treeRef)?.setCheckedKeys(currentRolePermissions.value, false)
    }
  } catch (error) {
    console.error('获取角色权限失败:', error)
  }
}

// 初始化
getPermissionTree()

const submit = async () => {
  const elForm = await getElFormExpose()
  const valid = await elForm?.validate().catch((err) => {
    console.log(err)
  })
  if (valid) {
    try {
      const formData = await getFormData()
      const checkedKeys = unref(treeRef)?.getCheckedKeys() || []

      // 准备角色数据
      const roleData: RoleFormData = {
        name: formData.name,
        displayName: formData.displayName,
        description: formData.description,
        priority: formData.priority || 0,
        isActive: formData.isActive !== false
      }

      let roleId: string

      if (props.currentRow) {
        // 更新角色
        await updateRoleApi(props.currentRow.id, roleData)
        roleId = props.currentRow.id
        ElMessage.success('角色更新成功')
      } else {
        // 创建角色
        const res = await createRoleApi(roleData)
        roleId = res.data.id
        ElMessage.success('角色创建成功')
      }

      // 设置角色权限
      if (checkedKeys.length > 0) {
        await setRolePermissionsApi(roleId, checkedKeys as string[])
      }

      return formData
    } catch (error: any) {
      ElMessage.error(error.message || '操作失败')
      throw error
    }
  }
}

watch(
  () => props.currentRow,
  async (currentRow) => {
    if (!currentRow) {
      // 新建时的默认值
      setValues({
        name: '',
        displayName: '',
        description: '',
        priority: 0,
        isActive: true
      })
      return
    }

    // 编辑时设置表单值
    setValues({
      name: currentRow.name,
      displayName: currentRow.displayName,
      description: currentRow.description || '',
      priority: currentRow.priority || 0,
      isActive: currentRow.isActive
    })

    // 获取角色权限
    await getRolePermissions(currentRow.id)
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})
</script>

<template>
  <Form :rules="rules" @register="formRegister" :schema="formSchema" />
</template>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSwitch,
  ElMessage,
  ElRow,
  ElCol
} from 'element-plus'
import { ref, reactive, onMounted } from 'vue'

const { t } = useI18n()

const loading = ref(false)
const formRef = ref()

const settings = reactive({
  siteName: 'GETAV.NET',
  siteDescription: '专业的影片资源管理平台',
  siteKeywords: 'GETAV, 影片, 管理, 平台',
  adminEmail: '<EMAIL>',
  enableRegistration: true,
  enableComments: true,
  enableRating: true,
  maxUploadSize: 100,
  cacheExpiration: 3600,
  enableEmailNotification: true,
  enableSMSNotification: false,
  apiBaseUrl: 'http://localhost:8000',
  cdnUrl: '',
  enableCDN: false
})

const rules = {
  siteName: [{ required: true, message: '请输入网站名称', trigger: 'blur' }],
  adminEmail: [
    { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  apiBaseUrl: [{ required: true, message: '请输入API基础URL', trigger: 'blur' }]
}

// 保存设置
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    // 模拟保存操作
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    loading.value = false
  }
}

// 重置设置
const handleReset = () => {
  formRef.value?.resetFields()
  ElMessage.info('设置已重置')
}

onMounted(() => {
  // 加载设置数据
})
</script>

<template>
  <ContentWrap>
    <ElCard>
      <template #header>
        <div class="card-header">
          <span class="font-medium">系统设置</span>
        </div>
      </template>

      <ElForm ref="formRef" :model="settings" :rules="rules" label-width="120px" size="default">
        <!-- 基本设置 -->
        <ElCard class="mb-20px">
          <template #header>
            <span class="text-lg font-medium">基本设置</span>
          </template>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="网站名称" prop="siteName">
                <ElInput v-model="settings.siteName" placeholder="请输入网站名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="管理员邮箱" prop="adminEmail">
                <ElInput v-model="settings.adminEmail" placeholder="请输入管理员邮箱" />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElFormItem label="网站描述">
            <ElInput
              v-model="settings.siteDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入网站描述"
            />
          </ElFormItem>

          <ElFormItem label="网站关键词">
            <ElInput v-model="settings.siteKeywords" placeholder="请输入网站关键词，用逗号分隔" />
          </ElFormItem>
        </ElCard>

        <!-- 功能设置 -->
        <ElCard class="mb-20px">
          <template #header>
            <span class="text-lg font-medium">功能设置</span>
          </template>

          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="允许注册">
                <ElSwitch v-model="settings.enableRegistration" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="允许评论">
                <ElSwitch v-model="settings.enableComments" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="允许评分">
                <ElSwitch v-model="settings.enableRating" />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="最大上传大小">
                <ElInput v-model.number="settings.maxUploadSize" type="number" placeholder="MB">
                  <template #suffix>MB</template>
                </ElInput>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="缓存过期时间">
                <ElInput v-model.number="settings.cacheExpiration" type="number" placeholder="秒">
                  <template #suffix>秒</template>
                </ElInput>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCard>

        <!-- 通知设置 -->
        <ElCard class="mb-20px">
          <template #header>
            <span class="text-lg font-medium">通知设置</span>
          </template>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="邮件通知">
                <ElSwitch v-model="settings.enableEmailNotification" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="短信通知">
                <ElSwitch v-model="settings.enableSMSNotification" />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCard>

        <!-- API设置 -->
        <ElCard class="mb-20px">
          <template #header>
            <span class="text-lg font-medium">API设置</span>
          </template>

          <ElFormItem label="API基础URL" prop="apiBaseUrl">
            <ElInput v-model="settings.apiBaseUrl" placeholder="请输入API基础URL" />
          </ElFormItem>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="启用CDN">
                <ElSwitch v-model="settings.enableCDN" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="CDN地址">
                <ElInput
                  v-model="settings.cdnUrl"
                  placeholder="请输入CDN地址"
                  :disabled="!settings.enableCDN"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCard>

        <!-- 操作按钮 -->
        <ElFormItem>
          <ElButton type="primary" :loading="loading" @click="handleSave"> 保存设置 </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>
  </ContentWrap>
</template>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

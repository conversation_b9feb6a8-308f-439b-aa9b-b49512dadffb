<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import {
  ElCard,
  ElRow,
  ElCol,
  ElStatistic,
  ElTable,
  ElTableColumn,
  ElTag,
  ElProgress
} from 'element-plus'
import { ref, onMounted } from 'vue'
import { getDashboardStatsApi } from '@/api/stats'

// const { t } = useI18n()

const loading = ref(true)
const stats = ref({
  totalMovies: 0,
  totalStars: 0,
  totalUsers: 0,
  totalViews: 0,
  recentMovies: [],
  popularStars: [],
  recentUsers: []
})

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    loading.value = true
    const response = await getDashboardStatsApi()

    if (response && response.data) {
      const data = response.data

      // 更新统计数据
      stats.value = {
        totalMovies: data.overview?.totalMovies || 0,
        totalStars: data.overview?.totalStars || 0,
        totalUsers: data.overview?.totalUsers || 0,
        totalViews: data.content?.interactions?.views || 0,
        recentMovies: data.overview?.popularMovies || [],
        popularStars: data.overview?.topStars || [],
        recentUsers: data.userActivity?.topCommenters || []
      }
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    // 如果API失败，使用默认值
    stats.value = {
      totalMovies: 0,
      totalStars: 0,
      totalUsers: 0,
      totalViews: 0,
      recentMovies: [],
      popularStars: [],
      recentUsers: []
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<template>
  <ContentWrap>
    <!-- 统计卡片 -->
    <ElRow :gutter="20" class="mb-20px">
      <ElCol :span="6">
        <ElCard>
          <ElStatistic title="影片总数" :value="stats.totalMovies" :formatter="formatNumber">
            <template #suffix>
              <span class="text-sm text-gray-500">部</span>
            </template>
          </ElStatistic>
        </ElCard>
      </ElCol>
      <ElCol :span="6">
        <ElCard>
          <ElStatistic title="演员总数" :value="stats.totalStars" :formatter="formatNumber">
            <template #suffix>
              <span class="text-sm text-gray-500">人</span>
            </template>
          </ElStatistic>
        </ElCard>
      </ElCol>
      <ElCol :span="6">
        <ElCard>
          <ElStatistic title="用户总数" :value="stats.totalUsers" :formatter="formatNumber">
            <template #suffix>
              <span class="text-sm text-gray-500">人</span>
            </template>
          </ElStatistic>
        </ElCard>
      </ElCol>
      <ElCol :span="6">
        <ElCard>
          <ElStatistic title="总观看量" :value="stats.totalViews" :formatter="formatNumber">
            <template #suffix>
              <span class="text-sm text-gray-500">次</span>
            </template>
          </ElStatistic>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 数据表格 -->
    <ElRow :gutter="20">
      <!-- 最新影片 -->
      <ElCol :span="12">
        <ElCard>
          <template #header>
            <div class="card-header">
              <span class="font-medium">最新影片</span>
            </div>
          </template>
          <ElTable :data="stats.recentMovies" style="width: 100%" v-loading="loading">
            <ElTableColumn prop="title" label="标题" min-width="150">
              <template #default="{ row }">
                <div class="font-medium">{{ row.title || row.name || '未知标题' }}</div>
                <div class="text-sm text-gray-500">{{ row.code || row.id || '-' }}</div>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="releaseDate" label="发布日期" width="100">
              <template #default="{ row }">
                {{
                  row.releaseDate
                    ? formatDate(row.releaseDate)
                    : row.createdAt
                      ? formatDate(row.createdAt)
                      : '-'
                }}
              </template>
            </ElTableColumn>
            <ElTableColumn prop="views" label="观看量" width="80">
              <template #default="{ row }">
                {{ formatNumber(row.views || row.viewCount || 0) }}
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElCol>

      <!-- 热门演员 -->
      <ElCol :span="12">
        <ElCard>
          <template #header>
            <div class="card-header">
              <span class="font-medium">热门演员</span>
            </div>
          </template>
          <ElTable :data="stats.popularStars" style="width: 100%" v-loading="loading">
            <ElTableColumn prop="name" label="姓名" min-width="120">
              <template #default="{ row }">
                <div class="font-medium">{{ row.name || '未知演员' }}</div>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="movieCount" label="影片数" width="80">
              <template #default="{ row }">
                <ElTag size="small" type="success">
                  {{ row.movieCount || row._count?.movies || 0 }}部
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="popularity" label="热度" width="100">
              <template #default="{ row }">
                <ElProgress
                  :percentage="Math.min(100, row.popularity || row.movieCount || 0)"
                  :show-text="false"
                  :stroke-width="8"
                />
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 最新用户 -->
    <ElRow :gutter="20" class="mt-20px">
      <ElCol :span="24">
        <ElCard>
          <template #header>
            <div class="card-header">
              <span class="font-medium">最新注册用户</span>
            </div>
          </template>
          <ElTable :data="stats.recentUsers" style="width: 100%" v-loading="loading">
            <ElTableColumn prop="username" label="用户名" width="150">
              <template #default="{ row }">
                <div class="font-medium">{{ row.username || row.name || '匿名用户' }}</div>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="email" label="邮箱" min-width="200">
              <template #default="{ row }">
                {{ row.email || '-' }}
              </template>
            </ElTableColumn>
            <ElTableColumn prop="emailVerified" label="状态" width="100">
              <template #default="{ row }">
                <ElTag size="small" :type="row.emailVerified ? 'success' : 'warning'">
                  {{ row.emailVerified ? '已验证' : '待验证' }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="createdAt" label="注册时间" width="120">
              <template #default="{ row }">
                {{ row.createdAt ? formatDate(row.createdAt) : '-' }}
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElCol>
    </ElRow>
  </ContentWrap>
</template>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import {
  ElTable,
  ElTableColumn,
  ElPagination,
  ElButton,
  ElInput,
  ElCard,
  ElImage,
  ElTag,
  ElSpace,
  ElMessage,
  ElAvatar
} from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { getStarsApi, deleteStarApi, type Star, type StarQueryParams } from '@/api/stars'

const { t } = useI18n()

const loading = ref(false)
const tableData = ref<Star[]>([])
const total = ref(0)

const queryParams = reactive<StarQueryParams>({
  page: 1,
  limit: 20,
  search: '',
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// 获取演员列表
const getStarList = async () => {
  loading.value = true
  try {
    const res = await getStarsApi(queryParams)
    if (res && res.data) {
      tableData.value = res.data.items
      total.value = res.data.total
    }
  } catch (error) {
    console.error('获取演员列表失败:', error)
    ElMessage.error('获取演员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.page = 1
  getStarList()
}

// 重置搜索
const handleReset = () => {
  queryParams.search = ''
  queryParams.page = 1
  getStarList()
}

// 分页变化
const handlePageChange = (page: number) => {
  queryParams.page = page
  getStarList()
}

const handleSizeChange = (size: number) => {
  queryParams.limit = size
  queryParams.page = 1
  getStarList()
}

// 删除演员
const handleDelete = async (id: string) => {
  try {
    await deleteStarApi(id)
    ElMessage.success('删除成功')
    getStarList()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 计算年龄
const calculateAge = (birthday?: string) => {
  if (!birthday) return '-'
  const birth = new Date(birthday)
  const today = new Date()
  const age = today.getFullYear() - birth.getFullYear()
  return age > 0 ? `${age}岁` : '-'
}

onMounted(() => {
  getStarList()
})
</script>

<template>
  <ContentWrap>
    <!-- 搜索区域 -->
    <ElCard class="mb-20px">
      <div class="flex items-center space-x-4">
        <ElInput
          v-model="queryParams.search"
          placeholder="搜索演员姓名..."
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        />
        <ElButton type="primary" @click="handleSearch">搜索</ElButton>
        <ElButton @click="handleReset">重置</ElButton>
      </div>
    </ElCard>

    <!-- 表格区域 -->
    <ElCard>
      <ElTable v-loading="loading" :data="tableData" style="width: 100%" row-key="id">
        <ElTableColumn prop="avatar" label="头像" width="100">
          <template #default="{ row }">
            <ElAvatar
              v-if="row.localAvatar || row.avatar"
              :src="row.localAvatar || row.avatar"
              :size="60"
              shape="circle"
            />
            <ElAvatar v-else :size="60" shape="circle">
              {{ row.name.charAt(0) }}
            </ElAvatar>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="name" label="姓名" min-width="150">
          <template #default="{ row }">
            <div class="font-medium">{{ row.name }}</div>
            <div class="text-sm text-gray-500 mt-1">{{ row.id }}</div>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="birthday" label="生日/年龄" width="120">
          <template #default="{ row }">
            <div v-if="row.birthday">
              <div class="text-sm">{{ row.birthday }}</div>
              <div class="text-xs text-gray-500">{{ calculateAge(row.birthday) }}</div>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="measurements" label="三围" width="120">
          <template #default="{ row }">
            <div v-if="row.bust || row.waist || row.hip" class="text-sm">
              <div v-if="row.bust">胸: {{ row.bust }}</div>
              <div v-if="row.waist">腰: {{ row.waist }}</div>
              <div v-if="row.hip">臀: {{ row.hip }}</div>
            </div>
            <div v-else-if="row.measurements" class="text-sm">
              {{ row.measurements }}
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="height" label="身高" width="80">
          <template #default="{ row }">
            {{ row.height || '-' }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="cupSize" label="罩杯" width="80">
          <template #default="{ row }">
            <ElTag v-if="row.cupSize" size="small" type="warning">
              {{ row.cupSize }}
            </ElTag>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="birthplace" label="出生地" width="120">
          <template #default="{ row }">
            {{ row.birthplace || '-' }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="movieCount" label="影片数量" width="100">
          <template #default="{ row }">
            <ElTag type="success" size="small"> {{ row.movieCount || 0 }}部 </ElTag>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="recentMovies" label="最新影片" min-width="200">
          <template #default="{ row }">
            <div v-if="row.recentMovies && row.recentMovies.length > 0">
              <div
                v-for="movie in row.recentMovies.slice(0, 2)"
                :key="movie.id"
                class="text-sm mb-1"
              >
                <span class="font-medium">{{ movie.title }}</span>
                <span class="text-gray-500 ml-2">{{ movie.date }}</span>
              </div>
              <div v-if="row.recentMovies.length > 2" class="text-xs text-gray-400">
                还有 {{ row.recentMovies.length - 2 }} 部...
              </div>
            </div>
            <span v-else class="text-gray-400">暂无</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="createdAt" label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </ElTableColumn>

        <ElTableColumn label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <ElSpace>
              <ElButton size="small" type="primary" link>编辑</ElButton>
              <ElButton size="small" type="danger" link @click="handleDelete(row.id)">
                删除
              </ElButton>
            </ElSpace>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="flex justify-center mt-20px">
        <ElPagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </ElCard>
  </ContentWrap>
</template>

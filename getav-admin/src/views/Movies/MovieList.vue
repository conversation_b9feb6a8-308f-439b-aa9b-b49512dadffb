<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import {
  ElTable,
  ElTableColumn,
  ElPagination,
  ElButton,
  ElInput,
  ElCard,
  ElImage,
  ElTag,
  ElSpace,
  ElMessage,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElUpload,
  ElIcon,
  ElTabs,
  ElTabPane,
  ElDivider,
  ElCollapse,
  ElCollapseItem
} from 'element-plus'
import { ref, reactive, onMounted, computed } from 'vue'
import {
  getMoviesApi,
  deleteMovieApi,
  updateMovieApi,
  type Movie,
  type MovieQueryParams
} from '@/api/movies'

const { t } = useI18n()

const loading = ref(false)
const tableData = ref<Movie[]>([])
const total = ref(0)

// 编辑对话框相关
const editDialogVisible = ref(false)
const editLoading = ref(false)
const currentMovie = ref<Movie | null>(null)
const newGenreName = ref('')

// 常用分类
const commonGenres = [
  '高画质',
  '4K',
  '单体作品',
  '中出',
  '巨乳',
  '美少女',
  '已婚妇女',
  '成熟的女人',
  '口交',
  '多P',
  '制服',
  '学生',
  'OL',
  '女教师',
  '护士',
  '空姐'
]
const editForm = ref({
  // 基本信息
  title: '',
  description: '',
  videoLength: 0,
  date: '',
  localImg: '',

  // 制作信息
  director: {
    id: '',
    name: ''
  },
  producer: {
    id: '',
    name: ''
  },
  publisher: {
    id: '',
    name: ''
  },
  series: {
    id: '',
    name: ''
  },

  // 演员信息
  stars: [] as Array<{
    id: string
    name: string
    avatar?: string
    localAvatar?: string
  }>,

  // 分类信息
  genres: [] as Array<{
    id: string
    name: string
  }>
})

const queryParams = reactive<MovieQueryParams>({
  page: 1,
  limit: 20,
  search: '',
  sortBy: 'createdAt',
  sortOrder: 'desc',

  // 新增筛选参数
  censored: 'all',
  hasSubtitle: undefined,
  durationMin: undefined,
  durationMax: undefined,
  releaseDateFrom: undefined,
  releaseDateTo: undefined,
  popularityType: undefined
})

// 获取影片列表
const getMovieList = async () => {
  loading.value = true
  try {
    const res = await getMoviesApi(queryParams)
    if (res && res.data) {
      tableData.value = res.data.items
      total.value = res.data.total
    }
  } catch (error) {
    console.error('获取影片列表失败:', error)
    ElMessage.error('获取影片列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.page = 1
  getMovieList()
}

// 重置搜索
const handleReset = () => {
  queryParams.search = ''
  queryParams.genre = ''
  queryParams.star = ''
  queryParams.censored = 'all'
  queryParams.hasSubtitle = undefined
  queryParams.durationMin = undefined
  queryParams.durationMax = undefined
  queryParams.releaseDateFrom = undefined
  queryParams.releaseDateTo = undefined
  queryParams.popularityType = undefined
  queryParams.sortBy = 'createdAt'
  queryParams.sortOrder = 'desc'
  queryParams.page = 1

  // 重置日期范围选择器
  releaseDateRange.value = null

  getMovieList()
}

// 分页变化
const handlePageChange = (page: number) => {
  queryParams.page = page
  getMovieList()
}

const handleSizeChange = (size: number) => {
  queryParams.limit = size
  queryParams.page = 1
  getMovieList()
}

// 编辑影片
const handleEdit = (movie: Movie) => {
  currentMovie.value = movie
  editForm.value = {
    // 基本信息
    title: movie.title,
    description: movie.description || '',
    videoLength: movie.videoLength || 0,
    date: movie.date || '',
    localImg: movie.localImg || '',

    // 制作信息
    director: {
      id: movie.director?.id || '',
      name: movie.director?.name || ''
    },
    producer: {
      id: movie.producer?.id || '',
      name: movie.producer?.name || ''
    },
    publisher: {
      id: movie.publisher?.id || '',
      name: movie.publisher?.name || ''
    },
    series: {
      id: movie.series?.id || '',
      name: movie.series?.name || ''
    },

    // 演员信息
    stars: movie.stars ? [...movie.stars] : [],

    // 分类信息
    genres: movie.genres ? [...movie.genres] : []
  }
  editDialogVisible.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  if (!currentMovie.value) return

  try {
    editLoading.value = true
    await updateMovieApi(currentMovie.value.id, editForm.value)
    ElMessage.success('更新成功')
    editDialogVisible.value = false
    getMovieList()
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('更新失败')
  } finally {
    editLoading.value = false
  }
}

// 取消编辑
const handleCancelEdit = () => {
  editDialogVisible.value = false
  currentMovie.value = null
  newGenreName.value = ''
  editForm.value = {
    // 基本信息
    title: '',
    description: '',
    videoLength: 0,
    date: '',
    localImg: '',

    // 制作信息
    director: { id: '', name: '' },
    producer: { id: '', name: '' },
    publisher: { id: '', name: '' },
    series: { id: '', name: '' },

    // 演员和分类信息
    stars: [],
    genres: []
  }
}

// 删除影片
const handleDelete = async (id: string) => {
  try {
    await deleteMovieApi(id)
    ElMessage.success('删除成功')
    getMovieList()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 格式化时长
const formatDuration = (minutes?: number) => {
  if (!minutes) return '-'
  return `${minutes}分钟`
}

// 获取图片URL - 只使用本地图片
const getImageUrl = (localImg?: string) => {
  // 只使用本地图片路径，转换为完整URL
  if (localImg) {
    return `http://localhost:3001${localImg}`
  }
  return ''
}

// 添加演员
const addStar = () => {
  editForm.value.stars.push({
    id: '',
    name: '',
    avatar: '',
    localAvatar: ''
  })
}

// 删除演员
const removeStar = (index: number) => {
  editForm.value.stars.splice(index, 1)
}

// 通过名称添加分类
const addGenreByName = () => {
  const name = newGenreName.value.trim()
  if (!name) return

  // 检查是否已存在
  if (editForm.value.genres.some((g) => g.name === name)) {
    ElMessage.warning('该分类已存在')
    return
  }

  // 生成简单的ID（可以是名称的拼音或者随机字符串）
  const id = Math.random().toString(36).substr(2, 8)

  editForm.value.genres.push({
    id,
    name
  })

  newGenreName.value = ''
  ElMessage.success('分类添加成功')
}

// 添加常用分类
const addCommonGenre = (genreName: string) => {
  // 检查是否已存在
  if (editForm.value.genres.some((g) => g.name === genreName)) {
    ElMessage.warning('该分类已存在')
    return
  }

  const id = Math.random().toString(36).substr(2, 8)
  editForm.value.genres.push({
    id,
    name: genreName
  })

  ElMessage.success('分类添加成功')
}

// 删除分类
const removeGenre = (index: number) => {
  editForm.value.genres.splice(index, 1)
  ElMessage.success('分类删除成功')
}

// 日期范围选择器的值
const releaseDateRange = ref<[string, string] | null>(null)

// 处理上市时间范围变化
const handleReleaseDateChange = (dates: [string, string] | null) => {
  if (dates && dates.length === 2) {
    queryParams.releaseDateFrom = dates[0]
    queryParams.releaseDateTo = dates[1]
  } else {
    queryParams.releaseDateFrom = undefined
    queryParams.releaseDateTo = undefined
  }
  handleSearch()
}

// 处理人气排序变化
const handlePopularityTypeChange = (value: string | undefined) => {
  if (value) {
    // 当选择人气排序时，自动设置sortBy为对应的人气类型
    queryParams.sortBy = value
  }
  handleSearch()
}

// 获取统计列标题
const getStatsColumnTitle = computed(() => {
  const popularityType = queryParams.popularityType
  if (popularityType) {
    const typeMap = {
      daily: '日人气统计',
      weekly: '周人气统计',
      monthly: '月人气统计',
      total: '总人气统计'
    }
    return typeMap[popularityType] || '统计数据'
  }
  return '统计数据'
})

onMounted(() => {
  getMovieList()
})
</script>

<template>
  <ContentWrap>
    <!-- 搜索区域 -->
    <ElCard class="mb-20px">
      <!-- 基础搜索 -->
      <div class="flex items-center space-x-4 mb-4">
        <ElInput
          v-model="queryParams.search"
          placeholder="搜索影片标题..."
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        />
        <ElButton type="primary" @click="handleSearch">搜索</ElButton>
        <ElButton @click="handleReset">重置</ElButton>
      </div>

      <!-- 高级筛选 -->
      <ElCollapse>
        <ElCollapseItem title="高级筛选" name="filters">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <!-- 有码无码筛选 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">有码/无码</label>
              <ElSelect
                v-model="queryParams.censored"
                placeholder="选择类型"
                style="width: 100%"
                @change="handleSearch"
              >
                <ElOption label="全部" value="all" />
                <ElOption label="有码" value="censored" />
                <ElOption label="无码" value="uncensored" />
              </ElSelect>
            </div>

            <!-- 字幕筛选 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">字幕</label>
              <ElSelect
                v-model="queryParams.hasSubtitle"
                placeholder="选择字幕"
                style="width: 100%"
                @change="handleSearch"
              >
                <ElOption label="全部" :value="undefined" />
                <ElOption label="有字幕" :value="true" />
                <ElOption label="无字幕" :value="false" />
              </ElSelect>
            </div>

            <!-- 时长范围 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">时长范围(分钟)</label>
              <div class="flex space-x-2">
                <ElInputNumber
                  v-model="queryParams.durationMin"
                  :min="0"
                  :max="9999"
                  placeholder="最小"
                  style="width: 100%"
                  @change="handleSearch"
                />
                <span class="flex items-center">-</span>
                <ElInputNumber
                  v-model="queryParams.durationMax"
                  :min="0"
                  :max="9999"
                  placeholder="最大"
                  style="width: 100%"
                  @change="handleSearch"
                />
              </div>
            </div>

            <!-- 上市时间范围 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">上市时间</label>
              <ElDatePicker
                v-model="releaseDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="handleReleaseDateChange"
              />
            </div>

            <!-- 排序方式 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">排序方式</label>
              <ElSelect
                v-model="queryParams.sortBy"
                placeholder="选择排序字段"
                style="width: 100%"
                @change="handleSearch"
              >
                <ElOption label="创建时间" value="createdAt" />
                <ElOption label="更新时间" value="updatedAt" />
                <ElOption label="标题" value="title" />
                <ElOption label="发布日期" value="date" />
                <ElOption label="影片时长" value="videoLength" />
                <ElOption label="总观看数" value="views" />
                <ElOption label="点赞数" value="likes" />
                <ElOption label="收藏数" value="favorites" />
                <ElOption label="评论数" value="comments" />
              </ElSelect>
            </div>

            <!-- 排序方向 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">排序方向</label>
              <ElSelect
                v-model="queryParams.sortOrder"
                placeholder="选择排序方向"
                style="width: 100%"
                @change="handleSearch"
              >
                <ElOption label="降序 (高到低)" value="desc" />
                <ElOption label="升序 (低到高)" value="asc" />
              </ElSelect>
            </div>

            <!-- 人气排序 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">人气排序</label>
              <ElSelect
                v-model="queryParams.popularityType"
                placeholder="选择人气类型"
                style="width: 100%"
                @change="handlePopularityTypeChange"
                clearable
              >
                <ElOption label="日人气" value="daily" />
                <ElOption label="周人气" value="weekly" />
                <ElOption label="月人气" value="monthly" />
                <ElOption label="总人气" value="total" />
              </ElSelect>
            </div>
          </div>
        </ElCollapseItem>
      </ElCollapse>
    </ElCard>

    <!-- 表格区域 -->
    <ElCard>
      <ElTable v-loading="loading" :data="tableData" style="width: 100%" row-key="id">
        <ElTableColumn prop="img" label="封面" width="120">
          <template #default="{ row }">
            <ElImage
              v-if="row.localImg"
              :src="getImageUrl(row.localImg)"
              :preview-src-list="[getImageUrl(row.localImg)]"
              style="width: 80px; height: 60px"
              fit="cover"
              preview-teleported
            />
            <span v-else class="text-gray-400">无封面</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="title" label="标题" min-width="200">
          <template #default="{ row }">
            <div class="font-medium">
              {{ row.title }}
              <!-- 有码/无码标识 -->
              <ElTag
                :type="row.id.startsWith('n') ? 'danger' : 'success'"
                size="small"
                class="ml-2"
              >
                {{ row.id.startsWith('n') ? '无码' : '有码' }}
              </ElTag>
            </div>
            <div class="text-sm text-gray-500 mt-1">{{ row.id }}</div>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="date" label="发布日期" width="120">
          <template #default="{ row }">
            {{ row.date || '-' }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="videoLength" label="时长" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.videoLength) }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="stars" label="演员" min-width="150">
          <template #default="{ row }">
            <ElSpace wrap>
              <ElTag v-for="star in row.stars.slice(0, 3)" :key="star.id" size="small" type="info">
                {{ star.name }}
              </ElTag>
              <ElTag v-if="row.stars.length > 3" size="small" type="info">
                +{{ row.stars.length - 3 }}
              </ElTag>
            </ElSpace>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="genres" label="分类" min-width="120">
          <template #default="{ row }">
            <ElSpace wrap>
              <ElTag v-for="genre in row.genres.slice(0, 2)" :key="genre.id" size="small">
                {{ genre.name }}
              </ElTag>
              <ElTag v-if="row.genres.length > 2" size="small">
                +{{ row.genres.length - 2 }}
              </ElTag>
            </ElSpace>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="magnets" label="磁力链接" width="140">
          <template #default="{ row }">
            <div class="text-sm space-y-1">
              <div class="flex items-center justify-between">
                <span>总数:</span>
                <ElTag size="small">{{ row.magnets.total }}</ElTag>
              </div>
              <div v-if="row.magnets.hd > 0" class="flex items-center justify-between">
                <span>高清:</span>
                <ElTag type="primary" size="small">{{ row.magnets.hd }}</ElTag>
              </div>
              <div v-if="row.magnets.subtitle > 0" class="flex items-center justify-between">
                <span>字幕:</span>
                <ElTag type="success" size="small">{{ row.magnets.subtitle }}</ElTag>
              </div>
            </div>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="stats" :label="getStatsColumnTitle" width="150">
          <template #default="{ row }">
            <div class="text-sm space-y-1">
              <!-- 基础统计 -->
              <div class="flex items-center justify-between">
                <span>总观看:</span>
                <ElTag size="small">{{ row.stats.views }}</ElTag>
              </div>

              <!-- 人气统计 -->
              <div
                v-if="queryParams.popularityType === 'daily' && row.stats.dailyViews !== undefined"
                class="flex items-center justify-between"
              >
                <span>日观看:</span>
                <ElTag type="warning" size="small">{{ row.stats.dailyViews }}</ElTag>
              </div>
              <div
                v-if="
                  queryParams.popularityType === 'weekly' && row.stats.weeklyViews !== undefined
                "
                class="flex items-center justify-between"
              >
                <span>周观看:</span>
                <ElTag type="warning" size="small">{{ row.stats.weeklyViews }}</ElTag>
              </div>
              <div
                v-if="
                  queryParams.popularityType === 'monthly' && row.stats.monthlyViews !== undefined
                "
                class="flex items-center justify-between"
              >
                <span>月观看:</span>
                <ElTag type="warning" size="small">{{ row.stats.monthlyViews }}</ElTag>
              </div>

              <!-- 其他统计 -->
              <div class="flex items-center justify-between">
                <span>点赞:</span>
                <ElTag type="danger" size="small">{{ row.stats.likes }}</ElTag>
              </div>
              <div class="flex items-center justify-between">
                <span>收藏:</span>
                <ElTag type="success" size="small">{{ row.stats.favorites }}</ElTag>
              </div>
            </div>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="updatedAt" label="更新时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="createdAt" label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </ElTableColumn>

        <ElTableColumn label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <ElSpace>
              <ElButton size="small" type="primary" link @click="handleEdit(row)"> 编辑 </ElButton>
              <ElButton size="small" type="danger" link @click="handleDelete(row.id)">
                删除
              </ElButton>
            </ElSpace>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="flex justify-center mt-20px">
        <ElPagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </ElCard>

    <!-- 编辑对话框 -->
    <ElDialog
      v-model="editDialogVisible"
      title="编辑影片"
      width="900px"
      :close-on-click-modal="false"
    >
      <ElTabs>
        <!-- 基本信息 -->
        <ElTabPane label="基本信息" name="basic">
          <ElForm :model="editForm" label-width="120px">
            <ElFormItem label="影片标题">
              <ElInput v-model="editForm.title" placeholder="请输入影片标题" />
            </ElFormItem>

            <ElFormItem label="发布日期">
              <ElDatePicker
                v-model="editForm.date"
                type="date"
                placeholder="选择发布日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </ElFormItem>

            <ElFormItem label="影片时长">
              <ElInputNumber
                v-model="editForm.videoLength"
                :min="0"
                :max="9999"
                placeholder="请输入影片时长(分钟)"
                style="width: 100%"
              />
              <span class="ml-2 text-gray-500">分钟</span>
            </ElFormItem>

            <ElFormItem label="影片描述">
              <ElInput
                v-model="editForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入影片描述"
              />
            </ElFormItem>

            <ElFormItem label="本地图片路径">
              <ElInput v-model="editForm.localImg" placeholder="请输入本地图片路径" />
              <div class="text-sm text-gray-500 mt-1">
                只支持本地图片路径，格式：/images/getav/cover/xxx.jpg
              </div>
            </ElFormItem>
          </ElForm>
        </ElTabPane>

        <!-- 制作信息 -->
        <ElTabPane label="制作信息" name="production">
          <ElForm :model="editForm" label-width="120px">
            <ElDivider content-position="left">导演信息</ElDivider>
            <ElFormItem label="导演ID">
              <ElInput v-model="editForm.director.id" placeholder="请输入导演ID" />
            </ElFormItem>
            <ElFormItem label="导演姓名">
              <ElInput v-model="editForm.director.name" placeholder="请输入导演姓名" />
            </ElFormItem>

            <ElDivider content-position="left">制作公司</ElDivider>
            <ElFormItem label="制作公司ID">
              <ElInput v-model="editForm.producer.id" placeholder="请输入制作公司ID" />
            </ElFormItem>
            <ElFormItem label="制作公司名称">
              <ElInput v-model="editForm.producer.name" placeholder="请输入制作公司名称" />
            </ElFormItem>

            <ElDivider content-position="left">发行商</ElDivider>
            <ElFormItem label="发行商ID">
              <ElInput v-model="editForm.publisher.id" placeholder="请输入发行商ID" />
            </ElFormItem>
            <ElFormItem label="发行商名称">
              <ElInput v-model="editForm.publisher.name" placeholder="请输入发行商名称" />
            </ElFormItem>

            <ElDivider content-position="left">系列信息</ElDivider>
            <ElFormItem label="系列ID">
              <ElInput v-model="editForm.series.id" placeholder="请输入系列ID" />
            </ElFormItem>
            <ElFormItem label="系列名称">
              <ElInput v-model="editForm.series.name" placeholder="请输入系列名称" />
            </ElFormItem>
          </ElForm>
        </ElTabPane>

        <!-- 演员信息 -->
        <ElTabPane label="演员信息" name="stars">
          <div class="mb-4">
            <ElButton type="primary" @click="addStar">添加演员</ElButton>
          </div>

          <div v-for="(star, index) in editForm.stars" :key="index" class="mb-4 p-4 border rounded">
            <div class="flex justify-between items-center mb-2">
              <span class="font-medium">演员 {{ index + 1 }}</span>
              <ElButton type="danger" size="small" @click="removeStar(index)">删除</ElButton>
            </div>

            <ElForm :model="star" label-width="120px">
              <ElFormItem label="演员ID">
                <ElInput v-model="star.id" placeholder="请输入演员ID" />
              </ElFormItem>
              <ElFormItem label="演员姓名">
                <ElInput v-model="star.name" placeholder="请输入演员姓名" />
              </ElFormItem>
              <ElFormItem label="头像URL">
                <ElInput v-model="star.avatar" placeholder="请输入头像URL" />
              </ElFormItem>
              <ElFormItem label="本地头像">
                <ElInput v-model="star.localAvatar" placeholder="请输入本地头像路径" />
              </ElFormItem>
            </ElForm>
          </div>

          <div v-if="editForm.stars.length === 0" class="text-center text-gray-500 py-8">
            暂无演员信息，点击"添加演员"按钮添加
          </div>
        </ElTabPane>

        <!-- 分类信息 -->
        <ElTabPane label="分类信息" name="genres">
          <ElFormItem label="影片分类">
            <div class="mb-2 text-sm text-gray-600"> 当前分类： </div>
            <ElSpace wrap>
              <ElTag
                v-for="(genre, index) in editForm.genres"
                :key="index"
                closable
                @close="removeGenre(index)"
                size="large"
              >
                {{ genre.name }}
              </ElTag>
            </ElSpace>

            <div v-if="editForm.genres.length === 0" class="text-gray-500 py-4"> 暂无分类信息 </div>

            <div class="mt-4">
              <ElInput
                v-model="newGenreName"
                placeholder="输入分类名称，按回车添加"
                @keyup.enter="addGenreByName"
                style="width: 300px"
              />
              <ElButton
                type="primary"
                @click="addGenreByName"
                :disabled="!newGenreName.trim()"
                class="ml-2"
              >
                添加分类
              </ElButton>
            </div>

            <div class="mt-4">
              <div class="text-sm text-gray-600 mb-2">常用分类：</div>
              <ElSpace wrap>
                <ElButton
                  v-for="commonGenre in commonGenres"
                  :key="commonGenre"
                  size="small"
                  @click="addCommonGenre(commonGenre)"
                  :disabled="editForm.genres.some((g) => g.name === commonGenre)"
                >
                  {{ commonGenre }}
                </ElButton>
              </ElSpace>
            </div>
          </ElFormItem>
        </ElTabPane>
      </ElTabs>

      <template #footer>
        <ElSpace>
          <ElButton @click="handleCancelEdit">取消</ElButton>
          <ElButton type="primary" :loading="editLoading" @click="handleSaveEdit"> 保存 </ElButton>
        </ElSpace>
      </template>
    </ElDialog>
  </ContentWrap>
</template>

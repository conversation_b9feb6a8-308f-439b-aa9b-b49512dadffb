/**
 * 用户行为日志相关类型定义
 */

// 行为类型枚举
export enum ActivityAction {
  VIEW = 'view',
  LIKE = 'like',
  DISLIKE = 'dislike',
  FAVORITE = 'favorite',
  UNFAVORITE = 'unfavorite',
  COMMENT = 'comment',
  RATING = 'rating',
  SEARCH = 'search',
  DOWNLOAD = 'download',
  SHARE = 'share',
  LOGIN = 'login',
  LOGOUT = 'logout',
  REGISTER = 'register',
  PROFILE_UPDATE = 'profile_update',
  PASSWORD_CHANGE = 'password_change',
  ADMIN_ACTION = 'admin_action',
  ERROR = 'error',
  OTHER = 'other'
}

// 目标类型枚举
export enum ActivityTargetType {
  MOVIE = 'movie',
  STAR = 'star',
  GENRE = 'genre',
  COMMENT = 'comment',
  MAGNET = 'magnet',
  USER = 'user',
  SYSTEM = 'system',
  OTHER = 'other'
}

// 用户信息接口
export interface ActivityUser {
  id: string
  username?: string
  name?: string
  email?: string
}

// 用户行为日志接口
export interface ActivityLog {
  id: string
  userId?: string
  action: ActivityAction
  targetType: ActivityTargetType
  targetId?: string
  ipAddress?: string
  userAgent?: string
  metadata?: Record<string, any>
  createdAt: string
  user?: ActivityUser
}

// 创建用户行为日志请求
export interface CreateActivityLogRequest {
  userId?: string
  action: ActivityAction
  targetType: ActivityTargetType
  targetId?: string
  ipAddress?: string
  userAgent?: string
  metadata?: Record<string, any>
}

// 查询参数接口
export interface ActivityLogQueryParams {
  page?: number
  limit?: number
  search?: string
  userId?: string
  action?: ActivityAction
  targetType?: ActivityTargetType
  targetId?: string
  ipAddress?: string
  dateFrom?: string
  dateTo?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应接口
export interface ActivityLogListResponse {
  items: ActivityLog[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 统计数据接口
export interface ActivityStats {
  totalLogs: number
  uniqueUsers: number
  topActions: Array<{
    action: ActivityAction
    count: number
    percentage: number
  }>
  topTargetTypes: Array<{
    targetType: ActivityTargetType
    count: number
    percentage: number
  }>
  dailyStats: Array<{
    date: string
    count: number
    uniqueUsers: number
  }>
  hourlyStats: Array<{
    hour: number
    count: number
  }>
  topUsers: Array<{
    user: ActivityUser
    count: number
  }>
  topIpAddresses: Array<{
    ipAddress: string
    count: number
    uniqueUsers: number
  }>
}

// 导出选项接口
export interface ExportOptions {
  format: 'csv' | 'json'
  dateFrom?: string
  dateTo?: string
  userId?: string
  action?: ActivityAction
  targetType?: ActivityTargetType
}

// 清理选项接口
export interface CleanupOptions {
  daysToKeep: number
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

// 行为类型选项
export const ACTIVITY_ACTION_OPTIONS = [
  { label: '查看', value: ActivityAction.VIEW },
  { label: '点赞', value: ActivityAction.LIKE },
  { label: '点踩', value: ActivityAction.DISLIKE },
  { label: '收藏', value: ActivityAction.FAVORITE },
  { label: '取消收藏', value: ActivityAction.UNFAVORITE },
  { label: '评论', value: ActivityAction.COMMENT },
  { label: '评分', value: ActivityAction.RATING },
  { label: '搜索', value: ActivityAction.SEARCH },
  { label: '下载', value: ActivityAction.DOWNLOAD },
  { label: '分享', value: ActivityAction.SHARE },
  { label: '登录', value: ActivityAction.LOGIN },
  { label: '登出', value: ActivityAction.LOGOUT },
  { label: '注册', value: ActivityAction.REGISTER },
  { label: '更新资料', value: ActivityAction.PROFILE_UPDATE },
  { label: '修改密码', value: ActivityAction.PASSWORD_CHANGE },
  { label: '管理操作', value: ActivityAction.ADMIN_ACTION },
  { label: '错误', value: ActivityAction.ERROR },
  { label: '其他', value: ActivityAction.OTHER }
]

// 目标类型选项
export const ACTIVITY_TARGET_TYPE_OPTIONS = [
  { label: '影片', value: ActivityTargetType.MOVIE },
  { label: '演员', value: ActivityTargetType.STAR },
  { label: '分类', value: ActivityTargetType.GENRE },
  { label: '评论', value: ActivityTargetType.COMMENT },
  { label: '磁力链接', value: ActivityTargetType.MAGNET },
  { label: '用户', value: ActivityTargetType.USER },
  { label: '系统', value: ActivityTargetType.SYSTEM },
  { label: '其他', value: ActivityTargetType.OTHER }
]

// 排序选项
export const SORT_OPTIONS = [
  { label: '创建时间', value: 'createdAt' },
  { label: '用户ID', value: 'userId' },
  { label: '行为类型', value: 'action' },
  { label: '目标类型', value: 'targetType' },
  { label: 'IP地址', value: 'ipAddress' }
]

// 时间范围选项
export const DATE_RANGE_OPTIONS = [
  { label: '今天', value: 1 },
  { label: '最近3天', value: 3 },
  { label: '最近7天', value: 7 },
  { label: '最近30天', value: 30 },
  { label: '最近90天', value: 90 }
]

import request from '@/axios'

// 演员相关类型定义
export interface Star {
  id: string
  name: string
  avatar?: string
  localAvatar?: string
  birthday?: string
  age?: number
  height?: string
  bust?: string
  waist?: string
  hip?: string
  birthplace?: string
  hobby?: string
  cupSize?: string
  measurements?: string
  description?: string
  createdAt: string
  updatedAt: string
  movieCount?: number
  recentMovies?: Array<{
    id: string
    title: string
    date: string
    img?: string
    localImg?: string
  }>
}

export interface StarQueryParams {
  page?: number
  limit?: number
  search?: string
  hasAvatar?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface StarListResponse {
  items: Star[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface StarStats {
  total: number
  withAvatars: number
  recentCount: number
  topStars: Array<{
    id: string
    name: string
    movieCount: number
  }>
}

// 获取演员列表
export const getStarsApi = (params: StarQueryParams): Promise<IResponse<StarListResponse>> => {
  return request.get({ url: '/stars', params })
}

// 搜索演员（自动完成）
export const searchStarsApi = (params: {
  q: string
  limit?: number
}): Promise<IResponse<Star[]>> => {
  return request.get({ url: '/stars/search', params })
}

// 获取演员详情
export const getStarByIdApi = (id: string): Promise<IResponse<Star>> => {
  return request.get({ url: `/stars/${id}` })
}

// 更新演员信息
export const updateStarApi = (id: string, data: Partial<Star>): Promise<IResponse<Star>> => {
  return request.put({ url: `/stars/${id}`, data })
}

// 删除演员
export const deleteStarApi = (id: string): Promise<IResponse> => {
  return request.delete({ url: `/stars/${id}` })
}

// 批量删除演员
export const deleteStarsApi = (ids: string[]): Promise<IResponse> => {
  return request.delete({ url: '/stars', data: { ids } })
}

// 获取演员统计信息
export const getStarStatsApi = (): Promise<IResponse<StarStats>> => {
  return request.get({ url: '/stars/stats' })
}

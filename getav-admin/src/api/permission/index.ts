import request from '@/axios'
import { UserPermissions, PermissionItem } from '@/api/login/types'

/**
 * 权限相关API
 */

/**
 * 获取用户权限信息
 */
export const getUserPermissionsApi = (): Promise<IResponse<UserPermissions>> => {
  return request.get({ url: '/auth/permissions' })
}

/**
 * 检查用户权限
 */
export const checkPermissionApi = (
  permission: string
): Promise<IResponse<{ permission: string; hasPermission: boolean }>> => {
  return request.post({ url: '/auth/check-permission', data: { permission } })
}

/**
 * 检查用户角色
 */
export const checkRoleApi = (
  role: string
): Promise<IResponse<{ role: string; hasRole: boolean }>> => {
  return request.post({ url: '/auth/check-role', data: { role } })
}

/**
 * 获取所有角色列表
 */
export const getRoleListApi = (): Promise<IResponse<any[]>> => {
  return request.get({ url: '/roles/all' })
}

/**
 * 获取角色列表（分页）
 */
export const getRolesApi = (params?: any): Promise<IResponse<any>> => {
  return request.get({ url: '/roles', params })
}

/**
 * 获取角色详情
 */
export const getRoleByIdApi = (id: string): Promise<IResponse<any>> => {
  return request.get({ url: `/roles/${id}` })
}

/**
 * 创建角色
 */
export const createRoleApi = (data: any): Promise<IResponse<any>> => {
  return request.post({ url: '/roles', data })
}

/**
 * 更新角色
 */
export const updateRoleApi = (id: string, data: any): Promise<IResponse<any>> => {
  return request.put({ url: `/roles/${id}`, data })
}

/**
 * 删除角色
 */
export const deleteRoleApi = (id: string): Promise<IResponse<any>> => {
  return request.delete({ url: `/roles/${id}` })
}

/**
 * 获取角色权限
 */
export const getRolePermissionsApi = (id: string): Promise<IResponse<PermissionItem[]>> => {
  return request.get({ url: `/roles/${id}/permissions` })
}

/**
 * 设置角色权限
 */
export const setRolePermissionsApi = (
  id: string,
  permissionIds: string[]
): Promise<IResponse<PermissionItem[]>> => {
  return request.put({ url: `/roles/${id}/permissions`, data: { permissionIds } })
}

/**
 * 获取所有权限列表
 */
export const getPermissionListApi = (): Promise<IResponse<PermissionItem[]>> => {
  return request.get({ url: '/permissions/all' })
}

/**
 * 获取权限列表（分页）
 */
export const getPermissionsApi = (params?: any): Promise<IResponse<any>> => {
  return request.get({ url: '/permissions', params })
}

/**
 * 获取权限树结构
 */
export const getPermissionTreeApi = (): Promise<IResponse<PermissionItem[]>> => {
  return request.get({ url: '/permissions/tree' })
}

/**
 * 获取权限资源列表
 */
export const getPermissionResourcesApi = (): Promise<IResponse<any[]>> => {
  return request.get({ url: '/permissions/resources' })
}

/**
 * 获取权限操作列表
 */
export const getPermissionActionsApi = (resource?: string): Promise<IResponse<any[]>> => {
  return request.get({ url: '/permissions/actions', params: { resource } })
}

/**
 * 获取权限详情
 */
export const getPermissionByIdApi = (id: string): Promise<IResponse<any>> => {
  return request.get({ url: `/permissions/${id}` })
}

/**
 * 创建权限
 */
export const createPermissionApi = (data: any): Promise<IResponse<any>> => {
  return request.post({ url: '/permissions', data })
}

/**
 * 更新权限
 */
export const updatePermissionApi = (id: string, data: any): Promise<IResponse<any>> => {
  return request.put({ url: `/permissions/${id}`, data })
}

/**
 * 删除权限
 */
export const deletePermissionApi = (id: string): Promise<IResponse<any>> => {
  return request.delete({ url: `/permissions/${id}` })
}

/**
 * 获取用户角色
 */
export const getUserRolesApi = (userId: string): Promise<IResponse<any[]>> => {
  return request.get({ url: `/users/${userId}/roles` })
}

/**
 * 获取用户权限
 */
export const getUserPermissionsByIdApi = (userId: string): Promise<IResponse<UserPermissions>> => {
  return request.get({ url: `/users/${userId}/permissions` })
}

/**
 * 设置用户角色
 */
export const setUserRolesApi = (
  userId: string,
  roleIds: string[],
  expiresAt?: string
): Promise<IResponse<any[]>> => {
  return request.put({ url: `/users/${userId}/roles`, data: { roleIds, expiresAt } })
}

/**
 * 添加用户角色
 */
export const addUserRoleApi = (
  userId: string,
  roleId: string,
  expiresAt?: string
): Promise<IResponse<any>> => {
  return request.post({ url: `/users/${userId}/roles`, data: { roleId, expiresAt } })
}

/**
 * 移除用户角色
 */
export const removeUserRoleApi = (userId: string, roleId: string): Promise<IResponse<any>> => {
  return request.delete({ url: `/users/${userId}/roles/${roleId}` })
}

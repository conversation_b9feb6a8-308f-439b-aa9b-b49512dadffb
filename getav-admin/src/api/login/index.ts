import request from '@/axios'
import type { UserType, UserLoginType } from './types'

interface RoleParams {
  roleName: string
}

export const loginApi = (
  data: UserLoginType
): Promise<IResponse<{ token: string; user: UserType }>> => {
  return request.post({ url: '/api/auth/login', data })
}

export const loginOutApi = (): Promise<IResponse> => {
  return request.post({ url: '/api/auth/logout' })
}

export const getUserListApi = ({ params }: AxiosConfig) => {
  return request.get<{
    code: string
    data: {
      list: UserType[]
      total: number
    }
  }>({ url: '/api/users', params })
}

export const getAdminRoleApi = (
  params: RoleParams
): Promise<IResponse<AppCustomRouteRecordRaw[]>> => {
  return request.get({ url: '/api/auth/roles', params })
}

export const getTestRoleApi = (params: RoleParams): Promise<IResponse<string[]>> => {
  return request.get({ url: '/api/auth/permissions', params })
}

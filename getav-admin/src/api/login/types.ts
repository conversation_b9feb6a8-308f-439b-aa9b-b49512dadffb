export interface UserLoginType {
  username: string
  password: string
}

export interface UserType {
  username: string
  password?: string
  role?: string // 保持向后兼容
  roleId?: string
  email?: string
  id?: string
  token?: string
  // 新增RBAC权限字段
  roles?: string[] // 用户角色列表
  permissions?: {
    menus: PermissionItem[]
    actions: PermissionItem[]
    data: PermissionItem[]
    api: PermissionItem[]
  }
  allPermissions?: PermissionItem[] // 所有权限的平铺列表
}

// 权限项接口
export interface PermissionItem {
  id: string
  name: string
  displayName: string
  description?: string
  resource: string
  action: string
  type: 'MENU' | 'ACTION' | 'DATA' | 'API'
  parentId?: string
}

// 用户权限信息接口
export interface UserPermissions {
  roles: string[]
  permissions: {
    menus: PermissionItem[]
    actions: PermissionItem[]
    data: PermissionItem[]
    api: PermissionItem[]
  }
  allPermissions: PermissionItem[]
}

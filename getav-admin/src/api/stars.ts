import request from '@/axios'

export interface Star {
  id: string
  name: string
  avatar?: string
  localAvatar?: string
  birthday?: string
  height?: string
  measurements?: string
  bust?: string
  waist?: string
  hip?: string
  cupSize?: string
  birthplace?: string
  movieCount?: number
  recentMovies?: Array<{
    id: string
    title: string
    date: string
  }>
  createdAt: string
  updatedAt: string
}

export interface StarQueryParams {
  page: number
  limit: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface StarListResponse {
  items: Star[]
  total: number
  page: number
  limit: number
}

// 获取演员列表
export const getStarsApi = (params: StarQueryParams) => {
  return request.get<StarListResponse>({
    url: '/api/stars',
    params
  })
}

// 获取演员详情
export const getStarApi = (id: string) => {
  return request.get<Star>({
    url: `/api/stars/${id}`
  })
}

// 删除演员
export const deleteStarApi = (id: string) => {
  return request.delete({
    url: `/api/stars/${id}`
  })
}

// 更新演员
export const updateStarApi = (id: string, data: Partial<Star>) => {
  return request.put<Star>({
    url: `/api/stars/${id}`,
    data
  })
}

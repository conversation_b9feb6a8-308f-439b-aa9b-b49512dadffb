import request from '@/axios'

// 影片相关类型定义
export interface Movie {
  id: string
  title: string
  img?: string
  localImg?: string
  date: string
  videoLength?: number
  description?: string
  createdAt: string
  updatedAt: string
  director?: {
    id: string
    name: string
  }
  producer?: {
    id: string
    name: string
  }
  publisher?: {
    id: string
    name: string
  }
  series?: {
    id: string
    name: string
  }
  stars: Array<{
    id: string
    name: string
    avatar?: string
    localAvatar?: string
  }>
  genres: Array<{
    id: string
    name: string
  }>
  magnets: {
    total: number
    hd: number
    subtitle: number
  }
  stats: {
    comments: number
    likes: number
    favorites: number
    views: number
    dailyViews?: number // 日观看数
    weeklyViews?: number // 周观看数
    monthlyViews?: number // 月观看数
  }
}

export interface MovieQueryParams {
  page?: number
  limit?: number
  search?: string
  genre?: string
  star?: string
  director?: string
  producer?: string
  dateFrom?: string
  dateTo?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'

  // 新增筛选参数
  censored?: 'censored' | 'uncensored' | 'all' // 有码/无码筛选
  hasSubtitle?: boolean // 字幕筛选
  durationMin?: number // 最小时长（分钟）
  durationMax?: number // 最大时长（分钟）
  releaseDateFrom?: string // 上市时间起始
  releaseDateTo?: string // 上市时间结束

  // 人气排序相关
  popularityType?: 'daily' | 'weekly' | 'monthly' | 'total' // 人气类型
}

export interface MovieListResponse {
  items: Movie[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface MovieStats {
  total: number
  withImages: number
  withMagnets: number
  withSubtitles: number
  recentCount: number
  popularMovies: Array<{
    id: string
    title: string
    viewCount: number
  }>
}

// 获取影片列表
export const getMoviesApi = (params: MovieQueryParams): Promise<IResponse<MovieListResponse>> => {
  return request.get({ url: '/movies', params })
}

// 获取影片详情
export const getMovieByIdApi = (id: string): Promise<IResponse<Movie>> => {
  return request.get({ url: `/movies/${id}` })
}

// 更新影片信息
export const updateMovieApi = (id: string, data: Partial<Movie>): Promise<IResponse<Movie>> => {
  return request.put({ url: `/movies/${id}`, data })
}

// 删除影片
export const deleteMovieApi = (id: string): Promise<IResponse> => {
  return request.delete({ url: `/movies/${id}` })
}

// 批量删除影片
export const deleteMoviesApi = (ids: string[]): Promise<IResponse> => {
  return request.delete({ url: '/movies', data: { ids } })
}

// 获取影片统计信息
export const getMovieStatsApi = (): Promise<IResponse<MovieStats>> => {
  return request.get({ url: '/movies/stats' })
}

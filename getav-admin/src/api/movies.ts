import request from '@/axios'

export interface Movie {
  id: string
  title: string
  img?: string
  localImg?: string
  date?: string
  videoLength?: number
  stars: Array<{
    id: string
    name: string
  }>
  genres: Array<{
    id: string
    name: string
  }>
  magnets: {
    total: number
    hd: number
    subtitle: number
  }
  stats: {
    views: number
    likes: number
    favorites: number
  }
  createdAt: string
  updatedAt: string
}

export interface MovieQueryParams {
  page: number
  limit: number
  search?: string
  genre?: string
  star?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'

  // 新增筛选参数
  censored?: 'censored' | 'uncensored' | 'all' // 有码/无码筛选
  hasSubtitle?: boolean // 字幕筛选
  durationMin?: number // 最小时长（分钟）
  durationMax?: number // 最大时长（分钟）
  releaseDateFrom?: string // 上市时间起始
  releaseDateTo?: string // 上市时间结束

  // 人气排序相关
  popularityType?: 'daily' | 'weekly' | 'monthly' | 'total' // 人气类型
}

export interface MovieListResponse {
  items: Movie[]
  total: number
  page: number
  limit: number
}

// 获取影片列表
export const getMoviesApi = (params: MovieQueryParams) => {
  return request.get<MovieListResponse>({
    url: '/api/movies',
    params
  })
}

// 获取影片详情
export const getMovieApi = (id: string) => {
  return request.get<Movie>({
    url: `/api/movies/${id}`
  })
}

// 删除影片
export const deleteMovieApi = (id: string) => {
  return request.delete({
    url: `/api/movies/${id}`
  })
}

// 更新影片
export const updateMovieApi = (id: string, data: Partial<Movie>) => {
  return request.put<Movie>({
    url: `/api/movies/${id}`,
    data
  })
}

import request from '@/axios'
import type {
  ActivityLog,
  ActivityLogQueryParams,
  ActivityLogListResponse,
  CreateActivityLogRequest,
  ActivityStats,
  ExportOptions,
  CleanupOptions,
  ApiResponse
} from '@/types/activity-logs'

/**
 * 用户行为日志API服务
 */

/**
 * 创建用户行为日志
 */
export const createActivityLogApi = (
  data: CreateActivityLogRequest
): Promise<ApiResponse<ActivityLog>> => {
  return request.post({ url: '/api/activity-logs', data })
}

/**
 * 获取用户行为日志列表
 */
export const getActivityLogsApi = (
  params: ActivityLogQueryParams
): Promise<ApiResponse<ActivityLogListResponse>> => {
  return request.get({ url: '/api/activity-logs', params })
}

/**
 * 获取单个用户行为日志详情
 */
export const getActivityLogByIdApi = (id: string): Promise<ApiResponse<ActivityLog>> => {
  return request.get({ url: `/api/activity-logs/${id}` })
}

/**
 * 获取用户行为统计数据
 */
export const getActivityStatsApi = (days: number = 7): Promise<ApiResponse<ActivityStats>> => {
  return request.get({ url: '/api/activity-logs/stats', params: { days } })
}

/**
 * 导出用户行为日志
 */
export const exportActivityLogsApi = (options: ExportOptions): Promise<Blob> => {
  return request.get({
    url: '/api/activity-logs/export',
    params: options,
    responseType: 'blob'
  })
}

/**
 * 清理过期的用户行为日志
 */
export const cleanupOldLogsApi = (
  options: CleanupOptions
): Promise<ApiResponse<{ deletedCount: number; daysToKeep: number }>> => {
  return request.post({ url: '/api/activity-logs/cleanup', data: options })
}

/**
 * 批量删除用户行为日志
 */
export const batchDeleteActivityLogsApi = (ids: string[]): Promise<ApiResponse> => {
  return request.delete({ url: '/api/activity-logs/batch', data: { ids } })
}

/**
 * 获取用户行为日志统计概览
 */
export const getActivityOverviewApi = (): Promise<
  ApiResponse<{
    totalLogs: number
    todayLogs: number
    weeklyLogs: number
    monthlyLogs: number
    uniqueUsers: number
    topActions: Array<{ action: string; count: number }>
  }>
> => {
  return request.get({ url: '/api/activity-logs/overview' })
}

/**
 * 获取实时活动数据
 */
export const getRealTimeActivityApi = (): Promise<
  ApiResponse<{
    recentLogs: ActivityLog[]
    activeUsers: number
    currentActions: Array<{ action: string; count: number }>
  }>
> => {
  return request.get({ url: '/api/activity-logs/realtime' })
}

/**
 * 获取用户行为趋势数据
 */
export const getActivityTrendsApi = (
  days: number = 30
): Promise<
  ApiResponse<{
    dailyTrends: Array<{
      date: string
      totalLogs: number
      uniqueUsers: number
      topAction: string
    }>
    hourlyTrends: Array<{
      hour: number
      count: number
    }>
  }>
> => {
  return request.get({ url: '/api/activity-logs/trends', params: { days } })
}

/**
 * 获取用户行为热力图数据
 */
export const getActivityHeatmapApi = (
  days: number = 7
): Promise<
  ApiResponse<{
    heatmapData: Array<{
      date: string
      hour: number
      count: number
    }>
  }>
> => {
  return request.get({ url: '/api/activity-logs/heatmap', params: { days } })
}

/**
 * 搜索用户行为日志
 */
export const searchActivityLogsApi = (
  query: string,
  filters?: Partial<ActivityLogQueryParams>
): Promise<ApiResponse<ActivityLogListResponse>> => {
  return request.get({
    url: '/api/activity-logs/search',
    params: {
      q: query,
      ...filters
    }
  })
}

/**
 * 获取用户行为分析报告
 */
export const getActivityReportApi = (options: {
  dateFrom: string
  dateTo: string
  userId?: string
  targetType?: string
}): Promise<
  ApiResponse<{
    summary: {
      totalLogs: number
      uniqueUsers: number
      avgLogsPerUser: number
      peakHour: number
      peakDay: string
    }
    actionBreakdown: Array<{ action: string; count: number; percentage: number }>
    targetBreakdown: Array<{ targetType: string; count: number; percentage: number }>
    userActivity: Array<{ userId: string; username: string; count: number }>
    timeDistribution: Array<{ hour: number; count: number }>
    recommendations: string[]
  }>
> => {
  return request.get({ url: '/api/activity-logs/report', params: options })
}

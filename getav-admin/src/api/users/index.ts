import request from '@/axios'

// 用户相关类型定义
export interface User {
  id: string
  username?: string
  email?: string
  name?: string
  image?: string
  emailVerified?: boolean
  createdAt: string
  updatedAt: string
}

export interface UserQueryParams {
  page?: number
  limit?: number
  search?: string
  hasEmail?: boolean
  verified?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface UserListResponse {
  items: User[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface UserStats {
  total: number
  verified: number
  withEmail: number
  recentCount: number
  activeUsers: number
  topCommenters: Array<{
    id: string
    username: string
    name: string
    count: number
  }>
  topLikers: Array<{
    id: string
    username: string
    name: string
    count: number
  }>
  topCollectors: Array<{
    id: string
    username: string
    name: string
    count: number
  }>
}

// 获取用户列表
export const getUsersApi = (params: UserQueryParams): Promise<IResponse<UserListResponse>> => {
  return request.get({ url: '/users', params })
}

// 搜索用户（自动完成）
export const searchUsersApi = (params: {
  q: string
  limit?: number
}): Promise<IResponse<User[]>> => {
  return request.get({ url: '/users/search', params })
}

// 获取用户详情
export const getUserByIdApi = (id: string): Promise<IResponse<User>> => {
  return request.get({ url: `/users/${id}` })
}

// 更新用户信息
export const updateUserApi = (id: string, data: Partial<User>): Promise<IResponse<User>> => {
  return request.put({ url: `/users/${id}`, data })
}

// 删除用户
export const deleteUserApi = (id: string): Promise<IResponse> => {
  return request.delete({ url: `/users/${id}` })
}

// 批量删除用户
export const deleteUsersApi = (ids: string[]): Promise<IResponse> => {
  return request.delete({ url: '/users', data: { ids } })
}

// 获取用户统计信息
export const getUserStatsApi = (): Promise<IResponse<UserStats>> => {
  return request.get({ url: '/users/stats' })
}

import request from '@/axios'

// 角色相关类型定义
export interface RoleItem {
  id: string
  name: string
  displayName: string
  description?: string
  isSystem: boolean
  isActive: boolean
  priority: number
  createdAt: string
  updatedAt: string
  stats?: {
    userCount: number
    permissionCount: number
  }
}

export interface RoleFormData {
  name: string
  displayName: string
  description?: string
  isActive?: boolean
  priority?: number
}

export interface RoleQueryParams {
  page?: number
  limit?: number
  search?: string
  isActive?: boolean
  isSystem?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 获取角色列表（分页）
export const getRoleListApi = (params?: RoleQueryParams) => {
  return request.get({ url: '/roles', params })
}

// 获取所有角色（用于下拉选择）
export const getAllRolesApi = () => {
  return request.get({ url: '/roles/all' })
}

// 获取角色详情
export const getRoleDetailApi = (id: string) => {
  return request.get({ url: `/roles/${id}` })
}

// 创建角色
export const createRoleApi = (data: RoleFormData) => {
  return request.post({ url: '/roles', data })
}

// 更新角色
export const updateRoleApi = (id: string, data: Partial<RoleFormData>) => {
  return request.put({ url: `/roles/${id}`, data })
}

// 删除角色
export const deleteRoleApi = (id: string) => {
  return request.delete({ url: `/roles/${id}` })
}

// 获取角色权限
export const getRolePermissionsApi = (id: string) => {
  return request.get({ url: `/roles/${id}/permissions` })
}

// 设置角色权限
export const setRolePermissionsApi = (id: string, permissionIds: string[]) => {
  return request.put({ url: `/roles/${id}/permissions`, data: { permissionIds } })
}

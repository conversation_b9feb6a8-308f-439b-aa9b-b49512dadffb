import request from '@/axios'

export interface User {
  id: string
  username?: string
  email?: string
  name?: string
  image?: string
  emailVerified?: boolean
  createdAt: string
  updatedAt: string
}

export interface UserQueryParams {
  page: number
  limit: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface UserListResponse {
  items: User[]
  total: number
  page: number
  limit: number
}

// 获取用户列表
export const getUsersApi = (params: UserQueryParams) => {
  return request.get<UserListResponse>({
    url: '/api/users',
    params
  })
}

// 获取用户详情
export const getUserApi = (id: string) => {
  return request.get<User>({
    url: `/api/users/${id}`
  })
}

// 删除用户
export const deleteUserApi = (id: string) => {
  return request.delete({
    url: `/api/users/${id}`
  })
}

// 更新用户
export const updateUserApi = (id: string, data: Partial<User>) => {
  return request.put<User>({
    url: `/api/users/${id}`,
    data
  })
}

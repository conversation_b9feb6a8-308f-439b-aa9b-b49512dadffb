import request from '@/axios'

// 统计数据相关类型定义
export interface OverviewStats {
  totalMovies: number
  totalStars: number
  totalUsers: number
  totalGenres: number
  recentMovies: number
  recentUsers: number
  popularMovies: Array<{
    id: string
    title: string
    viewCount: number
  }>
  topStars: Array<{
    id: string
    name: string
    movieCount: number
  }>
}

export interface ContentStats {
  movies: {
    withImages: number
    withMagnets: number
    withSubtitles: number
  }
  stars: {
    withAvatars: number
  }
  interactions: {
    magnets: number
    comments: number
    likes: number
    favorites: number
    views: number
  }
}

export interface TrendStats {
  period: string
  startDate: string
  endDate: string
  daily: Array<{
    date: string
    movies: number
    users: number
    comments: number
    views: number
  }>
}

export interface CategoryStats {
  genres: Array<{
    id: string
    name: string
    count: number
    percentage: number
  }>
  directors: Array<{
    id: string
    name: string
    count: number
  }>
  producers: Array<{
    id: string
    name: string
    count: number
  }>
}

export interface UserActivityStats {
  activeUsers: number
  topCommenters: Array<{
    id: string
    username: string
    name: string
    count: number
  }>
  topLikers: Array<{
    id: string
    username: string
    name: string
    count: number
  }>
  topCollectors: Array<{
    id: string
    username: string
    name: string
    count: number
  }>
}

export interface SystemHealth {
  database: {
    status: 'healthy' | 'warning' | 'error'
    connections: number
    responseTime: number
  }
  storage: {
    totalSpace: number
    usedSpace: number
    freeSpace: number
    usage: number
  }
  performance: {
    cpuUsage: number
    memoryUsage: number
    uptime: number
  }
}

export interface DashboardStats {
  overview: OverviewStats
  content: ContentStats
  trends: TrendStats
  userActivity: UserActivityStats
  timestamp: string
}

// 获取仪表板综合统计数据
export const getDashboardStatsApi = (): Promise<IResponse<DashboardStats>> => {
  return request.get({ url: '/api/stats/dashboard' })
}

// 获取系统总体统计数据
export const getOverviewStatsApi = (): Promise<IResponse<OverviewStats>> => {
  return request.get({ url: '/api/stats/overview' })
}

// 获取内容统计数据
export const getContentStatsApi = (): Promise<IResponse<ContentStats>> => {
  return request.get({ url: '/api/stats/content' })
}

// 获取时间趋势统计
export const getTrendStatsApi = (days: number = 30): Promise<IResponse<TrendStats>> => {
  return request.get({ url: '/api/stats/trends', params: { days } })
}

// 获取分类统计
export const getCategoryStatsApi = (): Promise<IResponse<CategoryStats>> => {
  return request.get({ url: '/api/stats/categories' })
}

// 获取用户活跃度统计
export const getUserActivityStatsApi = (): Promise<IResponse<UserActivityStats>> => {
  return request.get({ url: '/api/stats/user-activity' })
}

// 获取系统健康状态
export const getSystemHealthApi = (): Promise<IResponse<SystemHealth>> => {
  return request.get({ url: '/api/stats/health' })
}

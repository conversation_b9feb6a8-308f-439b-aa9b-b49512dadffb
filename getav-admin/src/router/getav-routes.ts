import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import type { App } from 'vue'
import { Layout, getParentLayout } from '@/utils/routerHelper'
import { useI18n } from '@/hooks/web/useI18n'
import { NO_RESET_WHITE_LIST } from '@/constants'

const { t } = useI18n()

export const constantRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard/analysis',
    name: 'Root',
    meta: {
      hidden: true
    }
  },
  {
    path: '/redirect',
    component: Layout,
    name: 'RedirectWrap',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: '登录',
      noTagsView: true
    }
  },
  {
    path: '/personal',
    component: Layout,
    redirect: '/personal/personal-center',
    name: 'Personal',
    meta: {
      title: '个人中心',
      hidden: true,
      canTo: true
    },
    children: [
      {
        path: 'personal-center',
        component: () => import('@/views/Personal/PersonalCenter/PersonalCenter.vue'),
        name: 'PersonalCenter',
        meta: {
          title: '个人中心',
          hidden: true,
          canTo: true
        }
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFind',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  }
]

export const asyncRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/analysis',
    name: 'Dashboard',
    meta: {
      title: '仪表板',
      icon: 'vi-ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'analysis',
        component: () => import('@/views/Dashboard/GetavAnalysis.vue'),
        name: 'Analysis',
        meta: {
          title: '数据分析',
          noCache: true,
          affix: true
        }
      }
    ]
  },
  {
    path: '/movies',
    component: Layout,
    redirect: '/movies/list',
    name: 'Movies',
    meta: {
      title: '影片管理',
      icon: 'vi-mdi:movie',
      alwaysShow: true
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/Movies/MovieList.vue'),
        name: 'MovieList',
        meta: {
          title: '影片列表',
          noCache: true
        }
      }
    ]
  },
  {
    path: '/stars',
    component: Layout,
    redirect: '/stars/list',
    name: 'Stars',
    meta: {
      title: '演员管理',
      icon: 'vi-mdi:account-star',
      alwaysShow: true
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/Stars/StarList.vue'),
        name: 'StarList',
        meta: {
          title: '演员列表',
          noCache: true
        }
      }
    ]
  },
  {
    path: '/users',
    component: Layout,
    redirect: '/users/list',
    name: 'Users',
    meta: {
      title: '用户管理',
      icon: 'vi-mdi:account-group',
      alwaysShow: true
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/Users/<USER>'),
        name: 'UserList',
        meta: {
          title: '用户列表',
          noCache: true
        }
      }
    ]
  },
  {
    path: '/activity-logs',
    component: Layout,
    redirect: '/activity-logs/list',
    name: 'ActivityLogs',
    meta: {
      title: '用户行为日志',
      icon: 'vi-mdi:chart-line',
      alwaysShow: true
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/ActivityLogs/ActivityLogList.vue'),
        name: 'ActivityLogList',
        meta: {
          title: '日志列表',
          noCache: true
        }
      },
      {
        path: 'stats',
        component: () => import('@/views/ActivityLogs/ActivityLogStats.vue'),
        name: 'ActivityLogStats',
        meta: {
          title: '统计分析',
          noCache: true
        }
      },
      {
        path: 'realtime',
        component: () => import('@/views/ActivityLogs/ActivityLogRealtime.vue'),
        name: 'ActivityLogRealtime',
        meta: {
          title: '实时监控',
          noCache: true
        }
      },
      {
        path: 'strategy',
        component: () => import('@/views/ActivityLogs/MonitoringStrategy.vue'),
        name: 'MonitoringStrategy',
        meta: {
          title: '监控策略说明',
          noCache: true
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/settings',
    name: 'System',
    meta: {
      title: '系统管理',
      icon: 'vi-mdi:cog',
      alwaysShow: true
    },
    children: [
      {
        path: 'settings',
        component: () => import('@/views/System/Settings.vue'),
        name: 'Settings',
        meta: {
          title: '系统设置',
          noCache: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  strict: true,
  routes: constantRouterMap as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  const resetWhiteNameList = ['Redirect', 'Login', 'NoFind', 'Root']
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (
      name &&
      !resetWhiteNameList.includes(name as string) &&
      !NO_RESET_WHITE_LIST.includes(name as string)
    ) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  app.use(router)
}

export default router

import { defineStore } from 'pinia'
import { store } from '../index'
import { UserLoginType, UserType, UserPermissions, PermissionItem } from '@/api/login/types'
import { ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { loginOutApi } from '@/api/login'
import { useTagsViewStore } from './tagsView'
import router from '@/router'

interface UserState {
  userInfo?: UserType
  tokenKey: string
  token: string
  roleRouters?: string[] | AppCustomRouteRecordRaw[]
  rememberMe: boolean
  loginInfo?: UserLoginType
  // 新增RBAC权限状态
  userPermissions?: UserPermissions
  roles: string[]
  permissions: {
    menus: PermissionItem[]
    actions: PermissionItem[]
    data: PermissionItem[]
    api: PermissionItem[]
  }
  allPermissions: PermissionItem[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => {
    return {
      userInfo: undefined,
      tokenKey: 'Authorization',
      token: '',
      roleRouters: undefined,
      // 记住我
      rememberMe: true,
      loginInfo: undefined,
      // 新增RBAC权限状态初始化
      userPermissions: undefined,
      roles: [],
      permissions: {
        menus: [],
        actions: [],
        data: [],
        api: []
      },
      allPermissions: []
    }
  },
  getters: {
    getTokenKey(): string {
      return this.tokenKey
    },
    getToken(): string {
      return this.token
    },
    getUserInfo(): UserType | undefined {
      return this.userInfo
    },
    getRoleRouters(): string[] | AppCustomRouteRecordRaw[] | undefined {
      return this.roleRouters
    },
    getRememberMe(): boolean {
      return this.rememberMe
    },
    getLoginInfo(): UserLoginType | undefined {
      return this.loginInfo
    },
    // 新增RBAC权限getters
    getUserPermissions(): UserPermissions | undefined {
      return this.userPermissions
    },
    getRoles(): string[] {
      return this.roles
    },
    getPermissions() {
      return this.permissions
    },
    getAllPermissions(): PermissionItem[] {
      return this.allPermissions
    },
    // 权限检查方法
    hasRole:
      (state) =>
      (roleName: string): boolean => {
        return state.roles.includes(roleName)
      },
    hasPermission:
      (state) =>
      (permissionName: string): boolean => {
        return state.allPermissions.some((permission) => permission.name === permissionName)
      },
    hasAnyRole:
      (state) =>
      (roleNames: string[]): boolean => {
        return roleNames.some((roleName) => state.roles.includes(roleName))
      },
    hasAnyPermission:
      (state) =>
      (permissionNames: string[]): boolean => {
        return permissionNames.some((permissionName) =>
          state.allPermissions.some((permission) => permission.name === permissionName)
        )
      },
    hasAllPermissions:
      (state) =>
      (permissionNames: string[]): boolean => {
        return permissionNames.every((permissionName) =>
          state.allPermissions.some((permission) => permission.name === permissionName)
        )
      }
  },
  actions: {
    setTokenKey(tokenKey: string) {
      this.tokenKey = tokenKey
    },
    setToken(token: string) {
      this.token = token
    },
    setUserInfo(userInfo?: UserType) {
      this.userInfo = userInfo
      // 如果用户信息包含权限数据，同时更新权限状态
      if (userInfo?.roles) {
        this.setRoles(userInfo.roles)
      }
      if (userInfo?.permissions) {
        this.setPermissions(userInfo.permissions)
      }
      if (userInfo?.allPermissions) {
        this.setAllPermissions(userInfo.allPermissions)
      }
    },
    setRoleRouters(roleRouters: string[] | AppCustomRouteRecordRaw[]) {
      this.roleRouters = roleRouters
    },
    // 新增RBAC权限设置方法
    setUserPermissions(userPermissions: UserPermissions) {
      this.userPermissions = userPermissions
      this.roles = userPermissions.roles
      this.permissions = userPermissions.permissions
      this.allPermissions = userPermissions.allPermissions
    },
    setRoles(roles: string[]) {
      this.roles = roles
    },
    setPermissions(permissions: {
      menus: PermissionItem[]
      actions: PermissionItem[]
      data: PermissionItem[]
      api: PermissionItem[]
    }) {
      this.permissions = permissions
    },
    setAllPermissions(allPermissions: PermissionItem[]) {
      this.allPermissions = allPermissions
    },
    // 清除权限数据
    clearPermissions() {
      this.userPermissions = undefined
      this.roles = []
      this.permissions = {
        menus: [],
        actions: [],
        data: [],
        api: []
      }
      this.allPermissions = []
    },
    logoutConfirm() {
      const { t } = useI18n()
      ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
        .then(async () => {
          // 只有在有token的情况下才调用API
          if (this.token) {
            try {
              await loginOutApi()
            } catch (error) {
              console.warn('退出登录API调用失败，但仍然清除本地状态:', error)
            }
          }
          // 无论API调用是否成功，都清除本地状态
          this.reset()
        })
        .catch(() => {})
    },
    reset() {
      const tagsViewStore = useTagsViewStore()
      tagsViewStore.delAllViews()
      this.setToken('')
      this.setUserInfo(undefined)
      this.setRoleRouters([])
      this.clearPermissions() // 清除权限数据
      router.replace('/login')
    },
    async logout() {
      // 只有在有token的情况下才调用API
      if (this.token) {
        try {
          await loginOutApi()
        } catch (error) {
          console.warn('退出登录API调用失败，但仍然清除本地状态:', error)
        }
      }
      // 无论API调用是否成功，都清除本地状态
      this.reset()
    },
    setRememberMe(rememberMe: boolean) {
      this.rememberMe = rememberMe
    },
    setLoginInfo(loginInfo: UserLoginType | undefined) {
      this.loginInfo = loginInfo
    }
  },
  persist: {
    key: 'getav-user-store-v2'
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}

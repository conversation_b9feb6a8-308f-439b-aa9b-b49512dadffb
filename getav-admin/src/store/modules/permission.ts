import { defineStore } from 'pinia'
import { asyncRouterMap, constantRouterMap } from '@/router'
import {
  generateRoutesByFrontEnd,
  generateRoutesByServer,
  flatMultiLevelRoutes
} from '@/utils/routerHelper'
import { store } from '../index'
import { cloneDeep } from 'lodash-es'
import { PermissionItem } from '@/api/login/types'

export interface PermissionState {
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  isAddRouters: boolean
  menuTabRouters: AppRouteRecordRaw[]
  // 新增RBAC权限状态
  userPermissions: PermissionItem[]
  menuPermissions: PermissionItem[]
  actionPermissions: PermissionItem[]
  dataPermissions: PermissionItem[]
  apiPermissions: PermissionItem[]
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routers: [],
    addRouters: [],
    isAddRouters: false,
    menuTabRouters: [],
    // 新增RBAC权限状态初始化
    userPermissions: [],
    menuPermissions: [],
    actionPermissions: [],
    dataPermissions: [],
    apiPermissions: []
  }),
  getters: {
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getIsAddRouters(): boolean {
      return this.isAddRouters
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    },
    // 新增RBAC权限getters
    getUserPermissions(): PermissionItem[] {
      return this.userPermissions
    },
    getMenuPermissions(): PermissionItem[] {
      return this.menuPermissions
    },
    getActionPermissions(): PermissionItem[] {
      return this.actionPermissions
    },
    getDataPermissions(): PermissionItem[] {
      return this.dataPermissions
    },
    getApiPermissions(): PermissionItem[] {
      return this.apiPermissions
    },
    // 权限检查方法
    hasMenuPermission:
      (state) =>
      (menuName: string): boolean => {
        return state.menuPermissions.some((permission) => permission.name === menuName)
      },
    hasActionPermission:
      (state) =>
      (actionName: string): boolean => {
        return state.actionPermissions.some((permission) => permission.name === actionName)
      },
    hasDataPermission:
      (state) =>
      (dataName: string): boolean => {
        return state.dataPermissions.some((permission) => permission.name === dataName)
      },
    hasApiPermission:
      (state) =>
      (apiName: string): boolean => {
        return state.apiPermissions.some((permission) => permission.name === apiName)
      },
    hasPermission:
      (state) =>
      (permissionName: string): boolean => {
        return state.userPermissions.some((permission) => permission.name === permissionName)
      }
  },
  actions: {
    generateRoutes(
      type: 'server' | 'frontEnd' | 'static',
      routers?: AppCustomRouteRecordRaw[] | string[]
    ): Promise<unknown> {
      return new Promise<void>((resolve) => {
        let routerMap: AppRouteRecordRaw[] = []
        if (type === 'server') {
          // 模拟后端过滤菜单
          routerMap = generateRoutesByServer(routers as AppCustomRouteRecordRaw[])
        } else if (type === 'frontEnd') {
          // 模拟前端过滤菜单
          routerMap = generateRoutesByFrontEnd(cloneDeep(asyncRouterMap), routers as string[])
        } else {
          // 直接读取静态路由表
          routerMap = cloneDeep(asyncRouterMap)
        }
        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        // 渲染菜单的所有路由
        this.routers = cloneDeep(constantRouterMap).concat(routerMap)
        resolve()
      })
    },
    setIsAddRouters(state: boolean): void {
      this.isAddRouters = state
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    },
    // 新增RBAC权限设置方法
    setUserPermissions(permissions: PermissionItem[]): void {
      this.userPermissions = permissions
      // 按类型分组权限
      this.menuPermissions = permissions.filter((p) => p.type === 'MENU')
      this.actionPermissions = permissions.filter((p) => p.type === 'ACTION')
      this.dataPermissions = permissions.filter((p) => p.type === 'DATA')
      this.apiPermissions = permissions.filter((p) => p.type === 'API')
    },
    setMenuPermissions(permissions: PermissionItem[]): void {
      this.menuPermissions = permissions
    },
    setActionPermissions(permissions: PermissionItem[]): void {
      this.actionPermissions = permissions
    },
    setDataPermissions(permissions: PermissionItem[]): void {
      this.dataPermissions = permissions
    },
    setApiPermissions(permissions: PermissionItem[]): void {
      this.apiPermissions = permissions
    },
    // 清除权限数据
    clearPermissions(): void {
      this.userPermissions = []
      this.menuPermissions = []
      this.actionPermissions = []
      this.dataPermissions = []
      this.apiPermissions = []
    },
    // 基于用户权限生成路由
    generateRoutesByPermissions(permissions: PermissionItem[]): Promise<unknown> {
      return new Promise<void>((resolve) => {
        // 获取菜单权限
        const menuPermissions = permissions.filter((p) => p.type === 'MENU')
        const allowedMenus = menuPermissions.map((p) => p.name)

        // 基于权限过滤路由
        let routerMap: AppRouteRecordRaw[] = []
        if (allowedMenus.length > 0) {
          routerMap = generateRoutesByFrontEnd(cloneDeep(asyncRouterMap), allowedMenus)
        } else {
          // 如果没有菜单权限，使用空路由
          routerMap = []
        }

        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])

        // 渲染菜单的所有路由
        this.routers = cloneDeep(constantRouterMap).concat(routerMap)

        // 设置权限数据
        this.setUserPermissions(permissions)

        resolve()
      })
    }
  },
  persist: [
    {
      pick: ['routers'],
      storage: localStorage,
      key: 'getav-routers-v3'
    },
    {
      pick: ['addRouters'],
      storage: localStorage,
      key: 'getav-addRouters-v3'
    },
    {
      pick: ['menuTabRouters'],
      storage: localStorage,
      key: 'getav-menuTabRouters-v3'
    },
    {
      pick: [
        'userPermissions',
        'menuPermissions',
        'actionPermissions',
        'dataPermissions',
        'apiPermissions'
      ],
      storage: localStorage,
      key: 'getav-permissions-v3'
    }
  ]
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}

# 开发环境配置
NODE_ENV = 'development'

# 应用标题
VITE_APP_TITLE = 'GETAV.NET 管理后台'

# 应用基础路径
VITE_BASE_PATH = '/'

# API基础路径
VITE_API_BASE_PATH = '/api'

# 是否使用Mock数据
VITE_USE_MOCK = 'false'

# 是否删除console.log
VITE_DROP_CONSOLE = 'false'

# 是否删除debugger
VITE_DROP_DEBUGGER = 'false'

# 是否开启包分析
VITE_USE_BUNDLE_ANALYZER = 'false'

# 是否开启gzip压缩
VITE_USE_GZIP = 'false'

# 是否开启CSS拆分
VITE_USE_CSS_SPLIT = 'true'

# 是否使用全部Element Plus样式
VITE_USE_ALL_ELEMENT_PLUS_STYLE = 'false'

# 输出目录
VITE_OUT_DIR = 'dist'

# 是否生成sourcemap
VITE_SOURCEMAP = 'false'

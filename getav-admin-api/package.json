{"name": "getav-admin-api", "version": "1.0.0", "description": "GETAV.NET后台管理API服务", "main": "dist/app.js", "type": "module", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "keywords": ["getav", "admin", "api", "express", "typescript"], "author": "GETAV.NET", "license": "MIT", "dependencies": {"@prisma/client": "^5.22.0", "express": "^4.19.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.13.3", "compression": "^1.7.4", "express-rate-limit": "^7.4.1", "dotenv": "^16.4.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.7", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/node": "^22.10.5", "typescript": "^5.7.3", "tsx": "^4.19.2", "eslint": "^9.17.0", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "prisma": "^5.22.0"}, "engines": {"node": ">=18.0.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}}
#!/usr/bin/env node

/**
 * 清空数据库中除账户相关表外的所有数据
 * 
 * 保留的表（账户相关）：
 * - users, accounts, sessions, verificationtokens, user_passwords
 * - roles, permissions, user_roles, role_permissions
 * 
 * 清空的表（内容数据）：
 * - movies, stars, genres, directors, producers, publishers, series
 * - movie_stars, movie_genres, similar_movies, related_movies
 * - comments, likes, favorites, ratings, views, reports
 * - magnets, samples, user_activity_logs
 */

import { PrismaClient } from '@prisma/client';
import { config } from '../src/utils/config.js';

const prisma = new PrismaClient();

/**
 * 清理非账户数据的主函数
 */
async function clearNonAccountData(): Promise<void> {
  console.log('🚀 开始清理数据库中的非账户数据...');
  console.log('⚠️  注意：此操作将清空所有内容数据，但保留用户账户和权限数据');
  
  // 环境安全检查
  if (config.NODE_ENV === 'production') {
    console.error('❌ 生产环境不允许执行此操作！');
    console.error('   如需在生产环境执行，请修改脚本中的环境检查逻辑');
    process.exit(1);
  }

  // 显示将要保留的表
  console.log('\n📋 将保留以下账户相关表的数据：');
  const preservedTables = [
    'users', 'accounts', 'sessions', 'verificationtokens', 'user_passwords',
    'roles', 'permissions', 'user_roles', 'role_permissions'
  ];
  preservedTables.forEach(table => console.log(`   ✅ ${table}`));

  // 显示将要清空的表
  console.log('\n🗑️  将清空以下表的数据：');
  const tablesToClear = [
    'user_activity_logs', 'comments', 'likes', 'favorites', 'ratings', 'views', 'reports',
    'magnets', 'samples', 'movie_genres', 'movie_stars', 'similar_movies', 'related_movies',
    'movies', 'stars', 'genres', 'directors', 'producers', 'publishers', 'series'
  ];
  tablesToClear.forEach(table => console.log(`   🗑️  ${table}`));

  // 等待用户确认（在非交互环境中可以通过环境变量跳过）
  if (!process.env.SKIP_CONFIRMATION) {
    console.log('\n⏳ 5秒后开始执行，按 Ctrl+C 取消...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  try {
    await executeCleanup();
    console.log('\n✅ 数据库清理完成！');
    console.log('📊 账户数据已保留，内容数据已清空');
  } catch (error) {
    console.error('\n❌ 数据库清理失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * 执行清理操作
 */
async function executeCleanup(): Promise<void> {
  console.log('\n🧹 开始执行清理操作...');
  
  // 记录清理前的统计信息
  await logPreCleanupStats();
  
  // 按依赖关系顺序清理数据
  await clearUserInteractionData();
  await clearMovieRelationData();
  await clearCoreContentData();
  
  // 记录清理后的统计信息
  await logPostCleanupStats();
}

/**
 * 记录清理前的统计信息
 */
async function logPreCleanupStats(): Promise<void> {
  console.log('\n📊 清理前数据统计：');
  
  try {
    const stats = await Promise.all([
      prisma.user.count(),
      prisma.movie.count(),
      prisma.star.count(),
      prisma.comment.count(),
      prisma.like.count(),
      prisma.favorite.count(),
    ]);
    
    console.log(`   👥 用户数量: ${stats[0]}`);
    console.log(`   🎬 影片数量: ${stats[1]}`);
    console.log(`   ⭐ 演员数量: ${stats[2]}`);
    console.log(`   💬 评论数量: ${stats[3]}`);
    console.log(`   👍 点赞数量: ${stats[4]}`);
    console.log(`   ❤️  收藏数量: ${stats[5]}`);
  } catch (error) {
    console.warn('⚠️  获取统计信息失败:', error);
  }
}

/**
 * 清理用户交互数据
 */
async function clearUserInteractionData(): Promise<void> {
  console.log('\n🔄 清理用户交互数据...');
  
  const operations = [
    { name: 'user_activity_logs', operation: () => prisma.userActivityLog.deleteMany() },
    { name: 'reports', operation: () => prisma.report.deleteMany() },
    { name: 'views', operation: () => prisma.view.deleteMany() },
    { name: 'ratings', operation: () => prisma.rating.deleteMany() },
    { name: 'favorites', operation: () => prisma.favorite.deleteMany() },
    { name: 'likes', operation: () => prisma.like.deleteMany() },
    { name: 'comments', operation: () => prisma.comment.deleteMany() },
  ];

  for (const { name, operation } of operations) {
    try {
      const result = await operation();
      console.log(`   ✅ ${name}: 删除 ${result.count} 条记录`);
    } catch (error) {
      console.error(`   ❌ ${name}: 删除失败 -`, error);
      throw error;
    }
  }
}

/**
 * 清理影片关联数据
 */
async function clearMovieRelationData(): Promise<void> {
  console.log('\n🔄 清理影片关联数据...');
  
  const operations = [
    { name: 'samples', operation: () => prisma.sample.deleteMany() },
    { name: 'magnets', operation: () => prisma.magnet.deleteMany() },
    { name: 'similar_movies', operation: () => prisma.similarMovie.deleteMany() },
    { name: 'related_movies', operation: () => prisma.relatedMovie.deleteMany() },
    { name: 'movie_genres', operation: () => prisma.movieGenre.deleteMany() },
    { name: 'movie_stars', operation: () => prisma.movieStar.deleteMany() },
  ];

  for (const { name, operation } of operations) {
    try {
      const result = await operation();
      console.log(`   ✅ ${name}: 删除 ${result.count} 条记录`);
    } catch (error) {
      console.error(`   ❌ ${name}: 删除失败 -`, error);
      throw error;
    }
  }
}

/**
 * 清理核心内容数据
 */
async function clearCoreContentData(): Promise<void> {
  console.log('\n🔄 清理核心内容数据...');

  const operations = [
    { name: 'movies', operation: () => prisma.movie.deleteMany() },
    { name: 'stars', operation: () => prisma.star.deleteMany() },
    { name: 'genres', operation: () => prisma.genre.deleteMany() },
    { name: 'directors', operation: () => prisma.director.deleteMany() },
    { name: 'producers', operation: () => prisma.producer.deleteMany() },
    { name: 'publishers', operation: () => prisma.publisher.deleteMany() },
    { name: 'series', operation: () => prisma.series.deleteMany() },
  ];

  for (const { name, operation } of operations) {
    try {
      const result = await operation();
      console.log(`   ✅ ${name}: 删除 ${result.count} 条记录`);
    } catch (error) {
      console.error(`   ❌ ${name}: 删除失败 -`, error);
      throw error;
    }
  }
}

/**
 * 记录清理后的统计信息
 */
async function logPostCleanupStats(): Promise<void> {
  console.log('\n📊 清理后数据统计：');

  try {
    const stats = await Promise.all([
      prisma.user.count(),
      prisma.movie.count(),
      prisma.star.count(),
      prisma.comment.count(),
      prisma.like.count(),
      prisma.favorite.count(),
    ]);

    console.log(`   👥 用户数量: ${stats[0]} (保留)`);
    console.log(`   🎬 影片数量: ${stats[1]} (应为0)`);
    console.log(`   ⭐ 演员数量: ${stats[2]} (应为0)`);
    console.log(`   💬 评论数量: ${stats[3]} (应为0)`);
    console.log(`   👍 点赞数量: ${stats[4]} (应为0)`);
    console.log(`   ❤️  收藏数量: ${stats[5]} (应为0)`);

    // 验证清理结果
    const contentDataRemaining = stats[1] + stats[2] + stats[3] + stats[4] + stats[5];
    if (contentDataRemaining === 0) {
      console.log('\n✅ 验证通过：所有内容数据已清空');
    } else {
      console.warn(`\n⚠️  警告：仍有 ${contentDataRemaining} 条内容数据未清空`);
    }
  } catch (error) {
    console.warn('⚠️  获取统计信息失败:', error);
  }
}

/**
 * 主执行函数
 */
async function main(): Promise<void> {
  try {
    await clearNonAccountData();
    process.exit(0);
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

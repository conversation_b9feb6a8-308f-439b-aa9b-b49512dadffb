import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode, PaginatedResponse, UserQueryParams } from '@/types/index.js';
import { DatabaseService } from './database.service.js';

/**
 * 用户管理服务类
 * 处理用户相关的业务逻辑
 */
export class UsersService extends DatabaseService {
  /**
   * 获取用户列表（分页）
   */
  public async getUsers(params: UserQueryParams): Promise<PaginatedResponse<any>> {
    try {
      const { page, limit, skip, take } = this.createPaginationParams(params);
      const { search, hasEmail, verified, sortBy, sortOrder } = params;

      // 构建查询条件
      const where: any = {};

      // 搜索条件
      if (search) {
        where.OR = [
          { id: { contains: search, mode: 'insensitive' } },
          { username: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { name: { contains: search, mode: 'insensitive' } },
        ];
      }

      // 邮箱筛选
      if (hasEmail !== undefined) {
        if (hasEmail) {
          where.email = { not: null };
        } else {
          where.email = null;
        }
      }

      // 验证状态筛选
      if (verified !== undefined) {
        if (verified) {
          where.emailVerified = { not: null };
        } else {
          where.emailVerified = null;
        }
      }

      // 排序参数
      const orderBy = this.createSortParams(sortBy, sortOrder);

      // 查询数据
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take,
          orderBy,
          select: {
            id: true,
            username: true,
            email: true,
            name: true,
            image: true,
            emailVerified: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                comments: true,
                likes: true,
                favorites: true,
                ratings: true,
                views: true,
                reports: true,
              },
            },
          },
        }),
        prisma.user.count({ where }),
      ]);

      // 格式化数据
      const formattedUsers = users.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        image: user.image,
        emailVerified: user.emailVerified,
        isVerified: !!user.emailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        stats: {
          comments: user._count.comments,
          likes: user._count.likes,
          favorites: user._count.favorites,
          ratings: user._count.ratings,
          views: user._count.views,
          reports: user._count.reports,
        },
      }));

      return this.createPaginatedResponse(formattedUsers, total, page, limit);
    } catch (error) {
      this.handleDatabaseError(error, 'getUsers');
    }
  }

  /**
   * 获取用户详情
   */
  public async getUserById(id: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          username: true,
          email: true,
          name: true,
          image: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          comments: {
            take: 10,
            orderBy: { createdAt: 'desc' },
            include: {
              movie: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
          favorites: {
            take: 10,
            orderBy: { createdAt: 'desc' },
            include: {
              movie: {
                select: {
                  id: true,
                  title: true,
                  img: true,
                  localImg: true,
                },
              },
            },
          },
          ratings: {
            take: 10,
            orderBy: { createdAt: 'desc' },
            include: {
              movie: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
          _count: {
            select: {
              comments: true,
              likes: true,
              favorites: true,
              ratings: true,
              views: true,
              reports: true,
            },
          },
        },
      });

      if (!user) {
        throw new ApiError(
          '用户不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 格式化返回数据
      return {
        ...user,
        isVerified: !!user.emailVerified,
        stats: {
          comments: user._count.comments,
          likes: user._count.likes,
          favorites: user._count.favorites,
          ratings: user._count.ratings,
          views: user._count.views,
          reports: user._count.reports,
        },
        recentComments: user.comments,
        recentFavorites: user.favorites,
        recentRatings: user.ratings,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'getUserById');
    }
  }

  /**
   * 更新用户信息
   */
  public async updateUser(id: string, updateData: any) {
    try {
      // 检查用户是否存在
      await this.checkRecordExists(
        prisma.user,
        { id },
        '用户不存在'
      );

      // 检查用户名和邮箱唯一性
      if (updateData.username || updateData.email) {
        const existingUser = await prisma.user.findFirst({
          where: {
            AND: [
              { id: { not: id } },
              {
                OR: [
                  updateData.username ? { username: updateData.username } : {},
                  updateData.email ? { email: updateData.email } : {},
                ].filter(condition => Object.keys(condition).length > 0),
              },
            ],
          },
        });

        if (existingUser) {
          throw new ApiError(
            '用户名或邮箱已存在',
            ErrorCode.DUPLICATE_ERROR,
            409
          );
        }
      }

      const updatedUser = await prisma.user.update({
        where: { id },
        data: {
          username: updateData.username,
          email: updateData.email,
          name: updateData.name,
          image: updateData.image,
          emailVerified: updateData.emailVerified ? new Date() : null,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          username: true,
          email: true,
          name: true,
          image: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return {
        ...updatedUser,
        isVerified: !!updatedUser.emailVerified,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'updateUser');
    }
  }

  /**
   * 删除用户
   */
  public async deleteUser(id: string): Promise<void> {
    try {
      await this.safeDelete(
        prisma.user,
        { id },
        async () => {
          // 检查是否有关联数据
          const userStats = await prisma.user.findUnique({
            where: { id },
            select: {
              _count: {
                select: {
                  comments: true,
                  likes: true,
                  favorites: true,
                  ratings: true,
                  views: true,
                },
              },
            },
          });

          const totalInteractions = userStats?._count ? 
            Object.values(userStats._count).reduce((sum, count) => sum + count, 0) : 0;

          if (totalInteractions > 0) {
            throw new ApiError(
              `无法删除用户，存在 ${totalInteractions} 条交互数据`,
              ErrorCode.VALIDATION_ERROR,
              400
            );
          }

          return false; // 没有关联数据，可以删除
        }
      );
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'deleteUser');
    }
  }

  /**
   * 批量删除用户
   */
  public async deleteUsers(ids: string[]): Promise<{ deleted: number; errors: string[] }> {
    try {
      const errors: string[] = [];
      let deleted = 0;

      // 逐个检查和删除
      for (const id of ids) {
        try {
          await this.deleteUser(id);
          deleted++;
        } catch (error) {
          if (error instanceof ApiError) {
            errors.push(`${id}: ${error.message}`);
          } else {
            errors.push(`${id}: 删除失败`);
          }
        }
      }

      return { deleted, errors };
    } catch (error) {
      this.handleDatabaseError(error, 'deleteUsers');
    }
  }

  /**
   * 获取用户统计信息
   */
  public async getUserStats() {
    try {
      const [
        totalUsers,
        verifiedUsers,
        activeUsers,
        recentUsers,
        topUsers,
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({
          where: {
            emailVerified: { not: null },
          },
        }),
        prisma.user.count({
          where: {
            updatedAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天活跃
            },
          },
        }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天注册
            },
          },
        }),
        prisma.user.findMany({
          take: 10,
          orderBy: {
            comments: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            username: true,
            name: true,
            _count: {
              select: {
                comments: true,
                likes: true,
                favorites: true,
              },
            },
          },
        }),
      ]);

      return {
        total: totalUsers,
        verified: verifiedUsers,
        active: activeUsers,
        recent: recentUsers,
        top: topUsers.map(user => ({
          id: user.id,
          username: user.username,
          name: user.name,
          activityCount: user._count.comments + user._count.likes + user._count.favorites,
        })),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getUserStats');
    }
  }

  /**
   * 搜索用户（用于自动完成）
   */
  public async searchUsers(query: string, limit: number = 10) {
    try {
      const users = await prisma.user.findMany({
        where: {
          OR: [
            { username: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { name: { contains: query, mode: 'insensitive' } },
          ],
        },
        select: {
          id: true,
          username: true,
          email: true,
          name: true,
          image: true,
          emailVerified: true,
        },
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      });

      return users.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        image: user.image,
        isVerified: !!user.emailVerified,
      }));
    } catch (error) {
      this.handleDatabaseError(error, 'searchUsers');
    }
  }
}

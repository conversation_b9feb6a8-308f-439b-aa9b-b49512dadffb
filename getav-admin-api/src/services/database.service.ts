import { prisma } from '@/utils/database.js';
import { PrismaClient } from '@prisma/client';
import { ApiError, ErrorCode, PaginatedResponse, PaginationParams } from '@/types/index.js';

/**
 * 数据库服务基类
 * 提供通用的数据库操作方法
 */
export class DatabaseService {
  /**
   * 创建分页查询参数
   */
  protected createPaginationParams(params: PaginationParams) {
    const page = Math.max(1, params.page || 1);
    const limit = Math.min(Math.max(1, params.limit || 20), 100); // 限制最大100条
    const skip = (page - 1) * limit;

    return {
      page,
      limit,
      skip,
      take: limit,
    };
  }

  /**
   * 创建分页响应
   */
  protected createPaginatedResponse<T>(
    items: T[],
    total: number,
    page: number,
    limit: number
  ): PaginatedResponse<T> {
    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 创建排序参数
   */
  protected createSortParams(sortBy?: string, sortOrder?: 'asc' | 'desc') {
    if (!sortBy) {
      return { createdAt: 'desc' as const };
    }

    return {
      [sortBy]: sortOrder || 'desc',
    };
  }

  /**
   * 处理数据库错误
   */
  protected handleDatabaseError(error: any, operation: string): never {
    console.error(`数据库操作失败 [${operation}]:`, error);

    // Prisma特定错误处理
    if (error.code === 'P2002') {
      throw new ApiError(
        '数据已存在，违反唯一性约束',
        ErrorCode.DUPLICATE_ERROR,
        409,
        { operation, originalError: error }
      );
    }

    if (error.code === 'P2025') {
      throw new ApiError(
        '记录不存在',
        ErrorCode.NOT_FOUND,
        404,
        { operation, originalError: error }
      );
    }

    if (error.code === 'P2003') {
      throw new ApiError(
        '外键约束失败',
        ErrorCode.VALIDATION_ERROR,
        400,
        { operation, originalError: error }
      );
    }

    // 通用数据库错误
    throw new ApiError(
      `数据库操作失败: ${operation}`,
      ErrorCode.DATABASE_ERROR,
      500,
      { operation, originalError: error }
    );
  }

  /**
   * 执行事务
   */
  protected async executeTransaction<T>(
    operations: (prisma: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>) => Promise<T>
  ): Promise<T> {
    try {
      return await prisma.$transaction(operations);
    } catch (error) {
      this.handleDatabaseError(error, 'transaction');
    }
  }

  /**
   * 检查记录是否存在
   */
  protected async checkRecordExists(
    model: any,
    where: any,
    errorMessage: string = '记录不存在'
  ): Promise<void> {
    try {
      const record = await model.findUnique({ where });
      if (!record) {
        throw new ApiError(errorMessage, ErrorCode.NOT_FOUND, 404);
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'checkRecordExists');
    }
  }

  /**
   * 安全删除（检查关联关系）
   */
  protected async safeDelete(
    model: any,
    where: any,
    checkRelations?: () => Promise<boolean>
  ): Promise<void> {
    try {
      // 检查记录是否存在
      await this.checkRecordExists(model, where);

      // 检查关联关系
      if (checkRelations) {
        const hasRelations = await checkRelations();
        if (hasRelations) {
          throw new ApiError(
            '无法删除，存在关联数据',
            ErrorCode.VALIDATION_ERROR,
            400
          );
        }
      }

      // 执行删除
      await model.delete({ where });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'safeDelete');
    }
  }

  /**
   * 批量操作
   */
  protected async batchOperation<T>(
    operations: Array<() => Promise<T>>,
    batchSize: number = 10
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(operation => operation())
      );
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * 搜索查询构建器
   */
  protected buildSearchQuery(searchTerm?: string, searchFields: string[] = []) {
    if (!searchTerm || searchFields.length === 0) {
      return {};
    }

    return {
      OR: searchFields.map(field => ({
        [field]: {
          contains: searchTerm,
          mode: 'insensitive' as const,
        },
      })),
    };
  }

  /**
   * 日期范围查询构建器
   */
  protected buildDateRangeQuery(
    dateField: string,
    dateFrom?: string,
    dateTo?: string
  ) {
    const query: any = {};

    if (dateFrom || dateTo) {
      query[dateField] = {};
      
      if (dateFrom) {
        query[dateField].gte = new Date(dateFrom);
      }
      
      if (dateTo) {
        query[dateField].lte = new Date(dateTo);
      }
    }

    return query;
  }
}

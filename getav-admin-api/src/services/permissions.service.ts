import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode, PaginatedResponse, PermissionQueryParams, PermissionData } from '@/types/index.js';
import { DatabaseService } from './database.service.js';

/**
 * 权限管理服务类
 * 处理权限相关的业务逻辑
 */
export class PermissionsService extends DatabaseService {
  /**
   * 获取权限列表（分页）
   */
  public async getPermissions(params: PermissionQueryParams): Promise<PaginatedResponse<any>> {
    try {
      const { page, limit, skip, take } = this.createPaginationParams(params);
      const { search, type, resource, isActive, sortBy, sortOrder } = params;

      // 构建查询条件
      const where: any = {};

      // 搜索条件
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { displayName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { resource: { contains: search, mode: 'insensitive' } },
          { action: { contains: search, mode: 'insensitive' } },
        ];
      }

      // 权限类型筛选
      if (type) {
        where.type = type;
      }

      // 资源筛选
      if (resource) {
        where.resource = resource;
      }

      // 状态筛选
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // 排序参数
      const orderBy = this.createSortParams(sortBy || 'resource', sortOrder || 'asc');

      // 查询数据
      const [permissions, total] = await Promise.all([
        prisma.permission.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            parent: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
            children: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
            _count: {
              select: {
                children: true,
                rolePermissions: true,
              },
            },
          },
        }),
        prisma.permission.count({ where }),
      ]);

      // 格式化数据
      const formattedPermissions = permissions.map(permission => ({
        id: permission.id,
        name: permission.name,
        displayName: permission.displayName,
        description: permission.description,
        resource: permission.resource,
        action: permission.action,
        type: permission.type,
        parentId: permission.parentId,
        parent: permission.parent,
        isSystem: permission.isSystem,
        isActive: permission.isActive,
        createdAt: permission.createdAt,
        updatedAt: permission.updatedAt,
        stats: {
          childrenCount: permission._count.children,
          roleCount: permission._count.rolePermissions,
        },
        children: permission.children,
      }));

      return this.createPaginatedResponse(formattedPermissions, total, page, limit);
    } catch (error) {
      this.handleDatabaseError(error, 'getPermissions');
    }
  }

  /**
   * 获取权限详情
   */
  public async getPermissionById(id: string) {
    try {
      const permission = await prisma.permission.findUnique({
        where: { id },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              displayName: true,
              resource: true,
              action: true,
            },
          },
          children: {
            select: {
              id: true,
              name: true,
              displayName: true,
              resource: true,
              action: true,
              type: true,
            },
          },
          rolePermissions: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                },
              },
            },
          },
          _count: {
            select: {
              children: true,
              rolePermissions: true,
            },
          },
        },
      });

      if (!permission) {
        throw new ApiError(
          '权限不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 格式化返回数据
      return {
        id: permission.id,
        name: permission.name,
        displayName: permission.displayName,
        description: permission.description,
        resource: permission.resource,
        action: permission.action,
        type: permission.type,
        parentId: permission.parentId,
        parent: permission.parent,
        isSystem: permission.isSystem,
        isActive: permission.isActive,
        createdAt: permission.createdAt,
        updatedAt: permission.updatedAt,
        children: permission.children,
        roles: permission.rolePermissions.map(rp => ({
          id: rp.role.id,
          name: rp.role.name,
          displayName: rp.role.displayName,
          assignedAt: rp.assignedAt,
          isActive: rp.isActive,
        })),
        stats: {
          childrenCount: permission._count.children,
          roleCount: permission._count.rolePermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'getPermissionById');
    }
  }

  /**
   * 创建权限
   */
  public async createPermission(permissionData: PermissionData) {
    try {
      // 检查权限名称唯一性
      const existingPermission = await prisma.permission.findUnique({
        where: { name: permissionData.name },
      });

      if (existingPermission) {
        throw new ApiError(
          '权限名称已存在',
          ErrorCode.DUPLICATE_ERROR,
          409
        );
      }

      // 检查资源+操作唯一性
      const existingResourceAction = await prisma.permission.findUnique({
        where: {
          resource_action: {
            resource: permissionData.resource,
            action: permissionData.action,
          },
        },
      });

      if (existingResourceAction) {
        throw new ApiError(
          '该资源的操作权限已存在',
          ErrorCode.DUPLICATE_ERROR,
          409
        );
      }

      // 检查父权限是否存在
      if (permissionData.parentId) {
        const parentPermission = await prisma.permission.findUnique({
          where: { id: permissionData.parentId },
        });

        if (!parentPermission) {
          throw new ApiError(
            '父权限不存在',
            ErrorCode.VALIDATION_ERROR,
            400
          );
        }
      }

      const newPermission = await prisma.permission.create({
        data: {
          name: permissionData.name,
          displayName: permissionData.displayName,
          description: permissionData.description || null,
          resource: permissionData.resource,
          action: permissionData.action,
          type: permissionData.type || 'ACTION',
          parentId: permissionData.parentId || null,
          isActive: permissionData.isActive ?? true,
        },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              displayName: true,
            },
          },
          _count: {
            select: {
              children: true,
              rolePermissions: true,
            },
          },
        },
      });

      return {
        id: newPermission.id,
        name: newPermission.name,
        displayName: newPermission.displayName,
        description: newPermission.description,
        resource: newPermission.resource,
        action: newPermission.action,
        type: newPermission.type,
        parentId: newPermission.parentId,
        parent: newPermission.parent,
        isSystem: newPermission.isSystem,
        isActive: newPermission.isActive,
        createdAt: newPermission.createdAt,
        updatedAt: newPermission.updatedAt,
        stats: {
          childrenCount: newPermission._count.children,
          roleCount: newPermission._count.rolePermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'createPermission');
    }
  }

  /**
   * 更新权限
   */
  public async updatePermission(id: string, permissionData: Partial<PermissionData>) {
    try {
      // 检查权限是否存在
      const existingPermission = await prisma.permission.findUnique({
        where: { id },
      });

      if (!existingPermission) {
        throw new ApiError(
          '权限不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 检查是否为系统权限
      if (existingPermission.isSystem && (permissionData.name || permissionData.isActive === false)) {
        throw new ApiError(
          '系统权限不能修改名称或禁用',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 检查权限名称唯一性（如果要修改名称）
      if (permissionData.name && permissionData.name !== existingPermission.name) {
        const duplicatePermission = await prisma.permission.findUnique({
          where: { name: permissionData.name },
        });

        if (duplicatePermission) {
          throw new ApiError(
            '权限名称已存在',
            ErrorCode.DUPLICATE_ERROR,
            409
          );
        }
      }

      // 检查资源+操作唯一性（如果要修改）
      if ((permissionData.resource || permissionData.action) &&
          (permissionData.resource !== existingPermission.resource ||
           permissionData.action !== existingPermission.action)) {
        const resource = permissionData.resource || existingPermission.resource;
        const action = permissionData.action || existingPermission.action;

        const duplicateResourceAction = await prisma.permission.findUnique({
          where: {
            resource_action: { resource, action },
          },
        });

        if (duplicateResourceAction && duplicateResourceAction.id !== id) {
          throw new ApiError(
            '该资源的操作权限已存在',
            ErrorCode.DUPLICATE_ERROR,
            409
          );
        }
      }

      // 检查父权限是否存在
      if (permissionData.parentId) {
        const parentPermission = await prisma.permission.findUnique({
          where: { id: permissionData.parentId },
        });

        if (!parentPermission) {
          throw new ApiError(
            '父权限不存在',
            ErrorCode.VALIDATION_ERROR,
            400
          );
        }

        // 防止循环引用
        if (permissionData.parentId === id) {
          throw new ApiError(
            '不能将自己设为父权限',
            ErrorCode.VALIDATION_ERROR,
            400
          );
        }
      }

      const updateData: any = {
        updatedAt: new Date(),
      };

      if (permissionData.name !== undefined) updateData.name = permissionData.name;
      if (permissionData.displayName !== undefined) updateData.displayName = permissionData.displayName;
      if (permissionData.description !== undefined) updateData.description = permissionData.description || null;
      if (permissionData.resource !== undefined) updateData.resource = permissionData.resource;
      if (permissionData.action !== undefined) updateData.action = permissionData.action;
      if (permissionData.type !== undefined) updateData.type = permissionData.type;
      if (permissionData.parentId !== undefined) updateData.parentId = permissionData.parentId || null;
      if (permissionData.isActive !== undefined) updateData.isActive = permissionData.isActive;

      const updatedPermission = await prisma.permission.update({
        where: { id },
        data: updateData,
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              displayName: true,
            },
          },
          _count: {
            select: {
              children: true,
              rolePermissions: true,
            },
          },
        },
      });

      return {
        id: updatedPermission.id,
        name: updatedPermission.name,
        displayName: updatedPermission.displayName,
        description: updatedPermission.description,
        resource: updatedPermission.resource,
        action: updatedPermission.action,
        type: updatedPermission.type,
        parentId: updatedPermission.parentId,
        parent: updatedPermission.parent,
        isSystem: updatedPermission.isSystem,
        isActive: updatedPermission.isActive,
        createdAt: updatedPermission.createdAt,
        updatedAt: updatedPermission.updatedAt,
        stats: {
          childrenCount: updatedPermission._count.children,
          roleCount: updatedPermission._count.rolePermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'updatePermission');
    }
  }

  /**
   * 删除权限
   */
  public async deletePermission(id: string): Promise<void> {
    try {
      // 检查权限是否存在
      const existingPermission = await prisma.permission.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              children: true,
              rolePermissions: true,
            },
          },
        },
      });

      if (!existingPermission) {
        throw new ApiError(
          '权限不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 检查是否为系统权限
      if (existingPermission.isSystem) {
        throw new ApiError(
          '系统权限不能删除',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 检查是否有子权限
      if (existingPermission._count.children > 0) {
        throw new ApiError(
          `无法删除权限，还有 ${existingPermission._count.children} 个子权限`,
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 检查是否有角色使用该权限
      if (existingPermission._count.rolePermissions > 0) {
        throw new ApiError(
          `无法删除权限，还有 ${existingPermission._count.rolePermissions} 个角色使用该权限`,
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      await prisma.permission.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'deletePermission');
    }
  }

  /**
   * 获取权限树结构
   */
  public async getPermissionTree() {
    try {
      // 获取所有活跃权限
      const permissions = await prisma.permission.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          displayName: true,
          description: true,
          resource: true,
          action: true,
          type: true,
          parentId: true,
        },
        orderBy: [
          { resource: 'asc' },
          { type: 'asc' },
          { action: 'asc' },
        ],
      });

      // 构建树结构
      const permissionMap = new Map();
      const rootPermissions: any[] = [];

      // 第一遍：创建所有节点
      permissions.forEach(permission => {
        permissionMap.set(permission.id, {
          ...permission,
          children: [],
        });
      });

      // 第二遍：建立父子关系
      permissions.forEach(permission => {
        const node = permissionMap.get(permission.id);
        if (permission.parentId) {
          const parent = permissionMap.get(permission.parentId);
          if (parent) {
            parent.children.push(node);
          } else {
            // 父节点不存在，作为根节点
            rootPermissions.push(node);
          }
        } else {
          rootPermissions.push(node);
        }
      });

      return rootPermissions;
    } catch (error) {
      this.handleDatabaseError(error, 'getPermissionTree');
    }
  }

  /**
   * 获取权限资源列表
   */
  public async getPermissionResources() {
    try {
      const resources = await prisma.permission.groupBy({
        by: ['resource'],
        where: { isActive: true },
        _count: {
          resource: true,
        },
        orderBy: {
          resource: 'asc',
        },
      });

      return resources.map(item => ({
        resource: item.resource,
        count: item._count.resource,
      }));
    } catch (error) {
      this.handleDatabaseError(error, 'getPermissionResources');
    }
  }

  /**
   * 获取权限操作列表
   */
  public async getPermissionActions(resource?: string) {
    try {
      const where: any = { isActive: true };
      if (resource) {
        where.resource = resource;
      }

      const actions = await prisma.permission.groupBy({
        by: ['action'],
        where,
        _count: {
          action: true,
        },
        orderBy: {
          action: 'asc',
        },
      });

      return actions.map(item => ({
        action: item.action,
        count: item._count.action,
      }));
    } catch (error) {
      this.handleDatabaseError(error, 'getPermissionActions');
    }
  }

  /**
   * 获取所有权限（用于下拉选择）
   */
  public async getAllPermissions() {
    try {
      const permissions = await prisma.permission.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          displayName: true,
          description: true,
          resource: true,
          action: true,
          type: true,
        },
        orderBy: [
          { resource: 'asc' },
          { type: 'asc' },
          { action: 'asc' },
        ],
      });

      return permissions;
    } catch (error) {
      this.handleDatabaseError(error, 'getAllPermissions');
    }
  }
}

import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode, StatsData } from '@/types/index.js';
import { DatabaseService } from './database.service.js';

/**
 * 统计数据服务类
 * 处理系统统计数据的业务逻辑
 */
export class StatsService extends DatabaseService {
  /**
   * 获取系统总体统计数据
   */
  public async getOverallStats(): Promise<StatsData> {
    try {
      const [
        totalMovies,
        totalStars,
        totalUsers,
        totalGenres,
        recentMovies,
        recentUsers,
        popularMovies,
        topStars,
      ] = await Promise.all([
        // 基础统计
        prisma.movie.count(),
        prisma.star.count(),
        prisma.user.count(),
        prisma.genre.count(),
        
        // 最近7天新增
        prisma.movie.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            },
          },
        }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            },
          },
        }),
        
        // 热门影片（按观看次数）
        prisma.movie.findMany({
          take: 10,
          orderBy: {
            views: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            title: true,
            _count: {
              select: {
                views: true,
              },
            },
          },
        }),
        
        // 热门演员（按影片数量）
        prisma.star.findMany({
          take: 10,
          orderBy: {
            movies: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            name: true,
            _count: {
              select: {
                movies: true,
              },
            },
          },
        }),
      ]);

      return {
        totalMovies,
        totalStars,
        totalUsers,
        totalGenres,
        recentMovies,
        recentUsers,
        popularMovies: popularMovies.map(movie => ({
          id: movie.id,
          title: movie.title,
          viewCount: movie._count.views,
        })),
        topStars: topStars.map(star => ({
          id: star.id,
          name: star.name,
          movieCount: star._count.movies,
        })),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getOverallStats');
    }
  }

  /**
   * 获取内容统计数据
   */
  public async getContentStats() {
    try {
      const [
        moviesWithImages,
        moviesWithMagnets,
        moviesWithSubtitles,
        starsWithAvatars,
        totalMagnets,
        totalComments,
        totalLikes,
        totalFavorites,
        totalViews,
      ] = await Promise.all([
        prisma.movie.count({
          where: {
            OR: [
              { img: { not: null } },
              { localImg: { not: null } },
            ],
          },
        }),
        prisma.movie.count({
          where: {
            magnets: {
              some: {},
            },
          },
        }),
        prisma.movie.count({
          where: {
            magnets: {
              some: {
                hasSubtitle: true,
              },
            },
          },
        }),
        prisma.star.count({
          where: {
            OR: [
              { avatar: { not: null } },
              { localAvatar: { not: null } },
            ],
          },
        }),
        prisma.magnet.count(),
        prisma.comment.count(),
        prisma.like.count(),
        prisma.favorite.count(),
        prisma.view.count(),
      ]);

      return {
        movies: {
          withImages: moviesWithImages,
          withMagnets: moviesWithMagnets,
          withSubtitles: moviesWithSubtitles,
        },
        stars: {
          withAvatars: starsWithAvatars,
        },
        interactions: {
          magnets: totalMagnets,
          comments: totalComments,
          likes: totalLikes,
          favorites: totalFavorites,
          views: totalViews,
        },
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getContentStats');
    }
  }

  /**
   * 获取时间趋势统计
   */
  public async getTrendStats(days: number = 30) {
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

      // 按天统计
      const dailyStats = [];
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
        const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);

        const [movies, users, comments, views] = await Promise.all([
          prisma.movie.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          }),
          prisma.user.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          }),
          prisma.comment.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          }),
          prisma.view.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          }),
        ]);

        dailyStats.push({
          date: date.toISOString().split('T')[0],
          movies,
          users,
          comments,
          views,
        });
      }

      return {
        period: `${days}天`,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        daily: dailyStats,
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getTrendStats');
    }
  }

  /**
   * 获取分类统计
   */
  public async getCategoryStats() {
    try {
      const genreStats = await prisma.genre.findMany({
        select: {
          id: true,
          name: true,
          _count: {
            select: {
              movies: true,
            },
          },
        },
        orderBy: {
          movies: {
            _count: 'desc',
          },
        },
        take: 20,
      });

      return {
        genres: genreStats.map(genre => ({
          id: genre.id,
          name: genre.name,
          movieCount: genre._count.movies,
        })),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getCategoryStats');
    }
  }

  /**
   * 获取用户活跃度统计
   */
  public async getUserActivityStats() {
    try {
      const [
        activeUsers,
        topCommenters,
        topLikers,
        topCollectors,
      ] = await Promise.all([
        // 活跃用户（最近30天有活动）
        prisma.user.count({
          where: {
            OR: [
              {
                comments: {
                  some: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                    },
                  },
                },
              },
              {
                likes: {
                  some: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                    },
                  },
                },
              },
              {
                favorites: {
                  some: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                    },
                  },
                },
              },
            ],
          },
        }),
        
        // 评论最多的用户
        prisma.user.findMany({
          take: 10,
          orderBy: {
            comments: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            username: true,
            name: true,
            _count: {
              select: {
                comments: true,
              },
            },
          },
        }),
        
        // 点赞最多的用户
        prisma.user.findMany({
          take: 10,
          orderBy: {
            likes: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            username: true,
            name: true,
            _count: {
              select: {
                likes: true,
              },
            },
          },
        }),
        
        // 收藏最多的用户
        prisma.user.findMany({
          take: 10,
          orderBy: {
            favorites: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            username: true,
            name: true,
            _count: {
              select: {
                favorites: true,
              },
            },
          },
        }),
      ]);

      return {
        activeUsers,
        topCommenters: topCommenters.map(user => ({
          id: user.id,
          username: user.username,
          name: user.name,
          count: user._count.comments,
        })),
        topLikers: topLikers.map(user => ({
          id: user.id,
          username: user.username,
          name: user.name,
          count: user._count.likes,
        })),
        topCollectors: topCollectors.map(user => ({
          id: user.id,
          username: user.username,
          name: user.name,
          count: user._count.favorites,
        })),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getUserActivityStats');
    }
  }

  /**
   * 获取系统健康状态
   */
  public async getSystemHealth() {
    try {
      const [
        dbStats,
        recentErrors,
        performanceMetrics,
      ] = await Promise.all([
        // 数据库统计
        this.getOverallStats(),
        
        // 最近错误（这里可以从日志表获取，暂时返回空）
        Promise.resolve([]),
        
        // 性能指标
        Promise.resolve({
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
        }),
      ]);

      return {
        status: 'healthy',
        database: {
          connected: true,
          stats: dbStats,
        },
        performance: performanceMetrics,
        errors: recentErrors,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getSystemHealth');
    }
  }
}

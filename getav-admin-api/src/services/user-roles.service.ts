import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode, UserRoleData } from '@/types/index.js';
import { DatabaseService } from './database.service.js';

/**
 * 用户角色管理服务类
 * 处理用户角色分配相关的业务逻辑
 */
export class UserRolesService extends DatabaseService {
  /**
   * 获取用户角色
   */
  public async getUserRoles(userId: string) {
    try {
      const userRoles = await prisma.userRole.findMany({
        where: { 
          userId,
          isActive: true,
        },
        include: {
          role: {
            select: {
              id: true,
              name: true,
              displayName: true,
              description: true,
              priority: true,
            },
          },
        },
        orderBy: {
          role: {
            priority: 'desc',
          },
        },
      });

      return userRoles.map(ur => ({
        id: ur.id,
        userId: ur.userId,
        roleId: ur.roleId,
        assignedAt: ur.assignedAt,
        expiresAt: ur.expiresAt,
        isActive: ur.isActive,
        role: ur.role,
      }));
    } catch (error) {
      this.handleDatabaseError(error, 'getUserRoles');
    }
  }

  /**
   * 获取用户权限（通过角色）
   */
  public async getUserPermissions(userId: string) {
    try {
      const userRoles = await prisma.userRole.findMany({
        where: { 
          userId,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        include: {
          role: {
            include: {
              rolePermissions: {
                where: { isActive: true },
                include: {
                  permission: {
                    select: {
                      id: true,
                      name: true,
                      displayName: true,
                      description: true,
                      resource: true,
                      action: true,
                      type: true,
                      isActive: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      // 收集所有权限，去重
      const permissionsMap = new Map();
      const roles: string[] = [];

      userRoles.forEach(ur => {
        roles.push(ur.role.name);
        ur.role.rolePermissions.forEach((rp: any) => {
          // 只添加活跃的权限
          if (rp.permission.isActive && !permissionsMap.has(rp.permission.id)) {
            permissionsMap.set(rp.permission.id, rp.permission);
          }
        });
      });

      const permissions = Array.from(permissionsMap.values());

      // 按类型分组权限
      const groupedPermissions = {
        menus: permissions.filter(p => p.type === 'MENU'),
        actions: permissions.filter(p => p.type === 'ACTION'),
        data: permissions.filter(p => p.type === 'DATA'),
        api: permissions.filter(p => p.type === 'API'),
      };

      return {
        roles,
        permissions: groupedPermissions,
        allPermissions: permissions,
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getUserPermissions');
    }
  }

  /**
   * 设置用户角色
   */
  public async setUserRoles(data: UserRoleData, assignedBy?: string) {
    try {
      const { userId, roleIds, expiresAt } = data;

      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new ApiError(
          '用户不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 检查角色是否存在
      const roles = await prisma.role.findMany({
        where: {
          id: { in: roleIds },
          isActive: true,
        },
      });

      if (roles.length !== roleIds.length) {
        throw new ApiError(
          '部分角色不存在或已禁用',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 使用事务处理角色分配
      await prisma.$transaction(async (tx) => {
        // 删除现有角色分配
        await tx.userRole.deleteMany({
          where: { userId },
        });

        // 添加新的角色分配
        if (roleIds.length > 0) {
          await tx.userRole.createMany({
            data: roleIds.map(roleId => ({
              userId,
              roleId,
              assignedBy: assignedBy || null,
              expiresAt: expiresAt || null,
            })),
          });
        }
      });

      // 返回更新后的用户角色
      return this.getUserRoles(userId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'setUserRoles');
    }
  }

  /**
   * 添加用户角色
   */
  public async addUserRole(userId: string, roleId: string, assignedBy?: string, expiresAt?: Date) {
    try {
      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new ApiError(
          '用户不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 检查角色是否存在
      const role = await prisma.role.findUnique({
        where: { id: roleId },
      });

      if (!role || !role.isActive) {
        throw new ApiError(
          '角色不存在或已禁用',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 检查是否已有该角色
      const existingUserRole = await prisma.userRole.findUnique({
        where: {
          userId_roleId: {
            userId,
            roleId,
          },
        },
      });

      if (existingUserRole) {
        throw new ApiError(
          '用户已拥有该角色',
          ErrorCode.DUPLICATE_ERROR,
          409
        );
      }

      const userRole = await prisma.userRole.create({
        data: {
          userId,
          roleId,
          assignedBy: assignedBy || null,
          expiresAt: expiresAt || null,
        },
        include: {
          role: {
            select: {
              id: true,
              name: true,
              displayName: true,
              description: true,
              priority: true,
            },
          },
        },
      });

      return {
        id: userRole.id,
        userId: userRole.userId,
        roleId: userRole.roleId,
        assignedAt: userRole.assignedAt,
        expiresAt: userRole.expiresAt,
        isActive: userRole.isActive,
        role: userRole.role,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'addUserRole');
    }
  }

  /**
   * 移除用户角色
   */
  public async removeUserRole(userId: string, roleId: string): Promise<void> {
    try {
      const userRole = await prisma.userRole.findUnique({
        where: {
          userId_roleId: {
            userId,
            roleId,
          },
        },
      });

      if (!userRole) {
        throw new ApiError(
          '用户角色关系不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      await prisma.userRole.delete({
        where: {
          userId_roleId: {
            userId,
            roleId,
          },
        },
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'removeUserRole');
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  public async hasPermission(userId: string, permissionName: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return userPermissions.allPermissions.some(p => p.name === permissionName);
    } catch (error) {
      this.handleDatabaseError(error, 'hasPermission');
      return false;
    }
  }

  /**
   * 检查用户是否有指定角色
   */
  public async hasRole(userId: string, roleName: string): Promise<boolean> {
    try {
      const userRoles = await this.getUserRoles(userId);
      return userRoles.some(ur => ur.role.name === roleName);
    } catch (error) {
      this.handleDatabaseError(error, 'hasRole');
      return false;
    }
  }
}

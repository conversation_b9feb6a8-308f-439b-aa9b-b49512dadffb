import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode, PaginatedResponse, StarQueryParams } from '@/types/index.js';
import { DatabaseService } from './database.service.js';

/**
 * 演员管理服务类
 * 处理演员相关的业务逻辑
 */
export class StarsService extends DatabaseService {
  /**
   * 获取演员列表（分页）
   */
  public async getStars(params: StarQueryParams): Promise<PaginatedResponse<any>> {
    try {
      const { page, limit, skip, take } = this.createPaginationParams(params);
      const { search, hasAvatar, sortBy, sortOrder } = params;

      // 构建查询条件
      const where: any = {};

      // 搜索条件
      if (search) {
        where.OR = [
          { id: { contains: search, mode: 'insensitive' } },
          { name: { contains: search, mode: 'insensitive' } },
        ];
      }

      // 头像筛选
      if (hasAvatar !== undefined) {
        if (hasAvatar) {
          where.OR = [
            { avatar: { not: null } },
            { localAvatar: { not: null } },
          ];
        } else {
          where.AND = [
            { avatar: null },
            { localAvatar: null },
          ];
        }
      }

      // 排序参数
      const orderBy = this.createSortParams(sortBy, sortOrder);

      // 查询数据
      const [stars, total] = await Promise.all([
        prisma.star.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            movies: {
              include: {
                movie: {
                  select: {
                    id: true,
                    title: true,
                    date: true,
                    img: true,
                    localImg: true,
                  },
                },
              },
            },
            _count: {
              select: {
                movies: true,
              },
            },
          },
        }),
        prisma.star.count({ where }),
      ]);

      // 格式化数据
      const formattedStars = stars.map(star => ({
        id: star.id,
        name: star.name,
        avatar: star.avatar,
        localAvatar: star.localAvatar,
        birthday: star.birthday,
        age: star.age,
        height: star.height,
        bust: star.bust,
        waist: star.waist,
        hip: star.hip,
        birthplace: star.birthplace,
        hobby: star.hobby,
        cupSize: star.cupSize,
        measurements: star.measurements,
        description: star.description,
        createdAt: star.createdAt,
        updatedAt: star.updatedAt,
        movieCount: star._count.movies,
        recentMovies: star.movies
          .slice(0, 5)
          .map(ms => ms.movie),
      }));

      return this.createPaginatedResponse(formattedStars, total, page, limit);
    } catch (error) {
      this.handleDatabaseError(error, 'getStars');
    }
  }

  /**
   * 获取演员详情
   */
  public async getStarById(id: string) {
    try {
      const star = await prisma.star.findUnique({
        where: { id },
        include: {
          movies: {
            include: {
              movie: {
                include: {
                  director: true,
                  producer: true,
                  genres: {
                    include: {
                      genre: true,
                    },
                  },
                  _count: {
                    select: {
                      views: true,
                      likes: true,
                      favorites: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              movie: {
                date: 'desc',
              },
            },
          },
          _count: {
            select: {
              movies: true,
            },
          },
        },
      });

      if (!star) {
        throw new ApiError(
          '演员不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 格式化返回数据
      return {
        ...star,
        movieCount: star._count.movies,
        movies: star.movies.map(ms => ({
          ...ms.movie,
          genres: ms.movie.genres.map(mg => mg.genre),
          stats: {
            views: ms.movie._count.views,
            likes: ms.movie._count.likes,
            favorites: ms.movie._count.favorites,
          },
        })),
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'getStarById');
    }
  }

  /**
   * 更新演员信息
   */
  public async updateStar(id: string, updateData: any) {
    try {
      // 检查演员是否存在
      await this.checkRecordExists(
        prisma.star,
        { id },
        '演员不存在'
      );

      const updatedStar = await prisma.star.update({
        where: { id },
        data: {
          name: updateData.name,
          birthday: updateData.birthday,
          age: updateData.age,
          height: updateData.height,
          bust: updateData.bust,
          waist: updateData.waist,
          hip: updateData.hip,
          birthplace: updateData.birthplace,
          hobby: updateData.hobby,
          cupSize: updateData.cupSize,
          measurements: updateData.measurements,
          description: updateData.description,
          updatedAt: new Date(),
        },
        include: {
          _count: {
            select: {
              movies: true,
            },
          },
        },
      });

      return {
        ...updatedStar,
        movieCount: updatedStar._count.movies,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'updateStar');
    }
  }

  /**
   * 删除演员
   */
  public async deleteStar(id: string): Promise<void> {
    try {
      await this.safeDelete(
        prisma.star,
        { id },
        async () => {
          // 检查是否有关联的影片
          const movieCount = await prisma.movieStar.count({
            where: { starId: id },
          });

          if (movieCount > 0) {
            throw new ApiError(
              `无法删除演员，存在 ${movieCount} 部关联影片`,
              ErrorCode.VALIDATION_ERROR,
              400
            );
          }

          return false; // 没有关联数据，可以删除
        }
      );
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'deleteStar');
    }
  }

  /**
   * 批量删除演员
   */
  public async deleteStars(ids: string[]): Promise<{ deleted: number; errors: string[] }> {
    try {
      const errors: string[] = [];
      let deleted = 0;

      // 逐个检查和删除
      for (const id of ids) {
        try {
          await this.deleteStar(id);
          deleted++;
        } catch (error) {
          if (error instanceof ApiError) {
            errors.push(`${id}: ${error.message}`);
          } else {
            errors.push(`${id}: 删除失败`);
          }
        }
      }

      return { deleted, errors };
    } catch (error) {
      this.handleDatabaseError(error, 'deleteStars');
    }
  }

  /**
   * 获取演员统计信息
   */
  public async getStarStats() {
    try {
      const [
        totalStars,
        totalWithAvatar,
        totalWithInfo,
        recentStars,
        popularStars,
      ] = await Promise.all([
        prisma.star.count(),
        prisma.star.count({
          where: {
            OR: [
              { avatar: { not: null } },
              { localAvatar: { not: null } },
            ],
          },
        }),
        prisma.star.count({
          where: {
            OR: [
              { birthday: { not: null } },
              { height: { not: null } },
              { measurements: { not: null } },
            ],
          },
        }),
        prisma.star.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
            },
          },
        }),
        prisma.star.findMany({
          take: 10,
          orderBy: {
            movies: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            name: true,
            _count: {
              select: {
                movies: true,
              },
            },
          },
        }),
      ]);

      return {
        total: totalStars,
        withAvatar: totalWithAvatar,
        withInfo: totalWithInfo,
        recent: recentStars,
        popular: popularStars.map(star => ({
          id: star.id,
          name: star.name,
          movieCount: star._count.movies,
        })),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getStarStats');
    }
  }

  /**
   * 搜索演员（用于自动完成）
   */
  public async searchStars(query: string, limit: number = 10) {
    try {
      const stars = await prisma.star.findMany({
        where: {
          name: {
            contains: query,
            mode: 'insensitive',
          },
        },
        select: {
          id: true,
          name: true,
          avatar: true,
          localAvatar: true,
          _count: {
            select: {
              movies: true,
            },
          },
        },
        take: limit,
        orderBy: {
          movies: {
            _count: 'desc',
          },
        },
      });

      return stars.map(star => ({
        id: star.id,
        name: star.name,
        avatar: star.avatar || star.localAvatar,
        movieCount: star._count.movies,
      }));
    } catch (error) {
      this.handleDatabaseError(error, 'searchStars');
    }
  }
}

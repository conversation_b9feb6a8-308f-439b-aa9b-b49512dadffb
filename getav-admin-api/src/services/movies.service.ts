import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode, PaginatedResponse, MovieQueryParams } from '@/types/index.js';
import { DatabaseService } from './database.service.js';

/**
 * 影片管理服务类
 * 处理影片相关的业务逻辑
 */
export class MoviesService extends DatabaseService {
  /**
   * 获取影片列表（分页）
   */
  public async getMovies(params: MovieQueryParams): Promise<PaginatedResponse<any>> {
    try {
      const { page, limit, skip, take } = this.createPaginationParams(params);
      const {
        search, genre, star, director, producer, dateFrom, dateTo, sortBy, sortOrder,
        censored, hasSubtitle, durationMin, durationMax, releaseDateFrom, releaseDateTo, popularityType
      } = params;

      // 构建查询条件
      const where: any = {};

      // 搜索条件
      if (search) {
        where.OR = [
          { id: { contains: search, mode: 'insensitive' } },
          { title: { contains: search, mode: 'insensitive' } },
        ];
      }

      // 分类筛选
      if (genre) {
        where.genres = {
          some: {
            genre: {
              OR: [
                { id: genre },
                { name: { contains: genre, mode: 'insensitive' } },
              ],
            },
          },
        };
      }

      // 演员筛选
      if (star) {
        where.stars = {
          some: {
            star: {
              OR: [
                { id: star },
                { name: { contains: star, mode: 'insensitive' } },
              ],
            },
          },
        };
      }

      // 导演筛选
      if (director) {
        where.director = {
          OR: [
            { id: director },
            { name: { contains: director, mode: 'insensitive' } },
          ],
        };
      }

      // 制作商筛选
      if (producer) {
        where.producer = {
          OR: [
            { id: producer },
            { name: { contains: producer, mode: 'insensitive' } },
          ],
        };
      }

      // 日期范围筛选
      if (dateFrom || dateTo) {
        where.createdAt = {};
        if (dateFrom) where.createdAt.gte = new Date(dateFrom);
        if (dateTo) where.createdAt.lte = new Date(dateTo);
      }

      // 新增筛选条件

      // 有码/无码筛选
      if (censored && censored !== 'all') {
        if (censored === 'uncensored') {
          // 无码：ID以'n'开头
          where.id = { startsWith: 'n' };
        } else if (censored === 'censored') {
          // 有码：ID不以'n'开头
          where.id = { not: { startsWith: 'n' } };
        }
      }

      // 字幕筛选
      if (hasSubtitle !== undefined) {
        // 如果已经有magnets条件，需要合并
        if (where.magnets) {
          // 合并现有的magnets条件
          where.magnets = {
            some: {
              ...where.magnets.some,
              hasSubtitle: hasSubtitle
            }
          };
        } else {
          where.magnets = {
            some: {
              hasSubtitle: hasSubtitle
            }
          };
        }
      }

      // 时长范围筛选
      if (durationMin !== undefined || durationMax !== undefined) {
        where.videoLength = {};
        if (durationMin !== undefined) where.videoLength.gte = durationMin;
        if (durationMax !== undefined) where.videoLength.lte = durationMax;
      }

      // 上市时间范围筛选
      if (releaseDateFrom || releaseDateTo) {
        where.date = {};
        if (releaseDateFrom) where.date.gte = releaseDateFrom;
        if (releaseDateTo) where.date.lte = releaseDateTo;
      }

      // 排序参数 - 使用影片专用的排序方法
      let orderBy = this.createMovieSortParams(sortBy, sortOrder);

      // 如果指定了人气类型，优先使用人气排序
      if (popularityType && ['daily', 'weekly', 'monthly', 'total'].includes(popularityType)) {
        const popularityOrderBy = this.createPopularityOrderBy(popularityType);
        if (popularityOrderBy) {
          orderBy = popularityOrderBy;
        }
      }

      // 查询数据
      const [movies, total] = await Promise.all([
        prisma.movie.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            director: true,
            producer: true,
            publisher: true,
            series: true,
            stars: {
              include: {
                star: true,
              },
            },
            genres: {
              include: {
                genre: true,
              },
            },
            magnets: {
              select: {
                id: true,
                isHD: true,
                hasSubtitle: true,
                size: true,
              },
            },
            _count: {
              select: {
                comments: true,
                likes: true,
                favorites: true,
                views: true,
              },
            },
          },
        }),
        prisma.movie.count({ where }),
      ]);

      // 计算人气统计数据
      const movieIds = movies.map(movie => movie.id);
      const popularityStats = await this.calculatePopularityStats(movieIds, popularityType);

      // 格式化数据
      const formattedMovies = movies.map(movie => ({
        id: movie.id,
        title: movie.title,
        img: movie.img,
        localImg: movie.localImg,
        date: movie.date,
        videoLength: movie.videoLength,
        description: movie.description,
        createdAt: movie.createdAt,
        updatedAt: movie.updatedAt,
        director: movie.director,
        producer: movie.producer,
        publisher: movie.publisher,
        series: movie.series,
        stars: movie.stars.map(ms => ms.star),
        genres: movie.genres.map(mg => mg.genre),
        magnets: {
          total: movie.magnets.length,
          hd: movie.magnets.filter(m => m.isHD).length,
          subtitle: movie.magnets.filter(m => m.hasSubtitle).length,
        },
        stats: {
          comments: movie._count.comments,
          likes: movie._count.likes,
          favorites: movie._count.favorites,
          views: movie._count.views,
          // 添加人气统计数据
          dailyViews: popularityType === 'daily' ? (popularityStats as Map<string, number>).get(movie.id) || 0 : undefined,
          weeklyViews: popularityType === 'weekly' ? (popularityStats as Map<string, number>).get(movie.id) || 0 : undefined,
          monthlyViews: popularityType === 'monthly' ? (popularityStats as Map<string, number>).get(movie.id) || 0 : undefined,
        },
      }));

      // 对于时间范围的人气排序，需要在应用层进行排序
      if (popularityType && ['daily', 'weekly', 'monthly'].includes(popularityType)) {
        formattedMovies.sort((a, b) => {
          let aViews = 0;
          let bViews = 0;

          switch (popularityType) {
            case 'daily':
              aViews = a.stats.dailyViews || 0;
              bViews = b.stats.dailyViews || 0;
              break;
            case 'weekly':
              aViews = a.stats.weeklyViews || 0;
              bViews = b.stats.weeklyViews || 0;
              break;
            case 'monthly':
              aViews = a.stats.monthlyViews || 0;
              bViews = b.stats.monthlyViews || 0;
              break;
          }

          return sortOrder === 'asc' ? aViews - bViews : bViews - aViews;
        });
      }

      return this.createPaginatedResponse(formattedMovies, total, page, limit);
    } catch (error) {
      this.handleDatabaseError(error, 'getMovies');
    }
  }

  /**
   * 获取影片详情
   */
  public async getMovieById(id: string) {
    try {
      const movie = await prisma.movie.findUnique({
        where: { id },
        include: {
          director: true,
          producer: true,
          publisher: true,
          series: true,
          stars: {
            include: {
              star: true,
            },
          },
          genres: {
            include: {
              genre: true,
            },
          },
          magnets: {
            orderBy: { createdAt: 'desc' },
          },
          samples: true,
          relatedMovies: true,
          _count: {
            select: {
              comments: true,
              likes: true,
              favorites: true,
              views: true,
              ratings: true,
            },
          },
        },
      });

      if (!movie) {
        throw new ApiError(
          '影片不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 格式化返回数据
      return {
        ...movie,
        stars: movie.stars.map(ms => ms.star),
        genres: movie.genres.map(mg => mg.genre),
        stats: {
          comments: movie._count.comments,
          likes: movie._count.likes,
          favorites: movie._count.favorites,
          views: movie._count.views,
          ratings: movie._count.ratings,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'getMovieById');
    }
  }

  /**
   * 更新影片信息
   */
  public async updateMovie(id: string, updateData: any) {
    try {
      // 检查影片是否存在
      await this.checkRecordExists(
        prisma.movie,
        { id },
        '影片不存在'
      );

      const updatedMovie = await prisma.movie.update({
        where: { id },
        data: {
          title: updateData.title,
          description: updateData.description,
          videoLength: updateData.videoLength,
          updatedAt: new Date(),
        },
        include: {
          director: true,
          producer: true,
          publisher: true,
          series: true,
          stars: {
            include: {
              star: true,
            },
          },
          genres: {
            include: {
              genre: true,
            },
          },
        },
      });

      return {
        ...updatedMovie,
        stars: updatedMovie.stars.map(ms => ms.star),
        genres: updatedMovie.genres.map(mg => mg.genre),
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'updateMovie');
    }
  }

  /**
   * 删除影片
   */
  public async deleteMovie(id: string): Promise<void> {
    try {
      await this.safeDelete(
        prisma.movie,
        { id },
        async () => {
          // 检查是否有关联数据需要处理
          const relatedData = await prisma.movie.findUnique({
            where: { id },
            include: {
              _count: {
                select: {
                  comments: true,
                  likes: true,
                  favorites: true,
                  views: true,
                },
              },
            },
          });

          // 如果有用户交互数据，可能需要特殊处理
          return false; // 允许删除，Prisma会处理级联删除
        }
      );
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'deleteMovie');
    }
  }

  /**
   * 批量删除影片
   */
  public async deleteMovies(ids: string[]): Promise<{ deleted: number }> {
    try {
      const result = await prisma.movie.deleteMany({
        where: {
          id: {
            in: ids,
          },
        },
      });

      return { deleted: result.count };
    } catch (error) {
      this.handleDatabaseError(error, 'deleteMovies');
    }
  }

  /**
   * 创建影片专用的排序参数
   */
  private createMovieSortParams(sortBy?: string, sortOrder?: 'asc' | 'desc'): any {
    if (!sortBy) {
      return { createdAt: 'desc' as const };
    }

    const order = sortOrder || 'desc';

    switch (sortBy) {
      case 'title':
        return { title: order };
      case 'date':
      case 'releaseDate':
        return { date: order };
      case 'videoLength':
      case 'duration':
        return { videoLength: order };
      case 'createdAt':
        return { createdAt: order };
      case 'updatedAt':
        return { updatedAt: order };
      case 'views':
      case 'totalViews':
        return { views: { _count: order } };
      case 'likes':
        return { likes: { _count: order } };
      case 'favorites':
        return { favorites: { _count: order } };
      case 'comments':
        return { comments: { _count: order } };
      default:
        // 对于人气排序，使用专门的方法处理
        if (['daily', 'weekly', 'monthly', 'total'].includes(sortBy)) {
          return this.createPopularityOrderBy(sortBy);
        }
        // 默认按创建时间排序
        return { createdAt: 'desc' as const };
    }
  }

  /**
   * 创建人气排序的查询条件
   */
  private createPopularityOrderBy(popularityType?: string): any {
    if (!popularityType) {
      return undefined;
    }

    switch (popularityType) {
      case 'total':
        return { views: { _count: 'desc' } };
      case 'daily':
      case 'weekly':
      case 'monthly':
        // 对于时间范围的人气排序，我们需要在查询后进行排序
        // 这里先返回默认排序，后续在应用层排序
        return { views: { _count: 'desc' } };
      default:
        return undefined;
    }
  }

  /**
   * 计算影片的人气统计数据
   */
  private async calculatePopularityStats(movieIds: string[], popularityType?: string) {
    if (!popularityType || popularityType === 'total') {
      // 总人气：直接使用 _count
      return {};
    }

    const now = new Date();
    let startDate: Date;

    switch (popularityType) {
      case 'daily':
        // 今日人气：从今天00:00开始
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'weekly':
        // 本周人气：从本周一00:00开始
        const dayOfWeek = now.getDay();
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 周日为0，需要特殊处理
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - daysToMonday);
        break;
      case 'monthly':
        // 本月人气：从本月1日00:00开始
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        return {};
    }

    // 查询指定时间范围内的观看数据
    const viewsData = await prisma.view.groupBy({
      by: ['movieId'],
      where: {
        movieId: { in: movieIds },
        createdAt: {
          gte: startDate,
          lte: now,
        },
      },
      _count: { id: true },
    });

    // 转换为Map格式便于查找
    const viewsMap = new Map<string, number>();
    viewsData.forEach(item => {
      viewsMap.set(item.movieId, item._count.id);
    });

    return viewsMap;
  }

  /**
   * 获取影片统计信息
   */
  public async getMovieStats() {
    try {
      const [
        totalMovies,
        totalWithImages,
        totalWithMagnets,
        recentMovies,
        popularMovies,
      ] = await Promise.all([
        prisma.movie.count(),
        prisma.movie.count({
          where: {
            OR: [
              { img: { not: null } },
              { localImg: { not: null } },
            ],
          },
        }),
        prisma.movie.count({
          where: {
            magnets: {
              some: {},
            },
          },
        }),
        prisma.movie.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
            },
          },
        }),
        prisma.movie.findMany({
          take: 10,
          orderBy: {
            views: {
              _count: 'desc',
            },
          },
          select: {
            id: true,
            title: true,
            _count: {
              select: {
                views: true,
              },
            },
          },
        }),
      ]);

      return {
        total: totalMovies,
        withImages: totalWithImages,
        withMagnets: totalWithMagnets,
        recent: recentMovies,
        popular: popularMovies.map(movie => ({
          id: movie.id,
          title: movie.title,
          viewCount: movie._count.views,
        })),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getMovieStats');
    }
  }
}

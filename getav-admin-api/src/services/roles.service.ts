import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode, PaginatedResponse, RoleQueryParams, RoleData, RolePermissionData } from '@/types/index.js';
import { DatabaseService } from './database.service.js';

/**
 * 角色管理服务类
 * 处理角色相关的业务逻辑
 */
export class RolesService extends DatabaseService {
  /**
   * 获取角色列表（分页）
   */
  public async getRoles(params: RoleQueryParams): Promise<PaginatedResponse<any>> {
    try {
      const { page, limit, skip, take } = this.createPaginationParams(params);
      const { search, isActive, isSystem, sortBy, sortOrder } = params;

      // 构建查询条件
      const where: any = {};

      // 搜索条件
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { displayName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      // 状态筛选
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // 系统角色筛选
      if (isSystem !== undefined) {
        where.isSystem = isSystem;
      }

      // 排序参数
      const orderBy = this.createSortParams(sortBy || 'priority', sortOrder || 'desc');

      // 查询数据
      const [roles, total] = await Promise.all([
        prisma.role.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            _count: {
              select: {
                userRoles: true,
                rolePermissions: true,
              },
            },
          },
        }),
        prisma.role.count({ where }),
      ]);

      // 格式化数据
      const formattedRoles = roles.map(role => ({
        id: role.id,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        isSystem: role.isSystem,
        isActive: role.isActive,
        priority: role.priority,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
        stats: {
          userCount: role._count.userRoles,
          permissionCount: role._count.rolePermissions,
        },
      }));

      return this.createPaginatedResponse(formattedRoles, total, page, limit);
    } catch (error) {
      this.handleDatabaseError(error, 'getRoles');
    }
  }

  /**
   * 获取角色详情
   */
  public async getRoleById(id: string) {
    try {
      const role = await prisma.role.findUnique({
        where: { id },
        include: {
          userRoles: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  email: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          rolePermissions: {
            include: {
              permission: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  description: true,
                  resource: true,
                  action: true,
                  type: true,
                },
              },
            },
          },
          _count: {
            select: {
              userRoles: true,
              rolePermissions: true,
            },
          },
        },
      });

      if (!role) {
        throw new ApiError(
          '角色不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 格式化返回数据
      return {
        id: role.id,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        isSystem: role.isSystem,
        isActive: role.isActive,
        priority: role.priority,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
        users: role.userRoles.map(ur => ({
          id: ur.user.id,
          username: ur.user.username,
          email: ur.user.email,
          name: ur.user.name,
          image: ur.user.image,
          assignedAt: ur.assignedAt,
          expiresAt: ur.expiresAt,
          isActive: ur.isActive,
        })),
        permissions: role.rolePermissions.map(rp => ({
          id: rp.permission.id,
          name: rp.permission.name,
          displayName: rp.permission.displayName,
          description: rp.permission.description,
          resource: rp.permission.resource,
          action: rp.permission.action,
          type: rp.permission.type,
          assignedAt: rp.assignedAt,
          isActive: rp.isActive,
        })),
        stats: {
          userCount: role._count.userRoles,
          permissionCount: role._count.rolePermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'getRoleById');
    }
  }

  /**
   * 创建角色
   */
  public async createRole(roleData: RoleData, assignedBy?: string) {
    try {
      // 检查角色名称唯一性
      const existingRole = await prisma.role.findUnique({
        where: { name: roleData.name },
      });

      if (existingRole) {
        throw new ApiError(
          '角色名称已存在',
          ErrorCode.DUPLICATE_ERROR,
          409
        );
      }

      const newRole = await prisma.role.create({
        data: {
          name: roleData.name,
          displayName: roleData.displayName,
          description: roleData.description || null,
          isActive: roleData.isActive ?? true,
          priority: roleData.priority ?? 0,
        },
        include: {
          _count: {
            select: {
              userRoles: true,
              rolePermissions: true,
            },
          },
        },
      });

      return {
        id: newRole.id,
        name: newRole.name,
        displayName: newRole.displayName,
        description: newRole.description,
        isSystem: newRole.isSystem,
        isActive: newRole.isActive,
        priority: newRole.priority,
        createdAt: newRole.createdAt,
        updatedAt: newRole.updatedAt,
        stats: {
          userCount: newRole._count.userRoles,
          permissionCount: newRole._count.rolePermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'createRole');
    }
  }

  /**
   * 更新角色
   */
  public async updateRole(id: string, roleData: Partial<RoleData>) {
    try {
      // 检查角色是否存在
      const existingRole = await prisma.role.findUnique({
        where: { id },
      });

      if (!existingRole) {
        throw new ApiError(
          '角色不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 检查是否为系统角色
      if (existingRole.isSystem && (roleData.name || roleData.isActive === false)) {
        throw new ApiError(
          '系统角色不能修改名称或禁用',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 检查角色名称唯一性（如果要修改名称）
      if (roleData.name && roleData.name !== existingRole.name) {
        const duplicateRole = await prisma.role.findUnique({
          where: { name: roleData.name },
        });

        if (duplicateRole) {
          throw new ApiError(
            '角色名称已存在',
            ErrorCode.DUPLICATE_ERROR,
            409
          );
        }
      }

      const updateData: any = {
        updatedAt: new Date(),
      };

      if (roleData.name !== undefined) updateData.name = roleData.name;
      if (roleData.displayName !== undefined) updateData.displayName = roleData.displayName;
      if (roleData.description !== undefined) updateData.description = roleData.description || null;
      if (roleData.isActive !== undefined) updateData.isActive = roleData.isActive;
      if (roleData.priority !== undefined) updateData.priority = roleData.priority;

      const updatedRole = await prisma.role.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: {
              userRoles: true,
              rolePermissions: true,
            },
          },
        },
      });

      return {
        id: updatedRole.id,
        name: updatedRole.name,
        displayName: updatedRole.displayName,
        description: updatedRole.description,
        isSystem: updatedRole.isSystem,
        isActive: updatedRole.isActive,
        priority: updatedRole.priority,
        createdAt: updatedRole.createdAt,
        updatedAt: updatedRole.updatedAt,
        stats: {
          userCount: updatedRole._count.userRoles,
          permissionCount: updatedRole._count.rolePermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'updateRole');
    }
  }

  /**
   * 删除角色
   */
  public async deleteRole(id: string): Promise<void> {
    try {
      // 检查角色是否存在
      const existingRole = await prisma.role.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              userRoles: true,
            },
          },
        },
      });

      if (!existingRole) {
        throw new ApiError(
          '角色不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 检查是否为系统角色
      if (existingRole.isSystem) {
        throw new ApiError(
          '系统角色不能删除',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 检查是否有用户使用该角色
      if (existingRole._count.userRoles > 0) {
        throw new ApiError(
          `无法删除角色，还有 ${existingRole._count.userRoles} 个用户使用该角色`,
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      await prisma.role.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'deleteRole');
    }
  }

  /**
   * 获取角色权限
   */
  public async getRolePermissions(roleId: string) {
    try {
      const role = await prisma.role.findUnique({
        where: { id: roleId },
        include: {
          rolePermissions: {
            where: { isActive: true },
            include: {
              permission: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  description: true,
                  resource: true,
                  action: true,
                  type: true,
                  parentId: true,
                },
              },
            },
          },
        },
      });

      if (!role) {
        throw new ApiError(
          '角色不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      return role.rolePermissions.map(rp => rp.permission);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'getRolePermissions');
    }
  }

  /**
   * 设置角色权限
   */
  public async setRolePermissions(data: RolePermissionData, assignedBy?: string) {
    try {
      const { roleId, permissionIds } = data;

      // 检查角色是否存在
      const role = await prisma.role.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new ApiError(
          '角色不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      // 检查权限是否存在
      const permissions = await prisma.permission.findMany({
        where: {
          id: { in: permissionIds },
          isActive: true,
        },
      });

      if (permissions.length !== permissionIds.length) {
        throw new ApiError(
          '部分权限不存在或已禁用',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 使用事务处理权限分配
      await prisma.$transaction(async (tx) => {
        // 删除现有权限分配
        await tx.rolePermission.deleteMany({
          where: { roleId },
        });

        // 添加新的权限分配
        if (permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: permissionIds.map(permissionId => ({
              roleId,
              permissionId,
              assignedBy: assignedBy || null,
            })),
          });
        }
      });

      // 返回更新后的权限列表
      return this.getRolePermissions(roleId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'setRolePermissions');
    }
  }

  /**
   * 获取所有角色（用于下拉选择）
   */
  public async getAllRoles() {
    try {
      const roles = await prisma.role.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          displayName: true,
          description: true,
          priority: true,
        },
        orderBy: { priority: 'desc' },
      });

      return roles;
    } catch (error) {
      this.handleDatabaseError(error, 'getAllRoles');
    }
  }
}

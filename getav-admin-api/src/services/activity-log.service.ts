import { prisma } from '@/utils/database.js';
import { ApiError, ErrorCode } from '@/types/index.js';
import { DatabaseService } from './database.service.js';
import { ActivityAction, ActivityTargetType } from '@prisma/client';

/**
 * 用户行为日志服务类
 * 处理用户行为日志相关的业务逻辑
 */
export class ActivityLogService extends DatabaseService {
  /**
   * 记录用户行为日志
   */
  async createActivityLog(params: {
    userId?: string;
    action: ActivityAction;
    targetType: ActivityTargetType;
    targetId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: Record<string, any>;
  }) {
    try {
      const createData: any = {
        userId: params.userId || null,
        action: params.action,
        targetType: params.targetType,
        targetId: params.targetId || null,
        ipAddress: params.ipAddress || null,
        userAgent: params.userAgent || null,
      };

      if (params.metadata) {
        createData.metadata = params.metadata;
      }

      const activityLog = await prisma.userActivityLog.create({
        data: createData,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return activityLog;
    } catch (error) {
      this.handleDatabaseError(error, 'createActivityLog');
    }
  }

  /**
   * 获取用户行为日志列表
   */
  async getActivityLogs(params: {
    page?: number;
    limit?: number;
    search?: string;
    userId?: string;
    action?: string;
    targetType?: string;
    targetId?: string;
    ipAddress?: string;
    dateFrom?: string;
    dateTo?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      // 分页参数
      const { page, limit, skip, take } = this.createPaginationParams(params);

      // 构建查询条件
      const where: any = {};

      // 搜索条件
      if (params.search) {
        where.OR = [
          { userId: { contains: params.search, mode: 'insensitive' } },
          { action: { contains: params.search, mode: 'insensitive' } },
          { targetType: { contains: params.search, mode: 'insensitive' } },
          { targetId: { contains: params.search, mode: 'insensitive' } },
          { ipAddress: { contains: params.search, mode: 'insensitive' } },
        ];
      }

      // 精确筛选条件
      if (params.userId) {
        where.userId = params.userId;
      }

      if (params.action) {
        where.action = params.action;
      }

      if (params.targetType) {
        where.targetType = params.targetType;
      }

      if (params.targetId) {
        where.targetId = params.targetId;
      }

      if (params.ipAddress) {
        where.ipAddress = params.ipAddress;
      }

      // 日期范围查询
      const dateQuery = this.buildDateRangeQuery('createdAt', params.dateFrom, params.dateTo);
      Object.assign(where, dateQuery);

      // 排序参数
      const orderBy = this.createSortParams(params.sortBy, params.sortOrder);

      // 查询数据
      const [activityLogs, total] = await Promise.all([
        prisma.userActivityLog.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                name: true,
                email: true,
              },
            },
          },
        }),
        prisma.userActivityLog.count({ where }),
      ]);

      // 格式化数据
      const formattedLogs = activityLogs.map(log => ({
        id: log.id,
        userId: log.userId,
        action: log.action,
        targetType: log.targetType,
        targetId: log.targetId,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        metadata: log.metadata,
        createdAt: log.createdAt,
        user: log.user,
      }));

      return this.createPaginatedResponse(formattedLogs, total, page, limit);
    } catch (error) {
      this.handleDatabaseError(error, 'getActivityLogs');
    }
  }

  /**
   * 获取单个用户行为日志详情
   */
  async getActivityLogById(id: string) {
    try {
      const activityLog = await prisma.userActivityLog.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!activityLog) {
        throw new ApiError('用户行为日志不存在', ErrorCode.NOT_FOUND, 404);
      }

      return {
        id: activityLog.id,
        userId: activityLog.userId,
        action: activityLog.action,
        targetType: activityLog.targetType,
        targetId: activityLog.targetId,
        ipAddress: activityLog.ipAddress,
        userAgent: activityLog.userAgent,
        metadata: activityLog.metadata,
        createdAt: activityLog.createdAt,
        user: activityLog.user,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'getActivityLogById');
    }
  }

  /**
   * 导出用户行为日志
   */
  async exportActivityLogs(options: {
    format: 'csv' | 'json';
    dateFrom?: string;
    dateTo?: string;
    userId?: string;
    action?: string;
    targetType?: string;
  }) {
    try {
      // 构建查询条件
      const where: any = {};

      if (options.userId) {
        where.userId = options.userId;
      }

      if (options.action) {
        where.action = options.action;
      }

      if (options.targetType) {
        where.targetType = options.targetType;
      }

      // 日期范围查询
      const dateQuery = this.buildDateRangeQuery('createdAt', options.dateFrom, options.dateTo);
      Object.assign(where, dateQuery);

      // 查询数据
      const logs = await prisma.userActivityLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (options.format === 'csv') {
        return this.convertToCSV(logs);
      } else {
        return logs;
      }
    } catch (error) {
      this.handleDatabaseError(error, 'exportActivityLogs');
    }
  }

  /**
   * 清理过期的用户行为日志
   */
  async cleanupOldLogs(daysToKeep: number): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await prisma.userActivityLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      return result.count;
    } catch (error) {
      this.handleDatabaseError(error, 'cleanupOldLogs');
    }
  }

  /**
   * 获取用户行为统计数据
   */
  async getActivityStats(days: number = 7) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // 基础统计
      const [totalLogs, uniqueUsers] = await Promise.all([
        prisma.userActivityLog.count({
          where: {
            createdAt: { gte: startDate },
          },
        }),
        prisma.userActivityLog.findMany({
          where: {
            createdAt: { gte: startDate },
            userId: { not: null },
          },
          select: { userId: true },
          distinct: ['userId'],
        }).then(users => users.length),
      ]);

      // 行为类型统计
      const actionStats = await prisma.userActivityLog.groupBy({
        by: ['action'],
        where: {
          createdAt: { gte: startDate },
        },
        _count: {
          action: true,
        },
        orderBy: {
          _count: {
            action: 'desc',
          },
        },
        take: 10,
      });

      // 目标类型统计
      const targetStats = await prisma.userActivityLog.groupBy({
        by: ['targetType'],
        where: {
          createdAt: { gte: startDate },
        },
        _count: {
          targetType: true,
        },
        orderBy: {
          _count: {
            targetType: 'desc',
          },
        },
        take: 10,
      });

      // 每日统计
      const dailyStats = await this.getDailyStats(days);

      // 每小时统计
      const hourlyStats = await this.getHourlyStats(days);

      // 活跃用户排行
      const topUsers = await this.getTopUsers(days);

      // IP地址排行
      const topIpAddresses = await this.getTopIpAddresses(days);

      return {
        totalLogs,
        uniqueUsers,
        topActions: actionStats.map(stat => ({
          action: stat.action,
          count: stat._count.action,
          percentage: (stat._count.action / totalLogs) * 100,
        })),
        topTargetTypes: targetStats.map(stat => ({
          targetType: stat.targetType,
          count: stat._count.targetType,
          percentage: (stat._count.targetType / totalLogs) * 100,
        })),
        dailyStats,
        hourlyStats,
        topUsers,
        topIpAddresses,
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getActivityStats');
    }
  }

  /**
   * 获取用户的行为统计
   */
  async getUserActivityStats(userId: string, days: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const userStats = await prisma.userActivityLog.groupBy({
        by: ['action'],
        where: {
          userId,
          createdAt: { gte: startDate },
        },
        _count: {
          action: true,
        },
        orderBy: {
          _count: {
            action: 'desc',
          },
        },
      });

      const totalActions = userStats.reduce((sum, stat) => sum + stat._count.action, 0);

      return {
        userId,
        totalActions,
        actionBreakdown: userStats.map(stat => ({
          action: stat.action,
          count: stat._count.action,
          percentage: (stat._count.action / totalActions) * 100,
        })),
      };
    } catch (error) {
      this.handleDatabaseError(error, 'getUserActivityStats');
    }
  }

  /**
   * 获取每日统计数据
   */
  private async getDailyStats(days: number) {
    // 这里应该实现每日统计逻辑
    // 由于Prisma的限制，可能需要使用原生SQL查询
    const dailyStats = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const [count, uniqueUsers] = await Promise.all([
        prisma.userActivityLog.count({
          where: {
            createdAt: {
              gte: startOfDay,
              lte: endOfDay,
            },
          },
        }),
        prisma.userActivityLog.findMany({
          where: {
            createdAt: {
              gte: startOfDay,
              lte: endOfDay,
            },
            userId: { not: null },
          },
          select: { userId: true },
          distinct: ['userId'],
        }).then(users => users.length),
      ]);

      dailyStats.push({
        date: startOfDay.toISOString().split('T')[0],
        count,
        uniqueUsers,
      });
    }

    return dailyStats;
  }

  /**
   * 获取每小时统计数据
   */
  private async getHourlyStats(days: number) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 简化实现，返回24小时的统计
    const hourlyStats = [];
    for (let hour = 0; hour < 24; hour++) {
      // 这里应该实现真实的每小时统计查询
      const count = Math.floor(Math.random() * 100); // 模拟数据
      hourlyStats.push({ hour, count });
    }

    return hourlyStats;
  }

  /**
   * 获取活跃用户排行
   */
  private async getTopUsers(days: number) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const topUsers = await prisma.userActivityLog.groupBy({
      by: ['userId'],
      where: {
        createdAt: { gte: startDate },
        userId: { not: null },
      },
      _count: {
        userId: true,
      },
      orderBy: {
        _count: {
          userId: 'desc',
        },
      },
      take: 10,
    });

    // 获取用户详细信息
    const userIds = topUsers.map(stat => stat.userId).filter(Boolean) as string[];
    const users = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: {
        id: true,
        username: true,
        name: true,
        email: true,
      },
    });

    return topUsers.map(stat => {
      const user = users.find(u => u.id === stat.userId);
      return {
        user: user || { id: stat.userId, username: '未知用户', name: null, email: null },
        count: stat._count.userId,
      };
    });
  }

  /**
   * 获取IP地址排行
   */
  private async getTopIpAddresses(days: number) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const topIps = await prisma.userActivityLog.groupBy({
      by: ['ipAddress'],
      where: {
        createdAt: { gte: startDate },
        ipAddress: { not: null },
      },
      _count: {
        ipAddress: true,
      },
      orderBy: {
        _count: {
          ipAddress: 'desc',
        },
      },
      take: 10,
    });

    return Promise.all(
      topIps.map(async stat => {
        const uniqueUsers = await prisma.userActivityLog.findMany({
          where: {
            ipAddress: stat.ipAddress,
            createdAt: { gte: startDate },
            userId: { not: null },
          },
          select: { userId: true },
          distinct: ['userId'],
        }).then(users => users.length);

        return {
          ipAddress: stat.ipAddress,
          count: stat._count.ipAddress,
          uniqueUsers,
        };
      })
    );
  }

  /**
   * 转换为CSV格式
   */
  private convertToCSV(logs: any[]): string {
    if (logs.length === 0) return '';

    const headers = [
      'ID',
      '用户ID',
      '用户名',
      '行为类型',
      '目标类型',
      '目标ID',
      'IP地址',
      '用户代理',
      '创建时间',
    ];

    const rows = logs.map(log => [
      log.id,
      log.userId || '',
      log.user?.username || '',
      log.action,
      log.targetType,
      log.targetId || '',
      log.ipAddress || '',
      log.userAgent || '',
      log.createdAt.toISOString(),
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  }
}

import { prisma } from '@/utils/database.js';
import { PasswordUtils } from '@/utils/password.js';
import { JwtUtils } from '@/utils/jwt.js';
import { ApiError, ErrorCode, LoginRequest, LoginResponse } from '@/types/index.js';
import { DatabaseService } from './database.service.js';
import { UserRolesService } from './user-roles.service.js';

/**
 * 认证服务类
 * 处理用户登录、注册、密码管理等认证相关业务逻辑
 */
export class AuthService extends DatabaseService {
  /**
   * 用户登录
   */
  public async login(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      const { username, password } = loginData;

      // 验证输入
      if (!username || !password) {
        throw new ApiError(
          '用户名和密码不能为空',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 查找用户
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { username: username },
            { email: username }, // 支持邮箱登录
          ],
        },
        include: {
          password: true,
        },
      });

      if (!user) {
        throw new ApiError(
          '用户名或密码错误',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      if (!user.password) {
        throw new ApiError(
          '用户未设置密码',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 验证密码
      const isPasswordValid = await PasswordUtils.verifyPassword(
        password,
        user.password.passwordHash
      );

      if (!isPasswordValid) {
        throw new ApiError(
          '用户名或密码错误',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 检查用户账户状态
      if (!user.isActive) {
        throw new ApiError(
          '用户账户已被禁用',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 检查密码是否需要重新哈希
      if (PasswordUtils.needsRehash(user.password.passwordHash)) {
        const newHashedPassword = await PasswordUtils.hashPassword(password);
        await prisma.userPassword.update({
          where: { userId: user.id },
          data: { passwordHash: newHashedPassword },
        });
      }

      // 获取用户权限信息
      const userRolesService = new UserRolesService();
      const userPermissions = await userRolesService.getUserPermissions(user.id);

      // 确定用户的主要角色（用于向后兼容）
      const primaryRole = userPermissions.roles.includes('super_admin') ? 'super_admin' :
                         userPermissions.roles.includes('admin') ? 'admin' :
                         userPermissions.roles.includes('editor') ? 'editor' :
                         userPermissions.roles.includes('viewer') ? 'viewer' : 'user';

      // 生成token响应
      const tokenResponse = JwtUtils.createTokenResponse({
        id: user.id,
        username: user.username || '',
        email: user.email || undefined,
        role: primaryRole, // 主要角色用于向后兼容
      });

      // 更新最后登录时间
      await prisma.user.update({
        where: { id: user.id },
        data: { updatedAt: new Date() },
      });

      // 扩展响应，包含完整的权限信息
      return {
        ...tokenResponse,
        user: {
          ...tokenResponse.user,
          roles: userPermissions.roles,
          permissions: userPermissions.permissions,
          allPermissions: userPermissions.allPermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'login');
    }
  }

  /**
   * 验证token并获取用户信息
   */
  public async validateToken(token: string) {
    try {
      // 验证token
      const decoded = JwtUtils.verifyToken(token);

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          username: true,
          email: true,
          name: true,
          image: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new ApiError(
          '用户不存在',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      if (!user.isActive) {
        throw new ApiError(
          '用户账户已被禁用',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 获取用户权限信息
      const userRolesService = new UserRolesService();
      const userPermissions = await userRolesService.getUserPermissions(user.id);

      return {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          name: user.name,
          image: user.image,
          role: decoded.role || 'user',
          roles: userPermissions.roles,
          permissions: userPermissions.permissions,
          allPermissions: userPermissions.allPermissions,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        token: {
          remainingTime: JwtUtils.getTokenRemainingTime(token),
          isExpiringSoon: JwtUtils.isTokenExpiringSoon(token),
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'validateToken');
    }
  }

  /**
   * 刷新token
   */
  public async refreshToken(token: string): Promise<LoginResponse> {
    try {
      // 验证当前token
      const decoded = JwtUtils.verifyToken(token);

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          username: true,
          email: true,
          name: true,
          isActive: true,
        },
      });

      if (!user) {
        throw new ApiError(
          '用户不存在',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      if (!user.isActive) {
        throw new ApiError(
          '用户账户已被禁用',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 获取用户权限信息
      const userRolesService = new UserRolesService();
      const userPermissions = await userRolesService.getUserPermissions(user.id);

      // 确定用户的主要角色（用于向后兼容）
      const primaryRole = userPermissions.roles.includes('super_admin') ? 'super_admin' :
                         userPermissions.roles.includes('admin') ? 'admin' :
                         userPermissions.roles.includes('editor') ? 'editor' :
                         userPermissions.roles.includes('viewer') ? 'viewer' : 'user';

      // 生成新token
      const tokenResponse = JwtUtils.createTokenResponse({
        id: user.id,
        username: user.username || '',
        email: user.email || undefined,
        role: primaryRole,
      });

      // 扩展响应，包含完整的权限信息
      return {
        ...tokenResponse,
        user: {
          ...tokenResponse.user,
          roles: userPermissions.roles,
          permissions: userPermissions.permissions,
          allPermissions: userPermissions.allPermissions,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'refreshToken');
    }
  }

  /**
   * 修改密码
   */
  public async changePassword(
    userId: string,
    oldPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      // 获取用户当前密码
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { password: true },
      });

      if (!user) {
        throw new ApiError(
          '用户不存在',
          ErrorCode.NOT_FOUND,
          404
        );
      }

      if (!user.password) {
        throw new ApiError(
          '用户未设置密码',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 验证旧密码
      const isOldPasswordValid = await PasswordUtils.verifyPassword(
        oldPassword,
        user.password.passwordHash
      );

      if (!isOldPasswordValid) {
        throw new ApiError(
          '当前密码错误',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 验证新密码强度
      PasswordUtils.validatePasswordStrength(newPassword);

      // 哈希新密码
      const newHashedPassword = await PasswordUtils.hashPassword(newPassword);

      // 更新密码
      await prisma.userPassword.update({
        where: { userId: userId },
        data: { passwordHash: newHashedPassword },
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'changePassword');
    }
  }

  /**
   * 重置密码
   */
  public async resetPassword(userId: string, newPassword: string): Promise<void> {
    try {
      // 验证用户存在
      await this.checkRecordExists(
        prisma.user,
        { id: userId },
        '用户不存在'
      );

      // 验证新密码强度
      PasswordUtils.validatePasswordStrength(newPassword);

      // 哈希新密码
      const hashedPassword = await PasswordUtils.hashPassword(newPassword);

      // 更新或创建密码记录
      await prisma.userPassword.upsert({
        where: { userId: userId },
        update: { passwordHash: hashedPassword },
        create: {
          userId: userId,
          passwordHash: hashedPassword,
        },
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      this.handleDatabaseError(error, 'resetPassword');
    }
  }

  /**
   * 检查用户名是否可用
   */
  public async isUsernameAvailable(username: string): Promise<boolean> {
    try {
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { username: username },
            { email: username },
          ],
        },
      });

      return !existingUser;
    } catch (error) {
      this.handleDatabaseError(error, 'isUsernameAvailable');
    }
  }

  /**
   * 获取密码强度
   */
  public getPasswordStrength(password: string) {
    return PasswordUtils.getPasswordStrength(password);
  }
}

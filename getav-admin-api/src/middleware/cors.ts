import cors from 'cors';
import { config } from '@/utils/config.js';

/**
 * CORS中间件配置
 * 配置跨域资源共享，允许getav-admin前端访问API
 */
export const corsMiddleware = cors({
  // 允许的源
  origin: (origin, callback) => {
    // 允许的源列表
    const allowedOrigins = [
      config.CORS_ORIGIN,
      'http://localhost:4000', // getav-admin开发环境
      'http://127.0.0.1:4000',
      'http://localhost:3001', // getav-web开发环境
      'http://127.0.0.1:3001',
    ];

    // 开发环境允许无origin的请求（如Postman）
    if (config.NODE_ENV === 'development' && !origin) {
      return callback(null, true);
    }

    // 检查origin是否在允许列表中
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS: 拒绝来自 ${origin} 的请求`);
      callback(new Error('CORS策略不允许此源'), false);
    }
  },

  // 允许的HTTP方法
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],

  // 允许的请求头
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-Access-Token',
  ],

  // 允许发送凭据（cookies, authorization headers等）
  credentials: true,

  // 预检请求的缓存时间（秒）
  maxAge: 86400, // 24小时

  // 暴露给客户端的响应头
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],

  // 是否通过预检请求
  preflightContinue: false,

  // 预检请求的状态码
  optionsSuccessStatus: 204,
});

/**
 * 开发环境的宽松CORS配置
 */
export const devCorsMiddleware = cors({
  origin: true, // 允许所有源
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: '*',
  credentials: true,
  maxAge: 86400,
});

/**
 * 根据环境选择CORS中间件
 */
export const getCorsMiddleware = () => {
  if (config.NODE_ENV === 'development') {
    console.log('🔓 使用开发环境CORS配置（宽松模式）');
    return devCorsMiddleware;
  } else {
    console.log('🔒 使用生产环境CORS配置（严格模式）');
    return corsMiddleware;
  }
};

import { Request, Response, NextFunction } from 'express';
import { JwtUtils } from '@/utils/jwt.js';
import { ApiError, ErrorCode, AuthenticatedRequest, ApiResponse } from '@/types/index.js';
import { prisma } from '@/utils/database.js';
import { UserRolesService } from '@/services/user-roles.service.js';

/**
 * JWT认证中间件
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 从请求头中提取token
    const authHeader = req.headers.authorization;
    const token = JwtUtils.extractTokenFromHeader(authHeader);

    if (!token) {
      throw new ApiError(
        '缺少认证token',
        ErrorCode.AUTHENTICATION_ERROR,
        401
      );
    }

    // 验证token格式
    if (!JwtUtils.isValidTokenFormat(token)) {
      throw new ApiError(
        'Token格式无效',
        ErrorCode.AUTHENTICATION_ERROR,
        401
      );
    }

    // 验证token
    const decoded = JwtUtils.verifyToken(token);

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new ApiError(
        '用户不存在',
        ErrorCode.AUTHENTICATION_ERROR,
        401
      );
    }

    if (!user.isActive) {
      throw new ApiError(
        '用户账户已被禁用',
        ErrorCode.AUTHENTICATION_ERROR,
        401
      );
    }

    // 获取用户权限信息
    const userRolesService = new UserRolesService();
    const userPermissions = await userRolesService.getUserPermissions(user.id);

    // 将用户信息和权限添加到请求对象
    req.user = {
      id: user.id,
      username: user.username || '',
      email: user.email || undefined,
      role: decoded.role || 'user', // 保持向后兼容
      roles: userPermissions.roles,
      permissions: userPermissions.permissions,
      allPermissions: userPermissions.allPermissions,
    };

    next();
  } catch (error) {
    if (error instanceof ApiError) {
      const response: ApiResponse = {
        success: false,
        error: error.message,
        code: error.statusCode,
      };
      res.status(error.statusCode).json(response);
      return;
    }

    console.error('认证中间件错误:', error);
    const response: ApiResponse = {
      success: false,
      error: '认证失败',
      code: 401,
    };
    res.status(401).json(response);
  }
};

/**
 * 可选认证中间件（token存在时验证，不存在时继续）
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = JwtUtils.extractTokenFromHeader(authHeader);

    if (token && JwtUtils.isValidTokenFormat(token)) {
      try {
        const decoded = JwtUtils.verifyToken(token);
        
        const user = await prisma.user.findUnique({
          where: { id: decoded.id },
          select: {
            id: true,
            username: true,
            email: true,
            name: true,
          },
        });

        if (user) {
          req.user = {
            id: user.id,
            username: user.username || '',
            email: user.email || undefined,
            role: decoded.role || 'admin',
          };
        }
      } catch (error) {
        // 可选认证失败时不阻止请求继续
        console.warn('可选认证失败:', error);
      }
    }

    next();
  } catch (error) {
    console.error('可选认证中间件错误:', error);
    next(); // 继续处理请求
  }
};

/**
 * 角色权限验证中间件
 */
export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new ApiError(
          '需要认证',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      const userRole = req.user.role || 'user';
      
      if (!allowedRoles.includes(userRole)) {
        throw new ApiError(
          '权限不足',
          ErrorCode.AUTHORIZATION_ERROR,
          403,
          { 
            userRole, 
            allowedRoles,
            userId: req.user.id 
          }
        );
      }

      next();
    } catch (error) {
      if (error instanceof ApiError) {
        const response: ApiResponse = {
          success: false,
          error: error.message,
          code: error.statusCode,
        };
        res.status(error.statusCode).json(response);
        return;
      }

      console.error('角色验证中间件错误:', error);
      const response: ApiResponse = {
        success: false,
        error: '权限验证失败',
        code: 403,
      };
      res.status(403).json(response);
    }
  };
};

/**
 * 管理员权限验证中间件
 */
export const requireAdmin = requireRole(['admin', 'super_admin']);

/**
 * 超级管理员权限验证中间件
 */
export const requireSuperAdmin = requireRole(['super_admin']);

/**
 * 用户自己或管理员权限验证中间件
 */
export const requireSelfOrAdmin = (userIdParam: string = 'id') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new ApiError(
          '需要认证',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      const targetUserId = req.params[userIdParam];
      const currentUserId = req.user.id;
      const userRole = req.user.role || 'user';

      // 管理员可以访问任何用户的数据
      if (['admin', 'super_admin'].includes(userRole)) {
        next();
        return;
      }

      // 用户只能访问自己的数据
      if (currentUserId === targetUserId) {
        next();
        return;
      }

      throw new ApiError(
        '只能访问自己的数据',
        ErrorCode.AUTHORIZATION_ERROR,
        403,
        {
          currentUserId,
          targetUserId,
          userRole
        }
      );
    } catch (error) {
      if (error instanceof ApiError) {
        const response: ApiResponse = {
          success: false,
          error: error.message,
          code: error.statusCode,
        };
        res.status(error.statusCode).json(response);
        return;
      }

      console.error('用户权限验证中间件错误:', error);
      const response: ApiResponse = {
        success: false,
        error: '权限验证失败',
        code: 403,
      };
      res.status(403).json(response);
    }
  };
};

/**
 * 权限验证中间件（基于权限名称）
 */
export const requirePermission = (permissionName: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new ApiError(
          '需要认证',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 检查用户是否有指定权限
      const hasPermission = req.user.allPermissions?.some(
        (permission: any) => permission.name === permissionName
      );

      if (!hasPermission) {
        throw new ApiError(
          `缺少权限: ${permissionName}`,
          ErrorCode.AUTHORIZATION_ERROR,
          403,
          {
            userId: req.user.id,
            requiredPermission: permissionName,
            userPermissions: req.user.allPermissions?.map((p: any) => p.name) || []
          }
        );
      }

      next();
    } catch (error) {
      if (error instanceof ApiError) {
        const response: ApiResponse = {
          success: false,
          error: error.message,
          code: error.statusCode,
        };
        res.status(error.statusCode).json(response);
        return;
      }

      console.error('权限验证中间件错误:', error);
      const response: ApiResponse = {
        success: false,
        error: '权限验证失败',
        code: 403,
      };
      res.status(403).json(response);
    }
  };
};

/**
 * 多权限验证中间件（用户需要拥有其中任一权限）
 */
export const requireAnyPermission = (permissionNames: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new ApiError(
          '需要认证',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 检查用户是否有任一指定权限
      const hasAnyPermission = permissionNames.some(permissionName =>
        req.user?.allPermissions?.some(
          (permission: any) => permission.name === permissionName
        )
      );

      if (!hasAnyPermission) {
        throw new ApiError(
          `缺少权限，需要以下权限之一: ${permissionNames.join(', ')}`,
          ErrorCode.AUTHORIZATION_ERROR,
          403,
          {
            userId: req.user.id,
            requiredPermissions: permissionNames,
            userPermissions: req.user.allPermissions?.map((p: any) => p.name) || []
          }
        );
      }

      next();
    } catch (error) {
      if (error instanceof ApiError) {
        const response: ApiResponse = {
          success: false,
          error: error.message,
          code: error.statusCode,
        };
        res.status(error.statusCode).json(response);
        return;
      }

      console.error('多权限验证中间件错误:', error);
      const response: ApiResponse = {
        success: false,
        error: '权限验证失败',
        code: 403,
      };
      res.status(403).json(response);
    }
  };
};

/**
 * 全权限验证中间件（用户需要拥有所有指定权限）
 */
export const requireAllPermissions = (permissionNames: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new ApiError(
          '需要认证',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      // 检查用户是否拥有所有指定权限
      const missingPermissions = permissionNames.filter(permissionName =>
        !req.user?.allPermissions?.some(
          (permission: any) => permission.name === permissionName
        )
      );

      if (missingPermissions.length > 0) {
        throw new ApiError(
          `缺少权限: ${missingPermissions.join(', ')}`,
          ErrorCode.AUTHORIZATION_ERROR,
          403,
          {
            userId: req.user.id,
            missingPermissions,
            userPermissions: req.user.allPermissions?.map((p: any) => p.name) || []
          }
        );
      }

      next();
    } catch (error) {
      if (error instanceof ApiError) {
        const response: ApiResponse = {
          success: false,
          error: error.message,
          code: error.statusCode,
        };
        res.status(error.statusCode).json(response);
        return;
      }

      console.error('全权限验证中间件错误:', error);
      const response: ApiResponse = {
        success: false,
        error: '权限验证失败',
        code: 403,
      };
      res.status(403).json(response);
    }
  };
};

/**
 * 动态角色验证中间件（基于数据库中的实际角色）
 */
export const requireDynamicRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new ApiError(
          '需要认证',
          ErrorCode.AUTHENTICATION_ERROR,
          401
        );
      }

      const userRoles = req.user.roles || [];

      // 检查用户是否拥有任一允许的角色
      const hasAllowedRole = allowedRoles.some(role => userRoles.includes(role));

      if (!hasAllowedRole) {
        throw new ApiError(
          '权限不足',
          ErrorCode.AUTHORIZATION_ERROR,
          403,
          {
            userRoles,
            allowedRoles,
            userId: req.user.id
          }
        );
      }

      next();
    } catch (error) {
      if (error instanceof ApiError) {
        const response: ApiResponse = {
          success: false,
          error: error.message,
          code: error.statusCode,
        };
        res.status(error.statusCode).json(response);
        return;
      }

      console.error('动态角色验证中间件错误:', error);
      const response: ApiResponse = {
        success: false,
        error: '权限验证失败',
        code: 403,
      };
      res.status(403).json(response);
    }
  };
};

/**
 * 动态管理员权限验证中间件
 */
export const requireDynamicAdmin = requireDynamicRole(['admin', 'super_admin']);

/**
 * 动态超级管理员权限验证中间件
 */
export const requireDynamicSuperAdmin = requireDynamicRole(['super_admin']);

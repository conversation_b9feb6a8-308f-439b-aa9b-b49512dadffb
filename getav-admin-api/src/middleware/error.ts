import { Request, Response, NextFunction } from 'express';
import { ApiError, ErrorCode, ApiResponse } from '@/types/index.js';
import { config } from '@/utils/config.js';

/**
 * 404错误处理中间件
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new ApiError(
    `路由 ${req.method} ${req.path} 不存在`,
    ErrorCode.NOT_FOUND,
    404,
    {
      method: req.method,
      path: req.path,
      query: req.query,
    }
  );
  next(error);
};

/**
 * 全局错误处理中间件
 */
export const errorHandler = (
  error: Error | ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // 如果响应已经发送，交给Express默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  // 记录错误日志
  console.error('🚨 API错误:', {
    message: error.message,
    stack: error.stack,
    method: req.method,
    path: req.path,
    query: req.query,
    body: req.body,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
  });

  // 处理ApiError
  if (error instanceof ApiError) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      code: error.statusCode,
    };

    // 开发环境返回详细错误信息
    if (config.NODE_ENV === 'development') {
      response.data = {
        errorCode: error.code,
        details: error.details,
        stack: error.stack,
      };
    }

    res.status(error.statusCode).json(response);
    return;
  }

  // 处理Prisma错误
  if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any;
    let message = '数据库操作失败';
    let statusCode = 500;

    switch (prismaError.code) {
      case 'P2002':
        message = '数据已存在，违反唯一性约束';
        statusCode = 409;
        break;
      case 'P2025':
        message = '记录不存在';
        statusCode = 404;
        break;
      case 'P2003':
        message = '外键约束失败';
        statusCode = 400;
        break;
      case 'P2014':
        message = '数据关联冲突';
        statusCode = 400;
        break;
    }

    const response: ApiResponse = {
      success: false,
      error: message,
      code: statusCode,
    };

    if (config.NODE_ENV === 'development') {
      response.data = {
        prismaCode: prismaError.code,
        meta: prismaError.meta,
        stack: error.stack,
      };
    }

    res.status(statusCode).json(response);
    return;
  }

  // 处理JWT错误
  if (error.name === 'JsonWebTokenError') {
    const response: ApiResponse = {
      success: false,
      error: 'Token无效',
      code: 401,
    };

    if (config.NODE_ENV === 'development') {
      response.data = {
        jwtError: error.message,
        stack: error.stack,
      };
    }

    res.status(401).json(response);
    return;
  }

  if (error.name === 'TokenExpiredError') {
    const response: ApiResponse = {
      success: false,
      error: 'Token已过期',
      code: 401,
    };

    res.status(401).json(response);
    return;
  }

  // 处理验证错误
  if (error.name === 'ValidationError') {
    const response: ApiResponse = {
      success: false,
      error: '请求参数验证失败',
      code: 400,
    };

    if (config.NODE_ENV === 'development') {
      response.data = {
        validationDetails: error.message,
        stack: error.stack,
      };
    }

    res.status(400).json(response);
    return;
  }

  // 处理语法错误（如JSON解析错误）
  if (error instanceof SyntaxError && 'body' in error) {
    const response: ApiResponse = {
      success: false,
      error: '请求体格式错误',
      code: 400,
    };

    res.status(400).json(response);
    return;
  }

  // 默认错误处理
  const response: ApiResponse = {
    success: false,
    error: config.NODE_ENV === 'development' ? error.message : '服务器内部错误',
    code: 500,
  };

  if (config.NODE_ENV === 'development') {
    response.data = {
      stack: error.stack,
      name: error.name,
    };
  }

  res.status(500).json(response);
};

/**
 * 异步错误处理包装器
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 请求超时处理中间件
 */
export const timeoutHandler = (timeoutMs: number = 30000) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        const error = new ApiError(
          '请求超时',
          ErrorCode.INTERNAL_ERROR,
          408
        );
        next(error);
      }
    }, timeoutMs);

    // 清理超时定时器
    res.on('finish', () => clearTimeout(timeout));
    res.on('close', () => clearTimeout(timeout));

    next();
  };
};

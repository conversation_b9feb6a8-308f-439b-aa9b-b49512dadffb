import morgan from 'morgan';
import { Request, Response } from 'express';
import { config } from '@/utils/config.js';

/**
 * 自定义日志格式
 */
const customFormat = ':date[iso] | :status | :method | :url | :response-time ms | :res[content-length] bytes | :remote-addr | ":user-agent"';

/**
 * 开发环境日志配置
 */
const developmentLogger = morgan(customFormat, {
  // 跳过静态资源和健康检查
  skip: (req: Request) => {
    return req.url.includes('/favicon.ico') || 
           req.url.includes('/health') ||
           req.url.startsWith('/static/');
  }
});

/**
 * 生产环境日志配置
 */
const productionLogger = morgan('combined', {
  // 只记录错误请求
  skip: (req: Request, res: Response) => {
    return res.statusCode < 400;
  }
});

/**
 * 简化的日志格式（用于测试环境）
 */
const testLogger = morgan('tiny', {
  skip: () => true // 测试环境不输出日志
});

/**
 * 根据环境选择日志中间件
 */
export const getLoggerMiddleware = () => {
  switch (config.NODE_ENV) {
    case 'production':
      console.log('📝 使用生产环境日志配置');
      return productionLogger;
    case 'test':
      return testLogger;
    case 'development':
    default:
      console.log('📝 使用开发环境日志配置');
      return developmentLogger;
  }
};

/**
 * API访问日志记录器
 */
export const apiLogger = {
  info: (message: string, data?: any) => {
    console.log(`ℹ️  [API] ${new Date().toISOString()} - ${message}`, data || '');
  },
  
  warn: (message: string, data?: any) => {
    console.warn(`⚠️  [API] ${new Date().toISOString()} - ${message}`, data || '');
  },
  
  error: (message: string, error?: any) => {
    console.error(`❌ [API] ${new Date().toISOString()} - ${message}`, error || '');
  },
  
  success: (message: string, data?: any) => {
    console.log(`✅ [API] ${new Date().toISOString()} - ${message}`, data || '');
  },
  
  debug: (message: string, data?: any) => {
    if (config.NODE_ENV === 'development') {
      console.log(`🐛 [API] ${new Date().toISOString()} - ${message}`, data || '');
    }
  }
};

/**
 * 请求开始时间记录中间件
 */
export const requestTimingMiddleware = (req: Request, res: Response, next: Function) => {
  req.startTime = Date.now();
  next();
};

/**
 * 扩展Request接口以包含startTime
 */
declare global {
  namespace Express {
    interface Request {
      startTime?: number;
    }
  }
}

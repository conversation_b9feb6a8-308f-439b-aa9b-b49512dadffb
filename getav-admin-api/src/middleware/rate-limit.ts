import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { config } from '@/utils/config.js';
import { ApiResponse } from '@/types/index.js';

/**
 * 创建速率限制错误响应
 */
const createRateLimitResponse = (req: Request, res: Response): void => {
  const response: ApiResponse = {
    success: false,
    error: '请求过于频繁，请稍后再试',
    code: 429,
  };

  if (config.NODE_ENV === 'development') {
    response.data = {
      ip: req.ip,
      path: req.path,
      method: req.method,
      resetTime: new Date(Date.now() + config.RATE_LIMIT_WINDOW_MS).toISOString(),
    };
  }

  res.status(429).json(response);
};

/**
 * 通用速率限制配置
 */
export const generalRateLimit = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS, // 时间窗口
  max: config.RATE_LIMIT_MAX_REQUESTS, // 最大请求数
  message: createRateLimitResponse,
  standardHeaders: true, // 返回标准的 `RateLimit` 头部
  legacyHeaders: false, // 禁用 `X-RateLimit-*` 头部
  
  // 自定义键生成器（基于IP和用户ID）
  keyGenerator: (req: Request): string => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userId = (req as any).user?.id || 'anonymous';
    return `${ip}:${userId}`;
  },

  // 跳过某些请求
  skip: (req: Request): boolean => {
    // 跳过健康检查和静态资源
    return req.path === '/health' || 
           req.path === '/favicon.ico' ||
           req.path.startsWith('/static/');
  },

  // 自定义处理器
  handler: createRateLimitResponse,
});

/**
 * 登录接口的严格速率限制
 */
export const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: (req: Request, res: Response) => {
    const response: ApiResponse = {
      success: false,
      error: '登录尝试过于频繁，请15分钟后再试',
      code: 429,
    };

    if (config.NODE_ENV === 'development') {
      response.data = {
        ip: req.ip,
        resetTime: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
        attemptsLeft: 0,
      };
    }

    res.status(429).json(response);
  },
  standardHeaders: true,
  legacyHeaders: false,
  
  // 基于IP的键生成器
  keyGenerator: (req: Request): string => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  },

  handler: (req: Request, res: Response) => {
    const response: ApiResponse = {
      success: false,
      error: '登录尝试过于频繁，请15分钟后再试',
      code: 429,
    };
    res.status(429).json(response);
  },
});

/**
 * API接口的中等速率限制
 */
export const apiRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 60, // 每分钟60次请求
  message: createRateLimitResponse,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userId = (req as any).user?.id || 'anonymous';
    return `api:${ip}:${userId}`;
  },
  handler: createRateLimitResponse,
});

/**
 * 数据导入接口的宽松速率限制
 */
export const importRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 10, // 每5分钟10次请求
  message: (req: Request, res: Response) => {
    const response: ApiResponse = {
      success: false,
      error: '数据导入请求过于频繁，请5分钟后再试',
      code: 429,
    };
    res.status(429).json(response);
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userId = (req as any).user?.id || 'anonymous';
    return `import:${ip}:${userId}`;
  },
});

/**
 * 开发环境的宽松速率限制
 */
export const devRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 1000, // 每分钟1000次请求（基本不限制）
  message: createRateLimitResponse,
  standardHeaders: true,
  legacyHeaders: false,
  skip: () => config.NODE_ENV === 'development', // 开发环境跳过限制
});

/**
 * 根据环境和路由选择合适的速率限制
 */
export const getRateLimitMiddleware = (type: 'general' | 'login' | 'api' | 'import' = 'general') => {
  if (config.NODE_ENV === 'development') {
    console.log(`🚦 使用开发环境速率限制配置 (${type})`);
    return devRateLimit;
  }

  switch (type) {
    case 'login':
      console.log('🚦 使用登录速率限制配置');
      return loginRateLimit;
    case 'api':
      console.log('🚦 使用API速率限制配置');
      return apiRateLimit;
    case 'import':
      console.log('🚦 使用导入速率限制配置');
      return importRateLimit;
    case 'general':
    default:
      console.log('🚦 使用通用速率限制配置');
      return generalRateLimit;
  }
};

import { Request } from 'express';

// 基础响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 分页查询参数
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 扩展的Request接口，包含用户信息
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    username: string;
    email?: string | undefined;
    role?: string | undefined; // 保持向后兼容
    roles?: string[]; // 用户角色列表
    permissions?: {
      menus: any[];
      actions: any[];
      data: any[];
      api: any[];
    };
    allPermissions?: any[]; // 所有权限的平铺列表
  };
}

// JWT载荷接口
export interface JwtPayload {
  id: string;
  username: string;
  email?: string | undefined;
  role?: string | undefined;
  iat?: number;
  exp?: number;
}

// 登录请求接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应接口
export interface LoginResponse {
  token: string;
  user: {
    id: string;
    username: string;
    email?: string | undefined;
    role?: string | undefined; // 保持向后兼容
    roles?: string[]; // 用户角色列表
    permissions?: {
      menus: any[];
      actions: any[];
      data: any[];
      api: any[];
    };
    allPermissions?: any[]; // 所有权限的平铺列表
  };
  expiresIn: string;
  tokenType?: string;
}

// 影片查询参数
export interface MovieQueryParams extends PaginationParams {
  search?: string;
  genre?: string;
  star?: string;
  director?: string;
  producer?: string;
  dateFrom?: string;
  dateTo?: string;

  // 新增筛选参数
  censored?: 'censored' | 'uncensored' | 'all' | undefined; // 有码/无码筛选
  hasSubtitle?: boolean | undefined; // 字幕筛选
  durationMin?: number | undefined; // 最小时长（分钟）
  durationMax?: number | undefined; // 最大时长（分钟）
  releaseDateFrom?: string | undefined; // 上市时间起始
  releaseDateTo?: string | undefined; // 上市时间结束

  // 人气排序相关
  popularityType?: 'daily' | 'weekly' | 'monthly' | 'total' | undefined; // 人气类型
}

// 演员查询参数
export interface StarQueryParams extends PaginationParams {
  search?: string;
  hasAvatar?: boolean | undefined;
}

// 用户查询参数
export interface UserQueryParams extends PaginationParams {
  search?: string;
  hasEmail?: boolean | undefined;
  verified?: boolean | undefined;
}

// 角色查询参数
export interface RoleQueryParams extends PaginationParams {
  search?: string;
  isActive?: boolean | undefined;
  isSystem?: boolean | undefined;
}

// 权限查询参数
export interface PermissionQueryParams extends PaginationParams {
  search?: string;
  type?: string;
  resource?: string;
  isActive?: boolean | undefined;
}

// 角色创建/更新数据
export interface RoleData {
  name: string;
  displayName: string;
  description?: string;
  isActive?: boolean;
  priority?: number;
}

// 权限数据
export interface PermissionData {
  name: string;
  displayName: string;
  description?: string;
  resource: string;
  action: string;
  type?: 'MENU' | 'ACTION' | 'DATA' | 'API';
  parentId?: string;
  isActive?: boolean;
}

// 用户角色分配数据
export interface UserRoleData {
  userId: string;
  roleIds: string[];
  expiresAt?: Date | null;
}

// 角色权限分配数据
export interface RolePermissionData {
  roleId: string;
  permissionIds: string[];
}

// 统计数据接口
export interface StatsData {
  totalMovies: number;
  totalStars: number;
  totalUsers: number;
  totalGenres: number;
  recentMovies: number;
  recentUsers: number;
  popularMovies: Array<{
    id: string;
    title: string;
    viewCount: number;
  }>;
  topStars: Array<{
    id: string;
    name: string;
    movieCount: number;
  }>;
}

// 数据导入状态
export interface ImportStatus {
  isRunning: boolean;
  progress: number;
  currentPage?: number;
  totalPages?: number;
  processedCount: number;
  errorCount: number;
  startTime?: Date;
  estimatedEndTime?: Date;
  lastError?: string;
}

// 错误类型
export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE_ERROR = 'DUPLICATE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR'
}

// 自定义错误类
export class ApiError extends Error {
  public code: ErrorCode;
  public statusCode: number;
  public details?: any;

  constructor(
    message: string,
    code: ErrorCode = ErrorCode.INTERNAL_ERROR,
    statusCode: number = 500,
    details?: any
  ) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

// 环境变量类型
export interface EnvConfig {
  PORT: number;
  NODE_ENV: string;
  DATABASE_URL: string;
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  CORS_ORIGIN: string;
  ADMIN_USERNAME: string;
  ADMIN_PASSWORD: string;
  ADMIN_EMAIL: string;
  LOG_LEVEL: string;
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
}

import { Request, Response } from 'express';
import { AuthService } from '@/services/auth.service.js';
import { UserRolesService } from '@/services/user-roles.service.js';
import { ApiResponse, LoginRequest, AuthenticatedRequest } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';
import { JwtUtils } from '@/utils/jwt.js';

/**
 * 认证控制器
 * 处理认证相关的HTTP请求
 */
export class AuthController {
  private authService: AuthService;
  private userRolesService: UserRolesService;

  constructor() {
    this.authService = new AuthService();
    this.userRolesService = new UserRolesService();
  }

  /**
   * 用户登录
   * POST /api/auth/login
   */
  public login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const loginData: LoginRequest = req.body;

    const result = await this.authService.login(loginData);

    const response: ApiResponse = {
      success: true,
      message: '登录成功',
      data: result,
    };

    res.json(response);
  });

  /**
   * 用户登出
   * POST /api/auth/logout
   */
  public logout = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    // 客户端应该删除token，服务端暂时不维护token黑名单
    const response: ApiResponse = {
      success: true,
      message: '登出成功',
    };

    res.json(response);
  });

  /**
   * 获取当前用户信息
   * GET /api/auth/me
   */
  public getCurrentUser = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证',
        code: 401,
      };
      res.status(401).json(response);
      return;
    }

    // 从请求头获取token以获取更详细信息
    const authHeader = req.headers.authorization;
    const token = JwtUtils.extractTokenFromHeader(authHeader);

    if (token) {
      const tokenInfo = await this.authService.validateToken(token);
      
      const response: ApiResponse = {
        success: true,
        data: tokenInfo,
      };
      res.json(response);
    } else {
      const response: ApiResponse = {
        success: true,
        data: {
          user: req.user,
        },
      };
      res.json(response);
    }
  });

  /**
   * 刷新token
   * POST /api/auth/refresh
   */
  public refreshToken = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const authHeader = req.headers.authorization;
    const token = JwtUtils.extractTokenFromHeader(authHeader);

    if (!token) {
      const response: ApiResponse = {
        success: false,
        error: '缺少认证token',
        code: 401,
      };
      res.status(401).json(response);
      return;
    }

    const result = await this.authService.refreshToken(token);

    const response: ApiResponse = {
      success: true,
      message: 'Token刷新成功',
      data: result,
    };

    res.json(response);
  });

  /**
   * 修改密码
   * POST /api/auth/change-password
   */
  public changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证',
        code: 401,
      };
      res.status(401).json(response);
      return;
    }

    const { oldPassword, newPassword } = req.body;

    if (!oldPassword || !newPassword) {
      const response: ApiResponse = {
        success: false,
        error: '旧密码和新密码不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    await this.authService.changePassword(req.user.id, oldPassword, newPassword);

    const response: ApiResponse = {
      success: true,
      message: '密码修改成功',
    };

    res.json(response);
  });

  /**
   * 检查用户名可用性
   * GET /api/auth/check-username/:username
   */
  public checkUsername = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { username } = req.params;

    if (!username) {
      const response: ApiResponse = {
        success: false,
        error: '用户名不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const isAvailable = await this.authService.isUsernameAvailable(username);

    const response: ApiResponse = {
      success: true,
      data: {
        username,
        available: isAvailable,
      },
    };

    res.json(response);
  });

  /**
   * 检查密码强度
   * POST /api/auth/check-password-strength
   */
  public checkPasswordStrength = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { password } = req.body;

    if (!password) {
      const response: ApiResponse = {
        success: false,
        error: '密码不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const strength = this.authService.getPasswordStrength(password);

    const response: ApiResponse = {
      success: true,
      data: strength,
    };

    res.json(response);
  });

  /**
   * 验证token
   * POST /api/auth/verify
   */
  public verifyToken = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { token } = req.body;
    const authHeader = req.headers.authorization;
    const tokenToVerify = token || JwtUtils.extractTokenFromHeader(authHeader);

    if (!tokenToVerify) {
      const response: ApiResponse = {
        success: false,
        error: '缺少token',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    try {
      const result = await this.authService.validateToken(tokenToVerify);

      const response: ApiResponse = {
        success: true,
        message: 'Token有效',
        data: result,
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Token无效或已过期',
        code: 401,
      };
      res.status(401).json(response);
    }
  });

  /**
   * 获取用户权限信息
   * GET /api/auth/permissions
   */
  public getUserPermissions = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证',
        code: 401,
      };
      res.status(401).json(response);
      return;
    }

    const permissions = await this.userRolesService.getUserPermissions(req.user.id);

    const response: ApiResponse = {
      success: true,
      data: permissions,
    };

    res.json(response);
  });

  /**
   * 检查用户权限
   * POST /api/auth/check-permission
   */
  public checkPermission = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证',
        code: 401,
      };
      res.status(401).json(response);
      return;
    }

    const { permission } = req.body;

    if (!permission) {
      const response: ApiResponse = {
        success: false,
        error: '权限名称不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const hasPermission = await this.userRolesService.hasPermission(req.user.id, permission);

    const response: ApiResponse = {
      success: true,
      data: {
        permission,
        hasPermission,
      },
    };

    res.json(response);
  });

  /**
   * 检查用户角色
   * POST /api/auth/check-role
   */
  public checkRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证',
        code: 401,
      };
      res.status(401).json(response);
      return;
    }

    const { role } = req.body;

    if (!role) {
      const response: ApiResponse = {
        success: false,
        error: '角色名称不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const hasRole = await this.userRolesService.hasRole(req.user.id, role);

    const response: ApiResponse = {
      success: true,
      data: {
        role,
        hasRole,
      },
    };

    res.json(response);
  });
}

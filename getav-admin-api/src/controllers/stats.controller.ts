import { Request, Response } from 'express';
import { StatsService } from '@/services/stats.service.js';
import { ApiResponse } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';

/**
 * 统计数据控制器
 * 处理统计数据相关的HTTP请求
 */
export class StatsController {
  private statsService: StatsService;

  constructor() {
    this.statsService = new StatsService();
  }

  /**
   * 获取系统总体统计数据
   * GET /api/stats/overview
   */
  public getOverviewStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.statsService.getOverallStats();

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 获取内容统计数据
   * GET /api/stats/content
   */
  public getContentStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.statsService.getContentStats();

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 获取时间趋势统计
   * GET /api/stats/trends
   */
  public getTrendStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const days = parseInt(req.query.days as string) || 30;

    if (days < 1 || days > 365) {
      const response: ApiResponse = {
        success: false,
        error: '天数范围应在1-365之间',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const stats = await this.statsService.getTrendStats(days);

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 获取分类统计
   * GET /api/stats/categories
   */
  public getCategoryStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.statsService.getCategoryStats();

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 获取用户活跃度统计
   * GET /api/stats/user-activity
   */
  public getUserActivityStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.statsService.getUserActivityStats();

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 获取系统健康状态
   * GET /api/stats/health
   */
  public getSystemHealth = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const health = await this.statsService.getSystemHealth();

    const response: ApiResponse = {
      success: true,
      data: health,
    };

    res.json(response);
  });

  /**
   * 获取仪表板数据（综合统计）
   * GET /api/stats/dashboard
   */
  public getDashboardStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const [
      overview,
      content,
      trends,
      userActivity,
    ] = await Promise.all([
      this.statsService.getOverallStats(),
      this.statsService.getContentStats(),
      this.statsService.getTrendStats(7), // 最近7天趋势
      this.statsService.getUserActivityStats(),
    ]);

    const response: ApiResponse = {
      success: true,
      data: {
        overview,
        content,
        trends,
        userActivity,
        timestamp: new Date().toISOString(),
      },
    };

    res.json(response);
  });
}

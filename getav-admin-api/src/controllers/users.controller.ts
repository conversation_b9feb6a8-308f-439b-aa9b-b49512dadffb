import { Request, Response } from 'express';
import { UsersService } from '@/services/users.service.js';
import { UserRolesService } from '@/services/user-roles.service.js';
import { ApiResponse, UserQueryParams, UserRoleData, AuthenticatedRequest } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';

/**
 * 用户管理控制器
 * 处理用户相关的HTTP请求
 */
export class UsersController {
  private usersService: UsersService;
  private userRolesService: UserRolesService;

  constructor() {
    this.usersService = new UsersService();
    this.userRolesService = new UserRolesService();
  }

  /**
   * 获取用户列表
   * GET /api/users
   */
  public getUsers = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const hasEmailQuery = req.query.hasEmail;
    const verifiedQuery = req.query.verified;
    const hasEmail = hasEmailQuery === 'true' ? true : hasEmailQuery === 'false' ? false : undefined;
    const verified = verifiedQuery === 'true' ? true : verifiedQuery === 'false' ? false : undefined;

    const params: UserQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      search: req.query.search as string,
      hasEmail,
      verified,
      sortBy: req.query.sortBy as string,
      sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
    };

    const result = await this.usersService.getUsers(params);

    const response: ApiResponse = {
      success: true,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取用户详情
   * GET /api/users/:id
   */
  public getUserById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const user = await this.usersService.getUserById(id);

    const response: ApiResponse = {
      success: true,
      data: user,
    };

    res.json(response);
  });

  /**
   * 更新用户信息
   * PUT /api/users/:id
   */
  public updateUser = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const updateData = req.body;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const updatedUser = await this.usersService.updateUser(id, updateData);

    const response: ApiResponse = {
      success: true,
      message: '用户信息更新成功',
      data: updatedUser,
    };

    res.json(response);
  });

  /**
   * 删除用户
   * DELETE /api/users/:id
   */
  public deleteUser = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    // 防止删除自己
    if (req.user && req.user.id === id) {
      const response: ApiResponse = {
        success: false,
        error: '不能删除自己的账户',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    await this.usersService.deleteUser(id);

    const response: ApiResponse = {
      success: true,
      message: '用户删除成功',
    };

    res.json(response);
  });

  /**
   * 批量删除用户
   * DELETE /api/users
   */
  public deleteUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: '请提供要删除的用户ID列表',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    // 防止删除自己
    if (req.user && ids.includes(req.user.id)) {
      const response: ApiResponse = {
        success: false,
        error: '不能删除自己的账户',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const result = await this.usersService.deleteUsers(ids);

    const response: ApiResponse = {
      success: true,
      message: `删除操作完成：成功 ${result.deleted} 个，失败 ${result.errors.length} 个`,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取用户统计信息
   * GET /api/users/stats
   */
  public getUserStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.usersService.getUserStats();

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 搜索用户
   * GET /api/users/search
   */
  public searchUsers = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query = req.query.q as string;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!query) {
      const response: ApiResponse = {
        success: false,
        error: '请提供搜索关键词',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const results = await this.usersService.searchUsers(query, limit);

    const response: ApiResponse = {
      success: true,
      data: results,
    };

    res.json(response);
  });

  /**
   * 获取用户角色
   * GET /api/users/:id/roles
   */
  public getUserRoles = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const roles = await this.userRolesService.getUserRoles(id);

    const response: ApiResponse = {
      success: true,
      data: roles,
    };

    res.json(response);
  });

  /**
   * 获取用户权限
   * GET /api/users/:id/permissions
   */
  public getUserPermissions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const permissions = await this.userRolesService.getUserPermissions(id);

    const response: ApiResponse = {
      success: true,
      data: permissions,
    };

    res.json(response);
  });

  /**
   * 设置用户角色
   * PUT /api/users/:id/roles
   */
  public setUserRoles = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const { roleIds, expiresAt } = req.body;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    if (!Array.isArray(roleIds)) {
      const response: ApiResponse = {
        success: false,
        error: '角色ID列表格式错误',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const data: UserRoleData = {
      userId: id,
      roleIds,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
    };

    const roles = await this.userRolesService.setUserRoles(data, req.user?.id);

    const response: ApiResponse = {
      success: true,
      message: '用户角色设置成功',
      data: roles,
    };

    res.json(response);
  });

  /**
   * 添加用户角色
   * POST /api/users/:id/roles
   */
  public addUserRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const { roleId, expiresAt } = req.body;

    if (!id || !roleId) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID和角色ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const userRole = await this.userRolesService.addUserRole(
      id,
      roleId,
      req.user?.id,
      expiresAt ? new Date(expiresAt) : undefined
    );

    const response: ApiResponse = {
      success: true,
      message: '用户角色添加成功',
      data: userRole,
    };

    res.status(201).json(response);
  });

  /**
   * 移除用户角色
   * DELETE /api/users/:id/roles/:roleId
   */
  public removeUserRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id, roleId } = req.params;

    if (!id || !roleId) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID和角色ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    await this.userRolesService.removeUserRole(id, roleId);

    const response: ApiResponse = {
      success: true,
      message: '用户角色移除成功',
    };

    res.json(response);
  });
}

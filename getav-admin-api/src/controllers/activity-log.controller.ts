import { Request, Response } from 'express';
import { ActivityLogService } from '@/services/activity-log.service.js';
import { ApiResponse, AuthenticatedRequest } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';
import { ActivityAction, ActivityTargetType } from '@prisma/client';

/**
 * 用户行为日志控制器
 * 处理用户行为日志相关的HTTP请求
 */
export class ActivityLogController {
  private activityLogService: ActivityLogService;

  constructor() {
    this.activityLogService = new ActivityLogService();
  }

  /**
   * 记录用户行为日志
   * POST /api/activity-logs
   */
  public createActivityLog = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const logData = req.body;

    // 自动获取IP地址和用户代理
    if (!logData.ipAddress) {
      logData.ipAddress = req.ip || req.connection?.remoteAddress || null;
    }
    if (!logData.userAgent) {
      logData.userAgent = req.get('User-Agent') || null;
    }

    const activityLog = await this.activityLogService.createActivityLog(logData);

    const response: ApiResponse = {
      success: true,
      message: '用户行为日志记录成功',
      data: activityLog,
    };

    res.status(201).json(response);
  });

  /**
   * 获取用户行为日志列表
   * GET /api/activity-logs
   */
  public getActivityLogs = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const queryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      search: req.query.search as string,
      userId: req.query.userId as string,
      action: req.query.action as string,
      targetType: req.query.targetType as string,
      targetId: req.query.targetId as string,
      ipAddress: req.query.ipAddress as string,
      dateFrom: req.query.dateFrom as string,
      dateTo: req.query.dateTo as string,
      sortBy: req.query.sortBy as string,
      sortOrder: req.query.sortOrder as 'asc' | 'desc',
    };

    const result = await this.activityLogService.getActivityLogs(queryParams);

    const response: ApiResponse = {
      success: true,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取用户行为日志详情
   * GET /api/activity-logs/:id
   */
  public getActivityLogById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '日志ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const activityLog = await this.activityLogService.getActivityLogById(id);

    const response: ApiResponse = {
      success: true,
      data: activityLog,
    };

    res.json(response);
  });

  /**
   * 导出用户行为日志
   * GET /api/activity-logs/export
   */
  public exportActivityLogs = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const options = {
      format: req.query.format as 'csv' | 'json' || 'csv',
      dateFrom: req.query.dateFrom as string,
      dateTo: req.query.dateTo as string,
      userId: req.query.userId as string,
      action: req.query.action as string,
      targetType: req.query.targetType as string,
    };

    const result = await this.activityLogService.exportActivityLogs(options);

    // 设置响应头
    const filename = `activity_logs_${Date.now()}.${options.format}`;
    const contentType = options.format === 'csv' ? 'text/csv' : 'application/json';
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    if (options.format === 'csv') {
      res.send(result);
    } else {
      res.json(result);
    }
  });

  /**
   * 清理过期的用户行为日志
   * POST /api/activity-logs/cleanup
   */
  public cleanupOldLogs = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { daysToKeep = 90 } = req.body;

    const deletedCount = await this.activityLogService.cleanupOldLogs(daysToKeep);

    const response: ApiResponse = {
      success: true,
      message: `清理完成，删除了 ${deletedCount} 条过期日志`,
      data: { deletedCount, daysToKeep },
    };

    res.json(response);
  });

  /**
   * 获取用户行为统计数据
   * GET /api/activity-logs/stats
   */
  public getActivityStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const days = parseInt(req.query.days as string) || 7;

    const stats = await this.activityLogService.getActivityStats(days);

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 获取用户的行为统计
   * GET /api/activity-logs/user/:userId/stats
   */
  public getUserActivityStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { userId } = req.params;
    const days = parseInt(req.query.days as string) || 30;

    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: '用户ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const stats = await this.activityLogService.getUserActivityStats(userId, days);

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 获取操作类型列表
   * GET /api/activity-logs/actions
   */
  public getActionTypes = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    // 从 Prisma 枚举中获取操作类型
    const actionTypes = [
      'LOGIN', 'LOGOUT', 'REGISTER',
      'VIEW_MOVIE', 'VIEW_STAR', 'VIEW_GENRE', 'VIEW_PRODUCER', 'VIEW_DIRECTOR', 'VIEW_SERIES',
      'SEARCH',
      'LIKE', 'UNLIKE', 'COMMENT', 'DELETE_COMMENT', 'FAVORITE', 'UNFAVORITE', 'RATE', 'UPDATE_RATING',
      'REPORT', 'DOWNLOAD_MAGNET', 'SHARE'
    ];

    const response: ApiResponse = {
      success: true,
      data: actionTypes,
    };

    res.json(response);
  });

  /**
   * 获取目标类型列表
   * GET /api/activity-logs/targets
   */
  public getTargetTypes = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    // 从 Prisma 枚举中获取目标类型
    const targetTypes = [
      'MOVIE', 'STAR', 'GENRE', 'PRODUCER', 'DIRECTOR', 'SERIES',
      'COMMENT', 'MAGNET', 'USER', 'SEARCH'
    ];

    const response: ApiResponse = {
      success: true,
      data: targetTypes,
    };

    res.json(response);
  });
}

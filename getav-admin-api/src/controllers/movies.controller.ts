import { Request, Response } from 'express';
import { MoviesService } from '@/services/movies.service.js';
import { ApiResponse, MovieQueryParams, AuthenticatedRequest } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';

/**
 * 影片管理控制器
 * 处理影片相关的HTTP请求
 */
export class MoviesController {
  private moviesService: MoviesService;

  constructor() {
    this.moviesService = new MoviesService();
  }

  /**
   * 获取影片列表
   * GET /api/movies
   */
  public getMovies = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const params: MovieQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      search: req.query.search as string,
      genre: req.query.genre as string,
      star: req.query.star as string,
      director: req.query.director as string,
      producer: req.query.producer as string,
      dateFrom: req.query.dateFrom as string,
      dateTo: req.query.dateTo as string,
      sortBy: req.query.sortBy as string,
      sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',

      // 新增筛选参数
      censored: req.query.censored as 'censored' | 'uncensored' | 'all' | undefined,
      hasSubtitle: req.query.hasSubtitle === 'true' ? true : req.query.hasSubtitle === 'false' ? false : undefined,
      durationMin: req.query.durationMin ? parseInt(req.query.durationMin as string) : undefined,
      durationMax: req.query.durationMax ? parseInt(req.query.durationMax as string) : undefined,
      releaseDateFrom: req.query.releaseDateFrom as string | undefined,
      releaseDateTo: req.query.releaseDateTo as string | undefined,
      popularityType: req.query.popularityType as 'daily' | 'weekly' | 'monthly' | 'total' | undefined,
    };

    const result = await this.moviesService.getMovies(params);

    const response: ApiResponse = {
      success: true,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取影片详情
   * GET /api/movies/:id
   */
  public getMovieById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '影片ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const movie = await this.moviesService.getMovieById(id);

    const response: ApiResponse = {
      success: true,
      data: movie,
    };

    res.json(response);
  });

  /**
   * 更新影片信息
   * PUT /api/movies/:id
   */
  public updateMovie = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const updateData = req.body;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '影片ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const updatedMovie = await this.moviesService.updateMovie(id, updateData);

    const response: ApiResponse = {
      success: true,
      message: '影片信息更新成功',
      data: updatedMovie,
    };

    res.json(response);
  });

  /**
   * 删除影片
   * DELETE /api/movies/:id
   */
  public deleteMovie = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '影片ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    await this.moviesService.deleteMovie(id);

    const response: ApiResponse = {
      success: true,
      message: '影片删除成功',
    };

    res.json(response);
  });

  /**
   * 批量删除影片
   * DELETE /api/movies
   */
  public deleteMovies = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: '请提供要删除的影片ID列表',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const result = await this.moviesService.deleteMovies(ids);

    const response: ApiResponse = {
      success: true,
      message: `成功删除 ${result.deleted} 部影片`,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取影片统计信息
   * GET /api/movies/stats
   */
  public getMovieStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.moviesService.getMovieStats();

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });
}

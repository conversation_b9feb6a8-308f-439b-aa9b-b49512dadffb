import { Request, Response } from 'express';
import { RolesService } from '@/services/roles.service.js';
import { ApiResponse, RoleQueryParams, RoleData, RolePermissionData, AuthenticatedRequest } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';

/**
 * 角色管理控制器
 * 处理角色相关的HTTP请求
 */
export class RolesController {
  private rolesService: RolesService;

  constructor() {
    this.rolesService = new RolesService();
  }

  /**
   * 获取角色列表
   * GET /api/roles
   */
  public getRoles = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const isActiveQuery = req.query.isActive;
    const isSystemQuery = req.query.isSystem;
    const isActive = isActiveQuery === 'true' ? true : isActiveQuery === 'false' ? false : undefined;
    const isSystem = isSystemQuery === 'true' ? true : isSystemQuery === 'false' ? false : undefined;

    const params: RoleQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      search: req.query.search as string,
      isActive,
      isSystem,
      sortBy: req.query.sortBy as string,
      sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
    };

    const result = await this.rolesService.getRoles(params);

    const response: ApiResponse = {
      success: true,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取角色详情
   * GET /api/roles/:id
   */
  public getRoleById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '角色ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const role = await this.rolesService.getRoleById(id);

    const response: ApiResponse = {
      success: true,
      data: role,
    };

    res.json(response);
  });

  /**
   * 创建角色
   * POST /api/roles
   */
  public createRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const roleData: RoleData = req.body;

    // 验证必填字段
    if (!roleData.name || !roleData.displayName) {
      const response: ApiResponse = {
        success: false,
        error: '角色名称和显示名称不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const newRole = await this.rolesService.createRole(roleData, req.user?.id);

    const response: ApiResponse = {
      success: true,
      message: '角色创建成功',
      data: newRole,
    };

    res.status(201).json(response);
  });

  /**
   * 更新角色
   * PUT /api/roles/:id
   */
  public updateRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const roleData: Partial<RoleData> = req.body;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '角色ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const updatedRole = await this.rolesService.updateRole(id, roleData);

    const response: ApiResponse = {
      success: true,
      message: '角色更新成功',
      data: updatedRole,
    };

    res.json(response);
  });

  /**
   * 删除角色
   * DELETE /api/roles/:id
   */
  public deleteRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '角色ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    await this.rolesService.deleteRole(id);

    const response: ApiResponse = {
      success: true,
      message: '角色删除成功',
    };

    res.json(response);
  });

  /**
   * 获取角色权限
   * GET /api/roles/:id/permissions
   */
  public getRolePermissions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '角色ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const permissions = await this.rolesService.getRolePermissions(id);

    const response: ApiResponse = {
      success: true,
      data: permissions,
    };

    res.json(response);
  });

  /**
   * 设置角色权限
   * PUT /api/roles/:id/permissions
   */
  public setRolePermissions = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const { permissionIds } = req.body;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '角色ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    if (!Array.isArray(permissionIds)) {
      const response: ApiResponse = {
        success: false,
        error: '权限ID列表格式错误',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const data: RolePermissionData = {
      roleId: id,
      permissionIds,
    };

    const permissions = await this.rolesService.setRolePermissions(data, req.user?.id);

    const response: ApiResponse = {
      success: true,
      message: '角色权限设置成功',
      data: permissions,
    };

    res.json(response);
  });

  /**
   * 获取所有角色（用于下拉选择）
   * GET /api/roles/all
   */
  public getAllRoles = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const roles = await this.rolesService.getAllRoles();

    const response: ApiResponse = {
      success: true,
      data: roles,
    };

    res.json(response);
  });
}

import { Request, Response } from 'express';
import { PermissionsService } from '@/services/permissions.service.js';
import { ApiResponse, PermissionQueryParams, PermissionData, AuthenticatedRequest } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';

/**
 * 权限管理控制器
 * 处理权限相关的HTTP请求
 */
export class PermissionsController {
  private permissionsService: PermissionsService;

  constructor() {
    this.permissionsService = new PermissionsService();
  }

  /**
   * 获取权限列表
   * GET /api/permissions
   */
  public getPermissions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const isActiveQuery = req.query.isActive;
    const isActive = isActiveQuery === 'true' ? true : isActiveQuery === 'false' ? false : undefined;

    const params: PermissionQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      search: req.query.search as string,
      type: req.query.type as string,
      resource: req.query.resource as string,
      isActive,
      sortBy: req.query.sortBy as string,
      sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'asc',
    };

    const result = await this.permissionsService.getPermissions(params);

    const response: ApiResponse = {
      success: true,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取权限详情
   * GET /api/permissions/:id
   */
  public getPermissionById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '权限ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const permission = await this.permissionsService.getPermissionById(id);

    const response: ApiResponse = {
      success: true,
      data: permission,
    };

    res.json(response);
  });

  /**
   * 创建权限
   * POST /api/permissions
   */
  public createPermission = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const permissionData: PermissionData = req.body;

    // 验证必填字段
    if (!permissionData.name || !permissionData.displayName || !permissionData.resource || !permissionData.action) {
      const response: ApiResponse = {
        success: false,
        error: '权限名称、显示名称、资源和操作不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const newPermission = await this.permissionsService.createPermission(permissionData);

    const response: ApiResponse = {
      success: true,
      message: '权限创建成功',
      data: newPermission,
    };

    res.status(201).json(response);
  });

  /**
   * 更新权限
   * PUT /api/permissions/:id
   */
  public updatePermission = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const permissionData: Partial<PermissionData> = req.body;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '权限ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const updatedPermission = await this.permissionsService.updatePermission(id, permissionData);

    const response: ApiResponse = {
      success: true,
      message: '权限更新成功',
      data: updatedPermission,
    };

    res.json(response);
  });

  /**
   * 删除权限
   * DELETE /api/permissions/:id
   */
  public deletePermission = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '权限ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    await this.permissionsService.deletePermission(id);

    const response: ApiResponse = {
      success: true,
      message: '权限删除成功',
    };

    res.json(response);
  });

  /**
   * 获取权限树结构
   * GET /api/permissions/tree
   */
  public getPermissionTree = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const tree = await this.permissionsService.getPermissionTree();

    const response: ApiResponse = {
      success: true,
      data: tree,
    };

    res.json(response);
  });

  /**
   * 获取权限资源列表
   * GET /api/permissions/resources
   */
  public getPermissionResources = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const resources = await this.permissionsService.getPermissionResources();

    const response: ApiResponse = {
      success: true,
      data: resources,
    };

    res.json(response);
  });

  /**
   * 获取权限操作列表
   * GET /api/permissions/actions
   */
  public getPermissionActions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const resource = req.query.resource as string;
    const actions = await this.permissionsService.getPermissionActions(resource);

    const response: ApiResponse = {
      success: true,
      data: actions,
    };

    res.json(response);
  });

  /**
   * 获取所有权限（用于下拉选择）
   * GET /api/permissions/all
   */
  public getAllPermissions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const permissions = await this.permissionsService.getAllPermissions();

    const response: ApiResponse = {
      success: true,
      data: permissions,
    };

    res.json(response);
  });
}

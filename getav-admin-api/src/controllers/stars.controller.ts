import { Request, Response } from 'express';
import { StarsService } from '@/services/stars.service.js';
import { ApiResponse, StarQueryParams, AuthenticatedRequest } from '@/types/index.js';
import { asyncHandler } from '@/middleware/error.js';

/**
 * 演员管理控制器
 * 处理演员相关的HTTP请求
 */
export class StarsController {
  private starsService: StarsService;

  constructor() {
    this.starsService = new StarsService();
  }

  /**
   * 获取演员列表
   * GET /api/stars
   */
  public getStars = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const hasAvatarQuery = req.query.hasAvatar;
    const hasAvatar = hasAvatarQuery === 'true' ? true : hasAvatarQuery === 'false' ? false : undefined;

    const params: StarQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      search: req.query.search as string,
      hasAvatar,
      sortBy: req.query.sortBy as string,
      sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
    };

    const result = await this.starsService.getStars(params);

    const response: ApiResponse = {
      success: true,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取演员详情
   * GET /api/stars/:id
   */
  public getStarById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '演员ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const star = await this.starsService.getStarById(id);

    const response: ApiResponse = {
      success: true,
      data: star,
    };

    res.json(response);
  });

  /**
   * 更新演员信息
   * PUT /api/stars/:id
   */
  public updateStar = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const updateData = req.body;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '演员ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const updatedStar = await this.starsService.updateStar(id, updateData);

    const response: ApiResponse = {
      success: true,
      message: '演员信息更新成功',
      data: updatedStar,
    };

    res.json(response);
  });

  /**
   * 删除演员
   * DELETE /api/stars/:id
   */
  public deleteStar = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '演员ID不能为空',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    await this.starsService.deleteStar(id);

    const response: ApiResponse = {
      success: true,
      message: '演员删除成功',
    };

    res.json(response);
  });

  /**
   * 批量删除演员
   * DELETE /api/stars
   */
  public deleteStars = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: '请提供要删除的演员ID列表',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const result = await this.starsService.deleteStars(ids);

    const response: ApiResponse = {
      success: true,
      message: `删除操作完成：成功 ${result.deleted} 个，失败 ${result.errors.length} 个`,
      data: result,
    };

    res.json(response);
  });

  /**
   * 获取演员统计信息
   * GET /api/stars/stats
   */
  public getStarStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.starsService.getStarStats();

    const response: ApiResponse = {
      success: true,
      data: stats,
    };

    res.json(response);
  });

  /**
   * 搜索演员
   * GET /api/stars/search
   */
  public searchStars = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query = req.query.q as string;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!query) {
      const response: ApiResponse = {
        success: false,
        error: '请提供搜索关键词',
        code: 400,
      };
      res.status(400).json(response);
      return;
    }

    const results = await this.starsService.searchStars(query, limit);

    const response: ApiResponse = {
      success: true,
      data: results,
    };

    res.json(response);
  });
}

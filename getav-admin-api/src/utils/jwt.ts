import jwt from 'jsonwebtoken';
import { config } from './config.js';
import { ApiError, ErrorCode, JwtPayload } from '@/types/index.js';

/**
 * JWT工具类
 * 提供token生成、验证、解析等功能
 */
export class JwtUtils {
  /**
   * 生成JWT token
   */
  public static generateToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    try {
      const tokenPayload: JwtPayload = {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
      };

      const token = jwt.sign(tokenPayload, config.JWT_SECRET, {
        expiresIn: config.JWT_EXPIRES_IN,
        issuer: 'getav-admin-api',
        audience: 'getav-admin',
      } as jwt.SignOptions);

      return token;
    } catch (error) {
      console.error('生成JWT token失败:', error);
      throw new ApiError(
        '生成认证token失败',
        ErrorCode.INTERNAL_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 验证JWT token
   */
  public static verifyToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, config.JWT_SECRET, {
        issuer: 'getav-admin-api',
        audience: 'getav-admin',
      }) as JwtPayload;

      return decoded;
    } catch (error) {
      console.error('验证JWT token失败:', error);

      if (error instanceof jwt.TokenExpiredError) {
        throw new ApiError(
          'Token已过期',
          ErrorCode.AUTHENTICATION_ERROR,
          401,
          { expiredAt: error.expiredAt }
        );
      }

      if (error instanceof jwt.JsonWebTokenError) {
        throw new ApiError(
          'Token无效',
          ErrorCode.AUTHENTICATION_ERROR,
          401,
          { originalError: error.message }
        );
      }

      throw new ApiError(
        'Token验证失败',
        ErrorCode.AUTHENTICATION_ERROR,
        401,
        { originalError: error }
      );
    }
  }

  /**
   * 解析JWT token（不验证签名）
   */
  public static decodeToken(token: string): JwtPayload | null {
    try {
      const decoded = jwt.decode(token) as JwtPayload;
      return decoded;
    } catch (error) {
      console.error('解析JWT token失败:', error);
      return null;
    }
  }

  /**
   * 检查token是否即将过期
   */
  public static isTokenExpiringSoon(token: string, thresholdMinutes: number = 30): boolean {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) {
        return true;
      }

      const now = Math.floor(Date.now() / 1000);
      const threshold = thresholdMinutes * 60;
      
      return (decoded.exp - now) < threshold;
    } catch (error) {
      console.error('检查token过期时间失败:', error);
      return true;
    }
  }

  /**
   * 刷新token
   */
  public static refreshToken(token: string): string {
    try {
      const decoded = this.verifyToken(token);
      
      // 移除时间相关字段，重新生成
      const { iat, exp, ...payload } = decoded;
      
      return this.generateToken(payload);
    } catch (error) {
      console.error('刷新token失败:', error);
      throw new ApiError(
        '刷新token失败',
        ErrorCode.AUTHENTICATION_ERROR,
        401,
        { originalError: error }
      );
    }
  }

  /**
   * 从请求头中提取token
   */
  public static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) {
      return null;
    }

    // 支持 "Bearer token" 和 "token" 两种格式
    const bearerMatch = authHeader.match(/^Bearer\s+(.+)$/i);
    if (bearerMatch) {
      return bearerMatch[1] || null;
    }

    // 直接返回token（兼容性处理）
    return authHeader || null;
  }

  /**
   * 生成token响应数据
   */
  public static createTokenResponse(user: {
    id: string;
    username: string;
    email?: string | undefined;
    role?: string | undefined;
  }) {
    const token = this.generateToken({
      id: user.id,
      username: user.username,
      email: user.email || undefined,
      role: user.role || 'admin',
    });

    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email || undefined,
        role: user.role || 'admin',
      },
      expiresIn: config.JWT_EXPIRES_IN,
      tokenType: 'Bearer',
    };
  }

  /**
   * 验证token格式
   */
  public static isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // JWT token应该有三个部分，用.分隔
    const parts = token.split('.');
    return parts.length === 3;
  }

  /**
   * 获取token剩余有效时间（秒）
   */
  public static getTokenRemainingTime(token: string): number {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) {
        return 0;
      }

      const now = Math.floor(Date.now() / 1000);
      const remaining = decoded.exp - now;
      
      return Math.max(0, remaining);
    } catch (error) {
      console.error('获取token剩余时间失败:', error);
      return 0;
    }
  }
}

import bcrypt from 'bcryptjs';
import { ApiError, ErrorCode } from '@/types/index.js';

/**
 * 密码加密工具类
 * 提供密码哈希、验证等功能
 */
export class PasswordUtils {
  // 盐值轮数，越高越安全但越慢
  private static readonly SALT_ROUNDS = 12;

  /**
   * 哈希密码
   */
  public static async hashPassword(password: string): Promise<string> {
    try {
      if (!password || password.length < 1) {
        throw new ApiError(
          '密码不能为空',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      // 验证密码强度
      this.validatePasswordStrength(password);

      const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS);
      return hashedPassword;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      console.error('密码哈希失败:', error);
      throw new ApiError(
        '密码加密失败',
        ErrorCode.INTERNAL_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 验证密码
   */
  public static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      if (!password || !hashedPassword) {
        return false;
      }

      const isValid = await bcrypt.compare(password, hashedPassword);
      return isValid;
    } catch (error) {
      console.error('密码验证失败:', error);
      throw new ApiError(
        '密码验证失败',
        ErrorCode.INTERNAL_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 验证密码强度
   */
  public static validatePasswordStrength(password: string): void {
    if (!password) {
      throw new ApiError(
        '密码不能为空',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    if (password.length < 6) {
      throw new ApiError(
        '密码长度至少6个字符',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    if (password.length > 128) {
      throw new ApiError(
        '密码长度不能超过128个字符',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    // 检查是否包含常见弱密码
    const weakPasswords = [
      '123456',
      'password',
      '123456789',
      '12345678',
      '12345',
      '1234567',
      '1234567890',
      'qwerty',
      'abc123',
      'password123',
      'admin',
      'root',
      'user',
      'guest',
    ];

    if (weakPasswords.includes(password.toLowerCase())) {
      throw new ApiError(
        '密码过于简单，请使用更复杂的密码',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }
  }

  /**
   * 生成随机密码
   */
  public static generateRandomPassword(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  /**
   * 检查密码是否需要重新哈希（盐值轮数变化时）
   */
  public static needsRehash(hashedPassword: string): boolean {
    try {
      // 获取当前哈希的盐值轮数
      const rounds = bcrypt.getRounds(hashedPassword);
      return rounds < this.SALT_ROUNDS;
    } catch (error) {
      console.error('检查密码重哈希需求失败:', error);
      return true; // 出错时建议重新哈希
    }
  }

  /**
   * 密码强度评分（1-5分）
   */
  public static getPasswordStrength(password: string): {
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    if (!password) {
      return { score: 0, feedback: ['密码不能为空'] };
    }

    // 长度检查
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('密码长度至少8个字符');
    }

    // 包含小写字母
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('应包含小写字母');
    }

    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('应包含大写字母');
    }

    // 包含数字
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('应包含数字');
    }

    // 包含特殊字符
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 1;
    } else {
      feedback.push('应包含特殊字符');
    }

    // 额外检查
    if (password.length >= 12) {
      score = Math.min(5, score + 1);
    }

    if (feedback.length === 0) {
      feedback.push('密码强度良好');
    }

    return { score: Math.max(1, score), feedback };
  }

  /**
   * 生成密码重置token
   */
  public static generateResetToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    
    for (let i = 0; i < 32; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return token;
  }
}

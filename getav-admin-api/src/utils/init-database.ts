import bcrypt from 'bcryptjs';
import { prisma } from './database.js';
import { config } from './config.js';
import { ApiError, ErrorCode } from '@/types/index.js';

/**
 * 数据库初始化工具
 */
export class DatabaseInitializer {
  /**
   * 初始化数据库
   */
  public static async initialize(): Promise<void> {
    try {
      console.log('🚀 开始初始化数据库...');

      // 创建默认管理员账户
      await DatabaseInitializer.createDefaultAdmin();

      // 验证数据库结构
      await DatabaseInitializer.validateDatabaseStructure();

      console.log('✅ 数据库初始化完成');
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error);
      throw new ApiError(
        '数据库初始化失败',
        ErrorCode.DATABASE_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 创建默认管理员账户
   */
  private static async createDefaultAdmin(): Promise<void> {
    try {
      // 检查是否已存在管理员账户
      const existingAdmin = await prisma.user.findFirst({
        where: {
          OR: [
            { username: config.ADMIN_USERNAME },
            { email: config.ADMIN_EMAIL },
          ],
        },
      });

      if (existingAdmin) {
        console.log('📋 管理员账户已存在，跳过创建');
        return;
      }

      // 创建管理员用户
      const hashedPassword = await bcrypt.hash(config.ADMIN_PASSWORD, 12);

      const adminUser = await prisma.user.create({
        data: {
          username: config.ADMIN_USERNAME,
          email: config.ADMIN_EMAIL,
          name: '系统管理员',
          emailVerified: new Date(),
          password: {
            create: {
              passwordHash: hashedPassword,
            },
          },
        },
        include: {
          password: true,
        },
      });

      console.log('✅ 默认管理员账户创建成功:', {
        id: adminUser.id,
        username: adminUser.username,
        email: adminUser.email,
      });
    } catch (error) {
      console.error('❌ 创建默认管理员账户失败:', error);
      throw error;
    }
  }

  /**
   * 验证数据库结构
   */
  private static async validateDatabaseStructure(): Promise<void> {
    try {
      // 检查主要表是否存在并可访问
      const tables = [
        { name: 'movies', count: () => prisma.movie.count() },
        { name: 'stars', count: () => prisma.star.count() },
        { name: 'users', count: () => prisma.user.count() },
        { name: 'genres', count: () => prisma.genre.count() },
      ];

      for (const table of tables) {
        try {
          await table.count();
          console.log(`✅ 表 ${table.name} 验证成功`);
        } catch (error) {
          console.error(`❌ 表 ${table.name} 验证失败:`, error);
          throw new ApiError(
            `数据库表 ${table.name} 不可访问`,
            ErrorCode.DATABASE_ERROR,
            500
          );
        }
      }
    } catch (error) {
      console.error('❌ 数据库结构验证失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据库统计信息
   */
  public static async getDatabaseStats(): Promise<{
    movies: number;
    stars: number;
    users: number;
    genres: number;
    magnets: number;
    comments: number;
  }> {
    try {
      const [movies, stars, users, genres, magnets, comments] = await Promise.all([
        prisma.movie.count(),
        prisma.star.count(),
        prisma.user.count(),
        prisma.genre.count(),
        prisma.magnet.count(),
        prisma.comment.count(),
      ]);

      return {
        movies,
        stars,
        users,
        genres,
        magnets,
        comments,
      };
    } catch (error) {
      console.error('❌ 获取数据库统计信息失败:', error);
      throw new ApiError(
        '获取数据库统计信息失败',
        ErrorCode.DATABASE_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 清理数据库（仅开发环境）
   */
  public static async cleanDatabase(): Promise<void> {
    if (config.NODE_ENV === 'production') {
      throw new ApiError(
        '生产环境不允许清理数据库',
        ErrorCode.AUTHORIZATION_ERROR,
        403
      );
    }

    try {
      console.log('🧹 开始清理数据库...');

      // 按依赖关系顺序删除数据
      await prisma.comment.deleteMany();
      await prisma.like.deleteMany();
      await prisma.favorite.deleteMany();
      await prisma.rating.deleteMany();
      await prisma.view.deleteMany();
      await prisma.report.deleteMany();
      await prisma.magnet.deleteMany();
      await prisma.sample.deleteMany();
      await prisma.movieGenre.deleteMany();
      await prisma.movieStar.deleteMany();
      await prisma.similarMovie.deleteMany();
      await prisma.relatedMovie.deleteMany();
      await prisma.movie.deleteMany();
      await prisma.star.deleteMany();
      await prisma.genre.deleteMany();
      await prisma.director.deleteMany();
      await prisma.producer.deleteMany();
      await prisma.publisher.deleteMany();
      await prisma.series.deleteMany();
      await prisma.userPassword.deleteMany();
      await prisma.account.deleteMany();
      await prisma.session.deleteMany();
      await prisma.user.deleteMany();

      console.log('✅ 数据库清理完成');
    } catch (error) {
      console.error('❌ 数据库清理失败:', error);
      throw new ApiError(
        '数据库清理失败',
        ErrorCode.DATABASE_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 重置数据库（清理后重新初始化）
   */
  public static async resetDatabase(): Promise<void> {
    if (config.NODE_ENV === 'production') {
      throw new ApiError(
        '生产环境不允许重置数据库',
        ErrorCode.AUTHORIZATION_ERROR,
        403
      );
    }

    try {
      console.log('🔄 开始重置数据库...');
      
      await DatabaseInitializer.cleanDatabase();
      await DatabaseInitializer.initialize();
      
      console.log('✅ 数据库重置完成');
    } catch (error) {
      console.error('❌ 数据库重置失败:', error);
      throw error;
    }
  }
}

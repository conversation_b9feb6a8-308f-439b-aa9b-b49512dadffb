import { PrismaClient } from '@prisma/client';
import { ApiError, ErrorCode } from '@/types/index.js';

/**
 * Prisma客户端单例实例
 * 确保整个应用中只有一个数据库连接实例
 */
class DatabaseConnection {
  private static instance: PrismaClient | null = null;
  private static isConnected = false;

  /**
   * 获取Prisma客户端实例
   */
  public static getInstance(): PrismaClient {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
        errorFormat: 'pretty',
      });

      // 添加连接事件监听
      DatabaseConnection.setupEventListeners();
    }

    return DatabaseConnection.instance;
  }

  /**
   * 设置数据库事件监听器
   */
  private static setupEventListeners(): void {
    if (!DatabaseConnection.instance) return;

    // 设置进程退出时的清理
    process.on('beforeExit', async () => {
      console.log('数据库连接即将关闭...');
      DatabaseConnection.isConnected = false;
      await DatabaseConnection.disconnect();
    });
  }

  /**
   * 连接数据库
   */
  public static async connect(): Promise<void> {
    try {
      const prisma = DatabaseConnection.getInstance();
      
      // 测试数据库连接
      await prisma.$connect();
      
      // 验证数据库连接
      await prisma.$queryRaw`SELECT 1`;
      
      DatabaseConnection.isConnected = true;
      console.log('✅ 数据库连接成功');
      
    } catch (error) {
      DatabaseConnection.isConnected = false;
      console.error('❌ 数据库连接失败:', error);
      
      throw new ApiError(
        '数据库连接失败',
        ErrorCode.DATABASE_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 断开数据库连接
   */
  public static async disconnect(): Promise<void> {
    try {
      if (DatabaseConnection.instance) {
        await DatabaseConnection.instance.$disconnect();
        DatabaseConnection.instance = null;
        DatabaseConnection.isConnected = false;
        console.log('✅ 数据库连接已断开');
      }
    } catch (error) {
      console.error('❌ 断开数据库连接时出错:', error);
      throw new ApiError(
        '断开数据库连接失败',
        ErrorCode.DATABASE_ERROR,
        500,
        { originalError: error }
      );
    }
  }

  /**
   * 检查数据库连接状态
   */
  public static isConnectionActive(): boolean {
    return DatabaseConnection.isConnected;
  }

  /**
   * 健康检查
   */
  public static async healthCheck(): Promise<boolean> {
    try {
      const prisma = DatabaseConnection.getInstance();
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('数据库健康检查失败:', error);
      return false;
    }
  }

  /**
   * 获取数据库统计信息
   */
  public static async getDatabaseStats(): Promise<{
    totalMovies: number;
    totalStars: number;
    totalUsers: number;
    totalGenres: number;
  }> {
    try {
      const prisma = DatabaseConnection.getInstance();
      
      const [totalMovies, totalStars, totalUsers, totalGenres] = await Promise.all([
        prisma.movie.count(),
        prisma.star.count(),
        prisma.user.count(),
        prisma.genre.count(),
      ]);

      return {
        totalMovies,
        totalStars,
        totalUsers,
        totalGenres,
      };
    } catch (error) {
      console.error('获取数据库统计信息失败:', error);
      throw new ApiError(
        '获取数据库统计信息失败',
        ErrorCode.DATABASE_ERROR,
        500,
        { originalError: error }
      );
    }
  }
}

// 导出Prisma客户端实例
export const prisma = DatabaseConnection.getInstance();

// 导出数据库连接管理类
export default DatabaseConnection;

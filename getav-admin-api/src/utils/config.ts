import dotenv from 'dotenv';
import { EnvConfig, ApiError, ErrorCode } from '@/types/index.js';

// 加载环境变量
dotenv.config();

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars(): void {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'PORT'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new ApiError(
      `缺少必需的环境变量: ${missingVars.join(', ')}`,
      ErrorCode.VALIDATION_ERROR,
      500
    );
  }
}

/**
 * 解析数字类型的环境变量
 */
function parseNumber(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 解析布尔类型的环境变量
 */
function parseBoolean(value: string | undefined, defaultValue: boolean): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

/**
 * 获取配置对象
 */
function getConfig(): EnvConfig {
  // 验证必需的环境变量
  validateRequiredEnvVars();

  return {
    // 服务器配置
    PORT: parseNumber(process.env.PORT, 8000),
    NODE_ENV: process.env.NODE_ENV || 'development',

    // 数据库配置
    DATABASE_URL: process.env.DATABASE_URL!,

    // JWT配置
    JWT_SECRET: process.env.JWT_SECRET!,
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',

    // CORS配置
    CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:4000',

    // 管理员配置
    ADMIN_USERNAME: process.env.ADMIN_USERNAME || 'admin',
    ADMIN_PASSWORD: process.env.ADMIN_PASSWORD || 'admin123',
    ADMIN_EMAIL: process.env.ADMIN_EMAIL || '<EMAIL>',

    // 日志配置
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',

    // 速率限制配置
    RATE_LIMIT_WINDOW_MS: parseNumber(process.env.RATE_LIMIT_WINDOW_MS, 15 * 60 * 1000), // 15分钟
    RATE_LIMIT_MAX_REQUESTS: parseNumber(process.env.RATE_LIMIT_MAX_REQUESTS, 100),
  };
}

/**
 * 验证配置的有效性
 */
function validateConfig(config: EnvConfig): void {
  // 验证端口范围
  if (config.PORT < 1 || config.PORT > 65535) {
    throw new ApiError(
      `无效的端口号: ${config.PORT}`,
      ErrorCode.VALIDATION_ERROR,
      500
    );
  }

  // 验证JWT密钥长度
  if (config.JWT_SECRET.length < 32) {
    throw new ApiError(
      'JWT密钥长度至少需要32个字符',
      ErrorCode.VALIDATION_ERROR,
      500
    );
  }

  // 验证数据库URL格式
  if (!config.DATABASE_URL.startsWith('postgresql://')) {
    throw new ApiError(
      '数据库URL必须是PostgreSQL格式',
      ErrorCode.VALIDATION_ERROR,
      500
    );
  }

  // 验证CORS源格式
  try {
    new URL(config.CORS_ORIGIN);
  } catch {
    throw new ApiError(
      `无效的CORS源格式: ${config.CORS_ORIGIN}`,
      ErrorCode.VALIDATION_ERROR,
      500
    );
  }
}

/**
 * 获取并验证配置
 */
export function createConfig(): EnvConfig {
  try {
    const config = getConfig();
    validateConfig(config);
    return config;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      '配置初始化失败',
      ErrorCode.INTERNAL_ERROR,
      500,
      { originalError: error }
    );
  }
}

/**
 * 导出配置实例
 */
export const config = createConfig();

/**
 * 检查是否为开发环境
 */
export const isDevelopment = config.NODE_ENV === 'development';

/**
 * 检查是否为生产环境
 */
export const isProduction = config.NODE_ENV === 'production';

/**
 * 检查是否为测试环境
 */
export const isTest = config.NODE_ENV === 'test';

/**
 * 打印配置信息（隐藏敏感信息）
 */
export function printConfig(): void {
  const safeConfig = {
    ...config,
    DATABASE_URL: config.DATABASE_URL.replace(/\/\/.*@/, '//***:***@'),
    JWT_SECRET: '***',
    ADMIN_PASSWORD: '***',
  };

  console.log('📋 应用配置:');
  console.table(safeConfig);
}

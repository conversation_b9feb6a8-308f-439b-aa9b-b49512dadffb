import express from 'express';
import helmet from 'helmet';
import compression from 'compression';
import { config, printConfig } from '@/utils/config.js';
import DatabaseConnection from '@/utils/database.js';
import { DatabaseInitializer } from '@/utils/init-database.js';
import { getCorsMiddleware } from '@/middleware/cors.js';
import { getLoggerMiddleware, requestTimingMiddleware, apiLogger } from '@/middleware/logger.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';
import { errorHandler, notFoundHandler, timeoutHandler } from '@/middleware/error.js';
import indexRoutes from '@/routes/index.js';
import authRoutes from '@/routes/auth.routes.js';
import moviesRoutes from '@/routes/movies.routes.js';
import starsRoutes from '@/routes/stars.routes.js';
import usersRoutes from '@/routes/users.routes.js';
import rolesRoutes from '@/routes/roles.routes.js';
import permissionsRoutes from '@/routes/permissions.routes.js';
import statsRoutes from '@/routes/stats.routes.js';
import activityLogsRoutes from '@/routes/activity-logs.routes.js';

/**
 * 创建Express应用
 */
function createApp(): express.Application {
  const app = express();

  // 信任代理（用于获取真实IP）
  app.set('trust proxy', 1);

  // 安全中间件
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false, // 允许跨域嵌入
  }));

  // CORS中间件
  app.use(getCorsMiddleware());

  // 压缩中间件
  app.use(compression({
    level: 6,
    threshold: 1024, // 只压缩大于1KB的响应
    filter: (req, res) => {
      // 不压缩已经压缩的内容
      if (req.headers['x-no-compression']) {
        return false;
      }
      return compression.filter(req, res);
    },
  }));

  // 请求解析中间件
  app.use(express.json({ 
    limit: '10mb',
    strict: true,
  }));
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb',
  }));

  // 请求时间记录
  app.use(requestTimingMiddleware);

  // 日志中间件
  app.use(getLoggerMiddleware());

  // 请求超时处理
  app.use(timeoutHandler(30000)); // 30秒超时

  // 通用速率限制
  app.use(getRateLimitMiddleware('general'));

  // 路由
  app.use('/', indexRoutes);
  app.use('/api/auth', authRoutes);
  app.use('/api/movies', moviesRoutes);
  app.use('/api/stars', starsRoutes);
  app.use('/api/users', usersRoutes);
  app.use('/api/roles', rolesRoutes);
  app.use('/api/permissions', permissionsRoutes);
  app.use('/api/stats', statsRoutes);
  app.use('/api/activity-logs', activityLogsRoutes);

  // TODO: 添加数据导入路由
  // app.use('/api/import', importRoutes);

  // 404处理
  app.use(notFoundHandler);

  // 全局错误处理
  app.use(errorHandler);

  return app;
}

/**
 * 启动服务器
 */
async function startServer(): Promise<void> {
  try {
    // 打印配置信息
    console.log('🚀 启动GETAV.NET后台管理API服务...\n');
    printConfig();

    // 连接数据库
    apiLogger.info('连接数据库...');
    await DatabaseConnection.connect();

    // 初始化数据库
    apiLogger.info('初始化数据库...');
    await DatabaseInitializer.initialize();

    // 创建Express应用
    const app = createApp();

    // 启动HTTP服务器
    const server = app.listen(config.PORT, () => {
      apiLogger.success(`服务器启动成功！`);
      console.log(`🌐 服务器地址: http://localhost:${config.PORT}`);
      console.log(`📋 API文档: http://localhost:${config.PORT}/`);
      console.log(`❤️  健康检查: http://localhost:${config.PORT}/health`);
      console.log(`🔧 环境: ${config.NODE_ENV}`);
      console.log(`⏰ 启动时间: ${new Date().toISOString()}\n`);
    });

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      apiLogger.info(`收到 ${signal} 信号，开始优雅关闭...`);

      // 停止接受新连接
      server.close(async () => {
        apiLogger.info('HTTP服务器已关闭');

        try {
          // 断开数据库连接
          await DatabaseConnection.disconnect();
          apiLogger.success('数据库连接已断开');

          // 退出进程
          process.exit(0);
        } catch (error) {
          apiLogger.error('优雅关闭过程中出错:', error);
          process.exit(1);
        }
      });

      // 强制关闭超时
      setTimeout(() => {
        apiLogger.error('强制关闭服务器（超时）');
        process.exit(1);
      }, 10000); // 10秒超时
    };

    // 监听进程信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 监听未捕获的异常
    process.on('uncaughtException', (error) => {
      apiLogger.error('未捕获的异常:', error);
      gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      apiLogger.error('未处理的Promise拒绝:', { reason, promise });
      gracefulShutdown('unhandledRejection');
    });

  } catch (error) {
    apiLogger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

export { createApp, startServer };

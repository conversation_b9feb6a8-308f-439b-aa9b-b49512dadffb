import { Router } from 'express';
import { AuthController } from '@/controllers/auth.controller.js';
import { authenticateToken, optionalAuth } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const authController = new AuthController();

/**
 * 认证路由
 * 处理用户登录、注册、密码管理等认证相关请求
 */

/**
 * @route POST /api/auth/login
 * @desc 用户登录
 * @access Public
 * @body { username: string, password: string }
 */
router.post(
  '/login',
  getRateLimitMiddleware('login'), // 登录速率限制
  authController.login
);

/**
 * @route POST /api/auth/logout
 * @desc 用户登出
 * @access Private
 */
router.post(
  '/logout',
  authenticateToken,
  authController.logout
);

/**
 * @route GET /api/auth/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get(
  '/me',
  authenticateToken,
  authController.getCurrentUser
);

/**
 * @route POST /api/auth/refresh
 * @desc 刷新token
 * @access Private
 * @headers { Authorization: "Bearer <token>" }
 */
router.post(
  '/refresh',
  getRateLimitMiddleware('api'),
  authController.refreshToken
);

/**
 * @route POST /api/auth/change-password
 * @desc 修改密码
 * @access Private
 * @body { oldPassword: string, newPassword: string }
 */
router.post(
  '/change-password',
  authenticateToken,
  getRateLimitMiddleware('api'),
  authController.changePassword
);

/**
 * @route GET /api/auth/check-username/:username
 * @desc 检查用户名可用性
 * @access Public
 * @params { username: string }
 */
router.get(
  '/check-username/:username',
  getRateLimitMiddleware('api'),
  authController.checkUsername
);

/**
 * @route POST /api/auth/check-password-strength
 * @desc 检查密码强度
 * @access Public
 * @body { password: string }
 */
router.post(
  '/check-password-strength',
  getRateLimitMiddleware('api'),
  authController.checkPasswordStrength
);

/**
 * @route POST /api/auth/verify
 * @desc 验证token
 * @access Public
 * @body { token?: string } 或 @headers { Authorization: "Bearer <token>" }
 */
router.post(
  '/verify',
  optionalAuth,
  getRateLimitMiddleware('api'),
  authController.verifyToken
);

/**
 * @route GET /api/auth/permissions
 * @desc 获取用户权限信息
 * @access Private
 */
router.get(
  '/permissions',
  authenticateToken,
  getRateLimitMiddleware('api'),
  authController.getUserPermissions
);

/**
 * @route POST /api/auth/check-permission
 * @desc 检查用户权限
 * @access Private
 * @body { permission: string }
 */
router.post(
  '/check-permission',
  authenticateToken,
  getRateLimitMiddleware('api'),
  authController.checkPermission
);

/**
 * @route POST /api/auth/check-role
 * @desc 检查用户角色
 * @access Private
 * @body { role: string }
 */
router.post(
  '/check-role',
  authenticateToken,
  getRateLimitMiddleware('api'),
  authController.checkRole
);

export default router;

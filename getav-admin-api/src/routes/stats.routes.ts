import { Router } from 'express';
import { StatsController } from '@/controllers/stats.controller.js';
import { authenticateToken, requireAdmin } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const statsController = new StatsController();

/**
 * 统计数据路由
 * 处理统计数据相关的请求
 */

/**
 * @route GET /api/stats/dashboard
 * @desc 获取仪表板综合统计数据
 * @access Private (Admin)
 */
router.get(
  '/dashboard',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  statsController.getDashboardStats
);

/**
 * @route GET /api/stats/overview
 * @desc 获取系统总体统计数据
 * @access Private (Admin)
 */
router.get(
  '/overview',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  statsController.getOverviewStats
);

/**
 * @route GET /api/stats/content
 * @desc 获取内容统计数据
 * @access Private (Admin)
 */
router.get(
  '/content',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  statsController.getContentStats
);

/**
 * @route GET /api/stats/trends
 * @desc 获取时间趋势统计
 * @access Private (Admin)
 * @query {number} days - 统计天数 (1-365)
 */
router.get(
  '/trends',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  statsController.getTrendStats
);

/**
 * @route GET /api/stats/categories
 * @desc 获取分类统计
 * @access Private (Admin)
 */
router.get(
  '/categories',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  statsController.getCategoryStats
);

/**
 * @route GET /api/stats/user-activity
 * @desc 获取用户活跃度统计
 * @access Private (Admin)
 */
router.get(
  '/user-activity',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  statsController.getUserActivityStats
);

/**
 * @route GET /api/stats/health
 * @desc 获取系统健康状态
 * @access Private (Admin)
 */
router.get(
  '/health',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  statsController.getSystemHealth
);

export default router;

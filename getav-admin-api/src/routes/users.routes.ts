import { Router } from 'express';
import { UsersController } from '@/controllers/users.controller.js';
import { authenticateToken, requireAdmin, requireSuperAdmin } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const usersController = new UsersController();

/**
 * 用户管理路由
 * 处理用户相关的请求
 */

/**
 * @route GET /api/users/stats
 * @desc 获取用户统计信息
 * @access Private (Admin)
 */
router.get(
  '/stats',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.getUserStats
);

/**
 * @route GET /api/users/search
 * @desc 搜索用户（自动完成）
 * @access Private (Admin)
 * @query {string} q - 搜索关键词
 * @query {number} limit - 返回数量限制
 */
router.get(
  '/search',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.searchUsers
);

/**
 * @route GET /api/users
 * @desc 获取用户列表（分页）
 * @access Private (Admin)
 * @query {number} page - 页码
 * @query {number} limit - 每页数量
 * @query {string} search - 搜索关键词
 * @query {boolean} hasEmail - 是否有邮箱
 * @query {boolean} verified - 是否已验证
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向 (asc|desc)
 */
router.get(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.getUsers
);

/**
 * @route GET /api/users/:id
 * @desc 获取用户详情
 * @access Private (Admin)
 * @params {string} id - 用户ID
 */
router.get(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.getUserById
);

/**
 * @route PUT /api/users/:id
 * @desc 更新用户信息
 * @access Private (Admin)
 * @params {string} id - 用户ID
 * @body {object} updateData - 更新数据
 */
router.put(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.updateUser
);

/**
 * @route DELETE /api/users/:id
 * @desc 删除单个用户
 * @access Private (Admin)
 * @params {string} id - 用户ID
 */
router.delete(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.deleteUser
);

/**
 * @route DELETE /api/users
 * @desc 批量删除用户
 * @access Private (Admin)
 * @body {string[]} ids - 用户ID列表
 */
router.delete(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.deleteUsers
);

/**
 * @route GET /api/users/:id/roles
 * @desc 获取用户角色
 * @access Private (Admin)
 */
router.get(
  '/:id/roles',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.getUserRoles
);

/**
 * @route GET /api/users/:id/permissions
 * @desc 获取用户权限
 * @access Private (Admin)
 */
router.get(
  '/:id/permissions',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  usersController.getUserPermissions
);

/**
 * @route PUT /api/users/:id/roles
 * @desc 设置用户角色
 * @access Private (Super Admin)
 */
router.put(
  '/:id/roles',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  usersController.setUserRoles
);

/**
 * @route POST /api/users/:id/roles
 * @desc 添加用户角色
 * @access Private (Super Admin)
 */
router.post(
  '/:id/roles',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  usersController.addUserRole
);

/**
 * @route DELETE /api/users/:id/roles/:roleId
 * @desc 移除用户角色
 * @access Private (Super Admin)
 */
router.delete(
  '/:id/roles/:roleId',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  usersController.removeUserRole
);

export default router;

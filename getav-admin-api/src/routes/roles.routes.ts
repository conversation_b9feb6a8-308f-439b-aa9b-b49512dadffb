import { Router } from 'express';
import { RolesController } from '@/controllers/roles.controller.js';
import { authenticateToken, requireAdmin, requireSuperAdmin } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const rolesController = new RolesController();

/**
 * 角色管理路由
 * 处理角色相关的请求
 */

/**
 * @route GET /api/roles/all
 * @desc 获取所有角色（用于下拉选择）
 * @access Private (Admin)
 */
router.get(
  '/all',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  rolesController.getAllRoles
);

/**
 * @route GET /api/roles
 * @desc 获取角色列表（分页）
 * @access Private (Admin)
 */
router.get(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  rolesController.getRoles
);

/**
 * @route POST /api/roles
 * @desc 创建角色
 * @access Private (Super Admin)
 */
router.post(
  '/',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  rolesController.createRole
);

/**
 * @route GET /api/roles/:id
 * @desc 获取角色详情
 * @access Private (Admin)
 */
router.get(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  rolesController.getRoleById
);

/**
 * @route PUT /api/roles/:id
 * @desc 更新角色
 * @access Private (Super Admin)
 */
router.put(
  '/:id',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  rolesController.updateRole
);

/**
 * @route DELETE /api/roles/:id
 * @desc 删除角色
 * @access Private (Super Admin)
 */
router.delete(
  '/:id',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  rolesController.deleteRole
);

/**
 * @route GET /api/roles/:id/permissions
 * @desc 获取角色权限
 * @access Private (Admin)
 */
router.get(
  '/:id/permissions',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  rolesController.getRolePermissions
);

/**
 * @route PUT /api/roles/:id/permissions
 * @desc 设置角色权限
 * @access Private (Super Admin)
 */
router.put(
  '/:id/permissions',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  rolesController.setRolePermissions
);

export default router;

import { Router } from 'express';
import { ActivityLogController } from '@/controllers/activity-log.controller.js';
import { authenticateToken, requireAdmin } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const activityLogController = new ActivityLogController();

/**
 * 用户行为日志路由
 * 处理用户行为日志相关的请求
 */

/**
 * @route POST /api/activity-logs
 * @desc 记录用户行为日志
 * @access Public
 */
router.post(
  '/',
  getRateLimitMiddleware('api'),
  activityLogController.createActivityLog
);

/**
 * @route GET /api/activity-logs
 * @desc 获取用户行为日志列表
 * @access Private (Admin)
 * @query {number} page - 页码
 * @query {number} limit - 每页数量
 * @query {string} search - 搜索关键词
 * @query {string} userId - 用户ID
 * @query {string} action - 行为类型
 * @query {string} targetType - 目标类型
 * @query {string} targetId - 目标ID
 * @query {string} ipAddress - IP地址
 * @query {string} dateFrom - 开始日期
 * @query {string} dateTo - 结束日期
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向
 */
router.get(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  activityLogController.getActivityLogs
);

/**
 * @route GET /api/activity-logs/stats
 * @desc 获取用户行为统计数据
 * @access Private (Admin)
 * @query {number} days - 统计天数
 */
router.get(
  '/stats',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  activityLogController.getActivityStats
);

/**
 * @route GET /api/activity-logs/export
 * @desc 导出用户行为日志
 * @access Private (Admin)
 * @query {string} format - 导出格式 (csv, json)
 * @query {string} dateFrom - 开始日期
 * @query {string} dateTo - 结束日期
 */
router.get(
  '/export',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  activityLogController.exportActivityLogs
);

/**
 * @route POST /api/activity-logs/cleanup
 * @desc 清理过期的用户行为日志
 * @access Private (Admin)
 * @body {number} daysToKeep - 保留天数
 */
router.post(
  '/cleanup',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  activityLogController.cleanupOldLogs
);

/**
 * @route GET /api/activity-logs/:id
 * @desc 获取单个用户行为日志详情
 * @access Private (Admin)
 */
router.get(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  activityLogController.getActivityLogById
);

export default router;

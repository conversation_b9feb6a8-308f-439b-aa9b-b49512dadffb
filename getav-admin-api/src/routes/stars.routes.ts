import { Router } from 'express';
import { StarsController } from '@/controllers/stars.controller.js';
import { authenticateToken, requireAdmin } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const starsController = new StarsController();

/**
 * 演员管理路由
 * 处理演员相关的请求
 */

/**
 * @route GET /api/stars/stats
 * @desc 获取演员统计信息
 * @access Private (Admin)
 */
router.get(
  '/stats',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  starsController.getStarStats
);

/**
 * @route GET /api/stars/search
 * @desc 搜索演员（自动完成）
 * @access Private (Admin)
 * @query {string} q - 搜索关键词
 * @query {number} limit - 返回数量限制
 */
router.get(
  '/search',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  starsController.searchStars
);

/**
 * @route GET /api/stars
 * @desc 获取演员列表（分页）
 * @access Private (Admin)
 * @query {number} page - 页码
 * @query {number} limit - 每页数量
 * @query {string} search - 搜索关键词
 * @query {boolean} hasAvatar - 是否有头像
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向 (asc|desc)
 */
router.get(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  starsController.getStars
);

/**
 * @route GET /api/stars/:id
 * @desc 获取演员详情
 * @access Private (Admin)
 * @params {string} id - 演员ID
 */
router.get(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  starsController.getStarById
);

/**
 * @route PUT /api/stars/:id
 * @desc 更新演员信息
 * @access Private (Admin)
 * @params {string} id - 演员ID
 * @body {object} updateData - 更新数据
 */
router.put(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  starsController.updateStar
);

/**
 * @route DELETE /api/stars/:id
 * @desc 删除单个演员
 * @access Private (Admin)
 * @params {string} id - 演员ID
 */
router.delete(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  starsController.deleteStar
);

/**
 * @route DELETE /api/stars
 * @desc 批量删除演员
 * @access Private (Admin)
 * @body {string[]} ids - 演员ID列表
 */
router.delete(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  starsController.deleteStars
);

export default router;

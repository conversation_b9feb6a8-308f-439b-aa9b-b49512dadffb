import { Router } from 'express';
import { ApiResponse } from '@/types/index.js';
import { config } from '@/utils/config.js';
import DatabaseConnection from '@/utils/database.js';

const router = Router();

/**
 * 根路由 - API信息
 */
router.get('/', (req, res) => {
  const response: ApiResponse = {
    success: true,
    message: 'GETAV.NET 后台管理API服务',
    data: {
      name: 'getav-admin-api',
      version: '1.0.0',
      environment: config.NODE_ENV,
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/health',
        auth: '/api/auth',
        movies: '/api/movies',
        stars: '/api/stars',
        users: '/api/users',
        roles: '/api/roles',
        permissions: '/api/permissions',
        stats: '/api/stats',
        activityLogs: '/api/activity-logs',
        import: '/api/import',
      },
    },
  };

  res.json(response);
});

/**
 * 健康检查路由
 */
router.get('/health', async (req, res) => {
  try {
    // 检查数据库连接
    const dbHealthy = await DatabaseConnection.healthCheck();
    
    // 获取基本统计信息
    const stats = dbHealthy ? await DatabaseConnection.getDatabaseStats() : null;

    const response: ApiResponse = {
      success: true,
      message: '服务运行正常',
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.NODE_ENV,
        database: {
          connected: dbHealthy,
          stats: stats || null,
        },
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024),
        },
        cpu: {
          usage: process.cpuUsage(),
        },
      },
    };

    res.json(response);
  } catch (error) {
    console.error('健康检查失败:', error);
    
    const response: ApiResponse = {
      success: false,
      message: '服务异常',
      error: '健康检查失败',
      code: 503,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: config.NODE_ENV === 'development' ? error : undefined,
      },
    };

    res.status(503).json(response);
  }
});

/**
 * API版本信息
 */
router.get('/version', (req, res) => {
  const response: ApiResponse = {
    success: true,
    data: {
      version: '1.0.0',
      apiVersion: 'v1',
      buildTime: new Date().toISOString(),
      nodeVersion: process.version,
      environment: config.NODE_ENV,
    },
  };

  res.json(response);
});

/**
 * 服务器状态信息
 */
router.get('/status', async (req, res) => {
  try {
    const dbStats = await DatabaseConnection.getDatabaseStats();
    
    const response: ApiResponse = {
      success: true,
      data: {
        server: {
          status: 'running',
          uptime: process.uptime(),
          timestamp: new Date().toISOString(),
          pid: process.pid,
          platform: process.platform,
          arch: process.arch,
        },
        database: {
          connected: DatabaseConnection.isConnectionActive(),
          stats: dbStats,
        },
        memory: {
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024),
        },
        environment: {
          nodeEnv: config.NODE_ENV,
          nodeVersion: process.version,
          port: config.PORT,
        },
      },
    };

    res.json(response);
  } catch (error) {
    console.error('获取服务器状态失败:', error);
    
    const response: ApiResponse = {
      success: false,
      error: '获取服务器状态失败',
      code: 500,
    };

    res.status(500).json(response);
  }
});

export default router;

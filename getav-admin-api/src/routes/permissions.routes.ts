import { Router } from 'express';
import { PermissionsController } from '@/controllers/permissions.controller.js';
import { authenticateToken, requireAdmin, requireSuperAdmin } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const permissionsController = new PermissionsController();

/**
 * 权限管理路由
 * 处理权限相关的请求
 */

/**
 * @route GET /api/permissions/all
 * @desc 获取所有权限（用于下拉选择）
 * @access Private (Admin)
 */
router.get(
  '/all',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.getAllPermissions
);

/**
 * @route GET /api/permissions/tree
 * @desc 获取权限树结构
 * @access Private (Admin)
 */
router.get(
  '/tree',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.getPermissionTree
);

/**
 * @route GET /api/permissions/resources
 * @desc 获取权限资源列表
 * @access Private (Admin)
 */
router.get(
  '/resources',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.getPermissionResources
);

/**
 * @route GET /api/permissions/actions
 * @desc 获取权限操作列表
 * @access Private (Admin)
 */
router.get(
  '/actions',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.getPermissionActions
);

/**
 * @route GET /api/permissions
 * @desc 获取权限列表（分页）
 * @access Private (Admin)
 */
router.get(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.getPermissions
);

/**
 * @route POST /api/permissions
 * @desc 创建权限
 * @access Private (Super Admin)
 */
router.post(
  '/',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.createPermission
);

/**
 * @route GET /api/permissions/:id
 * @desc 获取权限详情
 * @access Private (Admin)
 */
router.get(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.getPermissionById
);

/**
 * @route PUT /api/permissions/:id
 * @desc 更新权限
 * @access Private (Super Admin)
 */
router.put(
  '/:id',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.updatePermission
);

/**
 * @route DELETE /api/permissions/:id
 * @desc 删除权限
 * @access Private (Super Admin)
 */
router.delete(
  '/:id',
  authenticateToken,
  requireSuperAdmin,
  getRateLimitMiddleware('api'),
  permissionsController.deletePermission
);

export default router;

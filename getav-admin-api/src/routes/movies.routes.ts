import { Router } from 'express';
import { MoviesController } from '@/controllers/movies.controller.js';
import { authenticateToken, requireAdmin } from '@/middleware/auth.js';
import { getRateLimitMiddleware } from '@/middleware/rate-limit.js';

const router = Router();
const moviesController = new MoviesController();

/**
 * 影片管理路由
 * 处理影片相关的请求
 */

/**
 * @route GET /api/movies/stats
 * @desc 获取影片统计信息
 * @access Private (Admin)
 */
router.get(
  '/stats',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  moviesController.getMovieStats
);

/**
 * @route GET /api/movies
 * @desc 获取影片列表（分页）
 * @access Private (Admin)
 * @query {number} page - 页码
 * @query {number} limit - 每页数量
 * @query {string} search - 搜索关键词
 * @query {string} genre - 分类筛选
 * @query {string} star - 演员筛选
 * @query {string} director - 导演筛选
 * @query {string} producer - 制作商筛选
 * @query {string} dateFrom - 开始日期
 * @query {string} dateTo - 结束日期
 * @query {string} sortBy - 排序字段
 * @query {string} sortOrder - 排序方向 (asc|desc)
 */
router.get(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  moviesController.getMovies
);

/**
 * @route GET /api/movies/:id
 * @desc 获取影片详情
 * @access Private (Admin)
 * @params {string} id - 影片ID
 */
router.get(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  moviesController.getMovieById
);

/**
 * @route PUT /api/movies/:id
 * @desc 更新影片信息
 * @access Private (Admin)
 * @params {string} id - 影片ID
 * @body {object} updateData - 更新数据
 */
router.put(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  moviesController.updateMovie
);

/**
 * @route DELETE /api/movies/:id
 * @desc 删除单个影片
 * @access Private (Admin)
 * @params {string} id - 影片ID
 */
router.delete(
  '/:id',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  moviesController.deleteMovie
);

/**
 * @route DELETE /api/movies
 * @desc 批量删除影片
 * @access Private (Admin)
 * @body {string[]} ids - 影片ID列表
 */
router.delete(
  '/',
  authenticateToken,
  requireAdmin,
  getRateLimitMiddleware('api'),
  moviesController.deleteMovies
);

export default router;

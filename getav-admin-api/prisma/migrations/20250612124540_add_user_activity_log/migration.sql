/*
  Warnings:

  - Added the required column `targetType` to the `user_activity_logs` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "ActivityTargetType" AS ENUM ('MOVIE', 'STAR', 'GENRE', 'PRODUCER', 'DIRECTOR', 'SERIES', 'COMMENT', 'MAGNET', 'USER', 'SEARCH');

-- DropIndex
DROP INDEX "user_activity_logs_action_createdAt_idx";

-- DropIndex
DROP INDEX "user_activity_logs_ipAddress_createdAt_idx";

-- DropIndex
DROP INDEX "user_activity_logs_targetType_targetId_idx";

-- DropIndex
DROP INDEX "user_activity_logs_userId_createdAt_idx";

-- AlterTable
ALTER TABLE "user_activity_logs" DROP COLUMN "targetType",
ADD COLUMN     "targetType" "ActivityTargetType" NOT NULL;

-- DropEnum
DROP TYPE "ActivityTarget";

-- CreateIndex
CREATE INDEX "user_activity_logs_userId_idx" ON "user_activity_logs"("userId");

-- CreateIndex
CREATE INDEX "user_activity_logs_action_idx" ON "user_activity_logs"("action");

-- CreateIndex
CREATE INDEX "user_activity_logs_targetType_idx" ON "user_activity_logs"("targetType");

-- CreateIndex
CREATE INDEX "user_activity_logs_createdAt_idx" ON "user_activity_logs"("createdAt");

-- CreateIndex
CREATE INDEX "user_activity_logs_ipAddress_idx" ON "user_activity_logs"("ipAddress");

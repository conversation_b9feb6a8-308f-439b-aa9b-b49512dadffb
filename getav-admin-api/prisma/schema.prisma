// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 影片表
model Movie {
  id            String   @id // 影片番号，如 IPX-585
  title         String   // 影片标题
  img           String?  // 原始封面图片URL
  localImg      String?  // 本地封面图片路径
  date          String?  // 发行日期
  videoLength   Int?     // 影片时长（分钟）
  description   String?  // 描述
  gid           String?  // javbus gid
  uc            String?  // javbus uc
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  director      Director?    @relation(fields: [directorId], references: [id])
  directorId    String?
  producer      Producer?    @relation(fields: [producerId], references: [id])
  producerId    String?
  publisher     Publisher?   @relation(fields: [publisherId], references: [id])
  publisherId   String?
  series        Series?      @relation(fields: [seriesId], references: [id])
  seriesId      String?

  // 多对多关系
  stars         MovieStar[]
  genres        MovieGenre[]
  samples       Sample[]
  magnets       Magnet[]
  similarMovies SimilarMovie[] @relation("OriginalMovie")
  similarTo     SimilarMovie[] @relation("SimilarMovie")
  relatedMovies RelatedMovie[] // 相关影片信息

  // 用户交互关系
  comments      Comment[]
  likes         Like[]
  favorites     Favorite[]
  ratings       Rating[]
  views         View[]
  reports       Report[]

  @@map("movies")
}

// 演员表
model Star {
  id          String   @id // 演员ID，如 rsv
  name        String   // 演员姓名
  avatar      String?  // 原始头像URL
  localAvatar String?  // 本地头像路径
  birthday    String?  // 生日
  age         String?  // 年龄
  height      String?  // 身高
  bust        String?  // 胸围
  waist       String?  // 腰围
  hip         String?  // 臀围
  birthplace  String?  // 出生地
  hobby       String?  // 爱好
  cupSize     String?  // 罩杯
  measurements String? // 三围
  description String?  // 描述
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  movies      MovieStar[]

  @@map("stars")
}

// 影片-演员关联表
model MovieStar {
  movieId String
  starId  String
  movie   Movie  @relation(fields: [movieId], references: [id], onDelete: Cascade)
  star    Star   @relation(fields: [starId], references: [id], onDelete: Cascade)

  @@id([movieId, starId])
  @@map("movie_stars")
}

// 分类表
model Genre {
  id     String @id // 分类ID，如 4o
  name   String // 分类名称，如 高畫質
  movies MovieGenre[]

  @@map("genres")
}

// 影片-分类关联表
model MovieGenre {
  movieId String
  genreId String
  movie   Movie  @relation(fields: [movieId], references: [id], onDelete: Cascade)
  genre   Genre  @relation(fields: [genreId], references: [id], onDelete: Cascade)

  @@id([movieId, genreId])
  @@map("movie_genres")
}

// 导演表
model Director {
  id     String  @id // 导演ID
  name   String  // 导演姓名
  movies Movie[]

  @@map("directors")
}

// 制作商表
model Producer {
  id     String  @id // 制作商ID
  name   String  // 制作商名称
  movies Movie[]

  @@map("producers")
}

// 发行商表
model Publisher {
  id     String  @id // 发行商ID
  name   String  // 发行商名称
  movies Movie[]

  @@map("publishers")
}

// 系列表
model Series {
  id     String  @id // 系列ID
  name   String  // 系列名称
  movies Movie[]

  @@map("series")
}

// 样品图片表
model Sample {
  id        String  @id // 样品图片ID
  alt       String? // 图片描述
  src       String? // 原始图片URL
  thumbnail String? // 缩略图URL
  localSrc  String? // 本地图片路径
  movieId   String
  movie     Movie   @relation(fields: [movieId], references: [id], onDelete: Cascade)

  @@map("samples")
}

// 磁力链接表
model Magnet {
  id           String   @id // 磁力链接ID
  link         String   // 磁力链接
  isHD         Boolean? // 是否高清
  title        String?  // 标题
  size         String?  // 文件大小
  numberSize   BigInt?  // 文件大小（字节）
  shareDate    String?  // 分享日期
  hasSubtitle  Boolean? // 是否有字幕
  movieId      String
  movie        Movie    @relation(fields: [movieId], references: [id], onDelete: Cascade)
  createdAt    DateTime @default(now())

  // 用户交互关系
  comments     Comment[]
  likes        Like[]

  @@map("magnets")
}

// 相似影片关联表
model SimilarMovie {
  originalMovieId String
  similarMovieId  String
  originalMovie   Movie  @relation("OriginalMovie", fields: [originalMovieId], references: [id], onDelete: Cascade)
  similarMovie    Movie  @relation("SimilarMovie", fields: [similarMovieId], references: [id], onDelete: Cascade)

  @@id([originalMovieId, similarMovieId])
  @@map("similar_movies")
}

// 相关影片信息表（存储所有相关影片，不管是否存在于数据库）
model RelatedMovie {
  id              String   @id @default(cuid())
  originalMovieId String   // 源影片ID
  relatedMovieId  String   // 相关影片ID（可能不存在于数据库）
  relatedTitle    String   // 相关影片标题
  relatedImg      String?  // 相关影片图片URL
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // 关联到源影片
  originalMovie   Movie    @relation(fields: [originalMovieId], references: [id], onDelete: Cascade)

  @@unique([originalMovieId, relatedMovieId])
  @@map("related_movies")
}

// 用户表 - NextAuth.js兼容
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  username      String?   @unique
  defaultRole   String?   @default("user") // 默认角色
  isActive      Boolean   @default(true)   // 账户状态
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // NextAuth.js关联
  accounts Account[]
  sessions Session[]
  password UserPassword?

  // RBAC权限关联
  userRoles UserRole[]

  // 应用关联关系
  comments      Comment[]
  likes         Like[]
  favorites     Favorite[]
  ratings       Rating[]
  views         View[]
  reports       Report[]
  activityLogs  UserActivityLog[]

  @@map("users")
}

// NextAuth.js Account表
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// NextAuth.js Session表
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// NextAuth.js VerificationToken表
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// 用户密码表（简化实现）
model UserPassword {
  userId       String @id
  passwordHash String
  user         User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_passwords")
}

// 评论表
model Comment {
  id        String   @id @default(cuid())
  content   String   // 评论内容
  userId    String?  // 用户ID，可为空（匿名评论）
  movieId   String?  // 影片ID，可为空
  magnetId  String?  // 磁力链接ID，可为空
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  user   User?  @relation(fields: [userId], references: [id], onDelete: SetNull)
  movie  Movie? @relation(fields: [movieId], references: [id], onDelete: Cascade)
  magnet Magnet? @relation(fields: [magnetId], references: [id], onDelete: Cascade)
  likes  Like[]

  @@map("comments")
}

// 点赞表
model Like {
  id        String   @id @default(cuid())
  userId    String?  // 用户ID，可为空（匿名点赞）
  movieId   String?  // 影片ID，可为空
  commentId String?  // 评论ID，可为空
  magnetId  String?  // 磁力链接ID，可为空
  type      LikeType // 点赞类型：like 或 dislike
  createdAt DateTime @default(now())

  // 关联关系
  user    User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  movie   Movie?   @relation(fields: [movieId], references: [id], onDelete: Cascade)
  comment Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)
  magnet  Magnet?  @relation(fields: [magnetId], references: [id], onDelete: Cascade)

  // 确保同一用户对同一对象只能有一个点赞记录
  @@unique([userId, movieId])
  @@unique([userId, commentId])
  @@unique([userId, magnetId])
  @@map("likes")
}

// 收藏表
model Favorite {
  id        String   @id @default(cuid())
  userId    String
  movieId   String
  createdAt DateTime @default(now())

  // 关联关系
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  movie Movie @relation(fields: [movieId], references: [id], onDelete: Cascade)

  @@unique([userId, movieId])
  @@map("favorites")
}

// 评分表
model Rating {
  id        String   @id @default(cuid())
  userId    String
  movieId   String
  score     Int      // 评分 1-10
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  movie Movie @relation(fields: [movieId], references: [id], onDelete: Cascade)

  @@unique([userId, movieId])
  @@map("ratings")
}

// 观看记录表
model View {
  id        String   @id @default(cuid())
  movieId   String
  userId    String?  // 可为空，支持匿名观看
  ipAddress String?  // IP地址，用于匿名用户去重
  userAgent String?  // 用户代理，辅助去重
  createdAt DateTime @default(now())

  // 关联关系
  movie Movie @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user  User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  // 防止同一用户/IP短时间内重复计数
  @@unique([movieId, userId])
  @@index([movieId, ipAddress])
  @@map("views")
}

// 举报表
model Report {
  id          String      @id @default(cuid())
  movieId     String
  type        ReportType  // 举报类型
  description String      // 举报描述
  status      ReportStatus @default(PENDING) // 处理状态
  userId      String?     // 举报用户ID，可为空（匿名举报）
  userEmail   String?     // 举报用户邮箱
  ipAddress   String?     // IP地址
  userAgent   String?     // 用户代理
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  processedAt DateTime?   // 处理时间
  processedBy String?     // 处理人员ID

  // 关联关系
  movie Movie @relation(fields: [movieId], references: [id], onDelete: Cascade)
  user  User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("reports")
}

// 点赞类型枚举
enum LikeType {
  LIKE
  DISLIKE
}

// 举报类型枚举
enum ReportType {
  COPYRIGHT     // 版权侵犯
  INAPPROPRIATE // 不当内容
  SPAM          // 垃圾信息
  MISLEADING    // 误导性信息
  VIOLENCE      // 暴力内容
  OTHER         // 其他
}

// 举报状态枚举
enum ReportStatus {
  PENDING   // 待处理
  REVIEWING // 审核中
  RESOLVED  // 已解决
  REJECTED  // 已拒绝
}

// 用户行为类型枚举
enum ActivityAction {
  LOGIN
  LOGOUT
  REGISTER
  VIEW_MOVIE
  VIEW_STAR
  VIEW_GENRE
  VIEW_PRODUCER
  VIEW_DIRECTOR
  VIEW_SERIES
  SEARCH
  LIKE
  UNLIKE
  COMMENT
  DELETE_COMMENT
  FAVORITE
  UNFAVORITE
  RATE
  UPDATE_RATING
  REPORT
  DOWNLOAD_MAGNET
  SHARE
}

// 活动目标类型枚举
enum ActivityTargetType {
  MOVIE
  STAR
  GENRE
  PRODUCER
  DIRECTOR
  SERIES
  COMMENT
  MAGNET
  USER
  SEARCH
}

// 权限类型枚举
enum PermissionType {
  MENU     // 菜单权限，控制菜单显示
  ACTION   // 操作权限，控制按钮和功能
  DATA     // 数据权限，控制数据访问范围
  API      // API权限，控制接口访问
}

// 用户行为日志表
model UserActivityLog {
  id         String               @id @default(cuid())
  userId     String?              // 用户ID，可为空（匿名用户）
  action     ActivityAction       // 行为类型
  targetType ActivityTargetType   // 目标类型
  targetId   String?              // 目标ID，可为空
  ipAddress  String?              // IP地址
  userAgent  String?              // 用户代理
  metadata   Json?                // 元数据，存储额外信息
  createdAt  DateTime             @default(now())

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  // 索引
  @@index([userId])
  @@index([action])
  @@index([targetType])
  @@index([createdAt])
  @@index([ipAddress])
  @@map("user_activity_logs")
}

// ==================== RBAC权限系统表 ====================

// 角色表
model Role {
  id          String   @id @default(cuid())
  name        String   @unique // 角色名称，如 super_admin, admin, editor, viewer
  displayName String   // 显示名称，如 超级管理员, 管理员, 编辑员, 查看员
  description String?  // 角色描述
  isSystem    Boolean  @default(false) // 是否为系统角色（不可删除）
  isActive    Boolean  @default(true)  // 角色状态
  priority    Int      @default(0)     // 角色优先级，数字越大优先级越高
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  userRoles       UserRole[]
  rolePermissions RolePermission[]

  @@map("roles")
}

// 权限表
model Permission {
  id          String          @id @default(cuid())
  name        String          @unique // 权限名称，如 user:read, movie:create
  displayName String          // 显示名称，如 查看用户, 创建影片
  description String?         // 权限描述
  resource    String          // 资源名称，如 user, movie, star
  action      String          // 操作名称，如 read, create, update, delete
  type        PermissionType  @default(ACTION) // 权限类型
  parentId    String?         // 父权限ID，用于构建权限树
  isSystem    Boolean         @default(false) // 是否为系统权限（不可删除）
  isActive    Boolean         @default(true)  // 权限状态
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // 关联关系
  parent          Permission?       @relation("PermissionHierarchy", fields: [parentId], references: [id])
  children        Permission[]      @relation("PermissionHierarchy")
  rolePermissions RolePermission[]

  @@unique([resource, action])
  @@map("permissions")
}

// 用户角色关联表
model UserRole {
  id        String   @id @default(cuid())
  userId    String
  roleId    String
  assignedBy String? // 分配者ID
  assignedAt DateTime @default(now())
  expiresAt  DateTime? // 角色过期时间，可为空表示永不过期
  isActive   Boolean  @default(true)

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

// 角色权限关联表
model RolePermission {
  id           String   @id @default(cuid())
  roleId       String
  permissionId String
  assignedBy   String?  // 分配者ID
  assignedAt   DateTime @default(now())
  isActive     Boolean  @default(true)

  // 关联关系
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

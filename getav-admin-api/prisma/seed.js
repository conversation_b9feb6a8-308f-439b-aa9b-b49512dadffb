import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
/**
 * 默认权限数据
 */
const defaultPermissions = [
    // 仪表板权限
    {
        name: 'dashboard:view',
        displayName: '查看仪表板',
        description: '访问管理后台仪表板',
        resource: 'dashboard',
        action: 'view',
        type: 'MENU',
    },
    // 用户管理权限
    {
        name: 'user:read',
        displayName: '查看用户',
        description: '查看用户列表和详情',
        resource: 'user',
        action: 'read',
        type: 'ACTION',
    },
    {
        name: 'user:create',
        displayName: '创建用户',
        description: '创建新用户账户',
        resource: 'user',
        action: 'create',
        type: 'ACTION',
    },
    {
        name: 'user:update',
        displayName: '更新用户',
        description: '修改用户信息',
        resource: 'user',
        action: 'update',
        type: 'ACTION',
    },
    {
        name: 'user:delete',
        displayName: '删除用户',
        description: '删除用户账户',
        resource: 'user',
        action: 'delete',
        type: 'ACTION',
    },
    {
        name: 'user:manage',
        displayName: '用户管理',
        description: '用户管理菜单权限',
        resource: 'user',
        action: 'manage',
        type: 'MENU',
    },
    // 角色管理权限
    {
        name: 'role:read',
        displayName: '查看角色',
        description: '查看角色列表和详情',
        resource: 'role',
        action: 'read',
        type: 'ACTION',
    },
    {
        name: 'role:create',
        displayName: '创建角色',
        description: '创建新角色',
        resource: 'role',
        action: 'create',
        type: 'ACTION',
    },
    {
        name: 'role:update',
        displayName: '更新角色',
        description: '修改角色信息',
        resource: 'role',
        action: 'update',
        type: 'ACTION',
    },
    {
        name: 'role:delete',
        displayName: '删除角色',
        description: '删除角色',
        resource: 'role',
        action: 'delete',
        type: 'ACTION',
    },
    {
        name: 'role:manage',
        displayName: '角色管理',
        description: '角色管理菜单权限',
        resource: 'role',
        action: 'manage',
        type: 'MENU',
    },
    // 权限管理权限
    {
        name: 'permission:read',
        displayName: '查看权限',
        description: '查看权限列表和详情',
        resource: 'permission',
        action: 'read',
        type: 'ACTION',
    },
    {
        name: 'permission:create',
        displayName: '创建权限',
        description: '创建新权限',
        resource: 'permission',
        action: 'create',
        type: 'ACTION',
    },
    {
        name: 'permission:update',
        displayName: '更新权限',
        description: '修改权限信息',
        resource: 'permission',
        action: 'update',
        type: 'ACTION',
    },
    {
        name: 'permission:delete',
        displayName: '删除权限',
        description: '删除权限',
        resource: 'permission',
        action: 'delete',
        type: 'ACTION',
    },
    {
        name: 'permission:manage',
        displayName: '权限管理',
        description: '权限管理菜单权限',
        resource: 'permission',
        action: 'manage',
        type: 'MENU',
    },
    // 影片管理权限
    {
        name: 'movie:read',
        displayName: '查看影片',
        description: '查看影片列表和详情',
        resource: 'movie',
        action: 'read',
        type: 'ACTION',
    },
    {
        name: 'movie:create',
        displayName: '创建影片',
        description: '添加新影片',
        resource: 'movie',
        action: 'create',
        type: 'ACTION',
    },
    {
        name: 'movie:update',
        displayName: '更新影片',
        description: '修改影片信息',
        resource: 'movie',
        action: 'update',
        type: 'ACTION',
    },
    {
        name: 'movie:delete',
        displayName: '删除影片',
        description: '删除影片',
        resource: 'movie',
        action: 'delete',
        type: 'ACTION',
    },
    {
        name: 'movie:manage',
        displayName: '影片管理',
        description: '影片管理菜单权限',
        resource: 'movie',
        action: 'manage',
        type: 'MENU',
    },
    // 演员管理权限
    {
        name: 'star:read',
        displayName: '查看演员',
        description: '查看演员列表和详情',
        resource: 'star',
        action: 'read',
        type: 'ACTION',
    },
    {
        name: 'star:create',
        displayName: '创建演员',
        description: '添加新演员',
        resource: 'star',
        action: 'create',
        type: 'ACTION',
    },
    {
        name: 'star:update',
        displayName: '更新演员',
        description: '修改演员信息',
        resource: 'star',
        action: 'update',
        type: 'ACTION',
    },
    {
        name: 'star:delete',
        displayName: '删除演员',
        description: '删除演员',
        resource: 'star',
        action: 'delete',
        type: 'ACTION',
    },
    {
        name: 'star:manage',
        displayName: '演员管理',
        description: '演员管理菜单权限',
        resource: 'star',
        action: 'manage',
        type: 'MENU',
    },
    // 统计数据权限
    {
        name: 'stats:read',
        displayName: '查看统计',
        description: '查看统计数据',
        resource: 'stats',
        action: 'read',
        type: 'ACTION',
    },
    {
        name: 'stats:manage',
        displayName: '统计管理',
        description: '统计管理菜单权限',
        resource: 'stats',
        action: 'manage',
        type: 'MENU',
    },
    // 系统管理权限
    {
        name: 'system:read',
        displayName: '查看系统信息',
        description: '查看系统配置和状态',
        resource: 'system',
        action: 'read',
        type: 'ACTION',
    },
    {
        name: 'system:update',
        displayName: '更新系统配置',
        description: '修改系统配置',
        resource: 'system',
        action: 'update',
        type: 'ACTION',
    },
    {
        name: 'system:manage',
        displayName: '系统管理',
        description: '系统管理菜单权限',
        resource: 'system',
        action: 'manage',
        type: 'MENU',
    },
    // 数据导入权限
    {
        name: 'import:execute',
        displayName: '执行数据导入',
        description: '执行数据导入操作',
        resource: 'import',
        action: 'execute',
        type: 'ACTION',
    },
    {
        name: 'import:manage',
        displayName: '数据导入管理',
        description: '数据导入管理菜单权限',
        resource: 'import',
        action: 'manage',
        type: 'MENU',
    },
];
/**
 * 默认角色数据
 */
const defaultRoles = [
    {
        name: 'super_admin',
        displayName: '超级管理员',
        description: '拥有所有权限的超级管理员',
        isSystem: true,
        priority: 100,
    },
    {
        name: 'admin',
        displayName: '管理员',
        description: '拥有大部分管理权限的管理员',
        isSystem: true,
        priority: 80,
    },
    {
        name: 'editor',
        displayName: '编辑员',
        description: '拥有内容管理权限的编辑员',
        isSystem: true,
        priority: 60,
    },
    {
        name: 'viewer',
        displayName: '查看员',
        description: '只有查看权限的用户',
        isSystem: true,
        priority: 40,
    },
];
/**
 * 角色权限分配
 */
const rolePermissions = {
    super_admin: 'all', // 所有权限
    admin: [
        'dashboard:view',
        'user:read', 'user:create', 'user:update', 'user:manage',
        'role:read', 'role:manage',
        'movie:read', 'movie:create', 'movie:update', 'movie:delete', 'movie:manage',
        'star:read', 'star:create', 'star:update', 'star:delete', 'star:manage',
        'stats:read', 'stats:manage',
        'system:read', 'system:manage',
        'import:execute', 'import:manage',
    ],
    editor: [
        'dashboard:view',
        'user:read', 'user:manage',
        'movie:read', 'movie:create', 'movie:update', 'movie:manage',
        'star:read', 'star:create', 'star:update', 'star:manage',
        'stats:read', 'stats:manage',
    ],
    viewer: [
        'dashboard:view',
        'user:read', 'user:manage',
        'movie:read', 'movie:manage',
        'star:read', 'star:manage',
        'stats:read', 'stats:manage',
    ],
};
async function main() {
    console.log('🌱 开始初始化RBAC权限数据...');
    try {
        // 1. 创建权限
        console.log('📝 创建默认权限...');
        const createdPermissions = new Map();
        for (const permission of defaultPermissions) {
            const existingPermission = await prisma.permission.findUnique({
                where: { name: permission.name },
            });
            if (!existingPermission) {
                const created = await prisma.permission.create({
                    data: {
                        ...permission,
                        isSystem: true,
                    },
                });
                createdPermissions.set(permission.name, created);
                console.log(`  ✅ 创建权限: ${permission.displayName}`);
            }
            else {
                createdPermissions.set(permission.name, existingPermission);
                console.log(`  ⏭️  权限已存在: ${permission.displayName}`);
            }
        }
        // 2. 创建角色
        console.log('👥 创建默认角色...');
        const createdRoles = new Map();
        for (const role of defaultRoles) {
            const existingRole = await prisma.role.findUnique({
                where: { name: role.name },
            });
            if (!existingRole) {
                const created = await prisma.role.create({
                    data: role,
                });
                createdRoles.set(role.name, created);
                console.log(`  ✅ 创建角色: ${role.displayName}`);
            }
            else {
                createdRoles.set(role.name, existingRole);
                console.log(`  ⏭️  角色已存在: ${role.displayName}`);
            }
        }
        // 3. 分配角色权限
        console.log('🔗 分配角色权限...');
        for (const [roleName, permissions] of Object.entries(rolePermissions)) {
            const role = createdRoles.get(roleName);
            if (!role)
                continue;
            // 删除现有权限分配
            await prisma.rolePermission.deleteMany({
                where: { roleId: role.id },
            });
            let permissionIds = [];
            if (permissions === 'all') {
                // 超级管理员获得所有权限
                permissionIds = Array.from(createdPermissions.values()).map(p => p.id);
            }
            else {
                // 根据权限名称获取权限ID
                permissionIds = permissions
                    .map(permName => createdPermissions.get(permName)?.id)
                    .filter(Boolean);
            }
            if (permissionIds.length > 0) {
                await prisma.rolePermission.createMany({
                    data: permissionIds.map(permissionId => ({
                        roleId: role.id,
                        permissionId,
                    })),
                });
                console.log(`  ✅ 为角色 ${role.displayName} 分配了 ${permissionIds.length} 个权限`);
            }
        }
        // 4. 创建默认管理员用户（如果不存在）
        console.log('👤 检查默认管理员用户...');
        const adminUser = await prisma.user.findFirst({
            where: {
                OR: [
                    { username: 'admin' },
                    { email: '<EMAIL>' },
                ],
            },
        });
        if (!adminUser) {
            console.log('  ⚠️  未找到管理员用户，请手动创建管理员账户');
            console.log('  💡 提示：可以通过注册功能创建用户，然后使用以下命令分配超级管理员角色：');
            console.log('     npx prisma studio');
        }
        else {
            // 为现有管理员分配超级管理员角色
            const superAdminRole = createdRoles.get('super_admin');
            if (superAdminRole) {
                const existingUserRole = await prisma.userRole.findUnique({
                    where: {
                        userId_roleId: {
                            userId: adminUser.id,
                            roleId: superAdminRole.id,
                        },
                    },
                });
                if (!existingUserRole) {
                    await prisma.userRole.create({
                        data: {
                            userId: adminUser.id,
                            roleId: superAdminRole.id,
                        },
                    });
                    console.log(`  ✅ 为用户 ${adminUser.username || adminUser.email} 分配了超级管理员角色`);
                }
                else {
                    console.log(`  ⏭️  用户 ${adminUser.username || adminUser.email} 已有超级管理员角色`);
                }
            }
        }
        console.log('\n🎉 RBAC权限数据初始化完成！');
        console.log('\n📊 初始化统计：');
        console.log(`  - 权限数量: ${createdPermissions.size}`);
        console.log(`  - 角色数量: ${createdRoles.size}`);
        console.log(`  - 角色权限分配: ${Object.keys(rolePermissions).length} 个角色`);
    }
    catch (error) {
        console.error('❌ 初始化过程中出错:', error);
        throw error;
    }
}
main()
    .catch((e) => {
    console.error('❌ Seed脚本执行失败:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map
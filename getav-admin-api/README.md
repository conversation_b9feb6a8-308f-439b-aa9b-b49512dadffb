# GETAV.NET 后台管理API服务

这是GETAV.NET项目的后台管理API服务，为getav-admin管理面板提供数据接口。

## 技术栈

- **后端框架**: Express.js + TypeScript
- **数据库ORM**: Prisma
- **数据库**: PostgreSQL
- **认证**: JWT + bcrypt
- **开发工具**: tsx, ESLint

## 项目结构

```
getav-admin-api/
├── src/
│   ├── controllers/     # 控制器层
│   ├── middleware/      # 中间件
│   ├── routes/         # 路由定义
│   ├── services/       # 业务逻辑层
│   ├── types/          # TypeScript类型
│   ├── utils/          # 工具函数
│   └── app.ts          # 应用入口
├── prisma/             # 数据库配置
├── package.json
├── tsconfig.json
└── .env                # 环境变量
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并配置相应的环境变量：

```bash
cp .env.example .env
```

### 3. 生成Prisma客户端

```bash
npm run db:generate
```

### 4. 启动开发服务器

```bash
npm run dev
```

服务器将在 http://localhost:8000 启动

## 可用脚本

- `npm run dev` - 启动开发服务器（热重载）
- `npm run build` - 构建生产版本
- `npm start` - 启动生产服务器
- `npm run lint` - 运行ESLint检查
- `npm run lint:fix` - 自动修复ESLint错误
- `npm run type-check` - TypeScript类型检查
- `npm run db:generate` - 生成Prisma客户端
- `npm run db:push` - 推送数据库模式更改
- `npm run db:studio` - 启动Prisma Studio

## API接口

### 认证接口
- `POST /api/auth/login` - 管理员登录
- `POST /api/auth/logout` - 登出
- `GET /api/auth/me` - 获取当前用户信息

### 影片管理
- `GET /api/movies` - 获取影片列表
- `GET /api/movies/:id` - 获取影片详情
- `PUT /api/movies/:id` - 更新影片信息
- `DELETE /api/movies/:id` - 删除影片

### 演员管理
- `GET /api/stars` - 获取演员列表
- `GET /api/stars/:id` - 获取演员详情
- `PUT /api/stars/:id` - 更新演员信息
- `DELETE /api/stars/:id` - 删除演员

### 用户管理
- `GET /api/users` - 获取用户列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息
- `DELETE /api/users/:id` - 删除用户

### 统计数据
- `GET /api/stats` - 获取统计数据

### 数据导入
- `POST /api/import/start` - 开始数据导入
- `GET /api/import/status` - 获取导入状态
- `POST /api/import/stop` - 停止数据导入

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `PORT` | 服务器端口 | `8000` |
| `NODE_ENV` | 运行环境 | `development` |
| `DATABASE_URL` | 数据库连接字符串 | - |
| `JWT_SECRET` | JWT密钥 | - |
| `JWT_EXPIRES_IN` | JWT过期时间 | `7d` |
| `CORS_ORIGIN` | CORS允许的源 | `http://localhost:4000` |
| `ADMIN_USERNAME` | 管理员用户名 | `admin` |
| `ADMIN_PASSWORD` | 管理员密码 | `admin123` |

## 开发说明

1. 本项目使用TypeScript开发，请确保类型安全
2. 所有API接口都需要JWT认证（除登录接口外）
3. 使用Prisma ORM操作数据库，共享主项目的数据库
4. 遵循RESTful API设计规范
5. 错误处理统一使用ApiError类

## 部署

### 生产环境部署

1. 构建项目：
```bash
npm run build
```

2. 启动生产服务器：
```bash
npm start
```

### Docker部署

```bash
# 构建镜像
docker build -t getav-admin-api .

# 运行容器
docker run -p 8000:8000 --env-file .env getav-admin-api
```

## 许可证

MIT License
